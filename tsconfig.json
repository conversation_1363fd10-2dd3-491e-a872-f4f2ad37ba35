{
  "compilerOptions": {
    /* Bundled projects */
    "lib": ["dom", "dom.iterable", "ES2022"],
    "noEmit": true,
    "module": "ESNext",
    "moduleResolution": "Bundler",
    "jsx": "preserve",
    "plugins": [{ "name": "next" }],
    "incremental": true,

    "baseUrl": ".",
    "paths": {
      "~/*": [
        "./src/*"
      ]
    },
    "target": "es2022",
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "esModuleInterop": true,
    "resolveJsonModule": true,
    "moduleDetection": "force",
    "isolatedModules": true,
    "checkJs": true,
    "noUncheckedIndexedAccess": true,
  },
  "include": [
    "next-env.d.ts",
    "src/server/types/next-auth.d.ts",
    "**/*.ts",
    "**/*.tsx",
    "**/*.cjs",
    "**/*.mjs",
    ".next/types/**/*.ts"
  ],
  "exclude": [
    "node_modules",
    "test"
  ]
}
