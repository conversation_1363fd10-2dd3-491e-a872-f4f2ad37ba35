import type { NextRequest } from "next/server";
import { env } from "../../../env.js";
import { NextResponse } from "next/server";
import url from "url";
import { stripe } from "../../../utils/stripe/stripe";
import type Strip<PERSON> from "stripe";
import { downloadReportFile, createPayoutExportFile } from "../../../utils/stripe/reportHelper";
import prisma from "../../../server/db/prisma";
import { LogType, PayoutStatus, NotificationType } from "@prisma/client";
import Logger from "~/server/logger/logger";
import fs from "fs";
import { createSystemNotificationForAdmins } from "~/utils/notifications/createSystemNotification";

const onReportRunEvent = async (event: any) => {
  let report: Stripe.Reporting.ReportRun;
  try {
    Logger("Event Type Valid", "Event Type Valid", "stripe", LogType.DEBUG);

    // cast necessary. If not javascript default object is assumed
    report = event.data.object as Stripe.Reporting.ReportRun;
    try {
      fs.writeFileSync(
        `raw_report_object_${report.parameters.payout}.json`,
        JSON.stringify(report),
      );
    } catch (e) {
      if (report?.result?.url && report.result.filename) {
        const parsedUrl = url.parse(report?.result?.url);
        Logger(
          `Storing raw Report.Run object failed`,
          "Dumping Report.Run object to disk failed",
          "stripe",
          LogType.ERROR,
        );
      }
    }

    if (report?.result?.url && report.result.filename) {
      const parsedUrl = url.parse(report?.result?.url);
      Logger(`parsed url is ${parsedUrl.path}`, "Report URL", "stripe", LogType.DEBUG);

      const options = {
        hostname: parsedUrl.hostname,
        path: parsedUrl.path,
        method: "GET",
        headers: {
          Authorization: `Bearer ${env.STRIPE_SECRET_KEY}`,
        },
      };
      if (report.parameters.payout) {
        await prisma.stripePayout.update({
          where: { id: report.parameters.payout },
          data: { status: PayoutStatus.STRIPE_REPORT_RECEIVED },
        });

        const reportFilePath = `${env.STRIPE_PAYOUT_REPORTS}/${report.result.filename}`;

        await downloadReportFile(options, reportFilePath, report.parameters.payout);

        const success = createPayoutExportFile(report);
        if (!success) {
          NextResponse.json("Error creating payoutfile. Details see Logger entries", {
            status: 404,
          });
        }
      } else {
        NextResponse.json("No payout found", { status: 404 });
      }
    }
  } catch (err) {
    NextResponse.json("error", { status: 500 });
  }
};

const onPaymentSucceededEvent = async (event: any) => {
  Logger(
    `Payment Intent Succeeded ${JSON.stringify(event.data.object)}`,
    "Payment Intent Succeeded",
    "stripe",
    LogType.ERROR,
  );
  const paymentIntent = event.data.object as Stripe.PaymentIntent;
  Logger(
    `PaymentIntent Check: ID=${paymentIntent?.id}, CDR_ID=${paymentIntent?.metadata?.cdr_id}`,
    "Payment Intent Debug",
    "stripe",
    LogType.ERROR,
  );

  if (paymentIntent["metadata"]?.cdr_id && paymentIntent?.id) {
    Logger(
      `PaymentIntent If is okay for ${paymentIntent?.metadata?.cdr_id}`,
      "Payment Intent Debug",
      "stripe",
      LogType.ERROR,
    );

    try {
      const newPaymentIntent = await prisma.paymentIntent.create({
        data: {
          id: paymentIntent.id,
          cdrId: paymentIntent.metadata.cdr_id,
          status: paymentIntent.status,
          invoiceId: null,
        },
      });
      if (!newPaymentIntent) {
        return NextResponse.json(`${paymentIntent?.metadata?.cdr_id ?? "no cdr id"} not created`, {
          status: 500,
        });
      }
      return NextResponse.json(`${paymentIntent?.metadata?.cdr_id ?? "no cdr id"} created`, {
        status: 200,
      });
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : "An unknown error occurred";
      return NextResponse.json(`error1 - ${errorMessage}`, { status: 500 });
    }
  } else {
    try {
      const updatedPaymentIntent = await prisma.paymentIntent.update({
        where: { id: paymentIntent.id },
        data: { status: paymentIntent.status },
      });
      if (updatedPaymentIntent && updatedPaymentIntent.invoiceId) {
        await prisma.invoice.update({
          where: { id: updatedPaymentIntent.invoiceId },
          data: { paidAmount: paymentIntent.amount / 100, paidOnDate: new Date() },
        });
        return NextResponse.json("sepa updated", { status: 200 });
      }
    } catch (e) {
      return NextResponse.json("error2", { status: 500 });
    }
  }
  return NextResponse.json("default", { status: 200 });
};

const onSetupIntentSucceededEvent = async (event: any) => {
  Logger(
    `Setup Intent Succeeded ${JSON.stringify(event.data.object)}`,
    "Setup Intent Succeeded",
    "stripe",
    LogType.INFO,
  );

  const setupIntent = event.data.object as Stripe.SetupIntent;

  try {
    // Get customer information from Stripe
    if (setupIntent.customer && typeof setupIntent.customer === "string") {
      const customer = await stripe.customers.retrieve(setupIntent.customer);

      if (!customer.deleted) {
        // Check if this is a user or contact based on metadata
        const eulektroUserId = customer.metadata?.eulektroUserId;

        if (eulektroUserId) {
          // This is a private user SEPA mandate
          const user = await prisma.user.findUnique({
            where: { id: eulektroUserId },
            select: { name: true, lastName: true },
          });

          if (user) {
            await createSystemNotificationForAdmins({
              nachricht: `SEPA-Lastschriftmandat hinterlegt von ${user.name} ${user.lastName}`,
              type: NotificationType.INFO,
            });

            Logger(
              `Created admin notification for user SEPA mandate: ${user.name} ${user.lastName}`,
              "SEPA Notification Created",
              "stripe",
              LogType.INFO,
            );
          }
        } else {
          // This is likely a company SEPA mandate - find contact by stripeCustomerId
          const contact = await prisma.contact.findFirst({
            where: { stripeCustomerId: setupIntent.customer },
            select: { companyName: true, name: true },
          });

          if (contact) {
            await createSystemNotificationForAdmins({
              nachricht: `Firmen-SEPA-Lastschriftmandat hinterlegt von ${
                contact.companyName || contact.name
              }`,
              type: NotificationType.INFO,
            });

            Logger(
              `Created admin notification for company SEPA mandate: ${
                contact.companyName || contact.name
              }`,
              "SEPA Notification Created",
              "stripe",
              LogType.INFO,
            );
          }
        }
      }
    }

    return NextResponse.json("setup_intent processed", { status: 200 });
  } catch (error) {
    Logger(
      `Error processing setup_intent.succeeded: ${error}`,
      "Setup Intent Error",
      "stripe",
      LogType.ERROR,
    );
    return NextResponse.json("error processing setup_intent", { status: 500 });
  }
};

export async function POST(request: NextRequest) {
  Logger("Received Stripe Webhook", "New Webhook", "stripe", LogType.INFO);
  const sig = request.headers.get("stripe-signature") as string | string[];
  // TEST
  const endpointSecret = env.STRIPE_WEBHOOK_SECRET;

  const raw = await request.text();

  let event;

  try {
    Logger("Trying to construct Event", "Event Construction", "stripe", LogType.INFO);

    event = stripe.webhooks.constructEvent(raw, sig, endpointSecret);

    if (event.type == "reporting.report_run.succeeded") {
      await onReportRunEvent(event);
    } else if (event.type == "payment_intent.succeeded") {
      return await onPaymentSucceededEvent(event);
    } else if (event.type == "setup_intent.succeeded") {
      return await onSetupIntentSucceededEvent(event);
    } else {
      Logger(
        "Event it not report_run.succeeded, payment_intent.succeeded or setup_intent.succeeded so return",
        "Event Type Ignored",
        "stripe",
        LogType.INFO,
      );

      return NextResponse.json("success - event type ignored");
    }
  } catch (e) {
    return NextResponse.json("exception in webhook", { status: 500 });
  }

  return NextResponse.json("main success");
}
