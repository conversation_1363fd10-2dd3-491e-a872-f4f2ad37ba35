"use client";
import { useMemo } from "react";
import type { Ou } from "@prisma/client";
import { userStore } from "~/server/zustand/store";
import { useEffect, useTransition } from "react";
import { selectOuForUser } from "~/component/bar/actions";
import { useRouter, usePathname } from "next/navigation";
import Dropdown from "~/app/(app)/util/Dropdown";

import { SlOrganization } from "react-icons/sl";
import { ouColors } from "~/styles/oucolors/oucolors";

interface Props {
  ous: Ou[];
  defaultOuId: string;
}

const OUSelect = ({ ous, defaultOuId }: Props) => {
  const state = userStore();
  const router = useRouter();
  const pathname = usePathname();

  const [_isPending, startTransition] = useTransition();

  const handleOuChange = (ouId: string) => {
    startTransition(() => void selectOuForUser(ouId));
    const ouOption = options.find((ou) => ou.id == ouId);
    state.setSelectedOuId(ouId);
    state.setSelectedOuName(ouOption?.label ?? "");

    // Spezielle Behandlung für adhoc-tarif Seite
    if (pathname.startsWith("/adhoc-tarif")) {
      // Verzögerung für adhoc-tarif um sicherzustellen dass Session aktualisiert ist
      setTimeout(() => {
        window.location.reload();
      }, 300);
    } else {
      router.refresh(); // due to ou change, colors will change on server, so refresh
    }
  };
  const getBackgroundColor = (label: string) => {
    if (ouColors[label]) {
      const oucol = ouColors[label];
      if (oucol) {
        return oucol["--color-primary"];
      }
    }
    const oucol = ouColors.Default;
    if (oucol) {
      return oucol["--color-primary"];
    }
    return "";
  };

  useEffect(() => {
    if (state.selectedOuId) {
      return;
    }

    const ou = options.find((ou) => ou.id == defaultOuId);
    state.setSelectedOuId(defaultOuId);
    state.setSelectedOuName(ou?.label ?? "");
  }, [state.selectedOuId]);

  const options = useMemo(() => {
    ous = ous.filter((ou) => !ou.deleted && !ou.hide);
    const specialItemIndex = ous.findIndex((item) => item.name === "Eulektro_Public");
    const specialItem = ous.splice(specialItemIndex, 1)[0];
    ous = ous.sort((a, b) => a.name.localeCompare(b.name));
    if (specialItem) {
      ous.unshift(specialItem);
    }

    const list = ous.map((item) => {
      const option = {
        label: item.name,
        id: item.id,
        selected: item.id == state.selectedOuId,
        color: getBackgroundColor(item.name),
      };

      return option;
    });
    return list;
  }, [state.selectedOuId]);

  return (
    <>
      <div className="">
        <Dropdown
          title={"Auswahl der Organisationseinheit"}
          className={"w-full max-w-52 sm:max-w-64"}
          icon={<SlOrganization className={"mr-1 items-center"} size={18} />}
          options={options}
          onChange={handleOuChange}
          searchable={true}
          searchPlaceholder={"Suche nach Organisationseinheit"}
          canDelete={false}
          onDelete={() => console.log("delete")}
        />
      </div>
    </>
  );
};

export default OUSelect;
