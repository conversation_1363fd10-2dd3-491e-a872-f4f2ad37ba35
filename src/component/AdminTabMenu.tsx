"use client";
import React, { useState, useMemo, useCallback, useRef } from "react";
import { MenuItem, MenuType, TabMenuSection } from "~/utils/menu/menuDef";
import MenuEntry from "~/component/MenuEntry";
import { usePathname } from "next/navigation";
import { findMenuItemWithTabByPathname } from "~/utils/menu/menu";
import RippleEffect from "~/component/ui/RippleEffect";

interface AdminTabMenuProps {
  visibleMenuItems: MenuItem[];
}

const AdminTabMenu = ({ visibleMenuItems }: AdminTabMenuProps) => {
  const pathname = usePathname();
  const isUserInteractionRef = useRef(false);

  // Memoize tab sections to prevent unnecessary re-renders
  const tabSections = useMemo(
    () => visibleMenuItems.filter(
      (item): item is TabMenuSection => item.type === MenuType.tabSection,
    ),
    [visibleMenuItems]
  );

  // Bestimme den aktiven Tab basierend auf der aktuellen URL
  const getActiveTabName = useCallback((currentPathname: string, sections: TabMenuSection[]) => {
    if (!currentPathname || sections.length === 0) return "";

    // Verwende die neue Funktion, um das Menu-Item mit dem dazugehörigen Tab zu finden
    const result = findMenuItemWithTabByPathname(currentPathname, visibleMenuItems);

    if (result?.parentTab) {
      return result.parentTab.name;
    }

    // Fallback zum ersten Tab
    return sections[0]?.name || "";
  }, [visibleMenuItems]);

  // State für aktiven Tab
  const [activeTab, setActiveTab] = useState<string>("");

  // Bestimme aktiven Tab basierend auf aktueller Route
  React.useEffect(() => {
    // Nur automatisch setzen wenn es keine Benutzerinteraktion war
    if (!isUserInteractionRef.current && tabSections.length > 0) {
      const targetTab = getActiveTabName(pathname, tabSections);

      if (targetTab && targetTab !== activeTab) {
        setActiveTab(targetTab);
      } else if (!activeTab && tabSections.length > 0) {
        // Fallback: Setze ersten Tab wenn noch kein Tab aktiv ist
        setActiveTab(tabSections[0].name);
      }
    }
    // Reset user interaction flag after route change
    isUserInteractionRef.current = false;
  }, [pathname, tabSections, getActiveTabName, activeTab]);

  // Handle manual tab selection
  const handleTabClick = useCallback((tabName: string) => {
    isUserInteractionRef.current = true;
    setActiveTab(tabName);
  }, []);

  const activeTabSection = useMemo(
    () => tabSections.find((tab) => tab.name === activeTab),
    [tabSections, activeTab]
  );

  return (
    <div className="w-full">
      {/* Tab Navigation */}
      <div className="mb-4 border-b border-gray-200">
        <nav className="flex space-x-1 overflow-x-auto">
          {tabSections.map((tab) => (
            <RippleEffect
              key={tab.name}
              onClick={() => handleTabClick(tab.name)}
              className={`
                flex h-10 w-10 items-center justify-center rounded-t-lg border-b-2 text-lg transition-colors duration-200 ease-soft-in-out
                ${
                  activeTab === tab.name
                    ? "border-primary bg-gray-200 text-primary"
                    : "border-transparent text-slate-500 hover:border-gray-300 hover:text-slate-700"
                }
              `}
              rippleColor={activeTab === tab.name ? "rgba(var(--color-primary), 0.3)" : "rgba(148, 163, 184, 0.3)"}
              duration={600}
            >
              <span title={tab.name}>{tab.icon}</span>
            </RippleEffect>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="tab-content">
        {activeTabSection && (
          <ul>
            {activeTabSection.items.map((menuItem) => (
              <MenuEntry
                key={menuItem.name}
                menuItem={menuItem}
                visibleMenuItems={visibleMenuItems}
              />
            ))}
          </ul>
        )}
      </div>
    </div>
  );
};

export default AdminTabMenu;
