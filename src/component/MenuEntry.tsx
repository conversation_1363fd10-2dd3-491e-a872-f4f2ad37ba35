"use client";
import React, { useState } from "react";

import Link from "next/link";
import { MenuItem, MenuType, CollapsibleMenuSection, TabMenuSection } from "~/utils/menu/menuDef";
import { usePathname } from "next/navigation";
import { findMenuItemByPathname } from "~/utils/menu/menu";
import RippleEffect from "~/component/ui/RippleEffect";

interface MenuEntryProps {
  menuItem: MenuItem;
  visibleMenuItems: MenuItem[];
}

const MenuEntry = ({ menuItem, visibleMenuItems }: MenuEntryProps) => {
  const pathname = usePathname();
  const [isCollapsed, setIsCollapsed] = useState(
    menuItem.type === MenuType.collapsibleSection
      ? ((menuItem as CollapsibleMenuSection).defaultCollapsed ?? false)
      : false,
  );

  if (!pathname) {
    return <>No pathname found</>;
  }
  const activeMenuEntry = findMenuItemByPathname(pathname, visibleMenuItems);

  if (menuItem.type === MenuType.divider) {
    return (
      <li key={menuItem.name} className="mt-4 w-full">
        <h6 className="ml-2 pl-6 text-xs font-bold uppercase leading-tight opacity-60 dark:text-white">
          {menuItem.name}
        </h6>
      </li>
    );
  } else if (menuItem.type === MenuType.tabSection) {
    // Tab-Sektionen werden von AdminTabMenu gehandhabt, hier nicht rendern
    return null;
  } else if (menuItem.type === MenuType.collapsibleSection) {
    const collapsibleSection = menuItem as CollapsibleMenuSection;
    return (
      <li key={menuItem.name} className="mt-4 w-full">
        <RippleEffect
          onClick={() => setIsCollapsed(!isCollapsed)}
          className="mx-4 my-0 flex items-center whitespace-nowrap rounded-xl py-2.7 text-sm font-medium text-slate-500 shadow-none transition-colors ease-soft-in-out hover:cursor-pointer hover:bg-gray-200 dark:text-white dark:opacity-80"
          rippleColor="rgba(148, 163, 184, 0.4)"
          duration={500}
        >
          <div className="mr-2 flex h-8 w-8 items-center justify-center rounded-lg bg-white bg-center text-center shadow-md">
            <span className="text-lg">{collapsibleSection.icon || "📁"}</span>
          </div>
          <span className="pointer-events-none ml-1 flex-grow font-medium opacity-100 duration-300 ease-soft">
            {menuItem.name}
          </span>
          <span className="ml-2 text-xs transition-transform duration-200 ease-soft">
            {isCollapsed ? "▶" : "▼"}
          </span>
        </RippleEffect>
        {!isCollapsed && (
          <ul className="ml-4 border-l border-gray-200 pl-2">
            {collapsibleSection.items.map((subItem) => (
              <MenuEntry
                key={subItem.name}
                menuItem={subItem}
                visibleMenuItems={visibleMenuItems}
              />
            ))}
          </ul>
        )}
      </li>
    );
  } else {
    const isActive = activeMenuEntry?.name === menuItem.name;

    return (
      <li
        key={menuItem.name}
        className={`mt-0.5 w-full rounded-xl ${
          isActive
            ? "border-bg-gray-300 bg-gray-200"
            : "hover:bg-gray-200"
        }`}
      >
        <Link href={menuItem.href} className="block">
          <RippleEffect
            className={`mx-4 my-0 flex items-center whitespace-nowrap rounded-xl py-2.7 text-sm font-medium text-slate-500 shadow-none transition-colors ease-soft-in-out hover:cursor-pointer dark:text-white dark:opacity-80`}
            rippleColor={isActive ? "rgba(var(--color-primary), 0.3)" : "rgba(148, 163, 184, 0.4)"}
            duration={500}
          >
            <div className="mr-2 flex h-8 w-8 items-center justify-center rounded-lg bg-white bg-center text-center shadow-md">
              <span className="text-lg">{menuItem.icon}</span>
            </div>
            <span className="pointer-events-none ml-1 font-medium opacity-100 duration-300 ease-soft">
              {menuItem.name}
            </span>
          </RippleEffect>
        </Link>
      </li>
    );
  }
};

export default MenuEntry;
