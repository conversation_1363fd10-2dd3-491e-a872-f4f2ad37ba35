import type { ButtonHTMLAttributes } from "react";
import React from "react";

type ButtonProps = {
  ref?: React.RefObject<HTMLButtonElement>;
  children: React.ReactNode;
  className?: string;
  small?: boolean;
} & ButtonHTMLAttributes<HTMLButtonElement>;

const Button: React.FC<ButtonProps> = ({ children, className = "", ref, small, ...props }) => {
  return (
    <button
      ref={ref}
      className={`overflow-hidden whitespace-nowrap rounded-lg bg-primary px-2 ${
        !small ? "py-2" : "text-sm"
      } font-bold text-white shadow-md transition-colors duration-300 hover:brightness-90 disabled:cursor-not-allowed disabled:brightness-90 ${className}`}
      {...props}
    >
      {children}
    </button>
  );
};

export default Button;
