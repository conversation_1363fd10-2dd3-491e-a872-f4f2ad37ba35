"use client";

import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import <PERSON><PERSON> from "~/component/button";
import { <PERSON><PERSON><PERSON><PERSON>, FiSave } from "react-icons/fi";
import { FaCheckCircle, FaExclamationTriangle } from "react-icons/fa";

const ThgCompanyContactSchema = z.object({
  companyName: z.string().min(1, "Firmenname ist erforderlich"),
  legalForm: z.string().optional(),
  registrationNumber: z.string().optional(),
  taxNumber: z.string().optional(),
  vatId: z.string().optional(),
  street: z.string().optional(),
  streetNumber: z.string().optional(),
  postalCode: z.string().optional(),
  city: z.string().optional(),
  country: z.string().optional(),
  contactPerson: z.string().optional(),
  phone: z.string().optional(),
  email: z.string().email("Ungültige E-Mail-Adresse").optional().or(z.literal("")),
  website: z.string().optional(),
  bankName: z.string().optional(),
  iban: z.string().optional(),
  bic: z.string().optional(),
  thgCertificateNumber: z.string().optional(),
  thgCertificateValid: z.string().optional(),
});

type ThgCompanyContactFormData = z.infer<typeof ThgCompanyContactSchema>;

interface ThgCompanyContactFormProps {
  onSave?: () => void;
}

const ThgCompanyContactForm: React.FC<ThgCompanyContactFormProps> = ({ onSave }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [isFetching, setIsFetching] = useState(true);
  const [saveSuccess, setSaveSuccess] = useState(false);
  const [saveError, setSaveError] = useState<string | null>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<ThgCompanyContactFormData>({
    resolver: zodResolver(ThgCompanyContactSchema),
  });

  // Daten beim Laden der Komponente abrufen
  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await fetch("/api/thg/company-contact");
        if (response.ok) {
          const data = await response.json();
          // Datum für Input-Feld formatieren
          if (data.thgCertificateValid) {
            data.thgCertificateValid = new Date(data.thgCertificateValid).toISOString().split('T')[0];
          }
          reset(data);
        } else {
          console.error("Failed to fetch company contact data");
        }
      } catch (error) {
        console.error("Error fetching company contact data:", error);
      } finally {
        setIsFetching(false);
      }
    };

    fetchData();
  }, [reset]);

  const onSubmit = async (data: ThgCompanyContactFormData) => {
    setIsLoading(true);
    setSaveError(null);
    setSaveSuccess(false);

    try {
      const response = await fetch("/api/thg/company-contact", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (response.ok) {
        setSaveSuccess(true);
        onSave?.();
        setTimeout(() => setSaveSuccess(false), 3000);
      } else {
        const errorData = await response.json();
        setSaveError(errorData.error || "Fehler beim Speichern der Daten");
      }
    } catch (error) {
      setSaveError("Netzwerkfehler. Bitte versuchen Sie es erneut.");
      console.error("Save error:", error);
    } finally {
      setIsLoading(false);
    }
  };

  if (isFetching) {
    return (
      <div className="flex items-center justify-center p-8">
        <FiLoader className="animate-spin text-2xl text-primary" />
        <span className="ml-2">Lade Firmendaten...</span>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-6">
      <div className="bg-white rounded-lg shadow-md p-6">
        <h2 className="text-2xl font-bold text-gray-800 mb-6">Firmendaten bearbeiten</h2>
        
        {saveSuccess && (
          <div className="mb-4 p-4 bg-green-100 border border-green-400 text-green-700 rounded flex items-center">
            <FaCheckCircle className="mr-2" />
            Firmendaten erfolgreich gespeichert!
          </div>
        )}

        {saveError && (
          <div className="mb-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded flex items-center">
            <FaExclamationTriangle className="mr-2" />
            {saveError}
          </div>
        )}

        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Grunddaten */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Firmenname *
              </label>
              <input
                {...register("companyName")}
                type="text"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              />
              {errors.companyName && (
                <p className="text-red-500 text-sm mt-1">{errors.companyName.message}</p>
              )}
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Rechtsform
              </label>
              <input
                {...register("legalForm")}
                type="text"
                placeholder="z.B. GmbH, AG, UG"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              />
            </div>
          </div>

          {/* Registrierungsdaten */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Handelsregisternummer
              </label>
              <input
                {...register("registrationNumber")}
                type="text"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Steuernummer
              </label>
              <input
                {...register("taxNumber")}
                type="text"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                USt-ID
              </label>
              <input
                {...register("vatId")}
                type="text"
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
              />
            </div>
          </div>

          {/* Adresse */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-800">Adresse</h3>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="md:col-span-3">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Straße
                </label>
                <input
                  {...register("street")}
                  type="text"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Hausnummer
                </label>
                <input
                  {...register("streetNumber")}
                  type="text"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  PLZ
                </label>
                <input
                  {...register("postalCode")}
                  type="text"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Stadt
                </label>
                <input
                  {...register("city")}
                  type="text"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Land
                </label>
                <input
                  {...register("country")}
                  type="text"
                  defaultValue="Deutschland"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                />
              </div>
            </div>
          </div>

          {/* Kontaktdaten */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-800">Kontaktdaten</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Ansprechpartner
                </label>
                <input
                  {...register("contactPerson")}
                  type="text"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Telefon
                </label>
                <input
                  {...register("phone")}
                  type="text"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  E-Mail
                </label>
                <input
                  {...register("email")}
                  type="email"
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                />
                {errors.email && (
                  <p className="text-red-500 text-sm mt-1">{errors.email.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Website
                </label>
                <input
                  {...register("website")}
                  type="text"
                  placeholder="https://..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"
                />
              </div>
            </div>
          </div>

          {/* Speichern Button */}
          <div className="flex justify-end pt-6">
            <Button
              type="submit"
              disabled={isLoading}
              className="flex items-center"
            >
              {isLoading ? (
                <FiLoader className="animate-spin mr-2" />
              ) : (
                <FiSave className="mr-2" />
              )}
              {isLoading ? "Speichere..." : "Speichern"}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ThgCompanyContactForm;
