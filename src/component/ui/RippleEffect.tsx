"use client";
import React, { useState, useCallback } from "react";

interface RippleProps {
  children: React.ReactNode;
  className?: string;
  onClick?: (event: React.MouseEvent<HTMLElement>) => void;
  disabled?: boolean;
  rippleColor?: string;
  duration?: number;
}

interface Ripple {
  id: number;
  x: number;
  y: number;
  size: number;
}

const RippleEffect: React.FC<RippleProps> = ({
  children,
  className = "",
  onClick,
  disabled = false,
  rippleColor = "rgba(255, 255, 255, 0.6)",
  duration = 600,
}) => {
  const [ripples, setRipples] = useState<Ripple[]>([]);

  const addRipple = useCallback(
    (event: React.MouseEvent<HTMLElement>) => {
      const rect = event.currentTarget.getBoundingClientRect();
      const size = Math.max(rect.width, rect.height);
      const x = event.clientX - rect.left - size / 2;
      const y = event.clientY - rect.top - size / 2;
      
      const newRipple: Ripple = {
        id: Date.now(),
        x,
        y,
        size,
      };

      setRipples((prev) => [...prev, newRipple]);

      // Remove ripple after animation
      setTimeout(() => {
        setRipples((prev) => prev.filter((ripple) => ripple.id !== newRipple.id));
      }, duration);
    },
    [duration]
  );

  const handleClick = useCallback(
    (event: React.MouseEvent<HTMLElement>) => {
      if (!disabled) {
        addRipple(event);
        onClick?.(event);
      }
    },
    [addRipple, onClick, disabled]
  );

  return (
    <div
      className={`relative overflow-hidden ${className}`}
      onClick={handleClick}
      style={{ cursor: disabled ? "default" : "pointer" }}
    >
      {children}
      
      {/* Ripple animations */}
      {ripples.map((ripple) => (
        <span
          key={ripple.id}
          className="absolute animate-ripple pointer-events-none rounded-full"
          style={{
            left: ripple.x,
            top: ripple.y,
            width: ripple.size,
            height: ripple.size,
            backgroundColor: rippleColor,
            animationDuration: `${duration}ms`,
          }}
        />
      ))}
    </div>
  );
};

export default RippleEffect;
