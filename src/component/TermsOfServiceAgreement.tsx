"use client";

import React, { useState } from "react";

interface TermsOfServiceAgreementProps {
  /** Whether the checkbox is checked */
  checked?: boolean;
  /** Callback when checkbox state changes */
  onChange?: (checked: boolean) => void;
  /** Whether the component is disabled */
  disabled?: boolean;
  /** Error message to display */
  error?: string;
  /** Custom CSS classes */
  className?: string;
  /** Whether to show the full terms text or just a link */
  showFullText?: boolean;
  /** Custom terms text (optional) */
  customTermsText?: string;
  /** Link to external terms page */
  termsLink?: string;
}

const TermsOfServiceAgreement: React.FC<TermsOfServiceAgreementProps> = ({
  checked = false,
  onChange,
  disabled = false,
  error,
  className = "",
  showFullText = false,
  customTermsText,
  termsLink = "/agb",
}) => {
  const [internalChecked, setInternalChecked] = useState(checked);

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newChecked = event.target.checked;
    setInternalChecked(newChecked);
    onChange?.(newChecked);
  };

  const isChecked = onChange ? checked : internalChecked;

  const defaultTermsText = `
Allgemeine Geschäftsbedingungen (AGB) für das Ladeportal

1. Geltungsbereich
1.1 Diese Allgemeinen Geschäftsbedingungen (nachfolgend "AGB") gelten für alle Verträge zwischen dem Betreiber der Ladeinfrastruktur (nachfolgend "Betreiber") und den Nutzern des Ladeportals (nachfolgend "Nutzer").
1.2 Die AGB regeln die Registrierung im Ladeportal, die Aktivierung und Nutzung von Ladekarten sowie die Abrechnung von Ladevorgängen für Elektrofahrzeuge.
1.3 Abweichende Bedingungen des Nutzers werden nicht anerkannt, es sei denn, der Betreiber stimmt ihrer Geltung ausdrücklich schriftlich zu.

2. Vertragsschluss und Registrierung
2.1 Der Vertrag kommt durch die Registrierung im Ladeportal und die Bestätigung dieser AGB zustande.
2.2 Die Registrierung kann auf zwei Wegen erfolgen:
   a) Durch Eingabe der Identifikationsnummer (Visual Number) einer bereits erhaltenen Ladekarte
   b) Durch Einladung eines berechtigten Managers
2.3 Bei der Registrierung ist zunächst nur die Angabe einer gültigen E-Mail-Adresse erforderlich. Vollständige Adressdaten müssen vor der Aktivierung oder Bestellung einer Ladekarte ergänzt werden.
2.4 Der Nutzer sichert zu, dass alle von ihm angegebenen Daten korrekt und vollständig sind. Er verpflichtet sich, Änderungen unverzüglich im Nutzerkonto zu aktualisieren.

3. SEPA-Lastschriftmandat
3.1 Zur Aktivierung einer vorhandenen Ladekarte oder zur Bestellung einer neuen Ladekarte ist die Erteilung eines SEPA-Lastschriftmandats zwingend erforderlich.
3.2 Mit Erteilung des SEPA-Lastschriftmandats ermächtigt der Nutzer den Betreiber, fällige Zahlungen vom angegebenen Konto mittels Lastschrift einzuziehen.
3.3 Der Nutzer verpflichtet sich, für ausreichende Deckung des Kontos zu sorgen. Kosten, die durch Nichteinlösung oder Rückbuchung der Lastschrift entstehen, trägt der Nutzer.
3.4 Bei Erteilung des SEPA-Lastschriftmandats stimmt der Nutzer gleichzeitig den ausgewählten Tarifen zu.

4. Leistungen und Tarife
4.1 Der Betreiber stellt Ladeinfrastruktur für Elektrofahrzeuge zur Verfügung. Die Abrechnung erfolgt nach den jeweils gültigen Tarifen.
4.2 Die Tarife können folgende Preiskomponenten enthalten:
   a) Energiepreis pro kWh
   b) Sessionpreis pro Ladevorgang
   c) Blockierungsgebühren für übermäßig lange Belegung der Ladestation
   d) Grundgebühren
   e) Einmalige Gebühren
4.3 Die jeweils gültigen Tarife werden im Ladeportal angezeigt und bei der Auswahl eines Tarifs transparent dargestellt.
4.4 Preisänderungen werden dem Nutzer mindestens 4 Wochen vor Inkrafttreten mitgeteilt. Der Nutzer hat in diesem Fall ein Sonderkündigungsrecht.

5. Ladekarten
5.1 Ladekarten bleiben Eigentum des Betreibers und sind bei Vertragsende zurückzugeben.
5.2 Der Nutzer ist verpflichtet, die Ladekarte sorgfältig aufzubewahren und vor Missbrauch zu schützen.
5.3 Der Verlust einer Ladekarte ist unverzüglich zu melden. Bis zur Meldung haftet der Nutzer für alle mit der Karte getätigten Ladevorgänge.
5.4 Die Weitergabe von Ladekarten an Dritte ist nur mit ausdrücklicher Zustimmung des Betreibers gestattet.

6. Abrechnung und Zahlung
6.1 Die Abrechnung erfolgt monatlich per SEPA-Lastschriftverfahren.
6.2 Der Nutzer erhält eine detaillierte Übersicht aller Ladevorgänge und der entstandenen Kosten.
6.3 Einwände gegen die Abrechnung sind innerhalb von 14 Tagen nach Erhalt schriftlich geltend zu machen. Nach Ablauf dieser Frist gilt die Abrechnung als genehmigt.
6.4 Bei Zahlungsverzug ist der Betreiber berechtigt, die Ladekarte zu sperren und Verzugszinsen in gesetzlicher Höhe zu berechnen.

7. Pflichten des Nutzers
7.1 Der Nutzer verpflichtet sich, die Ladeinfrastruktur sachgemäß zu nutzen und Beschädigungen zu vermeiden.
7.2 Nach Abschluss des Ladevorgangs ist die Ladestation unverzüglich freizumachen, um Blockierungsgebühren zu vermeiden.
7.3 Der Nutzer ist verpflichtet, seine Zugangsdaten zum Ladeportal geheim zu halten und vor dem Zugriff Dritter zu schützen.

8. Haftung
8.1 Der Betreiber haftet unbeschränkt für Schäden aus der Verletzung des Lebens, des Körpers oder der Gesundheit, die auf einer vorsätzlichen oder fahrlässigen Pflichtverletzung des Betreibers beruhen.
8.2 Für sonstige Schäden haftet der Betreiber nur bei Vorsatz und grober Fahrlässigkeit. Bei leichter Fahrlässigkeit haftet der Betreiber nur bei Verletzung wesentlicher Vertragspflichten und beschränkt auf den vorhersehbaren, vertragstypischen Schaden.
8.3 Der Nutzer haftet für alle Schäden, die durch unsachgemäße Nutzung der Ladeinfrastruktur entstehen.

9. Datenschutz
9.1 Die Verarbeitung personenbezogener Daten erfolgt gemäß der separaten Datenschutzerklärung.
9.2 Der Betreiber erhebt und verarbeitet personenbezogene Daten nur, soweit dies für die Durchführung des Vertrags erforderlich ist.
9.3 Für die Abrechnung werden Daten zu Ladevorgängen (Zeitpunkt, Dauer, Energiemenge, Standort) gespeichert und verarbeitet.

10. Vertragslaufzeit und Kündigung
10.1 Der Vertrag wird auf unbestimmte Zeit geschlossen.
10.2 Der Vertrag kann von beiden Seiten mit einer Frist von 30 Tagen zum Monatsende gekündigt werden.
10.3 Das Recht zur außerordentlichen Kündigung aus wichtigem Grund bleibt unberührt.
10.4 Bei Kündigung werden alle aktiven Ladekarten des Nutzers deaktiviert.
10.5 Die Kündigung bedarf der Textform (z.B. E-Mail).

11. Änderungen der AGB
11.1 Der Betreiber behält sich vor, diese AGB zu ändern, soweit dies aus triftigen Gründen erforderlich ist.
11.2 Änderungen werden dem Nutzer mindestens 4 Wochen vor Inkrafttreten mitgeteilt.
11.3 Widerspricht der Nutzer der Änderung nicht innerhalb von 4 Wochen nach Erhalt der Mitteilung, gelten die geänderten AGB als angenommen.
11.4 Im Falle des Widerspruchs ist der Betreiber berechtigt, den Vertrag zum Zeitpunkt des Inkrafttretens der Änderung zu kündigen.

12. Schlussbestimmungen
12.1 Es gilt deutsches Recht unter Ausschluss des UN-Kaufrechts.
12.2 Gerichtsstand für alle Streitigkeiten aus diesem Vertrag ist der Sitz des Betreibers, sofern der Nutzer Kaufmann, juristische Person des öffentlichen Rechts oder öffentlich-rechtliches Sondervermögen ist.
12.3 Sollten einzelne Bestimmungen dieser AGB unwirksam sein oder werden, bleibt die Wirksamkeit der übrigen Bestimmungen unberührt.
12.4 Mündliche Nebenabreden bestehen nicht. Änderungen und Ergänzungen des Vertrags bedürfen der Textform.
  `;

  const termsContent = customTermsText || defaultTermsText;

  return (
    <div className={`w-full ${className}`}>
      {showFullText && (
        <div className="mb-4 max-h-60 overflow-y-auto rounded-lg border border-gray-300 bg-gray-50 p-4 text-sm">
          <pre className="font-sans whitespace-pre-wrap text-gray-700">{termsContent}</pre>
        </div>
      )}

      <div className="flex items-start space-x-3">
        <input
          id="terms-checkbox"
          type="checkbox"
          checked={isChecked}
          onChange={handleChange}
          disabled={disabled}
          className={`
            relative float-left mt-1 h-5 w-5 cursor-pointer appearance-none rounded-1.4
            border border-solid border-slate-150 bg-white bg-contain bg-center bg-no-repeat
            align-top text-base transition-all duration-250 ease-soft
            after:absolute after:flex after:h-full after:w-full after:items-center
            after:justify-center after:text-xxs after:text-white after:opacity-0
            after:transition-all after:duration-250 after:ease-soft-in-out
            after:content-['✓'] checked:bg-primary checked:after:opacity-100
            focus:shadow-soft-primary-outline focus:outline-none
            disabled:cursor-not-allowed disabled:opacity-50
            ${error ? "border-red-500" : "border-slate-150"}
          `}
        />
        <label
          htmlFor="terms-checkbox"
          className={`cursor-pointer select-none text-sm leading-relaxed ${
            disabled ? "cursor-not-allowed opacity-50" : ""
          } ${error ? "text-red-700" : "text-gray-700"}`}
        >
          Ich habe die{" "}
          {showFullText ? (
            <span className="font-medium text-primary">Allgemeinen Geschäftsbedingungen</span>
          ) : (
            <a
              href={termsLink}
              target="_blank"
              rel="noopener noreferrer"
              className="hover:text-primary/80 font-medium text-primary underline"
              onClick={(e) => e.stopPropagation()}
            >
              Allgemeinen Geschäftsbedingungen
            </a>
          )}{" "}
          gelesen und akzeptiere diese.
        </label>
      </div>

      {error && <p className="mt-2 text-sm text-red-600">{error}</p>}
    </div>
  );
};

export default TermsOfServiceAgreement;
