import React from "react";

import { getServerSession } from "next-auth/next";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { getMenuForRole, MenuItem } from "~/utils/menu/menuDef";
import MenuEntry from "~/component/MenuEntry";
import AdminTabMenu from "~/component/AdminTabMenu";
import { Role } from "@prisma/client";



const Menu = async () => {
  const session = await getServerSession(authOptions);

  const role = session?.user?.role;

  if (!session) {
    return <></>;
  }

  if (!role) {
    return <></>;
  }

  // Hole das rollenspezifische Menü
  const visibleMenuItems = getMenuForRole(role);

  // Verwende Tab-Menü für Admin-Benutzer
  if (role === Role.ADMIN) {
    return <AdminTabMenu visibleMenuItems={visibleMenuItems} />;
  }

  // Standard-Menü für andere Rollen
  return (
    <>
      <ul>
        {visibleMenuItems.map((menuItem) => {
          return (
            <MenuEntry
              key={menuItem.name}
              menuItem={menuItem}
              visibleMenuItems={visibleMenuItems}
            />
          );
        })}
      </ul>
    </>
  );
};

export default Menu;
