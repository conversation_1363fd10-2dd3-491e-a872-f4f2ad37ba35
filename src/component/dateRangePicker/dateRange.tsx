"use client";

import React, { useContext, useEffect, useState } from "react";
import DateContext from "./dateContext";
import Button from "~/component/button";

const DateRangePicker: React.FC = () => {
  const { startDate, endDate, setStartDate, setEndDate } = useContext(DateContext);
  const [localStart, setLocalStart] = useState(startDate);
  const [localEnd, setLocalEnd] = useState(endDate);

  // external change - sync local state with context
  useEffect(() => {
    if (startDate != localStart) {
      setLocalStart(startDate);
    }
    if (endDate != localEnd) {
      setLocalEnd(endDate);
    }
  }, [startDate, endDate]);

  return (
    <div className={"flex justify-end gap-x-2 px-1"}>
      <div className={"flex-none items-center p-1"}>Zeitraum:</div>
      <div className={"border-primary  flex-none items-center rounded-md "}>
        <input
          type={"date"}
          id={"start"}
          className={`rounded-xl ${localStart !== startDate ? "bg-yellow-100" : ""} p-1`}
          value={localStart.toLocaleString("sv-SE").slice(0, 10)}
          onChange={(date: React.ChangeEvent<HTMLInputElement>) => {
            setLocalStart(date.target.valueAsDate || new Date());
          }}
        />
      </div>
      <div className={"flex-nonev items-center p-1"}>bis</div>
      <div className={"flex-none items-center"}>
        <input
          type={"date"}
          id={"end"}
          className={`rounded-xl ${localEnd !== endDate ? "bg-yellow-100" : ""} p-1`}
          value={localEnd.toLocaleString("sv-SE").slice(0, 10)}
          onChange={(date) => {
            setLocalEnd(date.target.valueAsDate || new Date());
            return date.target.valueAsDate;
          }}
        />
      </div>
      <div className={"p-1"}>
        <Button
          type={"button"}
          className={""}
          disabled={localStart == startDate && localEnd === endDate}
          onClick={() => {
            setStartDate(localStart);
            setEndDate(localEnd);
          }}
          small={true}
        >
          Übernehmen
        </Button>
      </div>
    </div>
  );
};

export default DateRangePicker;
