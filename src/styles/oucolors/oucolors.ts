export const ouColors: { [identifier: string]: { [csstag: string]: string } } = {
  Cambio: {
    "--color-primary": "#0062af",
    "--color-secondary": "#e5f1ff",
    "--color-primary-text": "#0062af",
    "--color-secondary-text": "#0062af",
  },
  Hank<PERSON>: {
    "--color-primary": "#ff9d00",
    "--color-secondary": "#114433",
    "--color-primary-text": "#000000",
    "--color-secondary-text": "#ff9d00",
  },
  "AH Beteiligungsgesellschaft mbH": {
    "--color-primary": "#FFD100",
    "--color-secondary": "#114433",
    "--color-primary-text": "#000000",
    "--color-secondary-text": "#FFD100",
  },
  "R & M Beteiligungsgesellschaft mbH": {
    "--color-primary": "#FFD100",
    "--color-secondary": "#114433",
    "--color-primary-text": "#000000",
    "--color-secondary-text": "#FFD100",
  },

  "Bremer Transport Taxi GmbH & Co. KG": {
    "--color-primary": "#FFD100",
    "--color-secondary": "#114433",
    "--color-primary-text": "#000000",
    "--color-secondary-text": "#FFD100",
  },
  "Eulektro GmbH": {
    "--color-primary": "#214958",
    "--color-secondary": "#ECECE7",
    "--color-primary-text": "#21697C",
    "--color-secondary-text": "#214958",
  },
  Eulektro_Public: {
    "--color-primary": "#214958",
    "--color-secondary": "#ECECE7",
    "--color-primary-text": "#21697C",
    "--color-secondary-text": "#214958",
  },
  "Bremer Pflegedienst": {
    "--color-primary": "#c02028",
    "--color-secondary": "#5c5c5c",
    "--color-primary-text": "#000000",
    "--color-secondary-text": "#c02028",
  },
  "Rehl Energy GmbH": {
    "--color-primary": "#991639",
    "--color-secondary": "#787d85",
    "--color-primary-text": "#991639",
    "--color-secondary-text": "#787d85",
  },
  "Bernd Beneke": {
    "--color-primary": "#fab527",
    "--color-secondary": "#5c5c5c",
    "--color-primary-text": "#373635",
    "--color-secondary-text": "#fab527",
  },
  "Etelser Käsewerk GmbH": {
    "--color-primary": "#e2001d",
    "--color-secondary": "#ffffff",
    "--color-primary-text": "#000000",
    "--color-secondary-text": "#5c5c5c",
  },
  "smartOPS GmbH": {
    "--color-primary": "#1e6cb4",
    "--color-secondary": "#ffffff",
    "--color-primary-text": "#1e6cb4",
    "--color-secondary-text": "#5c5c5c",
  },
  "Green Energy Hüls uG (Bönniger)": {
    "--color-primary": "#a1c53a",
    "--color-secondary": "#ffffff",
    "--color-primary-text": "#000000",
    "--color-secondary-text": "#5c5c5c",
  },
  "RH-Reifencenter": {
    "--color-primary": "#84c571",
    "--color-secondary": "#ffffff",
    "--color-primary-text": "#313842",
    "--color-secondary-text": "#5c5c5c",
  },

  "ReitsportKonzepte Völker GmbH": {
    "--color-primary": "#666c37",
    "--color-secondary": "#ffffff",
    "--color-primary-text": "#666c37",
    "--color-secondary-text": "#5c5c5c",
  },
  "Hannes Lange  Schreinerei GmbH & Co. KG": {
    "--color-primary": "#F2780C",
    "--color-secondary": "#ffffff",
    "--color-primary-text": "#7c6853",
    "--color-secondary-text": "#5c5c5c",
  },

  "HOBA Green Energy GmbH": {
    "--color-primary": "#009245",
    "--color-secondary": "#f7931e",
    "--color-primary-text": "#000000",
    "--color-secondary-text": "#5c5c5c",
  },
  "Midorion GmbH & Co. KG": {
    "--color-primary": "#17B86E",
    "--color-secondary": "#89FEA1",
    "--color-primary-text": "#000000",
    "--color-secondary-text": "#000000",
  },
  "KS Holding GmbH": {
    "--color-primary": "#045cbc",
    "--color-secondary": "#89FEA1",
    "--color-primary-text": "#000000",
    "--color-secondary-text": "#000000",
  },

  "H2 Campus GmbH": {
    "--color-primary": "#045cbc",
    "--color-secondary": "#89FEA1",
    "--color-primary-text": "#000000",
    "--color-secondary-text": "#000000",
  },

  Default: {
    "--color-primary": "#214958",
    "--color-secondary": "#ECECE7",
    "--color-primary-text": "#21697C",
    "--color-secondary-text": "#214958",
  },

  // ... andere Themes
};

export const ouLogos: { [identifier: string]: { logo: string; logo_darkmode: string } } = {
  "Eulektro GmbH": {
    logo: "/logo/EULEKTRO_schwarz_C84_M40_Y36_K21.svg",
    logo_darkmode: "/logo/EULEKTRO_weiss_C84_M40_Y36_K21.svg",
  },
  Hanke: {
    logo: "/logo/EULEKTRO_schwarz_C84_M40_Y36_K21.svg",
    logo_darkmode: "/logo/EULEKTRO_weiss_C84_M40_Y36_K21.svg",
  },
  "AH Beteiligungsgesellschaft mbH": {
    logo: "/logo/EULEKTRO_schwarz_C84_M40_Y36_K21.svg",
    logo_darkmode: "/logo/EULEKTRO_weiss_C84_M40_Y36_K21.svg",
  },
  "R & M Beteiligungsgesellschaft mbH": {
    logo: "/logo/EULEKTRO_schwarz_C84_M40_Y36_K21.svg",
    logo_darkmode: "/logo/EULEKTRO_weiss_C84_M40_Y36_K21.svg",
  },
  "Bremer Transport Taxi GmbH & Co. KG": {
    logo: "/logo/EULEKTRO_schwarz_C84_M40_Y36_K21.svg",
    logo_darkmode: "/logo/EULEKTRO_weiss_C84_M40_Y36_K21.svg",
  },
  Eulektro_Public: {
    logo: "/logo/EULEKTRO_schwarz_C84_M40_Y36_K21.svg",
    logo_darkmode: "/logo/EULEKTRO_weiss_C84_M40_Y36_K21.svg",
  },
  Cambio: {
    logo: "/logo/cambio.svg",
    logo_darkmode: "/logo/cambio.svg",
  },
  "Bernd Beneke": {
    logo: "/logo/Logo-Homepage-Beneke2.png",
    logo_darkmode: "/logo/Logo-Homepage-Beneke2.png",
  },
  "Rehl Energy GmbH": {
    logo: "/logo/rehl.png",
    logo_darkmode: "/logo/rehl.png",
  },
  "Etelser Käsewerk GmbH": {
    logo: "/logo/etelser_kaesewerk_logo.svg",
    logo_darkmode: "/logo/etelser_kaesewerk_logo.svg",
  },
  "Green Energy Hüls uG (Bönniger)": {
    logo: "/logo/green_energy_huels.svg",
    logo_darkmode: "/logo/green_energy_huels.svg",
  },
  "ReitsportKonzepte Völker GmbH": {
    logo: "/logo/voelker.svg",
    logo_darkmode: "/logo/voelker.svg",
  },
  "Hannes Lange  Schreinerei GmbH & Co. KG": {
    logo: "/logo/lange.jpg",
    logo_darkmode: "/logo/lange.png",
  },
  "HOBA Green Energy GmbH": {
    logo: "/logo/firstcharge_logo.png",
    logo_darkmode: "/logo/firstcharge_logo.png",
  },
  "Midorion GmbH & Co. KG": {
    logo: "/logo/midorion_logo.png",
    logo_darkmode: "/logo/midorion_logo.png",
  },
  "KS Holding GmbH": {
    logo: "/logo/krimphoff-schulte-logo.svg",
    logo_darkmode: "/logo/krimphoff-schulte-logo.svg",
  },
  "H2 Campus GmbH": {
    logo: "/logo/krimphoff-schulte-logo.svg",
    logo_darkmode: "/logo/krimphoff-schulte-logo.svg",
  },

  Default: {
    logo: "/logo/EULEKTRO_schwarz_C84_M40_Y36_K21.svg",
    logo_darkmode: "/logo/EULEKTRO_weiss_C84_M40_Y36_K21.svg",
  },
};
