import prisma from "~/server/db/prisma";

export interface ChargingStationData {
  totalChargingPower: number; // Total power in kW
  activeSessionsCount: number;
  chargingStations: Array<{
    chargePointId: string;
    displayName: string;
    currentPower: number; // in kW
    status: string;
    sessionStart?: Date;
  }>;
  lastUpdated: Date;
}

/**
 * Get real-time charging power data for all active charging sessions at a specific location
 */
export async function getChargingPowerByLocation(locationId: string): Promise<ChargingStationData> {
  try {
    console.log(`🔌 Fetching charging data for location: ${locationId}`);

    // Get all charge points at the specified location
    const chargePoints = await prisma.chargePoint.findMany({
      where: {
        locationId: locationId,
        dateDeleted: null, // Only active charge points
      },
      select: {
        id: true,
        chargePointId: true,
        displayName: true,
        connectivityStatus: true,
        maxCapacityInKw: true,
        locationId: true,
      },
    });

    console.log(`📊 Found ${chargePoints.length} charge points at location ${locationId}`);

    if (chargePoints.length === 0) {
      return {
        totalChargingPower: 0,
        activeSessionsCount: 0,
        chargingStations: [],
        lastUpdated: new Date(),
      };
    }

    // Get real-time data for all charge points at this location
    const chargePointIds = chargePoints.map((cp) => cp.id);

    const realtimeData = await prisma.chargePointRealtimeData.findMany({
      where: {
        chargePointId: {
          in: chargePointIds,
        },
        status: {
          in: ["Charging", "SuspendedEV", "SuspendedEVSE"], // Active charging states
        },
      },
    });

    console.log(`⚡ Found ${realtimeData.length} active charging sessions`);

    // Calculate total charging power and build station data
    let totalChargingPower = 0;
    const chargingStations = chargePoints.map((chargePoint) => {
      const realtimeInfo = realtimeData.find((rt) => rt.chargePointId === chargePoint.id);
      const currentPower = realtimeInfo?.kW || 0;

      if (currentPower > 0) {
        totalChargingPower += currentPower;
      }

      return {
        chargePointId: chargePoint.chargePointId,
        displayName: chargePoint.displayName,
        currentPower: currentPower,
        status: realtimeInfo?.status || "Available",
        sessionStart: realtimeInfo?.currentSessionStart || undefined,
      };
    });

    // Filter to only show stations that are currently charging
    const activeChargingStations = chargingStations.filter((station) => station.currentPower > 0);

    const result = {
      totalChargingPower: Math.round(totalChargingPower * 100) / 100, // Round to 2 decimal places
      activeSessionsCount: activeChargingStations.length,
      chargingStations: activeChargingStations,
      lastUpdated: new Date(),
    };

    return result;
  } catch (error) {
    console.error(`❌ Error fetching charging data for location ${locationId}:`, error);

    // Return empty data on error
    return {
      totalChargingPower: 0,
      activeSessionsCount: 0,
      chargingStations: [],
      lastUpdated: new Date(),
    };
  }
}

/**
 * Get charging power data with caching support
 */
export async function getCachedChargingPower(locationId: string): Promise<ChargingStationData> {
  // For now, we'll fetch real-time data directly
  // In the future, we could implement caching similar to SolarEdge data
  return await getChargingPowerByLocation(locationId);
}

/**
 * Get historical charging power data for charts (optional enhancement)
 */
export async function getChargingPowerHistory(
  locationId: string,
  hours: number = 24,
): Promise<Array<{ timestamp: Date; power: number }>> {
  try {
    const startTime = new Date();
    startTime.setHours(startTime.getHours() - hours);

    // Get charge points at location
    const chargePoints = await prisma.chargePoint.findMany({
      where: {
        locationId: locationId,
        dateDeleted: null,
      },
      select: {
        id: true,
        historyPowerByCharger: {
          where: {
            timestamp: {
              gte: startTime,
            },
          },
          orderBy: {
            timestamp: "asc",
          },
        },
      },
    });

    // Aggregate power data by timestamp
    const powerByTimestamp = new Map<string, number>();

    chargePoints.forEach((chargePoint) => {
      chargePoint.historyPowerByCharger.forEach((history) => {
        const timestampKey = history.timestamp.toISOString();
        const currentPower = powerByTimestamp.get(timestampKey) || 0;
        powerByTimestamp.set(timestampKey, currentPower + history.power / 1000); // Convert W to kW
      });
    });

    // Convert to array and sort by timestamp
    const historyData = Array.from(powerByTimestamp.entries())
      .map(([timestamp, power]) => ({
        timestamp: new Date(timestamp),
        power: Math.round(power * 100) / 100, // Round to 2 decimal places
      }))
      .sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());

    console.log(
      `📈 Retrieved ${historyData.length} historical charging power data points for location ${locationId}`,
    );

    return historyData;
  } catch (error) {
    console.error(`❌ Error fetching charging power history for location ${locationId}:`, error);
    return [];
  }
}

/**
 * Get charging statistics for a location
 */
export async function getChargingStatistics(locationId: string): Promise<{
  totalStations: number;
  activeStations: number;
  availableStations: number;
  totalCapacity: number; // in kW
  utilizationRate: number; // percentage
}> {
  try {
    const chargePoints = await prisma.chargePoint.findMany({
      where: {
        locationId: locationId,
        dateDeleted: null,
      },
      select: {
        id: true,
        maxCapacityInKw: true,
      },
    });

    const realtimeData = await prisma.realtimeData.findMany({
      where: {
        id: {
          in: chargePoints.map((cp) => cp.id),
        },
      },
    });

    const totalStations = chargePoints.length;
    const activeStations = realtimeData.filter((rt) =>
      ["Charging", "SuspendedEV", "SuspendedEVSE"].includes(rt.status),
    ).length;
    const availableStations = totalStations - activeStations;
    const totalCapacity = chargePoints.reduce((sum, cp) => sum + cp.maxCapacityInKw, 0);
    const currentPower = realtimeData.reduce((sum, rt) => sum + (rt.kw || 0), 0);
    const utilizationRate = totalCapacity > 0 ? (currentPower / totalCapacity) * 100 : 0;

    return {
      totalStations,
      activeStations,
      availableStations,
      totalCapacity,
      utilizationRate: Math.round(utilizationRate * 100) / 100,
    };
  } catch (error) {
    console.error(`❌ Error fetching charging statistics for location ${locationId}:`, error);
    return {
      totalStations: 0,
      activeStations: 0,
      availableStations: 0,
      totalCapacity: 0,
      utilizationRate: 0,
    };
  }
}
