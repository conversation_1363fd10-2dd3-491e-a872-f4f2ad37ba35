export interface SunriseSunsetData {
  sunrise: string; // Time in HH:MM format
  sunset: string; // Time in HH:MM format
  solarNoon: string; // Time in HH:MM format
  dayLength: string; // Duration in HH:MM:SS format
  currentTime: string; // Current time in HH:MM format
  sunPosition: number; // Position from 0 (sunrise) to 1 (sunset)
  isDaytime: boolean; // Whether it's currently daytime
  timeUntilSunrise?: string; // Time until sunrise (if nighttime)
  timeUntilSunset?: string; // Time until sunset (if daytime)
  lastUpdated: Date;
}

interface SunriseSunsetApiResponse {
  results: {
    sunrise: string;
    sunset: string;
    solar_noon: string;
    day_length: string;
    civil_twilight_begin: string;
    civil_twilight_end: string;
    nautical_twilight_begin: string;
    nautical_twilight_end: string;
    astronomical_twilight_begin: string;
    astronomical_twilight_end: string;
  };
  status: string;
  tzid: string;
}

/**
 * Convert UTC ISO string to local time and format as HH:MM
 */
function formatTimeToLocal(utcIsoString: string): string {
  try {
    // Parse the UTC ISO string (format: "2024-07-28T03:32:03+00:00")
    const utcDate = new Date(utcIsoString);

    if (isNaN(utcDate.getTime())) {
      throw new Error(`Invalid date: ${utcIsoString}`);
    }

    // Convert to local time (German timezone)
    return utcDate.toLocaleTimeString('de-DE', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false,
      timeZone: 'Europe/Berlin'
    });
  } catch (error) {
    console.error('Error formatting time:', error, 'Input:', utcIsoString);
    return '00:00';
  }
}

/**
 * Calculate sun position based on current time (0 = sunrise, 1 = sunset)
 */
function calculateSunPosition(sunrise: string, sunset: string, currentTime: string): number {
  try {
    const today = new Date();
    const todayStr = today.toISOString().split('T')[0]; // YYYY-MM-DD format

    const sunriseTime = new Date(`${todayStr}T${sunrise}:00`);
    const sunsetTime = new Date(`${todayStr}T${sunset}:00`);
    const now = new Date(`${todayStr}T${currentTime}:00`);

    if (isNaN(sunriseTime.getTime()) || isNaN(sunsetTime.getTime()) || isNaN(now.getTime())) {
      console.error('Invalid time format in calculateSunPosition:', { sunrise, sunset, currentTime });
      return 0.5;
    }

    if (now < sunriseTime || now > sunsetTime) {
      return 0; // Nighttime
    }

    const totalDaylight = sunsetTime.getTime() - sunriseTime.getTime();
    const elapsed = now.getTime() - sunriseTime.getTime();

    return Math.max(0, Math.min(1, elapsed / totalDaylight));
  } catch (error) {
    console.error('Error calculating sun position:', error);
    return 0.5;
  }
}

/**
 * Calculate time until sunrise or sunset
 */
function calculateTimeUntil(targetTime: string, currentTime: string): string {
  try {
    const today = new Date();
    const todayStr = today.toISOString().split('T')[0]; // YYYY-MM-DD format

    const target = new Date(`${todayStr}T${targetTime}:00`);
    const now = new Date(`${todayStr}T${currentTime}:00`);

    if (isNaN(target.getTime()) || isNaN(now.getTime())) {
      console.error('Invalid time format in calculateTimeUntil:', { targetTime, currentTime });
      return '0h 0m';
    }

    let diff = target.getTime() - now.getTime();

    // If target is tomorrow (e.g., sunrise when it's evening)
    if (diff < 0) {
      const tomorrow = new Date(today);
      tomorrow.setDate(tomorrow.getDate() + 1);
      const tomorrowStr = tomorrow.toISOString().split('T')[0];
      const targetTomorrow = new Date(`${tomorrowStr}T${targetTime}:00`);
      diff = targetTomorrow.getTime() - now.getTime();
    }

    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

    return `${hours}h ${minutes}m`;
  } catch (error) {
    console.error('Error calculating time until:', error);
    return '0h 0m';
  }
}

/**
 * Fetch sunrise and sunset data from API
 */
export async function fetchSunriseSunsetData(
  latitude: number = 53.1199176,
  longitude: number = 8.5714827
): Promise<SunriseSunsetData> {
  try {
    console.log(`🌅 Fetching sunrise/sunset data for coordinates: ${latitude}, ${longitude}`);

    const response = await fetch(
      `https://api.sunrise-sunset.org/json?lat=${latitude}&lng=${longitude}&formatted=0`,
      {
        headers: {
          'User-Agent': 'SolarDashboard/1.0'
        }
      }
    );

    if (!response.ok) {
      throw new Error(`Sunrise API error: ${response.status}`);
    }

    const data: SunriseSunsetApiResponse = await response.json();

    if (data.status !== 'OK') {
      throw new Error(`Sunrise API status: ${data.status}`);
    }

    // Convert UTC ISO times to local times
    const sunrise = formatTimeToLocal(data.results.sunrise);
    const sunset = formatTimeToLocal(data.results.sunset);
    const solarNoon = formatTimeToLocal(data.results.solar_noon);

    console.log(`🌅 Raw API data:`, {
      sunrise: data.results.sunrise,
      sunset: data.results.sunset,
      converted: { sunrise, sunset, solarNoon }
    });

    // Get current local time
    const currentTime = new Date().toLocaleTimeString('de-DE', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    });

    // Calculate sun position and determine if it's daytime
    const sunPosition = calculateSunPosition(sunrise, sunset, currentTime);
    const isDaytime = sunPosition > 0;

    // Calculate time until next event
    const timeUntilSunrise = !isDaytime ? calculateTimeUntil(sunrise, currentTime) : undefined;
    const timeUntilSunset = isDaytime ? calculateTimeUntil(sunset, currentTime) : undefined;

    const result: SunriseSunsetData = {
      sunrise,
      sunset,
      solarNoon,
      dayLength: data.results.day_length,
      currentTime,
      sunPosition,
      isDaytime,
      timeUntilSunrise,
      timeUntilSunset,
      lastUpdated: new Date()
    };

    console.log(`🌞 Sunrise/sunset data processed:`, {
      sunrise: result.sunrise,
      sunset: result.sunset,
      currentTime: result.currentTime,
      isDaytime: result.isDaytime,
      sunPosition: result.sunPosition.toFixed(2)
    });

    return result;

  } catch (error) {
    console.error('❌ Error fetching sunrise/sunset data:', error);

    // Return fallback data with realistic values for summer in Germany
    const currentTime = new Date().toLocaleTimeString('de-DE', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false,
      timeZone: 'Europe/Berlin'
    });

    const fallbackSunrise = '05:30';
    const fallbackSunset = '21:30';
    const sunPosition = calculateSunPosition(fallbackSunrise, fallbackSunset, currentTime);
    const isDaytime = sunPosition > 0;

    return {
      sunrise: fallbackSunrise,
      sunset: fallbackSunset,
      solarNoon: '13:30',
      dayLength: '16:00:00',
      currentTime,
      sunPosition,
      isDaytime,
      timeUntilSunrise: !isDaytime ? calculateTimeUntil(fallbackSunrise, currentTime) : undefined,
      timeUntilSunset: isDaytime ? calculateTimeUntil(fallbackSunset, currentTime) : undefined,
      lastUpdated: new Date()
    };
  }
}

/**
 * Get cached sunrise/sunset data (for performance)
 */
let cachedSunData: SunriseSunsetData | null = null;
let lastFetch: Date | null = null;

export async function getCachedSunriseSunsetData(
  latitude?: number,
  longitude?: number
): Promise<SunriseSunsetData> {
  const now = new Date();

  // Update current time even if using cached data
  if (cachedSunData && lastFetch) {
    const hoursSinceLastFetch = (now.getTime() - lastFetch.getTime()) / (1000 * 60 * 60);

    // Refresh data every 6 hours or if it's a new day
    if (hoursSinceLastFetch < 6 && now.toDateString() === lastFetch.toDateString()) {
      // Update current time and recalculate position
      const currentTime = now.toLocaleTimeString('de-DE', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
      });

      const sunPosition = calculateSunPosition(cachedSunData.sunrise, cachedSunData.sunset, currentTime);
      const isDaytime = sunPosition > 0;

      return {
        ...cachedSunData,
        currentTime,
        sunPosition,
        isDaytime,
        timeUntilSunrise: !isDaytime ? calculateTimeUntil(cachedSunData.sunrise, currentTime) : undefined,
        timeUntilSunset: isDaytime ? calculateTimeUntil(cachedSunData.sunset, currentTime) : undefined,
      };
    }
  }

  // Fetch fresh data
  cachedSunData = await fetchSunriseSunsetData(latitude, longitude);
  lastFetch = now;

  return cachedSunData;
}
