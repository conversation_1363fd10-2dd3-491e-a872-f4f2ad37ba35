export interface WeatherData {
  temperature: number;
  windSpeed: number;
  windDirection: number;
  weatherCode: number;
  isDay: boolean;
  description: string;
  lastUpdated: Date;
  source: 'api' | 'cache' | 'error';
  error?: string;
}

interface OpenMeteoResponse {
  current_weather: {
    temperature: number;
    windspeed: number;
    winddirection: number;
    weathercode: number;
    is_day: number;
    time: string;
  };
}

/**
 * Map Open-Meteo weather codes to user-friendly descriptions
 */
function getWeatherDescription(weatherCode: number): string {
  const weatherCodes: { [key: number]: string } = {
    0: 'Klarer Himmel',
    1: 'Überwiegend klar',
    2: 'Te<PERSON>weise bewölkt',
    3: 'Bedeckt',
    45: '<PERSON><PERSON>el',
    48: 'Reif<PERSON>bel',
    51: 'Leichter Nieselregen',
    53: '<PERSON><PERSON>ßiger Nieselregen',
    55: 'Starker Nieselregen',
    56: 'Leichter gefrierender Nieselregen',
    57: 'Starker gefrierender Nieselregen',
    61: '<PERSON><PERSON><PERSON> Regen',
    63: '<PERSON><PERSON><PERSON>iger Regen',
    65: 'Starker Regen',
    66: 'Leichter gefrierender Regen',
    67: 'Starker gefrierender Regen',
    71: 'Leichter Schneefall',
    73: '<PERSON><PERSON><PERSON><PERSON>hneefall',
    75: 'Starker Schneefall',
    77: 'Schneekörner',
    80: 'Leichte Regenschauer',
    81: 'Mäßige Regenschauer',
    82: 'Starke Regenschauer',
    85: 'Leichte Schneeschauer',
    86: 'Starke Schneeschauer',
    95: 'Gewitter',
    96: 'Gewitter mit leichtem Hagel',
    99: 'Gewitter mit starkem Hagel'
  };

  return weatherCodes[weatherCode] || `Unbekannt (Code: ${weatherCode})`;
}

/**
 * Fetch weather data from Open-Meteo API
 */
export async function fetchWeatherData(
  latitude: number = 53.1199176,
  longitude: number = 8.5714827
): Promise<WeatherData> {
  try {
    console.log(`🌤️ Fetching weather data for coordinates: ${latitude}, ${longitude}`);

    const response = await fetch(
      `https://api.open-meteo.com/v1/forecast?latitude=${latitude}&longitude=${longitude}&current_weather=true`,
      {
        headers: {
          'User-Agent': 'SolarDashboard/1.0'
        }
      }
    );

    if (!response.ok) {
      throw new Error(`Open-Meteo API error: ${response.status}`);
    }

    const data: OpenMeteoResponse = await response.json();

    if (!data.current_weather) {
      throw new Error('Invalid weather data received from Open-Meteo API');
    }

    const weather = data.current_weather;
    const description = getWeatherDescription(weather.weathercode);

    const result: WeatherData = {
      temperature: Math.round(weather.temperature * 10) / 10,
      windSpeed: Math.round(weather.windspeed * 10) / 10,
      windDirection: weather.winddirection,
      weatherCode: weather.weathercode,
      isDay: weather.is_day === 1,
      description,
      lastUpdated: new Date(),
      source: 'api'
    };

    console.log(`🌤️ Weather data processed:`, {
      temperature: result.temperature,
      description: result.description,
      windSpeed: result.windSpeed,
      isDay: result.isDay
    });

    return result;

  } catch (error) {
    console.error('❌ Error fetching weather data:', error);

    // Return fallback data with error information
    return {
      temperature: 20,
      windSpeed: 0,
      windDirection: 0,
      weatherCode: 0,
      isDay: true,
      description: 'Wetterdaten nicht verfügbar',
      lastUpdated: new Date(),
      source: 'error',
      error: error instanceof Error ? error.message : 'Unbekannter Fehler'
    };
  }
}

/**
 * Get cached weather data (for performance)
 */
let cachedWeatherData: WeatherData | null = null;
let lastWeatherFetch: Date | null = null;

export async function getCachedWeatherData(
  latitude?: number,
  longitude?: number
): Promise<WeatherData> {
  const now = new Date();

  // Use cached data if it's less than 30 minutes old
  if (cachedWeatherData && lastWeatherFetch) {
    const minutesSinceLastFetch = (now.getTime() - lastWeatherFetch.getTime()) / (1000 * 60);

    if (minutesSinceLastFetch < 30) {
      console.log(`🌤️ Using cached weather data (${minutesSinceLastFetch.toFixed(1)} minutes old)`);
      return {
        ...cachedWeatherData,
        source: 'cache'
      };
    }
  }

  // Fetch fresh data
  console.log('🌤️ Fetching fresh weather data...');
  const weatherData = await fetchWeatherData(latitude, longitude);
  
  // Cache the data only if it's not an error
  if (weatherData.source !== 'error') {
    cachedWeatherData = weatherData;
    lastWeatherFetch = now;
  }

  return weatherData;
}

/**
 * Get coordinates from database location or use fallback
 */
export async function getLocationCoordinates(locationId?: string): Promise<{ latitude: number; longitude: number }> {
  // Default Bremen coordinates
  const fallbackCoordinates = {
    latitude: 53.1199176,
    longitude: 8.5714827
  };

  if (!locationId) {
    console.log('🗺️ No location ID provided, using Bremen fallback coordinates');
    return fallbackCoordinates;
  }

  try {
    // This would require importing prisma and querying the database
    // For now, return fallback coordinates
    // TODO: Implement database lookup when needed
    console.log(`🗺️ Location lookup not implemented yet, using Bremen fallback for location ${locationId}`);
    return fallbackCoordinates;
  } catch (error) {
    console.error('❌ Error fetching location coordinates:', error);
    return fallbackCoordinates;
  }
}
