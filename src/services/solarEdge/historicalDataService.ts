import prisma from '~/server/db/prisma';
import { canMakeApiRequest, incrementApiRequestCount } from './api';

export interface HistoricalDataPoint {
  date: Date;
  energyProduced?: number;
  energyConsumed?: number;
  gridFeedIn?: number;
  gridConsumption?: number;
  chargingConsumption?: number;
  dataSource: 'api' | 'manual' | 'estimated';
  dataQuality: 'good' | 'partial' | 'estimated';
}

export interface EnergyAnalytics {
  totalProduced: number;
  totalConsumed: number;
  totalGridFeedIn: number;
  totalGridConsumption: number;
  totalChargingConsumption: number;
  averageDaily: {
    produced: number;
    consumed: number;
    gridFeedIn: number;
    gridConsumption: number;
    chargingConsumption: number;
  };
  energyBalance: number; // produced - consumed
  selfConsumptionRate: number; // percentage
  gridIndependenceRate: number; // percentage
}

/**
 * Fetch daily energy data from SolarEdge API
 */
async function fetchDailyEnergyFromAPI(
  dashboardId: string,
  apiKey: string,
  siteId: string,
  startDate: Date,
  endDate: Date
): Promise<HistoricalDataPoint[]> {
  const results: HistoricalDataPoint[] = [];
  
  try {
    // Check rate limiting
    if (!(await canMakeApiRequest(dashboardId))) {
      return results;
    }

    // Format dates for SolarEdge API
    const formatDate = (date: Date) => date.toISOString().split('T')[0];
    const startDateStr = formatDate(startDate);
    const endDateStr = formatDate(endDate);

    // Fetch energy data from SolarEdge API
    const energyUrl = `https://monitoringapi.solaredge.com/site/${siteId}/energy?timeUnit=DAY&startDate=${startDateStr}&endDate=${endDateStr}&api_key=${apiKey}`;
    
    const energyResponse = await fetch(energyUrl);
    await incrementApiRequestCount(dashboardId);

    if (!energyResponse.ok) {
      throw new Error(`SolarEdge API error: ${energyResponse.status}`);
    }

    const energyData = await energyResponse.json();
    
    // Process energy data
    if (energyData.energy && energyData.energy.values) {
      for (const dataPoint of energyData.energy.values) {
        if (dataPoint.date && dataPoint.value !== null) {
          results.push({
            date: new Date(dataPoint.date),
            energyProduced: dataPoint.value / 1000, // Convert Wh to kWh
            dataSource: 'api',
            dataQuality: 'good'
          });
        }
      }
    }

    // Try to fetch power flow data for more detailed information
    if (await canMakeApiRequest(dashboardId)) {
      const powerUrl = `https://monitoringapi.solaredge.com/site/${siteId}/powerFlow?startDate=${startDateStr}&endDate=${endDateStr}&api_key=${apiKey}`;
      
      const powerResponse = await fetch(powerUrl);
      await incrementApiRequestCount(dashboardId);

      if (powerResponse.ok) {
        const powerData = await powerResponse.json();
        
        // Enhance results with power flow data if available
        if (powerData.siteCurrentPowerFlow) {
          const flow = powerData.siteCurrentPowerFlow;
          
          // Update the most recent data point with power flow information
          if (results.length > 0) {
            const lastPoint = results[results.length - 1];
            lastPoint.energyConsumed = flow.LOAD?.currentPower ? flow.LOAD.currentPower / 1000 : undefined;
            lastPoint.gridConsumption = flow.GRID?.currentPower && flow.GRID.currentPower > 0 ? 
              flow.GRID.currentPower / 1000 : undefined;
            lastPoint.gridFeedIn = flow.GRID?.currentPower && flow.GRID.currentPower < 0 ? 
              Math.abs(flow.GRID.currentPower) / 1000 : undefined;
          }
        }
      }
    }

    return results;

  } catch (error) {
    return results;
  }
}

/**
 * Store historical data in database
 */
export async function storeHistoricalData(
  dashboardId: string,
  dataPoints: HistoricalDataPoint[]
): Promise<number> {
  let storedCount = 0;

  try {
    for (const point of dataPoints) {
      await prisma.solarEdgeHistoricalData.upsert({
        where: {
          dashboardId_date: {
            dashboardId,
            date: point.date
          }
        },
        update: {
          energyProduced: point.energyProduced,
          energyConsumed: point.energyConsumed,
          gridFeedIn: point.gridFeedIn,
          gridConsumption: point.gridConsumption,
          chargingConsumption: point.chargingConsumption,
          dataSource: point.dataSource,
          dataQuality: point.dataQuality,
          updatedAt: new Date()
        },
        create: {
          dashboardId,
          date: point.date,
          energyProduced: point.energyProduced,
          energyConsumed: point.energyConsumed,
          gridFeedIn: point.gridFeedIn,
          gridConsumption: point.gridConsumption,
          chargingConsumption: point.chargingConsumption,
          dataSource: point.dataSource,
          dataQuality: point.dataQuality
        }
      });
      storedCount++;
    }

    return storedCount;

  } catch (error) {
    return storedCount;
  }
}

/**
 * Collect and store historical data for a dashboard
 */
export async function collectHistoricalData(
  dashboardId: string,
  apiKey: string,
  siteId: string,
  startDate: Date,
  endDate: Date
): Promise<number> {
  try {
    // Fetch data from API
    const dataPoints = await fetchDailyEnergyFromAPI(dashboardId, apiKey, siteId, startDate, endDate);

    if (dataPoints.length === 0) {
      return 0;
    }

    // Store in database
    const storedCount = await storeHistoricalData(dashboardId, dataPoints);

    return storedCount;

  } catch (error) {
    return 0;
  }
}

/**
 * Get energy analytics for a date range
 */
export async function getEnergyAnalytics(
  dashboardId: string,
  startDate: Date,
  endDate: Date
): Promise<EnergyAnalytics> {
  try {
    const data = await prisma.solarEdgeHistoricalData.findMany({
      where: {
        dashboardId,
        date: {
          gte: startDate,
          lte: endDate
        }
      },
      orderBy: {
        date: 'asc'
      }
    });

    const totalProduced = data.reduce((sum, d) => sum + (d.energyProduced || 0), 0);
    const totalConsumed = data.reduce((sum, d) => sum + (d.energyConsumed || 0), 0);
    const totalGridFeedIn = data.reduce((sum, d) => sum + (d.gridFeedIn || 0), 0);
    const totalGridConsumption = data.reduce((sum, d) => sum + (d.gridConsumption || 0), 0);
    const totalChargingConsumption = data.reduce((sum, d) => sum + (d.chargingConsumption || 0), 0);

    const dayCount = data.length || 1;
    const energyBalance = totalProduced - totalConsumed;
    const selfConsumptionRate = totalProduced > 0 ? ((totalProduced - totalGridFeedIn) / totalProduced) * 100 : 0;
    const gridIndependenceRate = totalConsumed > 0 ? ((totalConsumed - totalGridConsumption) / totalConsumed) * 100 : 0;

    return {
      totalProduced,
      totalConsumed,
      totalGridFeedIn,
      totalGridConsumption,
      totalChargingConsumption,
      averageDaily: {
        produced: totalProduced / dayCount,
        consumed: totalConsumed / dayCount,
        gridFeedIn: totalGridFeedIn / dayCount,
        gridConsumption: totalGridConsumption / dayCount,
        chargingConsumption: totalChargingConsumption / dayCount
      },
      energyBalance,
      selfConsumptionRate,
      gridIndependenceRate
    };

  } catch (error) {
    throw error;
  }
}

/**
 * Backfill historical data for existing dashboards
 */
export async function backfillHistoricalData(
  dashboardId: string,
  daysBack: number = 30
): Promise<number> {
  try {
    const dashboard = await prisma.solarDashboard.findUnique({
      where: { id: dashboardId }
    });

    if (!dashboard || !dashboard.solarEdgeApiKey || !dashboard.solarEdgeSiteId) {
      return 0;
    }

    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - daysBack);

    return await collectHistoricalData(
      dashboardId,
      dashboard.solarEdgeApiKey,
      dashboard.solarEdgeSiteId,
      startDate,
      endDate
    );

  } catch (error) {
    return 0;
  }
}
