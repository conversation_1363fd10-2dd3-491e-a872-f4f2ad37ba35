import prisma from '~/server/db/prisma';

// SolarEdge API configuration
const SOLAREDGE_BASE_URL = 'https://monitoringapi.solaredge.com';
const MAX_REQUESTS_PER_DAY = 250;
const REQUEST_TIMEOUT = 10000; // 10 seconds

// Data types for caching
export enum SolarEdgeDataType {
  CURRENT_POWER = 'currentPower',
  TODAY_ENERGY = 'todayEnergy',
  YEAR_ENERGY = 'yearEnergy',
  TODAY_HOURLY_ENERGY = 'todayHourlyEnergy',
  OVERVIEW = 'overview'
}

// Cache expiration times (in minutes)
const CACHE_EXPIRATION = {
  [SolarEdgeDataType.CURRENT_POWER]: 5,    // 5 minutes
  [SolarEdgeDataType.TODAY_ENERGY]: 60,    // 1 hour
  [SolarEdgeDataType.YEAR_ENERGY]: 1440,   // 24 hours
  [SolarEdgeDataType.TODAY_HOURLY_ENERGY]: 60, // 1 hour
  [SolarEdgeDataType.OVERVIEW]: 30         // 30 minutes
};

// SolarEdge API response interfaces
interface SolarEdgeCurrentPowerResponse {
  siteCurrentPowerFlow: {
    updateRefreshRate: number;
    unit: string;
    connections: Array<{
      from: string;
      to: string;
    }>;
    PV?: {
      status: string;
      currentPower: number;
    };
    LOAD?: {
      status: string;
      currentPower: number;
    };
    GRID?: {
      status: string;
      currentPower: number;
    };
  };
}

interface SolarEdgeEnergyResponse {
  energy: {
    timeUnit: string;
    unit: string;
    measuredBy: string;
    values: Array<{
      date: string;
      value: number;
    }>;
  };
}

interface SolarEdgeOverviewResponse {
  overview: {
    lastUpdateTime: string;
    lifeTimeData: {
      energy: number;
      revenue: number;
    };
    lastYearData: {
      energy: number;
      revenue: number;
    };
    lastMonthData: {
      energy: number;
      revenue: number;
    };
    lastDayData: {
      energy: number;
      revenue: number;
    };
    currentPower: {
      power: number;
    };
    measuredBy: string;
  };
}

/**
 * Check if dashboard can make API request based on rate limiting
 */
export async function canMakeApiRequest(dashboardId: string): Promise<boolean> {
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  const rateLimit = await prisma.solarEdgeRateLimit.findFirst({
    where: {
      dashboardId,
      date: today
    }
  });

  return !rateLimit || rateLimit.requestCount < MAX_REQUESTS_PER_DAY;
}

/**
 * Increment API request counter for rate limiting with robust race condition handling
 */
export async function incrementApiRequestCount(dashboardId: string): Promise<void> {
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  // Use multiple retry attempts to handle race conditions
  const maxRetries = 3;
  let attempt = 0;

  while (attempt < maxRetries) {
    try {
      // Try to update existing entry first
      const updated = await prisma.solarEdgeRateLimit.updateMany({
        where: {
          dashboardId,
          date: today
        },
        data: {
          requestCount: {
            increment: 1
          },
          lastRequestAt: new Date()
        }
      });

      // If no rows were updated, the entry doesn't exist yet
      if (updated.count === 0) {
        try {
          // Try to create new entry
          await prisma.solarEdgeRateLimit.create({
            data: {
              dashboardId,
              date: today,
              requestCount: 1,
              lastRequestAt: new Date()
            }
          });
        } catch (createError: any) {
          // If unique constraint failed, another process created it
          if (createError.code === 'P2002') {
            // Retry the update operation
            attempt++;
            if (attempt < maxRetries) {
              await new Promise(resolve => setTimeout(resolve, 10 * attempt)); // Small delay
              continue;
            } else {
              // Max retries reached, proceed anyway
            }
          } else {
            throw createError;
          }
        }
      }

      // Success, break out of retry loop
      break;

    } catch (error) {
      attempt++;
      if (attempt >= maxRetries) {
        // Don't throw the error, just continue - the API call should still proceed
        break;
      } else {
        await new Promise(resolve => setTimeout(resolve, 10 * attempt)); // Small delay
      }
    }
  }
}

/**
 * Log API call for monitoring and debugging
 */
async function logApiCall(
  dashboardId: string,
  endpoint: string,
  success: boolean,
  responseTime?: number,
  errorMessage?: string
): Promise<void> {
  await prisma.solarEdgeApiCall.create({
    data: {
      dashboardId,
      endpoint,
      success,
      responseTime,
      errorMessage
    }
  });
}

/**
 * Get cached data if available and not expired
 */
export async function getCachedData(
  dashboardId: string,
  dataType: SolarEdgeDataType
): Promise<{ value: number; unit: string; metadata?: any; cachedAt: Date } | null> {
  const cached = await prisma.solarEdgeDataCache.findUnique({
    where: {
      dashboardId_dataType: {
        dashboardId,
        dataType
      }
    }
  });

  if (!cached || cached.expiresAt < new Date()) {
    return null;
  }

  return {
    value: cached.value,
    unit: cached.unit,
    metadata: cached.metadata,
    cachedAt: cached.cachedAt
  };
}

/**
 * Cache API response data
 */
async function cacheData(
  dashboardId: string,
  dataType: SolarEdgeDataType,
  value: number,
  unit: string,
  metadata?: any
): Promise<void> {
  const expirationMinutes = CACHE_EXPIRATION[dataType];
  const expiresAt = new Date();
  expiresAt.setMinutes(expiresAt.getMinutes() + expirationMinutes);

  await prisma.solarEdgeDataCache.upsert({
    where: {
      dashboardId_dataType: {
        dashboardId,
        dataType
      }
    },
    update: {
      value,
      unit,
      metadata,
      cachedAt: new Date(),
      expiresAt
    },
    create: {
      dashboardId,
      dataType,
      value,
      unit,
      metadata,
      expiresAt
    }
  });
}

/**
 * Make HTTP request to SolarEdge API with timeout and error handling
 */
async function makeApiRequest(url: string): Promise<any> {
  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), REQUEST_TIMEOUT);

  try {
    const response = await fetch(url, {
      signal: controller.signal,
      headers: {
        'Accept': 'application/json',
        'User-Agent': 'Eulektro-Solar-Dashboard/1.0'
      }
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    return await response.json();
  } catch (error) {
    clearTimeout(timeoutId);
    throw error;
  }
}

/**
 * Get current power output from SolarEdge API
 */
export async function getCurrentPower(
  dashboardId: string,
  apiKey: string,
  siteId: string
): Promise<{ value: number; unit: string; metadata?: any } | null> {
  const startTime = Date.now();

  try {
    // Check rate limiting
    if (!(await canMakeApiRequest(dashboardId))) {
      throw new Error('Daily API rate limit exceeded');
    }

    const url = `${SOLAREDGE_BASE_URL}/site/${siteId}/currentPowerFlow?api_key=${apiKey}`;
    const response: SolarEdgeCurrentPowerResponse = await makeApiRequest(url);

    await incrementApiRequestCount(dashboardId);

    const responseTime = Date.now() - startTime;
    await logApiCall(dashboardId, 'currentPowerFlow', true, responseTime);

    // Extract power values directly from the API response
    const currentPower = response.siteCurrentPowerFlow.PV?.currentPower || 0;
    const unit = response.siteCurrentPowerFlow.unit || 'kW';

    const metadata = {
      loadPower: response.siteCurrentPowerFlow.LOAD?.currentPower || 0,
      gridPower: response.siteCurrentPowerFlow.GRID?.currentPower || 0,
      updateRefreshRate: response.siteCurrentPowerFlow.updateRefreshRate,
      lastUpdated: new Date().toISOString(),
      pvStatus: response.siteCurrentPowerFlow.PV?.status,
      loadStatus: response.siteCurrentPowerFlow.LOAD?.status,
      gridStatus: response.siteCurrentPowerFlow.GRID?.status
    };



    // Cache the data
    await cacheData(dashboardId, SolarEdgeDataType.CURRENT_POWER, currentPower, unit, metadata);

    return { value: currentPower, unit, metadata };
  } catch (error) {
    const responseTime = Date.now() - startTime;
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';

    await logApiCall(dashboardId, 'currentPowerFlow', false, responseTime, errorMessage);

    console.error(`SolarEdge API error for dashboard ${dashboardId}:`, errorMessage);
    return null;
  }
}

/**
 * Get today's energy production from SolarEdge API
 */
export async function getTodayEnergy(
  dashboardId: string,
  apiKey: string,
  siteId: string
): Promise<{ value: number; unit: string; metadata?: any } | null> {
  const startTime = Date.now();

  try {
    // Check rate limiting
    if (!(await canMakeApiRequest(dashboardId))) {
      throw new Error('Daily API rate limit exceeded');
    }

    const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD format
    const url = `${SOLAREDGE_BASE_URL}/site/${siteId}/energy?timeUnit=DAY&startDate=${today}&endDate=${today}&api_key=${apiKey}`;

    const response: SolarEdgeEnergyResponse = await makeApiRequest(url);

    await incrementApiRequestCount(dashboardId);

    const responseTime = Date.now() - startTime;
    await logApiCall(dashboardId, 'energy-today', true, responseTime);

    const todayEnergy = response.energy.values[0]?.value || 0;
    const unit = response.energy.unit || 'Wh';

    const metadata = {
      date: today,
      measuredBy: response.energy.measuredBy,
      lastUpdated: new Date().toISOString()
    };

    // Cache the data
    await cacheData(dashboardId, SolarEdgeDataType.TODAY_ENERGY, todayEnergy, unit, metadata);

    return { value: todayEnergy, unit, metadata };
  } catch (error) {
    const responseTime = Date.now() - startTime;
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';

    await logApiCall(dashboardId, 'energy-today', false, responseTime, errorMessage);

    console.error(`SolarEdge API error for dashboard ${dashboardId}:`, errorMessage);
    return null;
  }
}

/**
 * Get year-to-date energy production from SolarEdge API
 */
export async function getYearEnergy(
  dashboardId: string,
  apiKey: string,
  siteId: string
): Promise<{ value: number; unit: string; metadata?: any } | null> {
  const startTime = Date.now();

  try {
    // Check rate limiting
    if (!(await canMakeApiRequest(dashboardId))) {
      throw new Error('Daily API rate limit exceeded');
    }

    const currentYear = new Date().getFullYear();
    const startDate = `${currentYear}-01-01`;
    const endDate = new Date().toISOString().split('T')[0];

    const url = `${SOLAREDGE_BASE_URL}/site/${siteId}/energy?timeUnit=YEAR&startDate=${startDate}&endDate=${endDate}&api_key=${apiKey}`;

    const response: SolarEdgeEnergyResponse = await makeApiRequest(url);

    await incrementApiRequestCount(dashboardId);

    const responseTime = Date.now() - startTime;
    await logApiCall(dashboardId, 'energy-year', true, responseTime);

    const yearEnergy = response.energy.values[0]?.value || 0;
    const unit = response.energy.unit || 'Wh';

    const metadata = {
      year: currentYear,
      startDate,
      endDate,
      measuredBy: response.energy.measuredBy,
      lastUpdated: new Date().toISOString()
    };

    // Cache the data
    await cacheData(dashboardId, SolarEdgeDataType.YEAR_ENERGY, yearEnergy, unit, metadata);

    return { value: yearEnergy, unit, metadata };
  } catch (error) {
    const responseTime = Date.now() - startTime;
    const errorMessage = error instanceof Error ? error.message : 'Unknown error';

    await logApiCall(dashboardId, 'energy-year', false, responseTime, errorMessage);

    console.error(`SolarEdge API error for dashboard ${dashboardId}:`, errorMessage);
    return null;
  }
}

/**
 * Get hourly energy production data for today
 */
export async function getTodayHourlyEnergy(
  dashboardId: string,
  apiKey: string,
  siteId: string
): Promise<{ value: Array<{hour: number, energy: number}>; unit: string; source: 'api' | 'cache' } | null> {
  try {
    // Check if we can make API request
    if (!(await canMakeApiRequest(dashboardId))) {


      // Try to get cached data
      const cached = await getCachedData(dashboardId, SolarEdgeDataType.TODAY_HOURLY_ENERGY);
      if (cached) {
        return {
          value: JSON.parse(cached.metadata?.hourlyData || '[]'),
          unit: cached.unit,
          source: 'cache'
        };
      }
      return null;
    }

    const startTime = Date.now();

    // Get today's date range
    const today = new Date();

    // Format for SolarEdge API (YYYY-MM-DD format)
    const startDateStr = today.toISOString().split('T')[0]; // Today
    const endDateStr = today.toISOString().split('T')[0];   // Same day for hourly data

    const url = `${SOLAREDGE_BASE_URL}/site/${siteId}/energy?timeUnit=HOUR&startDate=${startDateStr}&endDate=${endDateStr}&api_key=${apiKey}`;

    const response: SolarEdgeEnergyResponse = await makeApiRequest(url);

    await incrementApiRequestCount(dashboardId);

    const responseTime = Date.now() - startTime;
    await logApiCall(dashboardId, 'energy-hourly-today', true, responseTime);

    // Process hourly data
    const hourlyData: Array<{hour: number, energy: number}> = [];

    if (response.energy && response.energy.values) {
      for (const dataPoint of response.energy.values) {
        if (dataPoint.date && dataPoint.value !== null) {
          const date = new Date(dataPoint.date);
          const hour = date.getHours();
          const energy = dataPoint.value / 1000; // Convert Wh to kWh

          hourlyData.push({ hour, energy });
        }
      }
    }

    // Sort by hour
    hourlyData.sort((a, b) => a.hour - b.hour);

    const unit = response.energy.unit || 'Wh';

    const metadata = {
      date: today.toISOString().split('T')[0],
      hourlyData: JSON.stringify(hourlyData),
      measuredBy: response.energy.measuredBy,
      lastUpdated: new Date().toISOString()
    };

    // Cache the result
    await cacheData(dashboardId, SolarEdgeDataType.TODAY_HOURLY_ENERGY, hourlyData.reduce((sum, h) => sum + h.energy, 0), unit, metadata);



    return {
      value: hourlyData,
      unit,
      source: 'api'
    };

  } catch (error) {
    console.error(`❌ Error fetching hourly energy data for dashboard ${dashboardId}:`, error);

    // Try to get cached data as fallback
    const cached = await getCachedData(dashboardId, SolarEdgeDataType.TODAY_HOURLY_ENERGY);
    if (cached) {
      return {
        value: JSON.parse(cached.metadata?.hourlyData || '[]'),
        unit: cached.unit,
        source: 'cache'
      };
    }

    return null;
  }
}

/**
 * Get all solar data for a dashboard (current power, today energy, year energy)
 * Uses intelligent caching and rate limiting
 */
export async function getSolarData(
  dashboardId: string,
  apiKey: string,
  siteId: string
): Promise<{
  currentPower: { value: number; unit: string; metadata?: any; source: 'api' | 'cache' } | null;
  todayEnergy: { value: number; unit: string; metadata?: any; source: 'api' | 'cache' } | null;
  yearEnergy: { value: number; unit: string; metadata?: any; source: 'api' | 'cache' } | null;
  lastUpdated: Date;
  apiCallsRemaining: number;
}> {
  const results = {
    currentPower: null as any,
    todayEnergy: null as any,
    yearEnergy: null as any,
    lastUpdated: new Date(),
    apiCallsRemaining: 0
  };

  // Check remaining API calls for today
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  const rateLimit = await prisma.solarEdgeRateLimit.findFirst({
    where: {
      dashboardId,
      date: today
    }
  });

  results.apiCallsRemaining = MAX_REQUESTS_PER_DAY - (rateLimit?.requestCount || 0);

  // Try to get cached data first, then API data if needed and allowed
  const dataTypes = [
    { type: SolarEdgeDataType.CURRENT_POWER, key: 'currentPower' as const, apiCall: getCurrentPower },
    { type: SolarEdgeDataType.TODAY_ENERGY, key: 'todayEnergy' as const, apiCall: getTodayEnergy },
    { type: SolarEdgeDataType.YEAR_ENERGY, key: 'yearEnergy' as const, apiCall: getYearEnergy }
  ];

  for (const { type, key, apiCall } of dataTypes) {
    // Try cache first
    const cached = await getCachedData(dashboardId, type);

    if (cached) {
      results[key] = {
        value: cached.value,
        unit: cached.unit,
        metadata: cached.metadata,
        source: 'cache' as const
      };
    } else if (results.apiCallsRemaining > 0) {
      // Try API if cache miss and rate limit allows
      const apiData = await apiCall(dashboardId, apiKey, siteId);

      if (apiData) {
        results[key] = {
          ...apiData,
          source: 'api' as const
        };
        results.apiCallsRemaining--;
      }
    }
  }

  return results;
}
