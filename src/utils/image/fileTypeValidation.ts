// Magic-Bytes Checks (ohne <PERSON>)
export function isPng(buf: Buffer) {
  return (
    buf.length >= 8 &&
    buf[0] === 0x89 && buf[1] === 0x50 && buf[2] === 0x4e && buf[3] === 0x47 &&
    buf[4] === 0x0d && buf[5] === 0x0a && buf[6] === 0x1a && buf[7] === 0x0a
  );
}
export function isJpeg(buf: Buffer) {
  return (
    buf.length >= 4 &&
    buf[0] === 0xff && buf[1] === 0xd8 &&
    buf[buf.length - 2] === 0xff && buf[buf.length - 1] === 0xd9
  );
}
export function isWebp(buf: Buffer) {
  return buf.length >= 12 &&
    buf.slice(0, 4).toString("ascii") === "RIFF" &&
    buf.slice(8, 12).toString("ascii") === "WEBP";
}
export function isAvif(buf: Buffer) {
  if (buf.length < 16) return false;
  const hasFtyp = buf.slice(4, 8).toString("ascii") === "ftyp";
  const brand = buf.slice(8, 12).toString("ascii");
  return hasFtyp && (brand === "avif" || brand === "avis");
}
export function sniffMime(buf: Buffer): string | null {
  if (isPng(buf)) return "image/png";
  if (isJpeg(buf)) return "image/jpeg";
  if (isWebp(buf)) return "image/webp";
  if (isAvif(buf)) return "image/avif";
  return null;
}
export function extForMime(mime: string): string {
  switch (mime) {
    case "image/png":  return ".png";
    case "image/jpeg": return ".jpg";
    case "image/webp": return ".webp";
    case "image/avif": return ".avif";
    default:           return "";
  }
}