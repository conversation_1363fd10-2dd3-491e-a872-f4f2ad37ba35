import { MenuItem, MenuType, CollapsibleMenuSection, TabMenuSection } from "~/utils/menu/menuDef";

export interface MenuItemWithTab {
  menuItem: MenuItem;
  parentTab?: TabMenuSection;
}

export const findMenuItemByPathname = (
  pathname: string,
  visibleMenuItems: MenuItem[],
): MenuItem | undefined => {
  if (!pathname) return undefined;

  // Hilfsfunktion zum rekursiven Durchsuchen von einklappbaren Bereichen und Tab-Sektionen
  const searchInSection = (
    section: CollapsibleMenuSection | TabMenuSection,
  ): MenuItem | undefined => {
    for (const item of section.items) {
      if (item.type === MenuType.collapsibleSection) {
        const found = searchInSection(item as CollapsibleMenuSection);
        if (found) return found;
      } else if (item.type === MenuType.tabSection) {
        const found = searchInSection(item as TabMenuSection);
        if (found) return found;
      } else if (item.type === MenuType.menu) {
        // Exakte Übereinstimmung hat Priorität
        if (item.href === pathname) {
          return item;
        }
      }
    }
    return undefined;
  };

  let bestMatch: MenuItem | undefined = undefined;
  let bestMatchLength = 0;

  // Durchsuche alle Menü-Items
  for (const item of visibleMenuItems) {
    if (item.type === MenuType.menu) {
      // Direkte Menü-Items
      if (item.href === pathname) {
        return item; // Exakte Übereinstimmung - sofort zurückgeben
      }
      if (item.href && pathname.startsWith(item.href) && item.href.length > bestMatchLength) {
        bestMatch = item;
        bestMatchLength = item.href.length;
      }
    } else if (item.type === MenuType.collapsibleSection) {
      // Durchsuche einklappbare Sektionen
      const found = searchInSection(item as CollapsibleMenuSection);
      if (found && found.type === MenuType.menu) {
        if (found.href === pathname) {
          return found; // Exakte Übereinstimmung - sofort zurückgeben
        }
        if (found.href && pathname.startsWith(found.href) && found.href.length > bestMatchLength) {
          bestMatch = found;
          bestMatchLength = found.href.length;
        }
      }
    } else if (item.type === MenuType.tabSection) {
      // Durchsuche Tab-Sektionen
      const found = searchInSection(item as TabMenuSection);
      if (found && found.type === MenuType.menu) {
        if (found.href === pathname) {
          return found; // Exakte Übereinstimmung - sofort zurückgeben
        }
        if (found.href && pathname.startsWith(found.href) && found.href.length > bestMatchLength) {
          bestMatch = found;
          bestMatchLength = found.href.length;
        }
      }
    }
  }

  return bestMatch;
};

export const findMenuItemWithTabByPathname = (
  pathname: string,
  visibleMenuItems: MenuItem[],
): MenuItemWithTab | undefined => {
  if (!pathname) return undefined;

  // Hilfsfunktion zum rekursiven Durchsuchen von einklappbaren Bereichen und Tab-Sektionen
  const searchInSection = (
    section: CollapsibleMenuSection | TabMenuSection,
    parentTab?: TabMenuSection,
  ): MenuItemWithTab | undefined => {
    for (const item of section.items) {
      if (item.type === MenuType.collapsibleSection) {
        const found = searchInSection(item as CollapsibleMenuSection, parentTab);
        if (found) return found;
      } else if (item.type === MenuType.tabSection) {
        const found = searchInSection(item as TabMenuSection, item as TabMenuSection);
        if (found) return found;
      } else if (item.type === MenuType.menu) {
        // Exakte Übereinstimmung hat Priorität
        if (item.href === pathname) {
          return { menuItem: item, parentTab };
        }
      }
    }
    return undefined;
  };

  let bestMatch: MenuItemWithTab | undefined = undefined;
  let bestMatchLength = 0;

  // Durchsuche alle Menü-Items
  for (const item of visibleMenuItems) {
    if (item.type === MenuType.menu) {
      // Direkte Menü-Items
      if (item.href === pathname) {
        return { menuItem: item }; // Exakte Übereinstimmung - sofort zurückgeben
      }
      if (item.href && pathname.startsWith(item.href) && item.href.length > bestMatchLength) {
        bestMatch = { menuItem: item };
        bestMatchLength = item.href.length;
      }
    } else if (item.type === MenuType.collapsibleSection) {
      // Durchsuche einklappbare Sektionen
      const found = searchInSection(item as CollapsibleMenuSection);
      if (found && found.menuItem.type === MenuType.menu) {
        if (found.menuItem.href === pathname) {
          return found; // Exakte Übereinstimmung - sofort zurückgeben
        }
        if (found.menuItem.href && pathname.startsWith(found.menuItem.href) && found.menuItem.href.length > bestMatchLength) {
          bestMatch = found;
          bestMatchLength = found.menuItem.href.length;
        }
      }
    } else if (item.type === MenuType.tabSection) {
      // Durchsuche Tab-Sektionen
      const found = searchInSection(item as TabMenuSection, item as TabMenuSection);
      if (found && found.menuItem.type === MenuType.menu) {
        if (found.menuItem.href === pathname) {
          return found; // Exakte Übereinstimmung - sofort zurückgeben
        }
        if (found.menuItem.href && pathname.startsWith(found.menuItem.href) && found.menuItem.href.length > bestMatchLength) {
          bestMatch = found;
          bestMatchLength = found.menuItem.href.length;
        }
      }
    }
  }

  return bestMatch;
};
