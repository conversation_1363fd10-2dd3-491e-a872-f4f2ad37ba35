import React from "react";
import { Role } from "@prisma/client";
import { FaRegAddressCard, FaStripeS, FaTrophy, FaTv, FaUserSecret } from "react-icons/fa";
import { PiLeafDuotone } from "react-icons/pi";
import { BsBank2 } from "react-icons/bs";
import { FiDatabase } from "react-icons/fi";
import { LuDatabaseZap } from "react-icons/lu";
import { MdEventAvailable } from "react-icons/md";
import { FcSurvey } from "react-icons/fc";
import { SiMinetest } from "react-icons/si";

export enum MenuType {
  divider,
  menu,
  collapsibleSection,
  tabSection,
}

export interface MenuEntry {
  name: string;
  type: MenuType.menu;
  href: string;
  subMenu?: MenuEntry[];
  icon: string | React.ReactNode;
}

export interface MenuDivider {
  name: string;
  type: MenuType.divider;
  href?: string;
}

export interface CollapsibleMenuSection {
  name: string;
  type: MenuType.collapsibleSection;
  items: MenuItem[];
  defaultCollapsed?: boolean;
  icon: string | React.ReactNode;
}

export interface TabMenuSection {
  name: string;
  type: MenuType.tabSection;
  items: MenuItem[];
  icon: string | React.ReactNode;
}

export type MenuItem = MenuEntry | MenuDivider | CollapsibleMenuSection | TabMenuSection;

export interface RoleMenuDefinition {
  [Role.ADMIN]: MenuItem[];
  [Role.CPO]: MenuItem[];
  [Role.CARD_MANAGER]: MenuItem[];
  [Role.CARD_HOLDER]: MenuItem[];
  [Role.USER]: MenuItem[];
  [Role.THG_BUYER]: MenuItem[];
  [Role.PNC_REMOTE]: MenuItem[];
}

// Admin Menu Definition with Tabs
const adminMenuDef: MenuItem[] = [
  {
    name: "Operations",
    type: MenuType.tabSection,
    icon: "📊",
    items: [
      {
        name: "Dashboard",
        type: MenuType.menu,
        href: "/",
        icon: "🎛️",
      },
      {
        name: "Power Graph",
        type: MenuType.menu,
        href: "/power-visualization",
        icon: "📊",
      },
      {
        name: "Map",
        type: MenuType.menu,
        href: "/map",
        icon: "🗺️",
      },
      {
        name: "Solar Dashboards",
        type: MenuType.menu,
        href: "/solar-dashboard",
        icon: "🌞",
      },
    ],
  },
  {
    name: "Financial",
    type: MenuType.tabSection,
    icon: "💰",
    items: [
      {
        name: "CPO Revenue Dashboard",
        href: "/cpo-revenue-dashboard",
        type: MenuType.menu,
        icon: "📊",
      },
      {
        name: "CPO Verträge",
        href: "/cpoContract",
        type: MenuType.menu,
        icon: "📄",
      },
      {
        name: "Finance Dashboard",
        href: "/finance-dashboard",
        type: MenuType.menu,
        icon: "💰",
      },
      {
        name: "Forecast",
        href: "/financal-forecast",
        type: MenuType.menu,
        icon: "📈",
      },
      {
        name: "Invoice & Credit",
        href: "/invoice",
        type: MenuType.menu,
        icon: "🧾",
      },
      {
        name: "Invoice forecast",
        href: "/invoice-forecast",
        type: MenuType.menu,
        icon: "🕐",
      },
      {
        name: "Rechnungsübersicht",
        href: "/invoice-overview",
        type: MenuType.menu,
        icon: "📋",
      },
      {
        name: "Qonto Transactions",
        href: "/qonto-transactions",
        type: MenuType.menu,
        icon: <BsBank2 />,
      },
      {
        name: "Stripe",
        href: "/stripe",
        type: MenuType.menu,
        icon: <FaStripeS color={"#635bff"} />,
      },
      {
        name: "Adhoc-Tarif",
        href: "/adhoc-tarif",
        type: MenuType.menu,
        icon: "💰",
      },
    ],
  },
  {
    name: "Data & API",
    type: MenuType.tabSection,
    icon: <FiDatabase />,
    items: [
      {
        name: "CDRs",
        href: "/cdr",
        type: MenuType.menu,
        icon: <LuDatabaseZap className={"bg-yellow-100"} />,
      },
      {
        name: "EMP & CPO",
        href: "/contact",
        type: MenuType.menu,
        icon: <FaRegAddressCard />,
      },
      {
        name: "Flottenkarten",
        href: "/tokenGroup",
        type: MenuType.menu,
        icon: "🚗",
      },
      {
        name: "EMP-Ladekarten",
        type: MenuType.menu,
        href: "/emp/card",
        icon: "💳",
      },
      {
        name: "Locations",
        type: MenuType.menu,
        href: "/location",
        icon: "📍",
      },
      {
        name: "Tarife",
        href: "/tarif",
        type: MenuType.menu,
        icon: "💰",
      },
      {
        name: "Maintenance",
        href: "/maintenance",
        type: MenuType.menu,
        icon: "🔧",
      },
      {
        name: "Smart Charging",
        type: MenuType.menu,
        href: "/smart-charging",
        icon: "🧠",
      },
      {
        name: "Plug & Charge Events",
        type: MenuType.menu,
        href: "/plug-and-charge-events",
        icon: "⚡",
      },
    ],
  },
  {
    name: "Admin",
    type: MenuType.tabSection,
    icon: "⚙️",
    items: [
      {
        name: "Benutzerverwaltung",
        type: MenuType.menu,
        href: "/users",
        icon: "👥",
      },
      {
        name: "THG-Käufer einladen",
        type: MenuType.menu,
        href: "/admin/thg-invites",
        icon: <PiLeafDuotone color={"green"} />,
      },
      {
        name: "THG Marktübersicht",
        type: MenuType.menu,
        href: "/thg/market",
        icon: "📉",
      },
      {
        name: "Command",
        type: MenuType.menu,
        icon: "💻",
        href: "/command",
      },
      {
        name: "Occp Monitor Key",
        type: MenuType.menu,
        href: "/occp-monitorKey",
        icon: <FaTv />,
      },
      {
        name: "Log",
        type: MenuType.menu,
        href: "/log",
        icon: "📋",
      },
      {
        name: "OU Verwaltung",
        type: MenuType.menu,
        href: "/ous",
        icon: "🏢",
      },
      {
        name: "API Key Verwaltung",
        href: "/contact/apikey",
        type: MenuType.menu,
        icon: "🔑",
      },
      {
        name: "Achievements",
        href: "/achievements",
        type: MenuType.menu,
        icon: <FaTrophy />,
      },
      {
        name: "Achievement Events ",
        href: "/achievements/admin",
        type: MenuType.menu,
        icon: <MdEventAvailable />,
      },
    ],
  },
  {
    name: "Other",
    type: MenuType.tabSection,
    icon: "📋",
    items: [
      {
        name: "Benachrichtigungen",
        type: MenuType.menu,
        href: "/notifications",
        icon: "🔔",
      },
      {
        name: "Profil",
        type: MenuType.menu,
        href: "/profile",
        icon: "👤",
      },
      {
        name: "Support",
        type: MenuType.menu,
        href: "/support",
        icon: "❓",
      },
      {
        name: "Tarif Umfrage",
        type: MenuType.menu,
        href: "/tarifSurvey",
        icon: <FcSurvey />,
      },
    ],
  },
  {
    name: "RoleTab",
    type: MenuType.tabSection,
    icon: <FaUserSecret />,
    items: [
      {
        name: "CPO Ansicht",
        type: MenuType.collapsibleSection,
        defaultCollapsed: false,
        icon: "🔌",
        items: [
          {
            name: "Operations",
            type: MenuType.divider,
          },
          {
            name: "Dashboard",
            type: MenuType.menu,
            href: "/",
            icon: "🎛️",
          },
          {
            name: "Power Graph",
            type: MenuType.menu,
            href: "/power-visualization",
            icon: "📊",
          },
          {
            name: "Solar Dashboards",
            type: MenuType.menu,
            href: "/solar-dashboard",
            icon: "🌞",
          },
          {
            name: "Map",
            type: MenuType.menu,
            href: "/map",
            icon: "🗺️",
          },
          {
            name: "Financial",
            type: MenuType.divider,
          },
          {
            name: "Forecast",
            href: "/financal-forecast",
            type: MenuType.menu,
            icon: "📈",
          },
          {
            name: "Data",
            type: MenuType.divider,
          },
          {
            name: "Mein API Key",
            href: "/my-api-key",
            type: MenuType.menu,
            icon: "🔐",
          },
          {
            name: "Roaming Matrix",
            href: "/roaming-matrix",
            type: MenuType.menu,
            icon: "🌐",
          },
          {
            name: "Sonstiges",
            type: MenuType.divider,
          },
          {
            name: "Benachrichtigungen",
            type: MenuType.menu,
            href: "/notifications",
            icon: "🔔",
          },
          {
            name: "Profil",
            type: MenuType.menu,
            href: "/profile",
            icon: "👤",
          },
          {
            name: "Support",
            type: MenuType.menu,
            href: "/support",
            icon: "❓",
          },
          {
            name: "Tarif Umfrage",
            type: MenuType.menu,
            href: "/tarifSurvey",
            icon: <FcSurvey />,
          },
        ],
      },
      {
        name: "Card Manager Ansicht",
        type: MenuType.collapsibleSection,
        defaultCollapsed: false,
        icon: "💳",
        items: [
          {
            name: "Operations",
            type: MenuType.divider,
          },
          {
            name: "Dashboard",
            type: MenuType.menu,
            href: "/",
            icon: "🎛️",
          },
          {
            name: "Map",
            type: MenuType.menu,
            href: "/map",
            icon: "🗺️",
          },
          {
            name: "Data",
            type: MenuType.divider,
          },
          {
            name: "Mein API Key",
            href: "/my-api-key",
            type: MenuType.menu,
            icon: "🔐",
          },
          {
            name: "Roaming Matrix",
            href: "/roaming-matrix",
            type: MenuType.menu,
            icon: "🌐",
          },
          {
            name: "Nutzergruppen",
            type: MenuType.menu,
            href: "/userGroups",
            icon: "👥",
          },
          {
            name: "Ladekarten-Management",
            type: MenuType.divider,
          },
          {
            name: "Ladekarten",
            type: MenuType.menu,
            href: "/emp/card",
            icon: "💳",
          },
          {
            name: "Firmentarife",
            type: MenuType.menu,
            href: "/emp/tarif/managerview",
            icon: "🏷️",
          },
          {
            name: "Ladevorgänge",
            type: MenuType.menu,
            href: "/emp/charging-history/managerview",
            icon: "📋",
          },
          {
            name: "Rechnungen",
            type: MenuType.menu,
            href: "/emp/invoice/managerview",
            icon: "🧾",
          },
          {
            name: "Sonstiges",
            type: MenuType.divider,
          },
          {
            name: "Benachrichtigungen",
            type: MenuType.menu,
            href: "/notifications",
            icon: "🔔",
          },
          {
            name: "Profil",
            type: MenuType.menu,
            href: "/profile",
            icon: "👤",
          },
          {
            name: "Support",
            type: MenuType.menu,
            href: "/support",
            icon: "❓",
          },
        ],
      },
      {
        name: "Card Holder Ansicht",
        type: MenuType.collapsibleSection,
        defaultCollapsed: false,
        icon: "👤",
        items: [
          {
            name: "Home",
            type: MenuType.menu,
            href: "/",
            icon: "🏠",
          },
          {
            name: "Zahlungsdaten",
            type: MenuType.menu,
            href: "/emp/payment",
            icon: "💰",
          },
          {
            name: "Ladekarten",
            type: MenuType.menu,
            href: "/emp/card",
            icon: "💳",
          },
          {
            name: "Mein Tarif",
            type: MenuType.menu,
            href: "/emp/tarif/userview",
            icon: "🏷️",
          },
          {
            name: "Ladevorgänge",
            type: MenuType.menu,
            href: "/emp/charging-history/userview",
            icon: "⚡️",
          },
          {
            name: "Rechnungen",
            type: MenuType.menu,
            href: "/emp/invoice/userview",
            icon: "🧾",
          },
          {
            name: "Achievements",
            href: "/achievements",
            type: MenuType.menu,
            icon: <FaTrophy />,
          },
          {
            name: "Benachrichtigungen",
            type: MenuType.menu,
            href: "/notifications",
            icon: "🔔",
          },
          {
            name: "Profil",
            type: MenuType.menu,
            href: "/profile",
            icon: "👤",
          },
          {
            name: "Support",
            type: MenuType.menu,
            href: "/support",
            icon: "❓",
          },
        ],
      },
      {
        name: "THG Buyer Ansicht",
        type: MenuType.collapsibleSection,
        defaultCollapsed: false,
        icon: <PiLeafDuotone color={"green"} />,
        items: [
          {
            name: "Home",
            type: MenuType.menu,
            href: "/thg",
            icon: "🏠",
          },
          {
            name: "Meine Angebote",
            type: MenuType.menu,
            href: "/thg/offers",
            icon: "💰",
          },
          {
            name: "Profil",
            type: MenuType.menu,
            href: "/profile",
            icon: "👤",
          },
          {
            name: "Support",
            type: MenuType.menu,
            href: "/support",
            icon: "❓",
          },
        ],
      },
    ],
  },
];

// CPO Menu Definition
const cpoMenuDef: MenuItem[] = [
  {
    name: "Operations",
    type: MenuType.divider,
  },
  {
    name: "Dashboard",
    type: MenuType.menu,
    href: "/",
    icon: "🎛️",
  },
  {
    name: "Power Graph",
    type: MenuType.menu,
    href: "/power-visualization",
    icon: "📊",
  },
  {
    name: "Solar Dashboards",
    type: MenuType.menu,
    href: "/solar-dashboard",
    icon: "🌞",
  },
  {
    name: "Map",
    type: MenuType.menu,
    href: "/map",
    icon: "🗺️",
  },
  {
    name: "Financial",
    type: MenuType.divider,
  },
  {
    name: "Forecast",
    href: "/financal-forecast",
    type: MenuType.menu,
    icon: "📈",
  },

  {
    name: "Data",
    type: MenuType.divider,
  },
  {
    name: "Mein API Key",
    href: "/my-api-key",
    type: MenuType.menu,
    icon: "🔐",
  },
  {
    name: "Roaming Matrix",
    href: "/roaming-matrix",
    type: MenuType.menu,
    icon: "🌐",
  },
  {
    name: "Maintenance",
    type: MenuType.menu,
    href: "/maintenance",
    icon: "🔧",
  },
  {
    name: "Ladevorgänge",
    type: MenuType.menu,
    href: "/emp/charging-history/managerview",
    icon: "📋",
  },
  {
    name: "Sonstiges",
    type: MenuType.divider,
  },
  {
    name: "Benachrichtigungen",
    type: MenuType.menu,
    href: "/notifications",
    icon: "🔔",
  },
  {
    name: "Profil",
    type: MenuType.menu,
    href: "/profile",
    icon: "👤",
  },
  {
    name: "Support",
    type: MenuType.menu,
    href: "/support",
    icon: "❓",
  },
];

// Card Manager Menu Definition
const cardManagerMenuDef: MenuItem[] = [
  {
    name: "Operations",
    type: MenuType.divider,
  },
  {
    name: "Dashboard",
    type: MenuType.menu,
    href: "/",
    icon: "🎛️",
  },
  {
    name: "Map",
    type: MenuType.menu,
    href: "/map",
    icon: "🗺️",
  },

  {
    name: "Data",
    type: MenuType.divider,
  },
  {
    name: "Mein API Key",
    href: "/my-api-key",
    type: MenuType.menu,
    icon: "🔐",
  },
  {
    name: "Roaming Matrix",
    href: "/roaming-matrix",
    type: MenuType.menu,
    icon: "🌐",
  },
  {
    name: "Nutzergruppen",
    type: MenuType.menu,
    href: "/userGroups",
    icon: "👥",
  },
  {
    name: "Ladekarten-Management",
    type: MenuType.divider,
  },
  {
    name: "Ladekarten",
    type: MenuType.menu,
    href: "/emp/card",
    icon: "💳",
  },
  {
    name: "Firmentarife",
    type: MenuType.menu,
    href: "/emp/tarif/managerview",
    icon: "🏷️",
  },
  {
    name: "Ladevorgänge",
    type: MenuType.menu,
    href: "/emp/charging-history/managerview",
    icon: "📋",
  },
  {
    name: "Rechnungen",
    type: MenuType.menu,
    href: "/emp/invoice/managerview",
    icon: "🧾",
  },
  {
    name: "Benutzerverwaltung",
    type: MenuType.menu,
    href: "/users",
    icon: "👥",
  },
  {
    name: "Sonstiges",
    type: MenuType.divider,
  },
  {
    name: "Benachrichtigungen",
    type: MenuType.menu,
    href: "/notifications",
    icon: "🔔",
  },
  {
    name: "Profil",
    type: MenuType.menu,
    href: "/profile",
    icon: "👤",
  },
  {
    name: "Support",
    type: MenuType.menu,
    href: "/support",
    icon: "❓",
  },
];

// Card Holder Menu Definition
const cardHolderMenuDef: MenuItem[] = [
  {
    name: "Home",
    type: MenuType.menu,
    href: "/",
    icon: "🏠",
  },
  {
    name: "Ladekarten-Management",
    type: MenuType.divider,
  },
  {
    name: "Zahlungsdaten",
    type: MenuType.menu,
    href: "/emp/payment",
    icon: "💰",
  },
  {
    name: "Ladekarten",
    type: MenuType.menu,
    href: "/emp/card",
    icon: "💳",
  },
  {
    name: "Mein Tarif",
    type: MenuType.menu,
    href: "/emp/tarif/userview",
    icon: "🏷️",
  },
  {
    name: "Ladevorgänge",
    type: MenuType.menu,
    href: "/emp/charging-history/userview",
    icon: "⚡️",
  },
  {
    name: "Rechnungen",
    type: MenuType.menu,
    href: "/emp/invoice/userview",
    icon: "🧾",
  },
  {
    name: "Sonstiges",
    type: MenuType.divider,
  },
  {
    name: "Achievements",
    href: "/achievements",
    type: MenuType.menu,
    icon: <FaTrophy />,
  },
  {
    name: "TestDashboard",
    href: "/testDashboard",
    type: MenuType.menu,
    icon: <SiMinetest />,
  },
  {
    name: "Benachrichtigungen",
    type: MenuType.menu,
    href: "/notifications",
    icon: "🔔",
  },
  {
    name: "Profil",
    type: MenuType.menu,
    href: "/profile",
    icon: "👤",
  },
  {
    name: "Support",
    type: MenuType.menu,
    href: "/support",
    icon: "❓",
  },
];

// User Menu Definition
const userMenuDef: MenuItem[] = [
  {
    name: "Operations",
    type: MenuType.divider,
  },
  {
    name: "Dashboard",
    type: MenuType.menu,
    href: "/",
    icon: "🎛️",
  },
  {
    name: "Ladekarten-Management",
    type: MenuType.divider,
  },
  {
    name: "Mitarbeiter-Ladekarten",
    type: MenuType.menu,
    href: "/emp/card",
    icon: "💳",
  },
  {
    name: "Sonstiges",
    type: MenuType.divider,
  },
  {
    name: "Benachrichtigungen",
    type: MenuType.menu,
    href: "/notifications",
    icon: "🔔",
  },
  {
    name: "Support",
    type: MenuType.menu,
    href: "/support",
    icon: "❓",
  },
];

// THG Buyer Menu Definition
const thgBuyerMenuDef: MenuItem[] = [
  {
    name: "THG-Quoten",
    type: MenuType.divider,
  },
  {
    name: "Home",
    type: MenuType.menu,
    href: "/thg",
    icon: "🏠",
  },
  {
    name: "Meine Angebote",
    type: MenuType.menu,
    href: "/thg/offers",
    icon: "💰",
  },
  {
    name: "Sonstiges",
    type: MenuType.divider,
  },
  {
    name: "Profil",
    type: MenuType.menu,
    href: "/profile",
    icon: "👤",
  },
  {
    name: "Support",
    type: MenuType.menu,
    href: "/support",
    icon: "❓",
  },
];

// PnC Remote Menu Definition
const pncRemoteMenuDef: MenuItem[] = [
  {
    name: "Plug & Charge",
    type: MenuType.divider,
  },
  {
    name: "Plug & Charge Events",
    type: MenuType.menu,
    href: "/plug-and-charge-events",
    icon: "⚡",
  },
  {
    name: "Sonstiges",
    type: MenuType.divider,
  },
  {
    name: "Support",
    type: MenuType.menu,
    href: "/support",
    icon: "❓",
  },
];

// Role-based menu definitions
export const roleMenuDefinitions: RoleMenuDefinition = {
  [Role.ADMIN]: adminMenuDef,
  [Role.CPO]: cpoMenuDef,
  [Role.CARD_MANAGER]: cardManagerMenuDef,
  [Role.CARD_HOLDER]: cardHolderMenuDef,
  [Role.USER]: userMenuDef,
  [Role.THG_BUYER]: thgBuyerMenuDef,
  [Role.PNC_REMOTE]: pncRemoteMenuDef,
};

// Helper function to get menu for a specific role
export const getMenuForRole = (role: Role): MenuItem[] => {
  return roleMenuDefinitions[role] || [];
};
