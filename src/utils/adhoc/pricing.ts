import { PrismaClient as PrismaClientMongoDb } from "~/../prismaMongoAdhoc/client";
import { getPowerType } from "~/utils/longship";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";
import prismaMongo from "~/server/db/mongo";

export const getPriceForStationByEvseId = async (
  evseId: string,
  startDatetime: Date,
  chargePointType: number,
) => {
  if (!evseId) {
    return null;
  }
  const powerType = getPowerType(chargePointType);

  const location = await prismaMongo.location.findFirst({
    where: {
      evses: {
        some: {
          evse_id: evseId,
        },
      },
    },
  });
  if (!location || !powerType) {
    Logger(
      "Location or powertype of evseId not found",
      `No location for Adhoc evseId ${evseId}, powerType ${powerType}`,
      "utills",
      LogType.ERROR,
    );
    return null;
  }

  const locationPrice = await prismaMongo.locationPrice.findFirst({
    where: {
      locationId: location.id,
      start: {
        lte: startDatetime,
      },
      current_type: powerType,
      end: {
        gte: startDatetime, // Todo gt ?!
      },
    },
  });
  if (!locationPrice) {
    const parts = evseId.split("*");

    const country_id = parts[0];
    const party_id = parts[1];

    const emp = await prismaMongo.emp.findFirst({
      where: {
        party_id: party_id,
        country_code: country_id,
      },
    });
    if (!emp) {
      return null;
    }

    const empPrice = await prismaMongo.empPrice.findFirst({
      where: {
        empId: emp.id,
        start: {
          lte: startDatetime,
        },
        current_type: powerType,
        end: {
          gte: startDatetime, //Todo gt ?!
        },
      },
    });
    return empPrice;
  }
  return locationPrice;
};
