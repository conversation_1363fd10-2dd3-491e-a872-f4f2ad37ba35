import { DateTime } from "luxon";
export const getUtcToBerlinDelta = (): number => {
  const utcNow = DateTime.utc();
  const berlinNow = utcNow.setZone("Europe/Berlin");
  const delta = berlinNow.offset / 60;
  return delta;
};

export const getFirstDayOfMonth = (d: Date = new Date()): Date =>
  new Date(d.getFullYear(), d.getMonth(), 1, 0, 0, 0, 0);

/**
 * Funktion nimmt einen iso string und interpretiert ihn mit richtiger lokalen zeitzone
 * Dann speichert wird das datetime object so formatiert ausgegeben, dass es
 * +1 ode +2 stunden später wäre (aber gefaked als UTC). So bekommt man mysql dazu auch
 * die zeit in der db so anzuzeigen
 * @param datetimeString
 */
export const convertTucToLocaltime = (datetimeString: string) => {
  const dateInTimeZone = DateTime.fromISO(datetimeString, { zone: "Europe/Berlin" });

  //simulate UTC
  return dateInTimeZone.toFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'");
};

export const getMonthNameInLocale = (date: Date) => {
  return date.toLocaleString(navigator.language, { month: "long" });
};

export const getLastDayOfMonth = (d: Date = new Date()): Date =>
  new Date(d.getFullYear(), d.getMonth() + 1, 0, 23, 59, 59, 999);

export const getFirstDayOfLastMonth = (): Date => {
  const date = new Date();
  const year = date.getMonth() === 0 ? date.getFullYear() - 1 : date.getFullYear();
  const month = date.getMonth() === 0 ? 11 : date.getMonth() - 1;
  return new Date(year, month, 1);
};

export const getLastDayOfLastMonth = (): Date => {
  const date = new Date();
  const year = date.getMonth() === 0 ? date.getFullYear() - 1 : date.getFullYear();
  const month = date.getMonth() === 0 ? 11 : date.getMonth() - 1;
  const lastDayOfMonth = new Date(year, month + 1, 0);
  return new Date(
    lastDayOfMonth.getFullYear(),
    lastDayOfMonth.getMonth(),
    lastDayOfMonth.getDate(),
    23,
    59,
    59,
    999,
  );
};

export const convertUtcToCet = (utcDate: Date, locale = "de-DE"): string => {
  // CET ist entweder UTC+1 (Normalzeit) oder UTC+2 (Sommerzeit)
  const cetOffsetNormal = 60; // Minuten
  const cetOffsetDst = 120; // Minuten

  // Hilfsfunktion zum Prüfen der Sommerzeit (DST)
  function isDst(date: Date): boolean {
    const year = date.getFullYear();
    const lastSundayOfMarch = getLastSundayOfMonth(year, 2);
    const lastSundayOfOctober = getLastSundayOfMonth(year, 9);

    return date >= lastSundayOfMarch && date < lastSundayOfOctober;
  }

  // Hilfsfunktion zum Ermitteln des letzten Sonntags im Monat
  function getLastSundayOfMonth(year: number, month: number): Date {
    const lastDayOfMonth = new Date(year, month + 1, 0);
    const dayOfWeek = lastDayOfMonth.getDay();

    return new Date(year, month, lastDayOfMonth.getDate() - dayOfWeek);
  }

  // Ermitteln des richtigen CET-Offsets basierend auf der Sommerzeit
  const cetOffset = isDst(utcDate) ? cetOffsetDst : cetOffsetNormal;

  // Erstellen des CET-Datums
  const cetDate = new Date(utcDate);
  cetDate.setUTCMinutes(cetDate.getUTCMinutes() + cetOffset);

  // Formatieren des CET-Datums basierend auf der angegebenen Sprache (locale)
  const formattedCetDate = cetDate.toLocaleDateString(locale);

  return formattedCetDate;
};

export const startOfDay = (dateString: string) => {
  const date = new Date(dateString);
  date.setHours(0, 0, 0, 0);
  return date;
};

export const endOfDay = (dateString: string) => {
  const date = new Date(dateString);
  date.setHours(23, 59, 59, 999);
  return date;
};

export const yesterdayStart = () => {
  const yesterday_start = new Date();
  yesterday_start.setDate(yesterday_start.getDate() - 1);
  yesterday_start.setHours(0, 0, 0, 0);
  return yesterday_start;
};

export const yesterdayEnd = () => {
  const yesterday_end = new Date();
  yesterday_end.setDate(yesterday_end.getDate() - 1);
  yesterday_end.setHours(23, 59, 59, 0);
  return yesterday_end;
};

export const todayEnd = () => {
  const today_end = new Date();
  today_end.setHours(23, 59, 59, 0);
  return today_end;
};
export const getFirstDayOfYear = (): Date => {
  const date = new Date();
  return new Date(date.getFullYear(), 0, 1, 0, 0, 0, 0);
};

export const getLastDayOfYear = (): Date => {
  const date = new Date();
  return new Date(date.getFullYear(), 11, 31, 23, 59, 59, 999);
};

export const getDateStringFromDateObject = (date: Date) => {
  return date.toISOString().split("T")[0];
};

export const formatSecondsToUnit = (seconds: number) => {
  if (seconds < 60) {
    return seconds.toFixed(0) + "s"; // Sekunden
  } else if (seconds < 3600) {
    return (seconds / 60).toFixed(0) + "m"; // Minuten
  } else {
    return (seconds / 3600).toFixed(1) + "h"; // Stunden
  }
};

export const getDateLabel = (date: Date) => {
  const today = new Date();
  const yesterday = new Date(today);
  const tomorrow = new Date(today);

  yesterday.setDate(today.getDate() - 1);
  tomorrow.setDate(today.getDate() + 1);

  const inputDate = new Date(date);

  if (inputDate.toDateString() === today.toDateString()) {
    return "heute";
  } else if (inputDate.toDateString() === tomorrow.toDateString()) {
    return "morgen";
  } else if (inputDate.toDateString() === yesterday.toDateString()) {
    return "gestern";
  } else {
    return inputDate.toLocaleDateString(); // Gibt das Datum im lokalen Format zurück
  }
};


export const ensureDate = (d: string | Date) => (typeof d === "string" ? new Date(d) : d);

export const pad = (n: number) => String(n).padStart(2, '0');
export const fmtDate = (d?: Date | string | null): string => {
  if (!d) return '';
  const x = typeof d === 'string' ? new Date(d) : d;
  return `${x.getFullYear()}-${pad(x.getMonth() + 1)}-${pad(x.getDate())} ${pad(x.getHours())}:${pad(x.getMinutes())}`;
};

export function startOfWeekMonday(d = new Date()) {
  const copy = new Date(d);
  const day = (copy.getDay() + 6) % 7; // Mo=0 … So=6
  copy.setDate(copy.getDate() - day);
  copy.setHours(0, 0, 0, 0);
  return copy;
}
export function endOfWeekSunday(d = new Date()) {
  const start = startOfWeekMonday(d);
  const end = new Date(start);
  end.setDate(start.getDate() + 6);
  end.setHours(23, 59, 59, 999);
  return end;
}

export function toDateString(dateLike?: string | number | Date): string {
    if (!dateLike) return "";
    const d = new Date(dateLike);
    if (isNaN(d.getTime())) return "";
    const pad = (n: number) => n.toString().padStart(2, "0");
    return `${d.getFullYear()}-${pad(d.getMonth() + 1)}-${pad(d.getDate())}`;
}




/**
 * Converts a number of days into a human-readable time period string
 * @param days Number of days to convert
 * @returns Formatted string (e.g., "2 Jahre", "3 Monate", "15 Tage")
 */
export function formatDaysPeriod(days: number): string {
  if (days < 0) {
    return "0 Tage";
  }

  // Round to nearest whole number
  const totalDays = Math.round(days);

  // Calculate years (365 days per year)
  const years = Math.floor(totalDays / 365);
  const remainingDaysAfterYears = totalDays % 365;

  // Calculate months (30 days per month for remaining days)
  const months = Math.floor(remainingDaysAfterYears / 30);
  const remainingDays = remainingDaysAfterYears % 30;

  // Build result string
  const parts: string[] = [];

  if (years > 0) {
    parts.push(`${years} ${years === 1 ? 'Jahr' : 'Jahre'}`);
  }

  if (months > 0) {
    parts.push(`${months} ${months === 1 ? 'Monat' : 'Monate'}`);
  }

  if (remainingDays > 0 || parts.length === 0) {
    parts.push(`${remainingDays} ${remainingDays === 1 ? 'Tag' : 'Tage'}`);
  }

  // Join with commas and "und" for the last part
  if (parts.length == 1) {
    return parts[0] ?? "0 Tage";
  } else if (parts.length === 2) {
    return parts.join(' und ');
  } else {
    const lastPart = parts.pop();
    return parts.join(', ') + ' und ' + lastPart;
  }
}
