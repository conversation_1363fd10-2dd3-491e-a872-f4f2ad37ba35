import { CsvRow, CurrentType } from "~/types/adhocTarif/adhocEmpPriceTypes";
import { fmtDate } from "~/utils/date/date";

const toStr = (v: unknown) => (v == null ? '' : String(v));

export function mapLocationPriceToCsvRow(lp: any): CsvRow {
  return {
    locationId: toStr(lp.locationId ?? ''),
    _id: toStr(lp.id ?? lp._id),
    start: fmtDate(lp.start),
    end: fmtDate(lp.end),
    energy_price: toStr(lp.energy_price),
    session_fee: toStr(lp.session_fee),
    tax_rate: toStr(lp.tax_rate),
    blocking_fee: toStr(lp.blocking_fee),
    blocking_fee_max: toStr(lp.blocking_fee_max),
    blocking_fee_start: toStr(lp.blocking_fee_start),
    current_type: toStr(lp.current_type as CurrentType),
    // LocationPrice hat locationId
    empId: toStr(lp.empId ?? lp.Emp?.id ?? ''), // Fallback auf verknüpftes Emp
  };
}

export function mapEmpPriceToCsvRow(ep: any): CsvRow {
  return {
    locationId: '',
    _id: toStr(ep.id ?? ep._id),
    start: fmtDate(ep.start),
    end: fmtDate(ep.end),
    energy_price: toStr(ep.energy_price),
    session_fee: toStr(ep.session_fee),
    tax_rate: toStr(ep.tax_rate),
    blocking_fee: toStr(ep.blocking_fee),
    blocking_fee_max: toStr(ep.blocking_fee_max),
    blocking_fee_start: toStr(ep.blocking_fee_start),
    current_type: toStr(ep.current_type as CurrentType),
    // << leerer Slot für EMP-Preise
    empId: toStr(ep.empId ?? ep.Emp?.id ?? ''),
  };
}