import { env } from "../env.js";
import Logger from "../server/logger/logger";
import { EMPCard, LogType, OCPPErrorStatusMessage, Prisma, TokenGroup } from "@prisma/client";
import { createUrl, endpoints } from "~/server/api/longship/constants";
import prisma from "~/server/db/prisma";

type LongshipHeadersPops = {
  runningOnly?: "1" | "0";
};
export const LONGSHIP_CHARGEPOINT_TYPE_LIST_AC = [1, 2, 21, 3, 4, 41];
export const LONGSHIP_CHARGEPOINT_TYPE_LIST_DC = [5, 50];

//export type ChargePointType = 0 | 1 | 2 | 21 | 3 | 4 | 41 | 5 | 50;
interface OcppMessage {
  id: string;
  chargePointId: string;
  messageId: string;
  wampMessageType: string;
  ocppMessageType: string;
  direction: string;
  tenantId: string;
  payload: string; // oder `payload: StatusNotificationPayload;` wenn die Struktur immer gleich ist
  timestamp: string;
}
const extractMessageData = (ocppNestedArray: OcppMessage[][]): OCPPErrorStatusMessage[] => {
  return ocppNestedArray.reduce((acc: OCPPErrorStatusMessage[], innerArray: OcppMessage[]) => {
    innerArray.forEach((item) => {
      if (item.ocppMessageType === "StatusNotification") {
        try {
          const payload = JSON.parse(item.payload)[3];

          const data: OCPPErrorStatusMessage = {
            messageId: item.messageId,
            connectorId: payload.connectorId,
            status: payload.status,
            errorCode: payload.errorCode,
            info: payload.info,
            timestamp: payload.timestamp,
            chargePointId: item.chargePointId,
            wampMessageType: item.wampMessageType,
            ocppMessageType: item.ocppMessageType,
          };

          acc.push(data);
        } catch (error) {
          console.error("Fehler beim Parsen des Payloads:", error);
        }
      }
    });
    return acc;
  }, []);
};

export const fetchMessagesFromChargepoints = async () => {
  if (!prisma) {
    Logger(`Prisma object not available`, "Prisma not found", "Error Monitoring", LogType.ERROR);
    return false;
  }
  const header = LongshipHeaders({});
  const chargepoints = await prisma.chargePoint.findMany({
    where: { connectivityStatus: "ONLINE" },
    select: {
      chargePointId: true,
    },
  });

  if (!chargepoints) {
    Logger(
      `No chargpoints found in DB`,
      "No chargpoints found in DB",
      "Error Monitoring",
      LogType.DEBUG,
    );
    return false;
  }

  //GET https://api.longship.io/v1/chargepoints/EUL_0011_01/messages?search=Plug&from=2024-01-01&to=2024-01-15&chargerToCpoOnly=true
  const today = new Date();
  const today_str = today.toISOString().split("T")[0];
  const tomorrow = new Date();
  tomorrow.setDate(today.getDate() + 1);
  const tomorrow_str = tomorrow.toISOString().split("T")[0];

  // collect all fetches for each chargepoint
  const fetchRequests: Promise<Response>[] = chargepoints?.map((chargepoint) =>
    fetch(
      `${env.LONGSHIP_API}chargepoints/${chargepoint.chargePointId}/messages?search=ConnectorLockFailure&from=${today_str}&to=${tomorrow_str}&chargerToCpoOnly=true`,
      {
        method: "GET",
        headers: header,
      },
    ),
  );
  // resolve all and use allSettled to avoid one bad request will stop all
  const results = await Promise.allSettled(fetchRequests);
  const reponses = results.map(async (result, index) => {
    if (result.status === "fulfilled") {
      const response: Response = result.value;
      return await response.json();
    }
  });
  const messages = await Promise.all(reponses);
  const filtered_messages = messages.filter((message_list) => message_list.length > 0);
  const messageData = extractMessageData(filtered_messages);
  const insertet = await prisma.oCPPErrorStatusMessage.createMany({
    data: messageData,
    skipDuplicates: true,
  });
};

export enum PowerType {
  AC = "AC",
  DC = "DC",
}

export const getPowerType = (chargePointType: number | null) => {
  if (!chargePointType) {
    return null;
  }
  if (LONGSHIP_CHARGEPOINT_TYPE_LIST_AC.includes(chargePointType)) {
    return PowerType.AC;
  }
  if (LONGSHIP_CHARGEPOINT_TYPE_LIST_DC.includes(chargePointType)) {
    return PowerType.DC;
  }
  return undefined;
};
/**
 * Requests 'ErrorsList' configuration item from all ONLINE chargers
 * and if there is an error it will be stored into database
 */
export const fetchErrorsFromChargepoints = async () => {
  if (!prisma) {
    Logger(`Prisma object not available`, "Prisma not found", "Error Monitoring", LogType.ERROR);
    return false;
  }
  const header = LongshipHeaders({});
  let chargepoints = await prisma.chargePoint.findMany({
    where: { connectivityStatus: "ONLINE" },
    select: {
      chargePointId: true,
      chargePointVendor: true,
      simCardNumber: true,
    },
  });

  if (!chargepoints) {
    Logger(
      `No chargepoints found in DB`,
      "No chargepoints found in DB",
      "Error Monitoring",
      LogType.DEBUG,
    );
    return false;
  }

  const simBlacklist = ["1NCE"];

  const vendorWhitelist = ["ebee", "technisat", "bender gmbh co. kg"];
  chargepoints = chargepoints.filter((chargePoint) =>
    vendorWhitelist.includes(chargePoint.chargePointVendor.toLowerCase()),
  );

  chargepoints = chargepoints.filter(
    (chargePoint) => !simBlacklist.includes(chargePoint?.simCardNumber ?? ""),
  );

  // collect all fetches for each chargepoint
  const fetchRequests: Promise<Response>[] = chargepoints?.map((chargepoint) =>
    fetch(`${env.LONGSHIP_API}chargepoints/${chargepoint.chargePointId}/getconfiguration`, {
      method: "POST",
      headers: header,
      body: JSON.stringify({ key: ["ErrorsList"] }),
    }),
  );
  // resolve all and use allSettled to avoid one bad request will stop all
  const results = await Promise.allSettled(fetchRequests);

  // check each response and create an array with location urls where to fetch the configuration item
  const responses = results.map((result, index) => {
    if (result.status === "fulfilled") {
      const response: Response = result.value;

      // Location contains the url where to fetch the message with configuration item
      const location = response.headers.get("Location");
      return {
        status: response.status,
        statusText: response.statusText,
        locationUrl: location?.replaceAll("/v1/", ""), //remove because env var already has /v1/ included
        chargePointId: chargepoints ? chargepoints[index]?.chargePointId : null,
      };
    } else {
      return {
        status: result.status,
        locationUrl: null,
        statusText: result.reason,
        chargePointId: chargepoints ? chargepoints[index]?.chargePointId : null,
      };
    }
  });

  // filter all successful responses to request the messages including the configuration item
  const fetchRequestsErrorsList = responses
    .filter((response) => response.status === 202)
    .map((response) =>
      fetch(`${env.LONGSHIP_API}${response.locationUrl}`, {
        method: "GET",
        headers: header,
      }),
    );
  // again - resolve all
  const errorsListResults = await Promise.allSettled(fetchRequestsErrorsList);

  // iterate through responses and return json response
  const configItemListsPromises = errorsListResults.map(async (result) => {
    if (result.status == "fulfilled") {
      const response: Response = result.value;
      return await response.json();
    }
    return null; // oder einen Standardwert zurückgeben, wenn das Promise nicht erfüllt wurde
  });
  // reolve all
  const configItemLists = await Promise.all(configItemListsPromises);

  //create array with error objects including error value and chargepointId
  const errors = configItemLists.map((configItemArray) => {
    try {
      if (configItemArray.length == 0) {
        return { error: null, chargePointId: null };
      }
      return {
        error: JSON.parse(configItemArray[0].payload)[2]["configurationKey"][0].value,
        chargePointId: configItemArray[0].chargePointId,
      };
    } catch (error: unknown) {
      if (error instanceof Error) {
        Logger(
          `Error parsing chargepoint error ${error.message}. JSON=${configItemArray[0]},`,
          "Exception in reading errorslist key",
          "Error Monitoring",
          LogType.DEBUG,
        );
      } else {
        Logger(
          `Error parsing chargepoint error JSON=${configItemArray[0]},`,
          "Exception in reading errorslist key",
          "Error Monitoring",
          LogType.DEBUG,
        );
      }
      return { error: null, chargePointId: null };
    }
  });
  // only take care about errors
  const filteredErrors = errors.filter(
    (errorItem) =>
      errorItem.error != null &&
      errorItem.error !== "No errors" &&
      errorItem.error !== "Keine Fehler",
  );

  const exploded_errors = filteredErrors.flatMap((item) => {
    const errors = item.error.split("<br>").filter((error: string) => error.trim() !== "");

    return errors.map((error: string) => ({
      error: error,
      chargePointId: item.chargePointId,
    }));
  });

  //persist errors
  const data = await prisma.chargePointError.createMany({
    data: exploded_errors,
  });
  return data?.count;
};

export const LongshipHeaders = (params: LongshipHeadersPops) => {
  return new Headers({
    accept: "application/json",
    "Ocp-Apim-Subscription-Key": `${env.LONGSHIP_API_OCP_KEY}`,
    "x-api-key": `${env.LONGSHIP_API_X_API_KEY}`,
    ...params,
  });
};
export interface GetAllDataProps {
  params?: Record<string, string | number>;
}

export const getAllData = async (
  endpoint: string,
  { params = {} }: GetAllDataProps = {},
): Promise<any[]> => {
  const url = createUrl(endpoint);
  let data: any[] = [];
  let offset = 0;
  const limit = 100;
  const headers = LongshipHeaders({});

  while (true) {
    const url_params = new URLSearchParams({
      ...params,
      skip: `${offset}`,
      take: `${limit}`,
    }).toString();

    Logger(
      `Preparing to send request to ${url}?${url_params}`,
      "Longship Realtime request",
      "longship",
      LogType.DEBUG,
    );

    try {
      const response = await fetch(`${url}?${url_params}`, {
        method: "GET",
        headers: headers,
      });

      if (!response.ok) {
        Logger(
          `Can't fetch data from Longship ${response.status} ${response.statusText}`,
          "Longship Realtime request",
          "longship",
          LogType.ERROR,
        );
        throw new Error(`Error response from server: ${response.status} ${response.statusText}`);
      }

      const json = await response.json();
      Logger(
        `Received ${json.length} records`,
        "Longship Realtime request",
        "longship",
        LogType.DEBUG,
      );

      if (json.length === 0) {
        Logger(
          `No more records, breaking the loop`,
          "Longship Realtime request",
          "longship",
          LogType.DEBUG,
        );
        break;
      }

      // Specific handling for sessions endpoint
      if (endpoint === endpoints.SESSIONS) {
        const runningSessions = json.filter((session: any) => session?.status === "ACTIVE");
        if (runningSessions.length !== json.length) {
          data = data.concat(runningSessions);
          return data;
        }
      }

      data = data.concat(json);
      offset += limit;
    } catch (error) {
      Logger(
        `An error occurred: ${error instanceof Error ? error.message : "Unknown error"}`,
        "Longship Realtime request",
        "longship",
        LogType.ERROR,
      );
      throw error;
    }
  }

  return data;
};

export type EMPCardWithPhysicalCardAndTarifsAndOu = Prisma.EMPCardGetPayload<{
  include: { tarifs: { include: { tarif: { include: { ou: true } } } }; physicalCard: true };
}>;

export type EMPCardWithPhysicalCard = Prisma.EMPCardGetPayload<{
  include: { physicalCard: true };
}>;

export type EMPCardWithPhysicalCardAndContact = Prisma.EMPCardGetPayload<{
  include: { physicalCard: true; contact: true };
}>;
interface LocalToken {
  isValid: boolean;
  name: string;
  uid: string;
  contractId: string;
  normalizedContractId: string;
}
interface LocalTokenGroup {
  id: string;
  oucode: string;
  tokens: LocalToken[];
}
export const setEMPCardInvalid = async (empCard: EMPCardWithPhysicalCardAndTarifsAndOu) => {
  const errors = [];
  const headers = LongshipHeaders({});
  if (!empCard?.physicalCard?.uid) {
    errors.push("Keine physische Karte zugeordnet");
    return errors;
  }
  const fetchRequest = await fetch(`${env.LONGSHIP_API}localtokengroups?skip=0&take=100`, {
    method: "GET",
    headers: headers,
  });

  if (fetchRequest.ok) {
    const ouCodes = empCard.tarifs.map((tarifOnEmpCard) => tarifOnEmpCard.tarif.ou.code);
    const tokenGroups = (await fetchRequest.json()) as LocalTokenGroup[];
    for (const oucode of ouCodes) {
      const tokengroup = tokenGroups.find((tokenGroup) => tokenGroup.oucode == oucode);
      if (tokengroup) {
        const tokenInLongship = tokengroup.tokens.find(
          (token) => token.uid?.toUpperCase() == empCard?.physicalCard?.uid?.toUpperCase(),
        );
        if (!tokenInLongship) {
          errors.push("Token im Partnersystem nicht gefunden");
          //todo abbrechen oder trotzdem pushen (dann wird er deaktiviert angelegt)
        }

        const tokenPushRequst = await fetch(
          `${env.LONGSHIP_API}localtokengroups/${tokengroup.id}/token/${empCard.physicalCard.uid}`,
          {
            method: "PUT",
            headers: headers,
            body: JSON.stringify({
              valid: false,
              contractId: tokenInLongship?.contractId,
              name: tokenInLongship?.name,
            }),
          },
        );
        if (!tokenPushRequst.ok) {
          errors.push(tokenPushRequst.statusText);
        }
      }
    }
  } else {
    errors.push(fetchRequest.statusText);
  }
  return errors;
};

export const pushEmpCard = async (empCard: EMPCardWithPhysicalCardAndTarifsAndOu) => {
  const errors = [];
  if (!empCard?.physicalCard?.uid) {
    errors.push("Keine physische Karte zugeordnet");
    return errors;
  }
  const headers = LongshipHeaders({});
  const fetchRequest = await fetch(`${env.LONGSHIP_API}localtokengroups?skip=0&take=100`, {
    method: "GET",
    headers: headers,
  });

  if (fetchRequest.ok) {
    const ouCodes = empCard.tarifs.map((tarifOnEmpCard) => tarifOnEmpCard.tarif.ou.code);
    const tokenGroups = (await fetchRequest.json()) as LocalTokenGroup[];
    for (const oucode of ouCodes) {
      const tokengroup = tokenGroups.find((tokenGroup) => tokenGroup.oucode == oucode);
      if (tokengroup) {
        const contractId = empCard.userId
          ? `UserId ${empCard.userId}`
          : `ContactId ${empCard.contactId}`;
        const tokenPushRequst = await fetch(
          `${env.LONGSHIP_API}localtokengroups/${tokengroup.id}/token/${empCard.physicalCard.uid}`,
          {
            method: "PUT",
            headers: headers,
            body: JSON.stringify({
              isValid: "true",
              name: `Mitarbeiter/Club (${empCard.physicalCard.visualNumber})`,
              contractId: contractId,
            }),
          },
        );
        if (!tokenPushRequst.ok) {
          errors.push(tokenPushRequst.statusText);
        }
      }
    }
  } else {
    errors.push(fetchRequest.statusText);
  }
  return errors;
};

export const softResetTechnagons = async () => {
  const technagonChargers = await prisma.chargePoint.findMany({
    where: { chargePointVendor: "Technagon" },
  });

  const chargepointIds = technagonChargers.map((cp) => cp.chargePointId);
  const params = {
    runningOnly: "true",
    completedOnly: "false",
    orderBy: "start",
    descending: "true",
  };

  const sessionData = await getAllData(endpoints.SESSIONS, { params });
  const activeTechnagonSessions = sessionData.filter((session) =>
    chargepointIds.includes(session.chargePointId),
  );
  const activeTechnagonChargePointIds = activeTechnagonSessions.map(
    (session) => session.chargePointId,
  );
  const headers = LongshipHeaders({});

  for (const chargePointID of chargepointIds) {
    if (activeTechnagonChargePointIds.includes(chargePointID)) {
      continue; // skipp technagons with runnin session
    }

    const fetchRequest = await fetch(`${env.LONGSHIP_API}chargepoints/${chargePointID}/reset`, {
      method: "POST",
      headers: headers,
      body: JSON.stringify({ type: "Soft" }),
    });
  }
};
