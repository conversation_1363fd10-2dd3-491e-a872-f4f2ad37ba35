import prisma from "~/server/db/prisma";
import Logger from "~/server/logger/logger";
import { LogType, PlugAndChargeEvent, PlugAndChargeEventType } from "@prisma/client";
import { IncomingPlugAndChargeEvent, OutgoingPlugAndChargeEvent } from "~/types/plugAndCharge";
import { DateTime } from "luxon";
import { env } from "~/env";

export const triggerPlugAndChargeEvent = async (
  evseId: string,
  chargePointId: string,
  connectorId: number,
  ocppTimestamp: string,
  locationId: string,
) => {
  const location = await prisma.location.findUnique({
    where: {
      id: locationId,
    },
    select: {
      coordinates: true,
      ou: true,
    },
  });
  if (location?.coordinates?.latitude && location?.coordinates?.longitude) {
    Logger(
      `Triggering PlugAndCharge event for ${evseId} at ${ocppTimestamp} with coordinates ${location?.coordinates?.latitude}, ${location?.coordinates?.longitude}`,
      "PlugAndCharge",
      "PlugAndCharge",
      LogType.INFO,
    );
    // Prepare outgoing event data (based on your format)
    const outgoingEventData = {
      evseid: evseId,
      plugInEventTimestamp: ocppTimestamp,
      locationid: locationId,
      latitude: location.coordinates.latitude,
      longitude: location.coordinates.longitude,
    };

    // Store outgoing event in database
    const outgoingEvent = await storePlugAndChargeEvent({
      raw: outgoingEventData,
      evseid: evseId,
      locationId: locationId,
      plugInEventTimestamp: new Date(ocppTimestamp),
      latitude: location.coordinates.latitude,
      longitude: location.coordinates.longitude,
      type: PlugAndChargeEventType.OUTGOING,
      emaid: null, // Not available for outgoing events
      pcid: null, // Not available for outgoing events
      sessionId: null, // Not available for outgoing events
      matchingConfidence: null, // Not applicable for outgoing events
      ouId: location.ou.id,
      outgoingRequestTimestamp: new Date(),
      outgoingResponseTimestamp: null,
      incomingRequestTimestamp: null,
      uuid: null,
    });

    // create timestamp according to matching service API
    const tstamp = new Date(ocppTimestamp).getTime() / 1000;
    const eventResponse = await fetch(
      env.PLUG_AND_CHAGE_DOMAIN + env.PLUG_AND_CHARGE_SEND_EVENT_SLUG,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${process.env.PLUG_AND_CHARGE_24H_DEV_BEARER}`,
        },

        body: JSON.stringify({
          location: {
            latitude: location.coordinates.latitude,
            longitude: location.coordinates.longitude,
          },
          plugInEventTimestamp: tstamp,
          evseid: evseId,
        }),
      },
    );

    if (eventResponse.ok) {
      const body = await eventResponse.json();
      //do not wait
      updatePlugAndChargeEvent(outgoingEvent.id, {
        uuid: body.uuid,
        outgoingResponseTimestamp: new Date(),
      }).catch((error) => {
        Logger(
          `Failed to update PlugAndChargeEvent ${outgoingEvent.id}: ${error instanceof Error ? error.message : "Unknown error"}`,
          "PlugAndCharge",
          "Database",
          LogType.ERROR,
        );
        return false;
      });
      console.log(`PlugAndCharge event sent successfully, uuid=${body.uuid}`);
      return true;
    } else {
      console.log(
        `PlugAndCharge event sent Error, ${eventResponse.status} - ${eventResponse.statusText}`,
      );
      return false;
    }
  }
};

export const updatePlugAndChargeEvent = async (id: string, updateData: any) => {
  try {
    const event = await prisma.plugAndChargeEvent.update({
      where: { id },
      data: updateData,
    });

    Logger(
      `Updated PlugAndChargeEvent ${event.id} for EVSE ${event.evseid}`,
      "PlugAndCharge",
      "Database",
      LogType.INFO,
    );

    return event;
  } catch (error) {
    Logger(
      `Failed to update PlugAndChargeEvent ${id}: ${error instanceof Error ? error.message : "Unknown error"}`,
      "PlugAndCharge",
      "Database",
      LogType.ERROR,
    );
    throw error;
  }
};

/**
 * Store a PlugAndChargeEvent in the database
 */
export const storePlugAndChargeEvent = async (
  eventData: Omit<PlugAndChargeEvent, "createdAt" | "updatedAt" | "id">,
) => {
  try {
    const event = await prisma.plugAndChargeEvent.create({
      data: eventData,
    });

    Logger(
      `Stored PlugAndChargeEvent ${event.id} for EVSE ${eventData.evseid}`,
      "PlugAndCharge",
      "Database",
      LogType.INFO,
    );

    return event;
  } catch (error) {
    Logger(
      `Failed to store PlugAndChargeEvent for EVSE ${eventData.evseid}: ${error}`,
      "PlugAndCharge",
      "Database",
      LogType.ERROR,
    );
    throw error;
  }
};

/**
 * Process incoming PlugAndChargeEvent from external API
 */
export const processIncomingPlugAndChargeEvent = async (eventData: IncomingPlugAndChargeEvent) => {
  try {
    // Store the incoming event first (OU will be synced from matched OUTGOING later)
    const { validatedContract, ...dbData } = eventData;
    const storedEvent = await storePlugAndChargeEvent({
      ...dbData,
      raw: {
        ...eventData,
        plugInEventTimestamp: eventData.plugInEventTimestamp.toISOString(),
        incomingRequestTimestamp: eventData.incomingRequestTimestamp.toISOString(),
      },
      type: PlugAndChargeEventType.INCOMING,
      locationId: null,
      latitude: null,
      longitude: null,
      outgoingRequestTimestamp: null,
      outgoingResponseTimestamp: null,
      ouId: null,
    });

    const matchedOutgoing = await prisma.plugAndChargeEvent.findFirst({
      where: {
        type: PlugAndChargeEventType.OUTGOING,
        uuid: eventData.uuid,
      },
    });

    Logger(
      `Processed incoming PnC for EVSE ${eventData.evseid} with uuid ${eventData.uuid}`,
      "PlugAndCharge",
      "Processing",
      LogType.INFO,
    );
    return storedEvent;
  } catch (error) {
    Logger(
      `Failed to process incoming PlugAndChargeEvent for EVSE ${eventData.evseid}: ${error instanceof Error ? error.message : String(error)}`,
      "PlugAndCharge",
      "Processing",
      LogType.ERROR,
    );
    throw error;
  }
};
