import prisma from "~/server/db/prisma";
import { env } from "~/env";


 function wait(ms  = 500): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}


// Helper: Fetch Konfiguration für ChargePoint + Keys von Longship
export async function getConfigurationForChargePoint(chargePointId: string, keys: string[]): Promise<any[]> {
  const endpoint = `https://api.longship.io/v1/chargepoints/${chargePointId}/getconfiguration`;
  const longshipResponse = await fetch(endpoint, {
    method: 'POST',
    headers: {
      accept: 'application/json',
      'Ocp-Apim-Subscription-Key': env.LONGSHIP_API_OCP_KEY,
      'x-api-key': env.LONGSHIP_API_X_API_KEY,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ key: keys }),
  });

  if (!longshipResponse.ok) {
    throw new Error(`HTTP error! status: ${longshipResponse.status}`);
  }

  const locationURL = longshipResponse.headers.get('Location');
  if (!locationURL) {
    throw new Error(`No location URL found in response headers`);
  }

  let chargepointResponse = await fetch("https://api.longship.io" + locationURL, {
    method: 'GET',
    headers: {
      accept: 'application/json',
      'Ocp-Apim-Subscription-Key': env.LONGSHIP_API_OCP_KEY,
      'x-api-key': env.LONGSHIP_API_X_API_KEY,
      'Content-Type': 'application/json',
    }
  });

  let messageData = await chargepointResponse.json();
  if (Array.isArray(messageData) && messageData.length == 0) {

    console.log("weiter versuche",chargePointId);

    await wait( 2000);

    chargepointResponse = await fetch("https://api.longship.io" + locationURL, {
      method: 'GET',
      headers: {
        accept: 'application/json',
        'Ocp-Apim-Subscription-Key': env.LONGSHIP_API_OCP_KEY,
        'x-api-key': env.LONGSHIP_API_X_API_KEY,
        'Content-Type': 'application/json',
      }
    });
    messageData = await chargepointResponse.json();


  }

  console.log("messageData", messageData,chargePointId);
  return messageData;
}

// Hauptfunktion
export async function checkOcppMonitorKeys() {
  // 1. Hole alle ChargePoints
  const allChargepoints = await prisma.chargePoint.findMany({
    select: { id: true, chargePointVendor: true, chargePointModel: true }
  });

  // 2. Gruppiere nach Vendor|Model
  const groupedByVendorModel: Record<string, typeof allChargepoints> = {};
  for (const cp of allChargepoints) {
    const key = `${cp.chargePointVendor}|${cp.chargePointModel}`;
    if (!groupedByVendorModel[key]) groupedByVendorModel[key] = [];
    groupedByVendorModel[key]!.push(cp);
  }

  // 3. Für jede Gruppe:
  for (const groupKey in groupedByVendorModel) {
    const [chargePointVendor, chargePointModel] = groupKey.split("|");
    let chargePoints = groupedByVendorModel[groupKey];
    if (!Array.isArray(chargePoints) || chargePoints.length === 0) continue;

    // 4. Monitoring-Keys laden
    const monitorKeys = await prisma.occpMonitorKey.findMany({
      where: { chargePointVendor, chargePointModel },
      include: { excludedChargePoints: { select: { chargePointId: true } } }
    });

    // 5. Falls KEIN MonitoringKey: KEIN Request!
    if (!Array.isArray(monitorKeys) || monitorKeys.length === 0) {
      console.log(`Kein Monitoring für ${chargePointVendor} ${chargePointModel}, skippe Gruppe.`);
      continue;
    }

    // 6. Excluded herausfiltern
    const allExcludedIds = new Set(
      monitorKeys.flatMap(mk => mk.excludedChargePoints.map(e => e.chargePointId))
    );
    chargePoints = chargePoints.filter(cp => !allExcludedIds.has(cp.id));
    if (!Array.isArray(chargePoints) || chargePoints.length === 0) {
      console.log(`Alle ChargePoints für ${chargePointVendor} ${chargePointModel} ausgeschlossen. Nichts zu tun.`);
      continue;
    }

    // 7. Alle Keys für diesen Vendor/Model kombination
    const allKeysForGroup = monitorKeys.map(k => k.configKeyName);

    // 8. Für jeden ChargePoint EINEN API-Request
    for (const cp of chargePoints) {
      let responseArray: any[] = [];
      try {
        responseArray = await getConfigurationForChargePoint(cp.id, allKeysForGroup);
      } catch (err) {
        console.error(`Fehler bei getConfiguration für ChargePoint ${cp.id}:`, err);
        continue;
      }

      if (!Array.isArray(responseArray) || responseArray.length === 0) {
        console.log(`Leere oder ungültige Antwort für ChargePoint ${cp.id}:`, responseArray);
        continue;
      }

      // 9. Payload parsen
      const response = responseArray[0];
      if (!response?.payload) {
        console.log(`Kein Payload für ChargePoint ${cp.id}:`, response);
        continue;
      }
      let payloadArray;
      try {
        payloadArray = JSON.parse(response.payload);
      } catch (err) {
        console.error(`Kann Payload nicht parsen für ChargePoint ${cp.id}:`, response.payload);
        continue;
      }

      const configArray = payloadArray[2]?.configurationKey;
      if (!Array.isArray(configArray)) {
        console.log(`Keine configurationKey Daten für ChargePoint ${cp.id}:`, payloadArray);
        continue;
      }

      // 10. Werte prüfen und Events anlegen
      for (const conf of configArray) {
        const monitorKey = monitorKeys.find(key => key.configKeyName === conf.key);
        if (!monitorKey) continue;
        const expectedValues = monitorKey.expectedValues.split(",").map(s => s.trim()).filter(Boolean);
        const errorValues = monitorKey.errorValues.split(",").map(s => s.trim()).filter(Boolean);

        // OK
        if (expectedValues.includes(conf.value)) continue;

        // Error
        if (errorValues.includes(conf.value)) {
          await prisma.monitoringEvent.create({
            data: {
              message: `OCPP Monitor Key Error: ${conf.key} = ${conf.value}`,
              type: "OCPP_MONITOR_KEY_ERROR",
              evseId: cp.id,
              href: env.LONGSHIP_PORTAL_URL + "/chargers/" + cp.id,
              lastevent: BigInt(Math.floor(Date.now() / 1000)),
            }
          });
          continue;
        }

        // Unerwarteter Wert
        if (expectedValues.length > 0) {
          await prisma.monitoringEvent.create({
            data: {
              message: `OCPP Monitor Key Error: unexpected value for ${conf.key}: ${conf.value}`,
              type: "OCPP_MONITOR_KEY_ERROR",
              evseId: cp.id,
              href: env.LONGSHIP_PORTAL_URL + "/chargers/" + cp.id,
              lastevent: BigInt(Math.floor(Date.now() / 1000)),
            }
          });
          continue;
        }
      }
    }
  }
}


export async function changeConfigurationForChargePoint(
  chargePointId: string,
  key: string,
  value: string | number
): Promise<string | undefined> {
  const endpoint = `https://api.longship.io/v1/chargepoints/${chargePointId}/changeconfiguration`;

  const res = await fetch(endpoint, {
    method: "POST",
    headers: {
      accept: "application/json",
      "Ocp-Apim-Subscription-Key": env.LONGSHIP_API_OCP_KEY,
      "x-api-key": env.LONGSHIP_API_X_API_KEY,
      "Content-Type": "application/json",
    },
    body: JSON.stringify({ key, value }),
  });

  if (!res.ok) {
    throw new Error(`HTTP error! status: ${res.status}`);
  }

  const locationURL = res.headers.get("Location");
  if (!locationURL) {
    throw new Error("No location URL found in response headers");
  }

  // 1. Poll
  let pollRes = await fetch("https://api.longship.io" + locationURL, {
    method: "GET",
    headers: {
      accept: "application/json",
      "Ocp-Apim-Subscription-Key": env.LONGSHIP_API_OCP_KEY,
      "x-api-key": env.LONGSHIP_API_X_API_KEY,
      "Content-Type": "application/json",
    },
  });

  let messageData: any = await pollRes.json();

  // Optionaler kurzer Retry, wenn noch leer
  if (Array.isArray(messageData) && messageData.length === 0) {
    await wait(2000);
    pollRes = await fetch("https://api.longship.io" + locationURL, {
      method: "GET",
      headers: {
        accept: "application/json",
        "Ocp-Apim-Subscription-Key": env.LONGSHIP_API_OCP_KEY,
        "x-api-key": env.LONGSHIP_API_X_API_KEY,
        "Content-Type": "application/json",
      },
    });
    messageData = await pollRes.json();
  }

  // Status aus payload ziehen: [3, "messageId", { status: "Accepted" }]
  if (Array.isArray(messageData) && messageData.length > 0) {
    const item = messageData[0];
    if (item?.payload) {
      try {
        const arr = JSON.parse(item.payload);
        return arr?.[2]?.status as string | undefined;
      } catch {
        // ignorieren -> undefined zurück
      }
    }
  }

  return undefined;
}

