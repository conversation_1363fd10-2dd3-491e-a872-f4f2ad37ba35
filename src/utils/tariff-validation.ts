import { Tarif, CreditTarif, Ou } from "@prisma/client";

/**
 * Checks if a tariff is valid for a specific OU based on explicit relation
 * @param tariff - The tariff to check (with validOus relation loaded)
 * @param ou - The OU to check against
 * @returns boolean - true if tariff is valid for the OU
 */
export function isTariffValidForOu(
  tariff: (Tarif | CreditTarif) & { validOus?: Ou[] } | any,
  ou: Ou | any
): boolean {
  // If tariff has no validOus relation loaded, assume it's valid for all OUs
  if (!tariff.validOus || !Array.isArray(tariff.validOus)) {
    return true;
  }

  // If validOus is empty, tariff is valid for all OUs
  if (tariff.validOus.length === 0) {
    return true;
  }

  // Check if OU is in the validOus list
  return tariff.validOus.some((validOu: Ou) => validOu.id === ou.id);
}

/**
 * Filters tariffs that are valid for a specific OU
 * @param tariffs - Array of tariffs to filter (with validOus relation loaded)
 * @param ou - The OU to check against
 * @returns Array of valid tariffs
 */
export function filterTariffsForOu<T extends { validOus?: Ou[] }>(
  tariffs: T[],
  ou: Ou | any
): T[] {
  return tariffs.filter(tariff => isTariffValidForOu(tariff, ou));
}

/**
 * Gets all OUs that a tariff is valid for based on explicit relation
 * @param tariff - The tariff to check (with validOus relation loaded)
 * @returns Array of valid OUs
 */
export function getValidOusForTariff(
  tariff: { validOus?: Ou[] } | any
): Ou[] {
  // If tariff has no validOus relation loaded or is empty, it's valid for all OUs
  if (!tariff.validOus || !Array.isArray(tariff.validOus) || tariff.validOus.length === 0) {
    return []; // Return empty array to indicate "all OUs" - caller should handle this case
  }

  return tariff.validOus;
}

/**
 * Validates if a contact can be assigned to a tariff based on their OU
 * @param contact - Contact with OU information
 * @param tariff - The tariff to check (with validOus relation loaded)
 * @returns boolean - true if assignment is valid
 */
export function canAssignContactToTariff(
  contact: { ou?: Ou | null } | any,
  tariff: { validOus?: Ou[] } | any
): boolean {
  // If contact has no OU, assignment is only valid for unrestricted tariffs
  if (!contact.ou) {
    return !tariff.validOus || tariff.validOus.length === 0;
  }

  return isTariffValidForOu(tariff, contact.ou);
}

/**
 * Gets validation message for tariff-OU compatibility
 * @param tariff - The tariff to check (with validOus relation loaded)
 * @param ou - The OU to check against
 * @returns string - Validation message
 */
export function getTariffValidationMessage(
  tariff: { validOus?: Ou[]; name?: string } | any,
  ou: { name?: string; code?: string } | any
): string {
  if (!tariff.validOus || tariff.validOus.length === 0) {
    return `Tariff "${tariff.name}" is valid for all OUs`;
  }

  const isValid = isTariffValidForOu(tariff, ou);

  if (isValid) {
    return `Tariff "${tariff.name}" is valid for OU "${ou.name}" (${ou.code})`;
  }

  const validOuNames = tariff.validOus.map((validOu: Ou) => `${validOu.name} (${validOu.code})`).join(', ');
  return `Tariff "${tariff.name}" is only valid for: ${validOuNames}`;
}
