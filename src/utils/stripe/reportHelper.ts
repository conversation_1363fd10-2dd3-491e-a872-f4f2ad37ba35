import fs from "fs";
import https from "https";
import type { IncomingMessage } from "http";
import { env } from "~/env";
import { type Invoice } from "../../../prismaMongoAdhoc/client";
import csv from "csv-parser";
import { createObjectCsvWriter } from "csv-writer";
import type { Stripe } from "stripe";
import { DateTime as LuxonDateTime } from "luxon";
import { LogType, Prisma, type StripePayoutItem } from "@prisma/client";
import prisma from "../../server/db/prisma";
import { PayoutStatus } from "@prisma/client";
import { getSha1 } from "~/utils/files/getSha1";

import Logger from "~/server/logger/logger";
import prismaMongo from "~/server/db/mongo";
export const downloadReportFile = async (
  options: any,
  targetFilePath: string,
  payoutId: string,
): Promise<void> => {
  return new Promise((resolve, reject) => {
    if (!fs.existsSync(env.STRIPE_PAYOUT_REPORTS)) {
      fs.mkdirSync(env.STRIPE_PAYOUT_REPORTS);
    }
    const file = fs.createWriteStream(targetFilePath);
    Logger("Try to download report", "Report Download", "stripe", LogType.DEBUG);

    const req = https.request(options, (response: IncomingMessage) => {
      response.pipe(file);
      file.on("finish", () => {
        file.close();
        prisma.stripePayout
          .update({
            where: { id: payoutId },
            data: { status: PayoutStatus.STRIPE_FILE_DOWNLOADED },
          })
          .then(() => {
            Logger("File downloaded", "Report Download success", "stripe", LogType.DEBUG);

            resolve(); // Erfolgreiches Beenden des Requests
          })
          .catch((error) => {
            Logger("Report Download failed", "Report Download", "stripe", LogType.ERROR);

            reject(error);
          });

        resolve(); // Erfolgreiches Beenden des Requests
      });
    });

    req.on("error", (error) => {
      Logger(
        `Problem with request: ${error.message}`,
        "Download Stripe Report",
        "stripe",
        LogType.ERROR,
      );
      reject(error); // Fehler beim Request
    });

    req.end();
  });
};

export interface PayoutCSVItem {
  automatic_payout_id: string;
  payment_intent_id: string;
  gross: string;
  net: string;
  fee: string;
  customer_facing_amount: string;
  "payment_metadata[evse]": string;
  reporting_category: string;
  "payment_metadata[cdr_id]": string;
}

export const createPayoutExportFile = (
  report: Stripe.Reporting.ReportRun | null | undefined,
  raw_report_file_path = "",
  create_tax_file_from_raw = true,
  createPayoutItems = true,
) => {
  if (raw_report_file_path) {
    Logger(
      `Try to read raw file ${raw_report_file_path}`,
      "Raw Report File",
      "stripe",
      LogType.INFO,
    );
    const content = fs.readFileSync(raw_report_file_path, "utf8");
    report = JSON.parse(content);
    //report = reportObject["object"] as Stripe.Reporting.ReportRun;
  }
  if (!report) {
    Logger(
      "No report object available neither per parameter nor per raw object file",
      "No StripeReport provides",
      "stripe",
      LogType.ERROR,
    );
    return false;
  }

  if (fs.existsSync(`${env.STRIPE_PAYOUT_REPORTS}/${report?.result?.filename}`)) {
    const csv_content: PayoutCSVItem[] = [];

    fs.createReadStream(`${env.STRIPE_PAYOUT_REPORTS}/${report?.result?.filename}`)
      .pipe(csv())
      .on("data", (data) => csv_content.push(data))
      .on("end", async () => {
        const paymentIntents = csv_content.map((entry) => entry.payment_intent_id);

        const uniquePaymentIntentIds = [...new Set(paymentIntents)];

        const existingPayoutItems = await prisma.stripePayoutItem.findMany({
          where: {
            paymentIntentId: { in: uniquePaymentIntentIds },
          },
        });

        if (existingPayoutItems.length > 0) {
          //
          Logger(
            `Payment Intents from payout already exists ${existingPayoutItems} (ignored currently)`,
            "Generate Payout Items",
            "stripe",
            LogType.WARN,
          );
        }

        const result = await prisma.stripePayoutToQontoTransaction.findFirst({
          where: {
            stripePayoutId: report?.parameters?.payout,
          },
          select: {
            qontoTransaction: true,
          },
        });

        const payoutQontoRef = result?.qontoTransaction?.reference?.replaceAll(" ", "-");

        if (!payoutQontoRef) {
          Logger(
            `No matching qonto transaction found for payout with id: ${report?.parameters.payout}`,
            "Prisma",
            "stripe",
            LogType.ERROR,
          );
          return false;
        }

        const adhocInvoices = await prismaMongo.invoice.findRaw({
          filter: {
            "metadata.paymentIntent": { $in: uniquePaymentIntentIds },
          },
        });

        if (!raw_report_file_path || createPayoutItems) {
          const payoutItemsForDb: StripePayoutItem[] = csv_content
            .filter((item) => item["payment_metadata[cdr_id]"]) // filter alle stripe items raus ohne CDR
            .map((csv_contentItem) => {
              let invoice;
              if (Array.isArray(adhocInvoices)) {
                invoice = adhocInvoices.find(
                  (invoice: Invoice) =>
                    invoice.metadata.paymentIntent === csv_contentItem.payment_intent_id,
                );
              }

              return {
                automaticPayoutId: csv_contentItem.automatic_payout_id,
                paymentIntentId: csv_contentItem.payment_intent_id,
                gross: parseFloat(csv_contentItem.gross),
                net: parseFloat(csv_contentItem.net),
                fee: parseFloat(csv_contentItem.fee),
                customerFacingAmount: parseFloat(csv_contentItem.customer_facing_amount),
                paymentMetadataEvse: csv_contentItem["payment_metadata[evse]"],
                reportingCategory: csv_contentItem.reporting_category,
                paymentMetadataCdrId: csv_contentItem["payment_metadata[cdr_id]"],
                adhocInvoiceNumber: invoice?.invoice_number,
              };
            });
          try {
            await prisma.stripePayoutItem.createMany({
              data: payoutItemsForDb,
              skipDuplicates: true,
            });
          } catch (e) {
            if (e instanceof Prisma.PrismaClientKnownRequestError) {
              Logger(
                `Prisma DB CreateMany PayoutItems ${e.message}`,
                "Prisma",
                "stripe",
                LogType.ERROR,
              );
            }
            return false;
          }
        }

        const userInvoicePaymentItems = await prisma.paymentIntent.findMany({
          where: { id: { in: uniquePaymentIntentIds } },
        });
        const userInvoiceIds = userInvoicePaymentItems
          .map((intent) => intent.invoiceId)
          .filter((invoiceId) => invoiceId !== null);
        const userInvoices = await prisma.invoice.findMany({
          where: { id: { in: userInvoiceIds } },
        });

        Logger(
          `Found ${userInvoices.length} user invoices `,
          "Payout Report User Invoices",
          "stripe",
          LogType.DEBUG,
        );
        const missingInvoices: any = [];
        // findRaw returns "PrismaObject" which depends on the filter. In the upper case it is an Array<object>
        if (adhocInvoices && Array.isArray(adhocInvoices)) {
          if (uniquePaymentIntentIds.length != adhocInvoices.length + userInvoices.length) {
            Logger(
              `Number of PaymentIntents does not match number of invoices ${uniquePaymentIntentIds.length} != ${adhocInvoices.length} + ${userInvoices.length} `,
              "PaymentIntent / Invoices Mismatch",
              "stripe",
              LogType.ERROR,
            );
            try {
              await prisma.stripePayout.update({
                where: { id: report?.parameters?.payout },
                data: { status: PayoutStatus.ERROR_INVOICES_DO_NOT_MATCH },
              });
            } catch (e) {
              Logger(
                `Prisma Error stripePayout.update for id=${report?.parameters?.payout}. Report = ${report}`,
                "PaymentIntent / Invoices Mismatch",
                "stripe",
                LogType.ERROR,
              );
            }

            Logger(
              "Now checking missing invoices",
              "PaymentIntent / Invoices Mismatch",
              "stripe",
              LogType.INFO,
            );
            for (const intentId of uniquePaymentIntentIds) {
              const adhocInvoice = adhocInvoices.find(
                (adHocInvoice) => adHocInvoice["metadata"]["paymentIntent"] == intentId,
              );
              const userInvoice = userInvoicePaymentItems.find(
                (paymentIntent) => paymentIntent.id == intentId,
              );
              if (!userInvoice && !adhocInvoice) {
                Logger(
                  `Missing invoice for paymentIntent ${intentId}`,
                  "PaymentIntent / Invoices Mismatch",
                  "stripe",
                  LogType.DEBUG,
                );
                missingInvoices.push({
                  createDate: `Missing invoice for paymentIntent ${intentId}`,
                  deliveryDate: "",
                  art: "",
                  belegnummer1: "",
                  belegnummer2: "",
                  kundennummer: "",
                  debnummer: "",
                  whrg: "",
                  gesamt: "",
                  gegenkonto: "",
                  debitorenkonto: "",
                });
              }
            }
          }
          let summe = 0;
          let csv_steuerberater = adhocInvoices.map((adhocInvoice) => {
            const invoiceDate = adhocInvoice.invoice_date["$date"].split("T")[0] ?? "";
            if (invoiceDate === "") {
              Logger(
                `Empty date from adhoc invoice ${adhocInvoice.invoice_number}`,
                "Tax file generator error",
                "stripe",
                LogType.ERROR,
              );
            }
            summe += adhocInvoice.sum_gross;
            return {
              createDate: invoiceDate,
              deliveryDate: invoiceDate,
              art: "RG",
              belegnummer1: adhocInvoice.invoice_number.replaceAll("_", "-"),
              belegnummer2: payoutQontoRef,
              kundennummer: "",
              debnummer: "",
              whrg: "EUR",
              gesamt: adhocInvoice.sum_gross,
              gegenkonto: "8400",
              debitorenkonto: "22001",
            };
          });
          // mitarbeiter/club rechnungen
          if (userInvoices.length > 0) {
            const csvUserInvoices = userInvoices.map((userInvoice) => {
              summe += userInvoice.sumGross;
              let invoiceDate = "";
              if (userInvoice.invoiceDate) {
                invoiceDate = LuxonDateTime.fromJSDate(userInvoice.invoiceDate).toFormat(
                  "yyyy-MM-dd",
                );
              }
              return {
                createDate: invoiceDate,
                deliveryDate: invoiceDate,
                art: "RG",
                belegnummer1: userInvoice?.invoiceNumber?.replaceAll("_", "-"),
                belegnummer2: payoutQontoRef,
                kundennummer: "",
                debnummer: "",
                whrg: "EUR",
                gesamt: userInvoice.sumGross,
                gegenkonto: "8400",
                debitorenkonto: "22001",
              };
            });
            csv_steuerberater = csv_steuerberater.concat(csvUserInvoices);
          }
          if (missingInvoices.length > 0) {
            csv_steuerberater = csv_steuerberater.concat(missingInvoices);
          }
          const csv_head = [
            { id: "createDate", title: "Datum Rechnung" },
            { id: "deliveryDate", title: "Liefer-/Leistungsdatum" },
            { id: "art", title: "Art" },
            { id: "belegnummer1", title: "Belegnr.1" },
            { id: "belegnummer2", title: "Belegnr.2" },
            { id: "kundennummer", title: "Kd.-Nr." },
            { id: "debnummer", title: "Deb.-Nr." },
            { id: "whrg", title: "Whrg" },
            { id: "gesamt", title: "Gesamt" },
            { id: "gegenkonto", title: "Gegenkonto" },
            { id: "debitorenkonto", title: "Debitorenkonto" },
          ];

          const filename = `eulektro_export_${report?.parameters.payout}_${summe.toFixed(
            2,
          )}_EUR.csv`;
          const path = `${env.STRIPE_PAYOUT_REPORTS}/${filename}`;

          const csvWriter = createObjectCsvWriter({
            path: path,
            header: csv_head,
          });

          try {
            await csvWriter.writeRecords(csv_steuerberater);

            if (
              fs.existsSync(path) &&
              report &&
              report.result &&
              report.result.id &&
              report.result.filename &&
              (!raw_report_file_path || create_tax_file_from_raw)
            ) {
              console.log(`Trying to create new file ref for ${filename} `);
              const newFileRef = await prisma.fileRef.create({
                data: {
                  stripePayoutId: report.parameters.payout,
                  name: filename,
                  path: `${env.STRIPE_PAYOUT_REPORTS}/${filename}`,
                  contentType: "text/csv",
                  sha1: getSha1(`${env.STRIPE_PAYOUT_REPORTS}/${filename}`),
                },
              });
              if (newFileRef && report?.parameters?.payout) {
                await prisma.stripePayout.update({
                  where: { id: report.parameters.payout },
                  data: { status: PayoutStatus.TAX_FILE_GENERATED },
                });
              } else {
                Logger(
                  `Could not create fileRef for TAX File ${filename}`,
                  "Prisma",
                  "stripe",
                  LogType.ERROR,
                );
                return false;
              }
            } else {
              Logger(
                `Could not create fileRef for TAX File ${filename} because file not available`,
                "Prisma",
                "stripe",
                LogType.ERROR,
              );
            }
          } catch (e) {
            if (e instanceof Prisma.PrismaClientKnownRequestError) {
              Logger(
                `Error updating payout db entries ${e.stack}`,
                "Prisma",
                "stripe",
                LogType.ERROR,
              );
              return false;
            }
            Logger(`Error updating payout db entries`, "Prisma", "stripe", LogType.ERROR);
            return false;
          }
        }
      });
    return true;
  } else {
    Logger(
      `Stripe report csv not found ${env.STRIPE_PAYOUT_REPORTS}/${report?.result?.filename}`,
      "Error reading stripe frr_ file",
      "stripe",
      LogType.ERROR,
    );
    return false;
  }
};
