
export const normalizePublicPath = (src: string) => {
  if (!src) return src;
  if (src.startsWith('http')) return src;             // echte URL unverändert
  let s = src.replace(/^\.?\/*/, '');                 // führende ./ oder / entfernen
  s = s.replace(/^public\//, '');                     // "public/" abschneiden
  return '/' + s;                                     // führenden Slash setzen
};