import prisma from "~/server/db/prisma";
import { createSystemNotificationForAdmins } from "~/utils/notifications/createSystemNotification";
import { NotificationType } from "@prisma/client";
export async function createThgPseudoOu() {
  try {
    // Pr<PERSON><PERSON>, ob die THG Pseudo-OU bereits existiert
    const existingOu = await prisma.ou.findFirst({
      where: {
        code: "THG_PSEUDO",
        ouType: "THG_BUYER",
      },
    });

    if (existingOu) {
      console.log("THG Pseudo-OU bereits vorhanden:", existingOu.id);
      return existingOu;
    }

    // Neue THG Pseudo-OU erstellen
    const thgPseudoOu = await prisma.ou.create({
      data: {
        name: "THG Buyer Pseudo OU",
        code: "THG_PSEUDO",
        ouType: "THG_BUYER",
        externalReference: "THG_BUYER_PSEUDO",
        customerReference: "THG_BUYER",
        address: "Virtual Address",
        state: "Virtual",
        country: "DE",
        city: "Virtual City",
        postalCode: "00000",
        companyEmail: "<EMAIL>",
        primaryContactperson: "THG System",
        primaryContactpersonEmail: "<EMAIL>",
        allowOrderEMPCards: false,
        hide: true, // Verstecken in normalen OU-Listen
        operatorId: "THG",
      },
    });
    createSystemNotificationForAdmins({
      type: NotificationType.INFO,
      nachricht: "THG Pseudo-OU erfolgreich erstellt",
    });

    console.log("THG Pseudo-OU erfolgreich erstellt:", thgPseudoOu.id);
    return thgPseudoOu;
  } catch (error) {
    console.error("Fehler beim Erstellen der THG Pseudo-OU:", error);
    throw error;
  }
}
