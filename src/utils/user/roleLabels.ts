import { Role } from "@prisma/client";

export const getRoleLabel = (role: Role) => {
  switch (role) {
    case Role.ADMIN:
      return "Admin";
    case Role.USER:
      return "Standardbenutzer";
    case Role.CARD_HOLDER:
      return "<PERSON>utzer mit Ladekarte";
    case Role.CARD_MANAGER:
      return "Manager";
    case Role.CPO:
      return "Betreiber";
    case Role.THG_BUYER:
      return "THG-Quotenkäufer";
    case Role.PNC_REMOTE:
      return "PnC Remote";
    default:
      return "Benutzer";
  }
};

/**
 * Checks if a role is included in a group of roles
 * @param role - The role to check
 * @param roleGroup - Array of roles to check against
 * @returns true if the role is in the group, false otherwise
 */
export const hasRole = (role: Role | undefined, roleGroup: Role[]): boolean => {
  if (role == undefined) {
    return false;
  }
  return roleGroup.includes(role);
};
