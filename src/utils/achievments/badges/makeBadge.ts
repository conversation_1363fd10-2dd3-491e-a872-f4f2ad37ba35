export function makeBadge(opts: { color: string; label: string }): string {
  const { color, label } = opts;
  const svg = `
    <svg xmlns='http://www.w3.org/2000/svg' width='224' height='224' viewBox='0 0 224 224'>
      <defs>
        <radialGradient id='g' cx='50%' cy='35%'>
          <stop offset='0%' stop-color='white' stop-opacity='0.25'/>
          <stop offset='100%' stop-color='black' stop-opacity='0.15'/>
        </radialGradient>
      </defs>
      <circle cx='112' cy='112' r='108' fill='${color}' />
      <circle cx='112' cy='112' r='108' fill='url(#g)' />
      <text x='50%' y='54%' dominant-baseline='middle' text-anchor='middle'
            font-family='Inter, system-ui, -apple-system, Segoe UI, Roboto, Arial'
            font-size='72' fill='white' opacity='0.95'>${label}</text>
    </svg>`;
  return `data:image/svg+xml;utf8,${encodeURIComponent(svg)}`;
}
