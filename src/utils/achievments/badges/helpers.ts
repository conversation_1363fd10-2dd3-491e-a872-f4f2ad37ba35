import { Tier } from "~/types/achievment/bagdesTypes";

export function at<T>(arr: readonly T[], i: number): T {
  const v = arr[i];
  if (v === undefined) throw new Error(`Index ${i} out of bounds (len=${arr.length})`);
  return v;
}

export const clamp01 = (n: number) => Math.max(0, Math.min(1, n));
export const pct = (n: number) => Math.round(n * 100);
export const clampIndex = (len: number, i: number) => Math.max(0, Math.min(i, len - 1));

export function computeTierState(count: number, tiers: readonly Tier[]) {
  const len = tiers.length;

  // nächste noch nicht erreichte Stufe
  const nextRaw = tiers.findIndex(t => count < t.threshold); // -1 => alle erreicht
  const nextTierIndex = nextRaw === -1 ? len - 1 : nextRaw;

  // aktuelle (erreichte) Stufe = vorherige (oder 0)
  const currentTierIndex = nextRaw <= 0 ? 0 : clampIndex(len, nextRaw - 1);

  const currentTier = at(tiers, currentTierIndex);
  const nextTier: Tier | null = nextRaw === -1 ? null : at(tiers, nextTierIndex);

  // Ring im Grid: Fortschritt zur nächsten Stufe (oder voll)
  const ringProgress = nextTier ? clamp01(count / nextTier.threshold) : 1;

  return { currentTierIndex, nextTierIndex, currentTier, nextTier, ringProgress, len };
}
