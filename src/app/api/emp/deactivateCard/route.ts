import type { NextRequest } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import prisma from "~/server/db/prisma";
import { pushEmpCard, setEMPCardInvalid } from "~/utils/longship";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";

export async function PATCH(request: NextRequest) {
  const { cardId } = await request.json();
  const session = await getServerSession(authOptions);

  if (!session) {
    return new Response("no auth", { status: 401 });
  }

  const empCard = await prisma?.eMPCard.findUnique({
    where: { id: cardId },
    include: { tarifs: { include: { tarif: { include: { ou: true } } } }, physicalCard: true },
  });

  if (!empCard || !empCard.physicalCard) {
    return new Response("Karte konnte nicht deaktiviert werden.", {
      status: 404,
    });
  }

  const physicalCardUid = empCard.physicalCard.uid;

  const updateEMPCard = prisma?.eMPCard.update({
    where: { id: cardId },
    data: {
      active: false,
      deactivatedAt: new Date(), // Setzen Sie deactivatedAt auf das aktuelle Datum
    },
  });

  const updatePhysicalCard = prisma?.physicalCard.update({
    where: { uid: physicalCardUid },
    data: {
      valid: false, // Setzen Sie valid auf false
    },
  });

  const [updatedEMPCard, updatedPhysicalCard] = await prisma.$transaction([
    updateEMPCard,
    updatePhysicalCard,
  ]);

  if (!updatedEMPCard || !updatedPhysicalCard) {
    return new Response("Karte konnte nicht deaktiviert werden (update error)", { status: 404 });
  }

  if (updatedEMPCard) {
    const errors = await setEMPCardInvalid(empCard);
    if (errors.length) {
      return new Response(errors.join(", "), { status: 500 });
    }
    return new Response("Karte wurde aktiviert", { status: 200 });
  }
  return new Response("Karte wurde deaktiviert", { status: 200 });
}
