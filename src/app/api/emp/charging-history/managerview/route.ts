import type { NextRequest } from "next/server";
import prisma from "~/server/db/prisma";
import { NextResponse } from "next/server";
import { Prisma, Role } from "@prisma/client";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { getOusBelowOu } from "~/server/model/ou/func";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";

export const dynamic = "force-dynamic";

const getData = async (startDate?: string, endDate?: string, selectedOu?: string) => {
  if (!selectedOu) {
    return [];
  }

  const whereClause: Prisma.CdrWhereInput = {};

  // Get all OUs below the selected OU (including the selected OU itself)
  const ouWithChildren = await getOusBelowOu(selectedOu);
  const ouCodes = ouWithChildren?.map((ou) => `'${ou.code}'`);
  
  whereClause.OU_Code = { in: ouCodes };

  if (startDate && endDate) {
    const endDateObj = new Date(endDate);
    endDateObj.setHours(23, 59, 59, 999);
    whereClause.End_datetime = {
      gte: new Date(startDate),
      lte: endDateObj,
    };
  } else {
    // Default to avoid old CDRs from 'recycled' OUs if no date range is provided
    whereClause.End_datetime = { gt: new Date("01.01.2024") };
  }

  return prisma.cdr.findMany({
    where: whereClause,
    include: { 
      companyTarif: true, 
      tarif: true, 
      cost: true, 
      creditPayout: true 
    },
    orderBy: {
      End_datetime: "desc",
    },
  });
};

export async function GET(request: NextRequest) {
  const session = await getServerSession(authOptions);

  // Check if user has permission to access this endpoint
  if (
    !session ||
    (session?.user?.role !== Role.CARD_MANAGER &&
      session?.user?.role !== Role.ADMIN &&
      session?.user?.role !== Role.CPO)
  ) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  const url = new URL(request.url);
  const startDate = url.searchParams.get("startDate");
  const endDate = url.searchParams.get("endDate");
  const selectedOu = session?.user?.selectedOu;

  if (!selectedOu) {
    return NextResponse.json({ error: "No selected OU" }, { status: 400 });
  }

  if (!startDate || !endDate) {
    return NextResponse.json({
      error: "Bad Request, missing startDate or endDate",
      timestamp: new Date().toISOString(),
    }, { status: 400 });
  }

  try {
    const data = await getData(startDate, endDate, selectedOu);
    return NextResponse.json(data);
  } catch (error) {
    {
      const msg = "Error fetching charging history:";
      const errorMsg = (error as Error)?.message || String(error);
      Logger(`${msg}: ${errorMsg}`, "API 500", "src/app/api/emp/charging-history/managerview/route.ts", LogType.ERROR);
    }
    Logger("API 500 Error", "API 500", "src/app/api/emp/charging-history/managerview/route.ts", LogType.ERROR);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
