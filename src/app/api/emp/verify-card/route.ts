import type { NextRequest } from "next/server";

import prisma from "~/server/db/prisma";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { activateCard } from "~/utils/user/empcard";
import { sendAcknowledgeMail } from "~/utils/email";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";

export async function GET(request: NextRequest) {
  const cardnumber = request.nextUrl.searchParams.get("cardnumber");
  const cposlug = request.nextUrl.searchParams.get("cpo");
  const create = request.nextUrl.searchParams.get("create");
  let session;
  if (!cardnumber) {
    return new Response("Unvollständige parameter", { status: 400 });
  }
  let ou;
  if (!cposlug) {
    session = await getServerSession(authOptions);
    if (!session) {
      return new Response("Unvollständige parameter", { status: 400 });
    } else {
      ou = await prisma.ou.findUnique({
        where: { id: session?.user?.ou?.id },
        include: { Contact: true },
      });
    }
  } else {
    ou = await prisma.ou.findUnique({
      where: { registrationSlug: cposlug },
      include: { Contact: true },
    });
  }

  if (!ou) {
    return new Response("Karte nicht gefunden (CPO correct?)", { status: 404 });
  }
  let physicalCard;
  try {
    // Get PhysicalCard uid by its number and contactid
    physicalCard = await prisma?.physicalCard.findUnique({
      where: {
        visualNumber: cardnumber,
        cpoId: ou?.Contact?.id,
      },
      include: { EMPCard: true },
    });
  } catch (e) {
    Logger("API 500 Error", "API 500", "src/app/api/emp/verify-card/route.ts", LogType.ERROR);
    return new Response("Fehler beim Prüfen der Karte", { status: 500 });
  }

  if (physicalCard?.EMPCard?.id) {
    return new Response("Karte bereits verwendet", { status: 409 });
  }
  if (create && session && session?.user && ou?.id && physicalCard) {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    let defaultTarifs = [];

    // Check if user has a userGroup and get tariffs from there
    const userWithGroup = await prisma.user.findUnique({
      where: { id: session.user.id },
      include: {
        userGroup: {
          include: {
            companyTarifs: {
              include: {
                tarif: true,
              },
            },
          },
        },
      },
    });

    if (userWithGroup?.userGroup) {
      // Get tariffs from user's userGroup
      defaultTarifs = userWithGroup.userGroup.companyTarifs
        .map(ct => ct.tarif)
        .filter(tarif => {
          const validFrom = new Date(tarif.validFrom);
          const validTo = new Date(tarif.validTo);
          return (
            validFrom <= today &&
            validTo >= today &&
            !tarif.internal &&
            !tarif.optional
          );
        });
    } else {
      // Fallback to OU default tariffs if user has no userGroup
      try {
        defaultTarifs = await prisma.companyTarif.findMany({
          where: {
            ouId: ou?.id,
            optional: false,
            internal: false,
            validFrom: {
              lte: today, // validFrom muss kleiner oder gleich heute sein
            },
            validTo: {
              gte: today, // validTo muss größer oder gleich heute sein
            },
          },
        });
      } catch (e) {
        Logger("API 500 Error", "API 500", "src/app/api/emp/verify-card/route.ts", LogType.ERROR);
    return new Response("Fehler beim Ermitteln der Tarife", { status: 500 });
      }
    }

    if (defaultTarifs.length == 0) {
      return new Response("No default tarifs found", { status: 404 });
    }
    let card;
    try {
      card = await prisma?.eMPCard.create({
        data: {
          userId: session?.user?.id,
          active: true,
          physicalCardId: physicalCard.uid,
          note: "Created with pre delivered card",
          preDelivered: true,
          tarifs: {
            // connect current default tarifs (optional = false)
            create: defaultTarifs.map((tarif) => {
              return { tarif: { connect: { id: tarif.id } } };
            }),
          },
        },
        include: {
          physicalCard: true, // hier wird das physicalCard-Objekt mitgeladen
          contact: true,
        },
      });
    } catch (e) {
      Logger("API 500 Error", "API 500", "src/app/api/emp/verify-card/route.ts", LogType.ERROR);
    return new Response("Fehler(DB) beim Anlegen der Karte", { status: 500 });
    }

    const errors = await activateCard(card?.id, cardnumber);

    if (errors.length) {
      return new Response(errors.join(", "), { status: 500 });
    }

    const status = await sendAcknowledgeMail(session.user, card);

    return new Response("Karte wurde aktiviert", { status: 200 });
  }

  if (physicalCard && !physicalCard.EMPCard?.id) {
    return new Response("Karte gefunden", { status: 200 });
  }

  return new Response("Karte nicht gefunden", { status: 404 });
}
