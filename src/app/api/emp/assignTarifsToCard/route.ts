import { getServerSession } from "next-auth";

import prisma from "~/server/db/prisma";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";

export async function POST(request: Request) {
  const session = await getServerSession(authOptions);
  if (!session?.user) {
    return new Response("Unauthorized", { status: 401 });
  }

  const { selectedTarifIds } = await request.json();

  if (!selectedTarifIds || !Array.isArray(selectedTarifIds)) {
    return new Response("Invalid tarif selection", { status: 400 });
  }

  try {
    // Finde die inaktive EMPCard des Benutzers
    const empCard = await prisma.eMPCard.findFirst({
      where: {
        userId: session.user.id,
        active: false,
        preDelivered: true,
        activatedAt: null,
      },
    });

    if (!empCard) {
      return new Response("No inactive card found", { status: 404 });
    }

    // Verknüpfe die ausgewählten Tarife mit der EMPCard
    await prisma.eMPCard.update({
      where: { id: empCard.id },
      data: {
        tarifs: {
          create: selectedTarifIds.map((tid: string) => {
            return { tarif: { connect: { id: tid } } };
          }),
        },
      },
    });

    return new Response("Tarifs assigned successfully", { status: 200 });
  } catch (error) {
    {
      const msg = "Error assigning tarifs:";
      const errorMsg = (error as Error)?.message || String(error);
      Logger(`${msg}: ${errorMsg}`, "API 500", "src/app/api/emp/assignTarifsToCard/route.ts", LogType.ERROR);
    }
    Logger("API 500 Error", "API 500", "src/app/api/emp/assignTarifsToCard/route.ts", LogType.ERROR);
    return new Response("Error assigning tarifs", { status: 500 });
  }
}
