import type { NextRequest } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import prisma from "~/server/db/prisma";
import nodemailer from "nodemailer";
import { env } from "~/env.js";
import { createSystemNotificationForAdmins } from "~/utils/notifications/createSystemNotification";
import { NotificationType } from "@prisma/client";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";

export async function POST(request: NextRequest) {
  const { selectedTarifIds } = await request.json();
  const session = await getServerSession(authOptions);

  if (!session) {
    return new Response("no auth", { status: 401 });
  }

  let contact;
  const ouId = session?.user.selectedOu.id;
  try {
    contact = await prisma?.contact.findFirstOrThrow({ where: { ouId: ouId } });
  } catch (e) {
    Logger("API 500 Error", "API 500", "src/app/api/emp/orderCard/internal/route.ts", LogType.ERROR);
    return new Response("No contact found", { status: 500 });
  }
  if (contact) {
    try {
      const newCard = await prisma?.eMPCard.create({
        data: {
          contactId: contact.id,
          tarifs: {
            create: selectedTarifIds.map((tid: string) => {
              return { tarif: { connect: { id: tid } } };
            }),
          },
        },
        include: { contact: true },
      });

      // Create admin notification
      await createSystemNotificationForAdmins({
        nachricht: `Interne Ladekarte bestellt für ${contact.companyName || contact.name}`,
        type: NotificationType.INFO,
      });
    } catch (e) {
      Logger("API 500 Error", "API 500", "src/app/api/emp/orderCard/internal/route.ts", LogType.ERROR);
    return new Response("cannot create EMPCard auth", { status: 500 });
    }
    return new Response("ok", { status: 200 });
  }
  Logger("API 500 Error", "API 500", "src/app/api/emp/orderCard/internal/route.ts", LogType.ERROR);
    return new Response("Unknown Error", { status: 500 });
}
