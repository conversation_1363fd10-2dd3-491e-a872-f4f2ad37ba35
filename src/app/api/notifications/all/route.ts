import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import prisma from "~/server/db/prisma";
import { NotificationType } from "@prisma/client";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";


export interface SystemNotificationResponse {
  id: string;
  datum: Date;
  type: NotificationType;
  nachricht: string;
  gelesen: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export async function GET(request: NextRequest) {
  const session = await getServerSession(authOptions);

  if (!session?.user?.email) {
    return NextResponse.json(
      { error: "Unauthorized" },
      { status: 401 }
    );
  }

  try {
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get("limit") || "20");
    const page = parseInt(searchParams.get("page") || "1");
    const unreadOnly = searchParams.get("unreadOnly") === "true";
    const type = searchParams.get("type");

    // Calculate offset for pagination
    const offset = (page - 1) * limit;

    // Build where clause
    const whereClause: any = {
      userId: session.user.id,
    };

    if (unreadOnly) {
      whereClause.gelesen = false;
    }

    if (type && type !== "all" && Object.values(NotificationType).includes(type as NotificationType)) {
      whereClause.type = type as NotificationType;
    }

    // Get notifications with pagination
    const notifications = await prisma.systemNotification.findMany({
      where: whereClause,
      orderBy: {
        createdAt: "desc",
      },
      skip: offset,
      take: limit + 1, // Take one extra to check if there are more
    });

    // Check if there are more notifications
    const hasMore = notifications.length > limit;
    const notificationsToReturn = hasMore ? notifications.slice(0, limit) : notifications;

    // Get total unread count (always needed for the badge)
    const unreadCount = await prisma.systemNotification.count({
      where: {
        userId: session.user.id,
        gelesen: false,
      },
    });

    // Get total count for the current filter
    const totalCount = await prisma.systemNotification.count({
      where: whereClause,
    });

    return NextResponse.json({
      notifications: notificationsToReturn,
      unreadCount,
      totalCount,
      hasMore,
      page,
      limit,
    });
  } catch (error) {
    {
      const msg = "Internal server error - fetching all notifications";
      const errorMsg = (error as Error)?.message || String(error);
      Logger(`${msg}: ${errorMsg}`, 'API 500', 'src/app/api/notifications/all/route.ts', LogType.ERROR);
    }
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
