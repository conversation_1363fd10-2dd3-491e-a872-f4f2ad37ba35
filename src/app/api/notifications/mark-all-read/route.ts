import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import prisma from "~/server/db/prisma";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";


export async function PATCH(request: NextRequest) {
  const session = await getServerSession(authOptions);

  if (!session?.user?.email) {
    return NextResponse.json(
      { error: "Unauthorized" },
      { status: 401 }
    );
  }

  try {
    // Mark all unread notifications as read for the current user
    const result = await prisma.systemNotification.updateMany({
      where: {
        userId: session.user.id,
        gelesen: false,
      },
      data: {
        gelesen: true,
        updatedAt: new Date(),
      },
    });

    return NextResponse.json({
      message: `${result.count} notifications marked as read`,
      count: result.count,
    });
  } catch (error) {
    {
      const msg = "Internal server error - marking all notifications as read";
      const errorMsg = (error as Error)?.message || String(error);
      Logger(`${msg}: ${errorMsg}`, 'API 500', 'src/app/api/notifications/mark-all-read/route.ts', LogType.ERROR);
    }
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
