import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import prisma from "~/server/db/prisma";
import { NotificationType } from "@prisma/client";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";


export interface SystemNotificationResponse {
  id: string;
  datum: Date;
  type: NotificationType;
  nachricht: string;
  gelesen: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export async function GET(request: NextRequest) {
  const session = await getServerSession(authOptions);

  if (!session?.user?.email) {
    return NextResponse.json(
      { error: "Unauthorized" },
      { status: 401 }
    );
  }

  try {
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get("limit") || "10");
    const unreadOnly = searchParams.get("unreadOnly") === "true";

    const whereClause = {
      userId: session.user.id,
      ...(unreadOnly && { gelesen: false }),
    };

    const notifications = await prisma.systemNotification.findMany({
      where: whereClause,
      orderBy: {
        createdAt: "desc",
      },
      take: limit,
    });

    const unreadCount = await prisma.systemNotification.count({
      where: {
        userId: session.user.id,
        gelesen: false,
      },
    });

    return NextResponse.json({
      notifications,
      unreadCount,
    });
  } catch (error) {
    {
      const msg = "Internal server error - fetching notifications";
      const errorMsg = (error as Error)?.message || String(error);
      Logger(`${msg}: ${errorMsg}`, 'API 500', 'src/app/api/notifications/route.ts', LogType.ERROR);
    }
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  const session = await getServerSession(authOptions);

  if (!session?.user?.email) {
    return NextResponse.json(
      { error: "Unauthorized" },
      { status: 401 }
    );
  }

  try {
    const { type, nachricht, userId } = await request.json();

    // Validate required fields
    if (!nachricht) {
      return NextResponse.json(
        { error: "Message is required" },
        { status: 400 }
      );
    }

    // Only admins can create notifications for other users
    const targetUserId = userId || session.user.id;
    if (userId && session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Only admins can create notifications for other users" },
        { status: 403 }
      );
    }

    const notification = await prisma.systemNotification.create({
      data: {
        type: type || NotificationType.INFO,
        nachricht,
        userId: targetUserId,
      },
    });

    return NextResponse.json(notification, { status: 201 });
  } catch (error) {
    {
      const msg = "Internal server error - creating notification";
      const errorMsg = (error as Error)?.message || String(error);
      Logger(`${msg}: ${errorMsg}`, 'API 500', 'src/app/api/notifications/route.ts', LogType.ERROR);
    }
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
