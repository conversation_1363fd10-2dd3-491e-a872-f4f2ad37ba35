import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import prisma from "~/server/db/prisma";
import { getServerSession } from "next-auth/next";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role, ThgOfferStatus } from "@prisma/client";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";

export const dynamic = "force-dynamic";

// GET - THG-Statistiken für Admins
export async function GET(request: NextRequest) {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json({ error: "Not authenticated" }, { status: 401 });
  }

  if (session.user.role !== Role.ADMIN) {
    return NextResponse.json({ error: "Access denied. Admin role required." }, { status: 403 });
  }

  try {
    const { searchParams } = new URL(request.url);
    const timeframe = searchParams.get("timeframe") || "30"; // days
    const days = parseInt(timeframe);

    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    // Grundlegende Statistiken
    const [
      totalOffers,
      activeOffers,
      expiredOffers,
      cancelledOffers,
      acceptedOffers,
      rejectedOffers,
    ] = await Promise.all([
      prisma.thgOffer.count(),
      prisma.thgOffer.count({ where: { status: ThgOfferStatus.ACTIVE } }),
      prisma.thgOffer.count({ where: { status: ThgOfferStatus.EXPIRED } }),
      prisma.thgOffer.count({ where: { status: ThgOfferStatus.CANCELLED } }),
      prisma.thgOffer.count({ where: { status: ThgOfferStatus.ACCEPTED } }),
      prisma.thgOffer.count({ where: { status: ThgOfferStatus.REJECTED } }),
    ]);

    // Zeitbasierte Statistiken
    const offersInTimeframe = await prisma.thgOffer.count({
      where: {
        createdAt: {
          gte: startDate,
        },
      },
    });

    // Preis-Statistiken für aktive Angebote
    const priceStats = await prisma.thgOffer.aggregate({
      where: { status: ThgOfferStatus.ACTIVE },
      _avg: { pricePerKwh: true },
      _min: { pricePerKwh: true },
      _max: { pricePerKwh: true },
      _count: { pricePerKwh: true },
    });

    // Volumen-Statistiken für aktive Angebote
    const volumeStats = await prisma.thgOffer.aggregate({
      where: { status: ThgOfferStatus.ACTIVE },
      _sum: { quantity: true },
      _avg: { quantity: true },
      _min: { quantity: true },
      _max: { quantity: true },
    });

    // Angebote nach Monaten gruppiert (letzte 12 Monate)
    const twelveMonthsAgo = new Date();
    twelveMonthsAgo.setMonth(twelveMonthsAgo.getMonth() - 12);

    const monthlyOffers = await prisma.$queryRaw`
      SELECT 
        DATE_FORMAT(createdAt, '%Y-%m') as month,
        COUNT(*) as count,
        AVG(pricePerKwh) as avgPrice,
        SUM(quantity) as totalVolume
      FROM ThgOffer 
      WHERE createdAt >= ${twelveMonthsAgo}
      GROUP BY DATE_FORMAT(createdAt, '%Y-%m')
      ORDER BY month ASC
    `;

    // Top Käufer nach Anzahl Angebote
    const topBuyersByCount = await prisma.thgOffer.groupBy({
      by: ['buyerId'],
      _count: { buyerId: true },
      orderBy: { _count: { buyerId: 'desc' } },
      take: 10,
    });

    // Top Käufer nach Volumen
    const topBuyersByVolume = await prisma.thgOffer.groupBy({
      by: ['buyerId'],
      _sum: { quantity: true },
      orderBy: { _sum: { quantity: 'desc' } },
      take: 10,
    });

    // Käufer-Details abrufen
    const buyerIds = [...new Set([
      ...topBuyersByCount.map(b => b.buyerId),
      ...topBuyersByVolume.map(b => b.buyerId),
    ])];

    const buyerDetails = await prisma.user.findMany({
      where: { id: { in: buyerIds } },
      select: {
        id: true,
        name: true,
        email: true,
        companyName: true,
      },
    });

    const buyerMap = buyerDetails.reduce((acc, buyer) => {
      acc[buyer.id] = buyer;
      return acc;
    }, {} as Record<string, any>);

    // Angebote nach Status und Zeitraum
    const statusDistribution = await prisma.thgOffer.groupBy({
      by: ['status'],
      _count: { status: true },
      where: {
        createdAt: {
          gte: startDate,
        },
      },
    });

    const statistics = {
      overview: {
        totalOffers,
        activeOffers,
        expiredOffers,
        cancelledOffers,
        acceptedOffers,
        rejectedOffers,
        offersInTimeframe,
      },
      pricing: {
        averagePrice: priceStats._avg.pricePerKwh ? Number(priceStats._avg.pricePerKwh) : null,
        minPrice: priceStats._min.pricePerKwh ? Number(priceStats._min.pricePerKwh) : null,
        maxPrice: priceStats._max.pricePerKwh ? Number(priceStats._max.pricePerKwh) : null,
        activePriceOffers: priceStats._count.pricePerKwh,
      },
      volume: {
        totalVolume: volumeStats._sum.quantity || 0,
        averageVolume: volumeStats._avg.quantity ? Number(volumeStats._avg.quantity) : null,
        minVolume: volumeStats._min.quantity || 0,
        maxVolume: volumeStats._max.quantity || 0,
      },
      trends: {
        monthlyOffers,
        statusDistribution: statusDistribution.map(item => ({
          status: item.status,
          count: item._count.status,
        })),
      },
      topBuyers: {
        byCount: topBuyersByCount.map(item => ({
          buyer: buyerMap[item.buyerId],
          offerCount: item._count.buyerId,
        })),
        byVolume: topBuyersByVolume.map(item => ({
          buyer: buyerMap[item.buyerId],
          totalVolume: item._sum.quantity || 0,
        })),
      },
      timeframe: {
        days,
        startDate,
        endDate: new Date(),
      },
    };

    return NextResponse.json(statistics);
  } catch (error) {
    {
      const msg = "Error fetching THG statistics:";
      const errorMsg = (error as Error)?.message || String(error);
      Logger(`${msg}: ${errorMsg}`, "API 500", "src/app/api/admin/thg/statistics/route.ts", LogType.ERROR);
    }
    Logger("API 500 Error", "API 500", "src/app/api/admin/thg/statistics/route.ts", LogType.ERROR);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
