import { NextResponse } from "next/server";
import prisma from "~/server/db/prisma";
import { getServerSession } from "next-auth/next";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role, ThgOfferStatus } from "@prisma/client";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";


export const dynamic = "force-dynamic";

// GET - THG-Marktübersicht für Admins
export async function GET() {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json({ error: "Not authenticated" }, { status: 401 });
  }

  if (session.user.role !== Role.ADMIN) {
    return NextResponse.json({ error: "Access denied. Admin role required." }, { status: 403 });
  }

  try {
    // Aktuelle Statistiken
    const [
      totalOffers,
      activeOffers,
      expiredOffers,
      totalBuyers,
      averagePrice,
      totalVolume,
      recentOffers,
    ] = await Promise.all([
      // Gesamtanzahl Angebote
      prisma.thgOffer.count(),

      // Aktive Angebote
      prisma.thgOffer.count({
        where: { status: ThgOfferStatus.ACTIVE },
      }),

      // Abgelaufene Angebote
      prisma.thgOffer.count({
        where: { status: ThgOfferStatus.EXPIRED },
      }),

      // Anzahl einzigartige Käufer
      prisma.thgOffer.groupBy({
        by: ['buyerId'],
        _count: { buyerId: true },
      }).then(result => result.length),

      // Durchschnittspreis aktiver Angebote
      prisma.thgOffer.aggregate({
        where: { status: ThgOfferStatus.ACTIVE },
        _avg: { pricePerKwh: true },
      }).then(result => result._avg.pricePerKwh),

      // Gesamtvolumen aktiver Angebote
      prisma.thgOffer.aggregate({
        where: { status: ThgOfferStatus.ACTIVE },
        _sum: { quantity: true },
      }).then(result => result._sum.quantity),

      // Neueste Angebote (letzte 10)
      prisma.thgOffer.findMany({
        take: 10,
        orderBy: { createdAt: 'desc' },
        include: {
          buyer: {
            select: {
              name: true,
              companyName: true,
            },
          },
        },
      }),
    ]);

    // Preisspanne aktiver Angebote
    const priceRange = await prisma.thgOffer.aggregate({
      where: { status: ThgOfferStatus.ACTIVE },
      _min: { pricePerKwh: true },
      _max: { pricePerKwh: true },
    });

    // Angebote nach Status gruppiert
    const offersByStatus = await prisma.thgOffer.groupBy({
      by: ['status'],
      _count: { status: true },
    });

    // Angebote der letzten 30 Tage
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

    const recentOffersCount = await prisma.thgOffer.count({
      where: {
        createdAt: {
          gte: thirtyDaysAgo,
        },
      },
    });

    // Top Käufer nach Anzahl Angebote
    const topBuyers = await prisma.thgOffer.groupBy({
      by: ['buyerId'],
      _count: { buyerId: true },
      orderBy: { _count: { buyerId: 'desc' } },
      take: 5,
    });

    // Käufer-Details für Top-Käufer abrufen
    const topBuyersWithDetails = await Promise.all(
      topBuyers.map(async (buyer) => {
        const userDetails = await prisma.user.findUnique({
          where: { id: buyer.buyerId },
          select: {
            name: true,
            email: true,
            companyName: true,
          },
        });
        return {
          ...buyer,
          user: userDetails,
        };
      })
    );

    const marketOverview = {
      statistics: {
        totalOffers,
        activeOffers,
        expiredOffers,
        totalBuyers,
        recentOffersCount,
        averagePrice: averagePrice ? Number(averagePrice) : null,
        totalVolume: totalVolume || 0,
        priceRange: {
          min: priceRange._min.pricePerKwh ? Number(priceRange._min.pricePerKwh) : null,
          max: priceRange._max.pricePerKwh ? Number(priceRange._max.pricePerKwh) : null,
        },
      },
      offersByStatus: offersByStatus.map(item => ({
        status: item.status,
        count: item._count.status,
      })),
      topBuyers: topBuyersWithDetails,
      recentOffers: recentOffers.map(offer => ({
        id: offer.id,
        title: offer.title,
        pricePerKwh: Number(offer.pricePerKwh),
        quantity: offer.quantity,
        status: offer.status,
        createdAt: offer.createdAt,
        buyer: {
          name: offer.buyer.name,
          companyName: offer.buyer.companyName,
        },
      })),
    };

    return NextResponse.json(marketOverview);
  } catch (error) {
    {
      const msg = "Internal server error - fetching THG market overview";
      const errorMsg = (error as Error)?.message || String(error);
      Logger(`${msg}: ${errorMsg}`, 'API 500', 'src/app/api/admin/thg/market-overview/route.ts', LogType.ERROR);
    }
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
