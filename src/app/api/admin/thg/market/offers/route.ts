import { NextResponse } from "next/server";
import prisma from "~/server/db/prisma";
import { getServerSession } from "next-auth/next";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role, ThgOfferStatus } from "@prisma/client";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";

export const dynamic = "force-dynamic";

// GET - Aktive Marktangebote für die Marktübersicht
export async function GET() {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json({ error: "Not authenticated" }, { status: 401 });
  }

  if (session.user.role !== Role.ADMIN) {
    return NextResponse.json({ error: "Access denied. Admin role required." }, { status: 403 });
  }

  try {
    // Nur aktive und gültige Angebote für die Marktübersicht
    const offers = await prisma.thgOffer.findMany({
      where: {
        status: ThgOfferStatus.ACTIVE,
        validUntil: {
          gte: new Date(), // Nur noch gültige Angebote
        },
      },
      include: {
        buyer: {
          select: {
            id: true,
            name: true,
            companyName: true,
            thgCompanyContact: {
              select: {
                contactPerson: true,
                phone: true,
                website: true,
              },
            },
          },
        },
      },
      orderBy: [
        { createdAt: "desc" },
        { pricePerKwh: "asc" },
      ],
      take: 50, // Limit für Performance
    });

    // Transform data for frontend
    const marketOffers = offers.map(offer => ({
      id: offer.id,
      type: offer.type,
      title: offer.title,
      companyName: offer.buyer.companyName || offer.buyer.name || "Unbekannt",
      pricePerKwh: Number(offer.pricePerKwh),
      priceUnit: offer.priceUnit,
      quantity: offer.quantity,
      quantityMin: offer.quantityMin,
      quantityMax: offer.quantityMax,
      validUntil: offer.validUntil.toISOString(),
      createdAt: offer.createdAt.toISOString(),
      description: offer.description,
      contactPerson: offer.contactPerson,
    }));

    return NextResponse.json(marketOffers);
  } catch (error) {
    {
      const msg = "Error fetching market offers:";
      const errorMsg = (error as Error)?.message || String(error);
      Logger(`${msg}: ${errorMsg}`, "API 500", "src/app/api/admin/thg/market/offers/route.ts", LogType.ERROR);
    }
    Logger("API 500 Error", "API 500", "src/app/api/admin/thg/market/offers/route.ts", LogType.ERROR);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
