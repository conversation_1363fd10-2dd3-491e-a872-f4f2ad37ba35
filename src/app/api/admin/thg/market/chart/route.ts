import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import prisma from "~/server/db/prisma";
import { getServerSession } from "next-auth/next";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role, ThgOfferStatus } from "@prisma/client";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";

export const dynamic = "force-dynamic";

// GET - Chart-Daten für die Marktpreisentwicklung
export async function GET(request: NextRequest) {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json({ error: "Not authenticated" }, { status: 401 });
  }

  if (session.user.role !== Role.ADMIN) {
    return NextResponse.json({ error: "Access denied. Admin role required." }, { status: 403 });
  }

  try {
    const { searchParams } = new URL(request.url);
    const range = searchParams.get("range") || "30d";

    // Zeitraum bestimmen
    const now = new Date();
    const startDate = new Date(now);
    
    switch (range) {
      case "7d":
        startDate.setDate(startDate.getDate() - 7);
        break;
      case "30d":
        startDate.setDate(startDate.getDate() - 30);
        break;
      case "90d":
        startDate.setDate(startDate.getDate() - 90);
        break;
      default:
        startDate.setDate(startDate.getDate() - 30);
    }

    // Tägliche Aggregation der Angebotsdaten
    const dailyData = await prisma.$queryRaw`
      SELECT 
        DATE(createdAt) as date,
        AVG(pricePerKwh) as averagePrice,
        MIN(pricePerKwh) as minPrice,
        MAX(pricePerKwh) as maxPrice,
        SUM(quantity) as volume,
        COUNT(*) as offerCount
      FROM ThgOffer 
      WHERE createdAt >= ${startDate}
        AND status = ${ThgOfferStatus.ACTIVE}
      GROUP BY DATE(createdAt)
      ORDER BY date ASC
    `;

    // Daten für Frontend formatieren
    const chartData = (dailyData as any[]).map(row => ({
      date: row.date.toISOString().split('T')[0],
      averagePrice: row.averagePrice ? Number(row.averagePrice) : 0,
      minPrice: row.minPrice ? Number(row.minPrice) : 0,
      maxPrice: row.maxPrice ? Number(row.maxPrice) : 0,
      volume: row.volume ? Number(row.volume) : 0,
      offerCount: Number(row.offerCount),
    }));

    // Fehlende Tage mit 0-Werten auffüllen
    const filledData = [];
    const currentDate = new Date(startDate);
    
    while (currentDate <= now) {
      const dateStr = currentDate.toISOString().split('T')[0];
      const existingData = chartData.find(d => d.date === dateStr);
      
      if (existingData) {
        filledData.push(existingData);
      } else {
        filledData.push({
          date: dateStr,
          averagePrice: 0,
          minPrice: 0,
          maxPrice: 0,
          volume: 0,
          offerCount: 0,
        });
      }
      
      currentDate.setDate(currentDate.getDate() + 1);
    }

    return NextResponse.json(filledData);
  } catch (error) {
    {
      const msg = "Error fetching market chart data:";
      const errorMsg = (error as Error)?.message || String(error);
      Logger(`${msg}: ${errorMsg}`, "API 500", "src/app/api/admin/thg/market/chart/route.ts", LogType.ERROR);
    }
    Logger("API 500 Error", "API 500", "src/app/api/admin/thg/market/chart/route.ts", LogType.ERROR);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
