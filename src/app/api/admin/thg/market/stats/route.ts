import { NextResponse } from "next/server";
import prisma from "~/server/db/prisma";
import { getServerSession } from "next-auth/next";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role, ThgOfferStatus } from "@prisma/client";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";

export const dynamic = "force-dynamic";

// GET - Marktstatistiken für die Marktübersicht
export async function GET() {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json({ error: "Not authenticated" }, { status: 401 });
  }

  if (session.user.role !== Role.ADMIN) {
    return NextResponse.json({ error: "Access denied. Admin role required." }, { status: 403 });
  }

  try {
    const now = new Date();
    const yesterday = new Date(now);
    yesterday.setDate(yesterday.getDate() - 1);

    // Aktuelle Marktstatistiken (nur aktive und gültige Angebote)
    const [
      currentStats,
      yesterdayStats,
      totalVolume,
      activeOffersCount,
    ] = await Promise.all([
      // Aktuelle Preis-Statistiken
      prisma.thgOffer.aggregate({
        where: {
          status: ThgOfferStatus.ACTIVE,
          validUntil: { gte: now },
        },
        _avg: { pricePerKwh: true },
        _min: { pricePerKwh: true },
        _max: { pricePerKwh: true },
        _count: { pricePerKwh: true },
      }),

      // Gestrige Preis-Statistiken für Vergleich
      prisma.thgOffer.aggregate({
        where: {
          status: ThgOfferStatus.ACTIVE,
          createdAt: { lte: yesterday },
          validUntil: { gte: yesterday },
        },
        _avg: { pricePerKwh: true },
      }),

      // Gesamtvolumen aktiver Angebote
      prisma.thgOffer.aggregate({
        where: {
          status: ThgOfferStatus.ACTIVE,
          validUntil: { gte: now },
        },
        _sum: { quantity: true },
      }),

      // Anzahl aktiver Angebote
      prisma.thgOffer.count({
        where: {
          status: ThgOfferStatus.ACTIVE,
          validUntil: { gte: now },
        },
      }),
    ]);

    // Preisänderung berechnen
    const currentAvgPrice = currentStats._avg.pricePerKwh ? Number(currentStats._avg.pricePerKwh) : 0;
    const yesterdayAvgPrice = yesterdayStats._avg.pricePerKwh ? Number(yesterdayStats._avg.pricePerKwh) : 0;
    
    let priceChange = 0;
    if (yesterdayAvgPrice > 0) {
      priceChange = ((currentAvgPrice - yesterdayAvgPrice) / yesterdayAvgPrice) * 100;
    }

    const marketStats = {
      averagePrice: currentAvgPrice,
      priceChange: Number(priceChange.toFixed(2)),
      totalVolume: totalVolume._sum.quantity || 0,
      activeOffers: activeOffersCount,
      minPrice: currentStats._min.pricePerKwh ? Number(currentStats._min.pricePerKwh) : 0,
      maxPrice: currentStats._max.pricePerKwh ? Number(currentStats._max.pricePerKwh) : 0,
      offersWithPrices: currentStats._count.pricePerKwh,
    };

    return NextResponse.json(marketStats);
  } catch (error) {
    {
      const msg = "Error fetching market stats:";
      const errorMsg = (error as Error)?.message || String(error);
      Logger(`${msg}: ${errorMsg}`, "API 500", "src/app/api/admin/thg/market/stats/route.ts", LogType.ERROR);
    }
    Logger("API 500 Error", "API 500", "src/app/api/admin/thg/market/stats/route.ts", LogType.ERROR);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
