import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import prisma from "~/server/db/prisma";
import { getServerSession } from "next-auth/next";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role, ThgOfferStatus, ThgOfferType } from "@prisma/client";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";

export const dynamic = "force-dynamic";

// GET - Erweiterte Suche für THG-Angebote (Admin)
export async function GET(request: NextRequest) {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json({ error: "Not authenticated" }, { status: 401 });
  }

  if (session.user.role !== Role.ADMIN) {
    return NextResponse.json({ error: "Access denied. Admin role required." }, { status: 403 });
  }

  try {
    const { searchParams } = new URL(request.url);
    
    // Filter-Parameter
    const status = searchParams.get("status");
    const type = searchParams.get("type");
    const minPrice = searchParams.get("minPrice");
    const maxPrice = searchParams.get("maxPrice");
    const minQuantity = searchParams.get("minQuantity");
    const maxQuantity = searchParams.get("maxQuantity");
    const buyerEmail = searchParams.get("buyerEmail");
    const companyName = searchParams.get("companyName");
    const dateFrom = searchParams.get("dateFrom");
    const dateTo = searchParams.get("dateTo");
    const validUntilFrom = searchParams.get("validUntilFrom");
    const validUntilTo = searchParams.get("validUntilTo");
    const searchTerm = searchParams.get("search");
    
    // Pagination
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "25");
    const sortBy = searchParams.get("sortBy") || "createdAt";
    const sortOrder = searchParams.get("sortOrder") || "desc";

    // Build where clause
    const whereClause: any = {};
    
    if (status && Object.values(ThgOfferStatus).includes(status as ThgOfferStatus)) {
      whereClause.status = status as ThgOfferStatus;
    }
    
    if (type && Object.values(ThgOfferType).includes(type as ThgOfferType)) {
      whereClause.type = type as ThgOfferType;
    }

    // Preis-Filter
    if (minPrice || maxPrice) {
      whereClause.pricePerKwh = {};
      if (minPrice) whereClause.pricePerKwh.gte = parseFloat(minPrice);
      if (maxPrice) whereClause.pricePerKwh.lte = parseFloat(maxPrice);
    }

    // Mengen-Filter
    if (minQuantity || maxQuantity) {
      whereClause.quantity = {};
      if (minQuantity) whereClause.quantity.gte = parseInt(minQuantity);
      if (maxQuantity) whereClause.quantity.lte = parseInt(maxQuantity);
    }

    // Datum-Filter
    if (dateFrom || dateTo) {
      whereClause.createdAt = {};
      if (dateFrom) whereClause.createdAt.gte = new Date(dateFrom);
      if (dateTo) {
        const endDate = new Date(dateTo);
        endDate.setHours(23, 59, 59, 999);
        whereClause.createdAt.lte = endDate;
      }
    }

    // Gültigkeitsdatum-Filter
    if (validUntilFrom || validUntilTo) {
      whereClause.validUntil = {};
      if (validUntilFrom) whereClause.validUntil.gte = new Date(validUntilFrom);
      if (validUntilTo) {
        const endDate = new Date(validUntilTo);
        endDate.setHours(23, 59, 59, 999);
        whereClause.validUntil.lte = endDate;
      }
    }

    // Käufer-Filter
    if (buyerEmail || companyName) {
      whereClause.buyer = {};
      if (buyerEmail) {
        whereClause.buyer.email = { contains: buyerEmail, mode: 'insensitive' };
      }
      if (companyName) {
        whereClause.buyer.companyName = { contains: companyName, mode: 'insensitive' };
      }
    }

    // Volltext-Suche
    if (searchTerm) {
      whereClause.OR = [
        { title: { contains: searchTerm, mode: 'insensitive' } },
        { description: { contains: searchTerm, mode: 'insensitive' } },
        { contactPerson: { contains: searchTerm, mode: 'insensitive' } },
        { buyer: { name: { contains: searchTerm, mode: 'insensitive' } } },
        { buyer: { email: { contains: searchTerm, mode: 'insensitive' } } },
        { buyer: { companyName: { contains: searchTerm, mode: 'insensitive' } } },
      ];
    }

    // Pagination
    const skip = (page - 1) * limit;

    // Sortierung
    const orderBy: any = {};
    if (sortBy === "buyerName") {
      orderBy.buyer = { name: sortOrder };
    } else if (sortBy === "companyName") {
      orderBy.buyer = { companyName: sortOrder };
    } else {
      orderBy[sortBy] = sortOrder;
    }

    // Abfrage ausführen
    const [offers, totalCount] = await Promise.all([
      prisma.thgOffer.findMany({
        where: whereClause,
        include: {
          buyer: {
            select: {
              id: true,
              name: true,
              email: true,
              companyName: true,
              thgCompanyContact: {
                select: {
                  contactPerson: true,
                  phone: true,
                  website: true,
                },
              },
            },
          },
          _count: {
            select: {
              quotes: true,
              contracts: true,
            },
          },
        },
        orderBy,
        skip,
        take: limit,
      }),
      prisma.thgOffer.count({ where: whereClause }),
    ]);

    // Pagination-Info
    const totalPages = Math.ceil(totalCount / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    // Aggregierte Daten für die gefilterten Ergebnisse
    const aggregatedData = await prisma.thgOffer.aggregate({
      where: whereClause,
      _count: { id: true },
      _sum: { quantity: true },
      _avg: { pricePerKwh: true },
      _min: { pricePerKwh: true },
      _max: { pricePerKwh: true },
    });

    return NextResponse.json({
      offers: offers.map(offer => ({
        ...offer,
        pricePerKwh: Number(offer.pricePerKwh),
        totalAmount: offer.totalAmount ? Number(offer.totalAmount) : null,
      })),
      pagination: {
        currentPage: page,
        totalPages,
        totalCount,
        hasNextPage,
        hasPrevPage,
        limit,
      },
      aggregatedData: {
        totalOffers: aggregatedData._count.id,
        totalVolume: aggregatedData._sum.quantity || 0,
        averagePrice: aggregatedData._avg.pricePerKwh ? Number(aggregatedData._avg.pricePerKwh) : null,
        minPrice: aggregatedData._min.pricePerKwh ? Number(aggregatedData._min.pricePerKwh) : null,
        maxPrice: aggregatedData._max.pricePerKwh ? Number(aggregatedData._max.pricePerKwh) : null,
      },
      filters: {
        status,
        type,
        minPrice,
        maxPrice,
        minQuantity,
        maxQuantity,
        buyerEmail,
        companyName,
        dateFrom,
        dateTo,
        validUntilFrom,
        validUntilTo,
        searchTerm,
      },
    });
  } catch (error) {
    {
      const msg = "Error searching THG offers:";
      const errorMsg = (error as Error)?.message || String(error);
      Logger(`${msg}: ${errorMsg}`, "API 500", "src/app/api/admin/thg/offers/search/route.ts", LogType.ERROR);
    }
    Logger("API 500 Error", "API 500", "src/app/api/admin/thg/offers/search/route.ts", LogType.ERROR);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
