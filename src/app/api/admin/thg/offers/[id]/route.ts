import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import prisma from "~/server/db/prisma";
import { getServerSession } from "next-auth/next";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role } from "@prisma/client";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";

export const dynamic = "force-dynamic";

interface RouteParams {
  params: {
    id: string;
  };
}

// GET - Einzelnes THG-Angebot für Admins abrufen
export async function GET(request: NextRequest, { params }: RouteParams) {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json({ error: "Not authenticated" }, { status: 401 });
  }

  if (session.user.role !== Role.ADMIN) {
    return NextResponse.json({ error: "Access denied. Admin role required." }, { status: 403 });
  }

  try {
    const offer = await prisma.thgOffer.findUnique({
      where: { id: params.id },
      include: {
        buyer: {
          select: {
            id: true,
            name: true,
            email: true,
            companyName: true,
            thgCompanyContact: {
              select: {
                contactPerson: true,
                phone: true,
                website: true,
                notes: true,
              },
            },
          },
        },
        seller: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        quotes: {
          include: {
            quoter: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
          orderBy: { createdAt: "desc" },
        },
        contracts: {
          include: {
            buyer: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
            seller: {
              select: {
                id: true,
                name: true,
                email: true,
              },
            },
          },
          orderBy: { createdAt: "desc" },
        },
      },
    });

    if (!offer) {
      return NextResponse.json({ error: "THG offer not found" }, { status: 404 });
    }

    return NextResponse.json(offer);
  } catch (error) {
    {
      const msg = "Error fetching THG offer:";
      const errorMsg = (error as Error)?.message || String(error);
      Logger(`${msg}: ${errorMsg}`, "API 500", "src/app/api/admin/thg/offers/[id]/route.ts", LogType.ERROR);
    }
    Logger("API 500 Error", "API 500", "src/app/api/admin/thg/offers/[id]/route.ts", LogType.ERROR);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
