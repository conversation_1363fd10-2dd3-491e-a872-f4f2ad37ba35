import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role } from "@prisma/client";
import prisma from "~/server/db/prisma";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";

export async function GET(request: NextRequest) {
  const session = await getServerSession(authOptions);

  if (!session || session?.user?.role !== Role.ADMIN) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const { searchParams } = new URL(request.url);
    const operatorId = searchParams.get("operatorId");

    const whereClause: any = {
      deleted: null,
      hide: false,
    };

    // Filter by operatorId if provided
    if (operatorId) {
      whereClause.operatorId = operatorId;
    }

    const ous = await prisma.ou.findMany({
      where: whereClause,
      select: {
        id: true,
        name: true,
        code: true,
        operatorId: true,
        externalReference: true,
        _count: {
          select: {
            User: true,
            Location: true,
          },
        },
      },
      orderBy: {
        name: "asc",
      },
    });

    return NextResponse.json(ous);
  } catch (error) {
    {
      const msg = "Internal server error - fetching OUs";
      const errorMsg = (error as Error)?.message || String(error);
      Logger(`${msg}: ${errorMsg}`, 'API 500', 'src/app/api/admin/ous/route.ts', LogType.ERROR);
    }
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
