// src/app/api/occpMonitorKey/occpMonitorKeyDelete/[id]/route.ts
import { NextResponse } from "next/server";
import prisma from "~/server/db/prisma";

export async function DELETE(
  _req: Request,
  { params }: { params: { id: string } }
) {
  try {
    await prisma.$transaction([
      prisma.occpMonitorKeyOnChargePoint.deleteMany({
        where: { occpMonitorKeyId: params.id },
      }),
      prisma.occpMonitorKey.delete({ where: { id: params.id } }),
    ]);

    return NextResponse.json({ ok: true });
  } catch (e: any) {
    return NextResponse.json({ ok: false, error: e?.message ?? "invalid request" }, { status: 400 });
  }
}