import { NextResponse } from "next/server";

import { changeConfigurationForChargePoint } from "~/utils/monitoring/ocppMonitoringKeys";
import { bodySchemaChangeConfiguration } from "~/types/schemaZod/changeConfigurationOccpMonitorKey";



export async function POST(req: Request) {
  try {
    const json = await req.json();
    const { chargePointId, monitoringKey, setValue } = bodySchemaChangeConfiguration.parse(json);

    const status = await changeConfigurationForChargePoint(chargePointId, monitoringKey, setValue);

    // Map in dein ApiResult-Schema
    let result;
    if (status === "Accepted") {
      result = {
        status: "ok",
        key: monitoringKey,
        value: setValue,
        chargePointId,
      };
    } else if (typeof status === "string") {
      // z.B. "Rejected" | "RebootRequired" -> als gelisteter Fehler
      result = {
        status: "error",
        key: monitoring<PERSON>ey,
        value: setValue,
        chargePointId,
        reason: "listed-error",
        message: status,
      };
    } else {
      // kein/kaputter Payload
      result = {
        status: "no-payload",
        key: monitoringKey,
        chargePointId,
      };
    }

    return NextResponse.json({ ok: true, results: [result] });
  } catch (e: any) {
    return NextResponse.json(
      { ok: false, error: e?.message ?? "invalid request" },
      { status: 400 }
    );
  }
}
