import { NextResponse } from "next/server";
import prisma from "~/server/db/prisma";
import { occpMonitorKeySchemaEdit } from "~/types/schemaZod/editOccpMonitorKeyZodSchema";
import { Prisma } from "@prisma/client";

export async function PUT(req: Request, { params }: { params: { id: string } }) {
  const id = params.id;

  try {
    const body = await req.json();
    const data = occpMonitorKeySchemaEdit.parse(body);

    const updated = await prisma.$transaction(async (db) => {
      // 1) Hauptdatensatz
      const head = await db.occpMonitorKey.update({
        where: { id },
        data: {
          chargePointVendor: data.chargePointVendor,
          chargePointModel:  data.chargePointModel,
          configKeyName:     data.configKeyName,
          expectedValues:    data.expectedValues,
          errorValues:       data.errorValues,
        },
      });

      // 2) Excluded ChargePoints ersetzen (nur wenn Feld übergeben wurde)
      if (data.chargePointIds) {
        // alte Links weg
        await db.occpMonitorKeyOnChargePoint.deleteMany({
          where: { occpMonitorKeyId: id },
        });

        if (data.chargePointIds.length > 0) {
          // übergebene Werte können entweder interne `ChargePoint.id`
          // ODER die öffentliche `ChargePoint.chargePointId` sein:
          const cps = await db.chargePoint.findMany({
            where: {
              OR: [
                { id: { in: data.chargePointIds } },
                { chargePointId: { in: data.chargePointIds } },
              ],
            },
            select: { id: true },
          });

          if (cps.length > 0) {
            await db.occpMonitorKeyOnChargePoint.createMany({
              data: cps.map(cp => ({ occpMonitorKeyId: id, chargePointId: cp.id })),
              skipDuplicates: true,
            });
          }
        }
      }

      return head;
    });

    return NextResponse.json({ ok: true, data: updated });
  } catch (e: any) {
    if (e instanceof Prisma.PrismaClientKnownRequestError) {
      if (e.code === "P2002") {
        return NextResponse.json(
          { ok: false, error: "Kombination aus Vendor/Model/Key existiert bereits." },
          { status: 409 }
        );
      }
      if (e.code === "P2025") {
        return NextResponse.json(
          { ok: false, error: "MonitorKey nicht gefunden." },
          { status: 404 }
        );
      }
    }
    return NextResponse.json(
      { ok: false, error: e?.message ?? "Unbekannter Fehler." },
      { status: 500 }
    );
  }
}
