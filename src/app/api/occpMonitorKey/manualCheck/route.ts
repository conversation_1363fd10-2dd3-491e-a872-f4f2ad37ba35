import { NextResponse } from "next/server";
import {
  manualCheckBodySchema,
  normalizeManualCheckBody,
  type ManualCheckItem,
} from "~/types/schemaZod/manualCheckZod";
import { getConfigurationForChargePoint } from "~/utils/monitoring/ocppMonitoringKeys";
import {splitCSV} from "~/utils/csv/csvHelper";
import {ApiResult} from "~/types/monitorKey/monitorKey";




async function checkOneItem(item: ManualCheckItem): Promise<ApiResult> {
  const { chargePointId, monitoringKey, expectedValues, errorValues } = item;

  let responseArray: any[];
  try {
    responseArray = await getConfigurationForChargePoint(chargePointId, [monitoringKey]);
  } catch (e: any) {
    return { status: "fetch-failed", key: monitoringKey, chargePointId, message: String(e?.message ?? e) };
  }

  const response = Array.isArray(responseArray) ? responseArray[0] : undefined;
  if (!response?.payload) return { status: "no-payload", key: monitoringKey, chargePointId };

  let payload: any;
  try {
    payload = JSON.parse(response.payload);
  } catch {
    return { status: "no-payload", key: monitoringKey, chargePointId };
  }

  const configArray = payload?.[2]?.configurationKey;
  if (!Array.isArray(configArray)) return { status: "no-payload", key: monitoringKey, chargePointId };

  const entry = configArray.find((c: any) => c?.key === monitoringKey);
  if (!entry) return { status: "not-found", key: monitoringKey, chargePointId, reason: "key-not-in-payload" };

  const value = String(entry.value ?? "");
  const expected = splitCSV(expectedValues);
  const errors = splitCSV(errorValues);

  if (expected.length > 0 && expected.includes(value)) {
    return { status: "ok", key: monitoringKey, value, chargePointId };
  }

  if (errors.length > 0 && errors.includes(value)) {
    return { status: "error", key: monitoringKey, value, chargePointId, reason: "listed-error" };
  }

  if (expected.length > 0) {
    return { status: "error", key: monitoringKey, value, chargePointId, reason: "unexpected" };
  }

  return { status: "ok", key: monitoringKey, value, chargePointId };
}

export async function POST(req: Request) {
  try {
    const json = await req.json();

    // harte Validierung & Normalisierung
    const parsed = manualCheckBodySchema.parse(json);
    const items = normalizeManualCheckBody(parsed);

    // prüfen (parallel)
    const results = await Promise.all(items.map(checkOneItem));

    return NextResponse.json({ ok: true, results });
  } catch (e: any) {
    const errorPayload = e?.issues ?? e?.message ?? "invalid request";
    return NextResponse.json({ ok: false, error: errorPayload }, { status: 400 });
  }
}
