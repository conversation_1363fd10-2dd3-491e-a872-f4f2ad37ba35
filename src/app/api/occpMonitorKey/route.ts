import { NextRequest, NextResponse } from "next/server";
import { occpMonitorKeySchema } from "~/types/schemaZod/occpMonitorKeySchema";
import prisma from "~/server/db/prisma";
import { Prisma } from "@prisma/client";



export async function POST(req: NextRequest) {
  try {
    const json = await req.json();
    // Zod prüft nur die Formdaten (ohne chargePointIds)
    const formData = occpMonitorKeySchema.parse(json);

    // Die chargePointIds holen (optional, ggf. leer)
    const chargePointIds: string[] = Array.isArray(json.chargePointIds)
      ? json.chargePointIds
      : [];

    // 1. OccpMonitorKey speichern
    const newOccpMonitorKey = await prisma.occpMonitorKey.create({
      data: formData,
    });

    // 2. Verknüpfung zu ChargePoints anlegen (nur, wenn mind. 1 ID)
    if (chargePointIds.length > 0) {
      // Bulk-insert (<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>)
      await prisma.occpMonitorKeyOnChargePoint.createMany({
        data: chargePointIds.map(chargePointId => ({
          occpMonitorKeyId: newOccpMonitorKey.id,
          chargePointId,
        })),
        skipDuplicates: true, // falls du doppelte Verknüpfungen vermeiden willst
      });
    }

    // 3. Optionale Rückgabe: gleich mit den verbundenen ChargePoints
    const fullEntry = await prisma.occpMonitorKey.findUnique({
      where: { id: newOccpMonitorKey.id },
      include: { excludedChargePoints: true },
    });

    return NextResponse.json(fullEntry, { status: 201 });

  } catch (err: any) {
    console.error("❌ Fehler beim Erstellen von occpMonitorKey :", err);

    if (
      err instanceof Prisma.PrismaClientKnownRequestError &&
      err.code === "P2002"
    ) {
      return NextResponse.json(
        { error: "Ein Eintrag mit dieser Kombination existiert bereits." },
        { status: 409 }
      );
    }

    // Zod-Fehler (Validation)
    if (err instanceof Error && "issues" in err) {
      return NextResponse.json({ error: "Ungültige Eingabe", details: err }, { status: 400 });
    }

    return NextResponse.json({ error: "Konnte occpMonitorKey nicht erstellen" }, { status: 500 });
  }
}
