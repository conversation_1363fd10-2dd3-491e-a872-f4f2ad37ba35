import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role } from "@prisma/client";
import { z } from "zod";
import prisma from "~/server/db/prisma";
import { PrismaClientKnownRequestError } from "@prisma/client/runtime/library";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";

const UpdateOuSchema = z.object({
  operatorId: z.string().max(5, "OperatorId darf maximal 5 Zeichen haben").optional().nullable(),
  adhocEmpId: z.string().optional().nullable(),
});

interface Props {
  params: {
    id: string;
  };
}

export async function GET(request: NextRequest, { params }: Props) {
  const session = await getServerSession(authOptions);
  
  if (!session || session?.user?.role !== Role.ADMIN) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const ou = await prisma.ou.findUnique({
      where: { 
        id: params.id,
        deleted: null,
      },
      select: {
        id: true,
        name: true,
        code: true,
        operatorId: true,
        externalReference: true,
        customerReference: true,
        address: true,
        city: true,
        postalCode: true,
        country: true,
        companyEmail: true,
        primaryContactperson: true,
        primaryContactpersonEmail: true,
        hotlinePhoneNumber: true,
      },
    });

    if (!ou) {
      return NextResponse.json({ error: "OU not found" }, { status: 404 });
    }

    return NextResponse.json(ou);
  } catch (error) {
    {
      const msg = "Error fetching OU:";
      const errorMsg = (error as Error)?.message || String(error);
      Logger(`${msg}: ${errorMsg}`, "API 500", "src/app/api/ou/[id]/route.ts", LogType.ERROR);
    }
    Logger("API 500 Error", "API 500", "src/app/api/ou/[id]/route.ts", LogType.ERROR);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function PATCH(request: NextRequest, { params }: Props) {
  const session = await getServerSession(authOptions);
  
  if (!session || session?.user?.role !== Role.ADMIN) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const body = await request.json();
    const result = UpdateOuSchema.safeParse(body);

    if (!result.success) {
      return NextResponse.json({ error: result.error.issues }, { status: 400 });
    }

    const { operatorId, adhocEmpId } = result.data;

    // Check if OU exists
    const existingOu = await prisma.ou.findUnique({
      where: { 
        id: params.id,
        deleted: null,
      },
    });

    if (!existingOu) {
      return NextResponse.json({ error: "OU not found" }, { status: 404 });
    }

    // Update OU
    const updatedOu = await prisma.ou.update({
      where: { id: params.id },
      data: {
        operatorId: operatorId || null,
        adhocEmpId: adhocEmpId || null,
      },
      select: {
        id: true,
        name: true,
        code: true,
        operatorId: true,
        adhocEmpId: true,
      },
    });

    return NextResponse.json(updatedOu);
  } catch (error) {
    {
      const msg = "Error updating OU:";
      const errorMsg = (error as Error)?.message || String(error);
      Logger(`${msg}: ${errorMsg}`, "API 500", "src/app/api/ou/[id]/route.ts", LogType.ERROR);
    }
    
    if (error instanceof PrismaClientKnownRequestError) {
      if (error.code === "P2016") {
        return NextResponse.json({ error: "OU not found" }, { status: 404 });
      }
    }
    
    Logger("API 500 Error", "API 500", "src/app/api/ou/[id]/route.ts", LogType.ERROR);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
