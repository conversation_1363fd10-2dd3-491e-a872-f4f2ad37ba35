// src/app/api/achievments/events/progress/recalc/route.ts
import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import prisma from "~/server/db/prisma";
import { recalcProfileProgress } from "~/server/achievments/progress";

export async function POST() {
  const session = await getServerSession(authOptions);
  const userId = session?.user?.id;
  if (!userId) return new NextResponse("Unauthorized", { status: 401 });

  const profile = await prisma.achievementProfile.findUnique({
    where: { userId },
    select: { id: true },
  });
  if (!profile) return new NextResponse("Profile missing", { status: 400 });

  await recalcProfileProgress(profile.id);
  return NextResponse.json({ ok: true });
}
