// /api/achievments/events/enroll/route.ts
import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import prisma from "~/server/db/prisma";

export async function POST(req: Request) {
  const session = await getServerSession(authOptions);
  const userId = session?.user?.id;
  if (!userId) return new NextResponse("Unauthorized", { status: 401 });

  const { eventId } = await req.json() as { eventId: string };
  if (!eventId) return new NextResponse("Missing eventId", { status: 400 });

  // Profil holen
  const profile = await prisma.achievementProfile.findUnique({
    where: { userId },
    select: { id: true },
  });
  if (!profile) return new NextResponse("Profile missing", { status: 400 });

  // Enrollment upserten
  await prisma.achievementEnrollment.upsert({
    where: { profileId_eventId: { profileId: profile.id, eventId } },
    create: { profileId: profile.id, eventId },
    update: {},
  });

  // Metriken des Events
  const metrics = await prisma.achievementEventMetric.findMany({
    where: { eventId },
    select: { id: true, start: true },
  });

  // Progress anlegen (oder lassen, falls existiert)
  await prisma.$transaction(
    metrics.map(m =>
      prisma.achievementMetricProgress.upsert({
        where: { metricId_profileId: { metricId: m.id, profileId: profile.id } },
        create: { metricId: m.id, profileId: profile.id, value: m.start ?? 0 },
        update: {},
      })
    )
  );

  return NextResponse.json({ ok: true });
}
