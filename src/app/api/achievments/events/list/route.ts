// app/api/achievments/events/list/route.ts
import { NextResponse } from "next/server";
import prisma from "~/server/db/prisma";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";

export const dynamic = "force-dynamic";

export async function GET(req: Request) {
  try {

    const session = await getServerSession(authOptions);
    const userId = session?.user?.id ?? null;

    const url = new URL(req.url);
    const includeLegacy = url.searchParams.get("includeLegacy") === "1";
    const onlyActive    = url.searchParams.get("onlyActive") === "1";

    const now = new Date();

    const events = await prisma.achievementEvent.findMany({
      where: {
        ...(onlyActive
          ? {
            OR: [{ startsAt: null }, { startsAt: { lte: now } }],
            AND: [{ OR: [{ endsAt: null }, { endsAt: { gte: now } }] }],
          }
          : {}),
        ...(!includeLegacy ? { metrics: { some: {} } } : {}),
      },
      select: {
        id: true,
        code: true,
        title: true,
        description: true,
        badgeImageUrl: true,
        startsAt: true,
        endsAt: true,

        // Enrollment-Status für aktuellen User
        enrollments: userId
          ? { where: { profile: { userId } }, select: { status: true }, take: 1 }
          : false,

        // ▼▼ Stats/Metriken + optionaler Progress des Users
        metrics: {
          select: {
            order: true,
            label: true,
            param: true,
            target: true,
            start: true,
            color: true,
            window: true, // ENUM: EVENT/MONTH/WEEK/ROLLING30D
            // Progress des aktuellen Users für diese Metrik
            progresses: userId
              ? {
                where: { profile: { userId } },
                select: { value: true },
                take: 1,
              }
              : false,
          },
        },
      },
      orderBy: { createdAt: "desc" },
    });

    const mapped = events.map(e => {
      const phase: "UPCOMING" | "ONGOING" | "ENDED" =
        e.startsAt && now < e.startsAt ? "UPCOMING" :
          e.endsAt   && now > e.endsAt   ? "ENDED"    :
            "ONGOING";

      const enrollmentStatus =
        Array.isArray(e.enrollments) && e.enrollments[0]?.status
          ? (e.enrollments[0].status as "PARTICIPATING" | "COMPLETED" | "FAILED")
          : null;

      // Metriken in das vom Frontend erwartete Shape mappen
      const stats =
        e.metrics
          ?.slice()
          .sort((a, b) => (a.order ?? 0) - (b.order ?? 0))
          .map(m => ({
            label: m.label,
            param: m.param,
            target: Number(m.target ?? 0),
            start: m.start != null ? Number(m.start) : undefined,
            // ENUM (EVENT/MONTH/WEEK/ROLLING30D) -> lowercase für dein UI ("event" | "month" | "week" | "rolling30d")
            window: (m.window?.toString().toLowerCase() ?? "event") as "event" | "month" | "week" | "rolling30d",
            color: m.color ?? undefined,
            value: Array.isArray(m.progresses) && m.progresses[0]?.value != null ? Number(m.progresses[0].value) : 0,
          }))
        ?? [];

      return {
        id: e.id,
        code: e.code,
        title: e.title,
        description: e.description ?? null,
        badgeImageUrl: e.badgeImageUrl ?? null,
        startsAt: e.startsAt?.toISOString() ?? null,
        endsAt: e.endsAt?.toISOString() ?? null,
        phase,
        enrollmentStatus,
        stats,
        reward: null, // falls du später Rewards mitschickst
      };
    });

    return NextResponse.json({ events: mapped });
  } catch (err: any) {
    const msg = typeof err?.message === "string" ? err.message : "Internal Server Error";
    return new NextResponse(msg, { status: 500 });
  }
}
