import { NextRequest } from "next/server";
import prisma from "~/server/db/prisma";
import { newCollectorZodSchema } from "~/app/api/achievments/newCollector/newCollectorZodSchema";

export async function POST(request: NextRequest) {
  const raw = await request.json();
  const parsed = newCollectorZodSchema.safeParse(raw);

  if (!parsed.success) {
    // optional: parsed.error.flatten() senden
    return new Response("Validation failed", { status: 400 });
  }

  const { userId } = parsed.data; // <-- jetzt sicher & getypt

  await prisma.achievementProfile.upsert({
    where: { userId },
    update: { trophiesOptIn: true },
    create: { userId, trophiesOptIn: true },
  });

  return new Response("ok", { status: 200 });
}
