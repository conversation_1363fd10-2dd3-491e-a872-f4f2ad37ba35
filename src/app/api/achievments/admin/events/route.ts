import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import prisma from "~/server/db/prisma";
import {MetricDTO, RewardDTO} from "~/types/achievment/achievmentEventTypes";



export async function POST(req: Request) {
  const session = await getServerSession(authOptions);
  if (!session?.user?.id || session.user.role !== "ADMIN") {
    return new NextResponse("Forbidden", { status: 403 });
  }

  const body = await req.json().catch(() => ({}));
  const {
    code, title, description, badgeImageUrl, startsAt, endsAt,
    metrics = [], reward = {},
  }: {
    code: string; title: string; description?: string; badgeImageUrl?: string;
    startsAt?: string; endsAt?: string; metrics?: MetricDTO[]; reward?: RewardDTO;
  } = body;

  if (!code || !title) return new NextResponse("code & title required", { status: 400 });

  const normalized = (Array.isArray(metrics) ? metrics : [])
    .map((m, i) => ({
      label: (m.label || "").trim() || m.param,
      param: String(m.param || "").trim(),
      target: Number(m.target || 0),
      start: m.start != null ? Number(m.start) : null,
      window: (m.window ?? "event").toUpperCase() as "EVENT" | "MONTH" | "WEEK" | "ROLLING30D",
      color: m.color || null,
      order: m.order ?? i,
    }))
    .filter(m => m.param && m.target > 0);

  const rewardType =
    reward?.type === "discount_percent" ? "DISCOUNT_PERCENT" :
      reward?.type === "badge"            ? "BADGE" :
        reward?.type === "text"             ? "TEXT" : "NONE";

  const event = await prisma.achievementEvent.create({
    data: {
      code,
      title,
      description: description ?? null,
      badgeImageUrl: badgeImageUrl ?? null,
      startsAt: startsAt ? new Date(startsAt) : null,
      endsAt:   endsAt   ? new Date(endsAt)   : null,
      rewardType,
      rewardValue: reward?.value != null ? Math.round(Number(reward.value)) : null,
      rewardDescription: reward?.description ?? null,
      metrics: {
        create: normalized.map(m => ({
          label: m.label, param: m.param, target: m.target,
          start: m.start, window: m.window, color: m.color, order: m.order,
        })),
      },
    },
    select: { id: true },
  });

  return NextResponse.json({ id: event.id });
}
