import Imap from "imap";
import type { ParsedMail } from "mailparser";
import { simpleParser } from "mailparser";
import fs from "fs/promises";
import path from "path";
import { env } from "~/env";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";

// Define types for Imap callback parameters
type ImapError = Error | null;
type ImapBox = Imap.Box;
type ImapMessage = Imap.ImapMessage;
type ImapSearchResults = number[];

const imapConfig = {
  user: env.VOLTEGO_MATCHER_EMAIL_USER,
  password: env.VOLTEGO_MATCHER_EMAIL_PASSWORD,
  host: env.VOLTEGO_MATCHER_EMAIL_HOST,
  port: env.VOLTEGO_MATCHER_EMAIL_PORT,
  tls: true,
};

const imap = new Imap(imapConfig);

const pdfDir = env.VOLTEGO_MATCHER_PDF_SAVE_FOLDER;

async function ensureDirectoryExists(dir: string) {
  try {
    await fs.access(dir);
  } catch {
    await fs.mkdir(dir, { recursive: true });
  }
}

function openInbox(): Promise<ImapBox> {
  return new Promise((resolve, reject) => {
    imap.openBox("INBOX", false, (err: ImapError, box: ImapBox) => {
      if (err) {
        reject(err);
      } else {
        resolve(box);
      }
    });
  });
}

function searchEmails(sinceDateString: string): Promise<ImapSearchResults> {
  return new Promise((resolve, reject) => {
    imap.search(
      [
        ["FROM", "<EMAIL>"],
        ["SINCE", sinceDateString],
      ],
      (err: ImapError, results: ImapSearchResults) => {
        if (err) {
          reject(err);
        } else {
          resolve(results);
        }
      },
    );
  });
}

async function handleMessage(msg: ImapMessage, seqno: number, feedback: any[]): Promise<void> {
  console.log("Nachricht #%d", seqno);
  const chunks: Uint8Array[] = [];
  msg.on("body", (stream) => {
    stream.on("data", (chunk) => chunks.push(chunk));
    stream.on("end", async () => {
      const buffer = Buffer.concat(chunks);
      try {
        const mail: ParsedMail = await simpleParser(buffer);
        if (mail.attachments) {
          for (const attachment of mail.attachments) {
            const filename = attachment.filename || `unknown_${Date.now()}.pdf`;
            if (attachment.contentType === "application/pdf") {
              console.log("PDF gefunden: " + filename);
              const filePath = path.join(pdfDir, filename);
              await fs.writeFile(filePath, attachment.content);
              console.log(`PDF gespeichert: ${filePath}`);
              feedback.push({ seqno, filename, status: "saved", filePath });
            }
          }
        }
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : "Unbekannter Fehler";
        {
      const msg = `Fehler beim Parsen der E-Mail: ${errorMessage}`;
      Logger(msg, "API 500", "src/app/api/voltego/matcher/fetchMails.ts", LogType.ERROR);
    }
        feedback.push({ seqno, error: errorMessage });
      }
    });
  });
}

async function fetchMails(): Promise<{ status: string; message: string; feedback: any[] }> {
  const feedback: any[] = [];
  await ensureDirectoryExists(pdfDir);

  return new Promise((resolve, reject) => {
    imap.once("ready", async () => {
      try {
        await openInbox();
        const daysBack = env.VOLTEGO_MATCHER_DAYS_BACK;
        const sinceDate = new Date();
        sinceDate.setDate(sinceDate.getDate() - daysBack);
        const sinceDateString = sinceDate.toISOString().split("T")[0] || "1970-01-01"; // Provide a default value
        console.log("Suche nach E-Mails von", sinceDateString, "bis heute.");
        const results = await searchEmails(sinceDateString);
        if (!results.length) {
          const message = `Keine E-<NAME_EMAIL> seit den letzten ${daysBack} Tagen gefunden.`;
          console.log(message);
          imap.end();
          resolve({ status: "success", message, feedback });
          return;
        }
        const f = imap.fetch(results, { bodies: "", struct: true });
        f.on("message", (msg, seqno) => {
          handleMessage(msg, seqno, feedback).catch((err) =>
            reject({ status: "error", message: err.message, feedback }),
          );
        });
        f.once("error", (err: ImapError) => {
          const errorMessage = err instanceof Error ? err.message : "Unbekannter Fehler";
          console.log("Fetch-Fehler: " + errorMessage);
          reject({ status: "error", message: errorMessage, feedback });
        });
        f.once("end", () => {
          console.log("Fertig mit dem Abrufen von Nachrichten.");
          imap.end();
          resolve({ status: "success", message: "E-Mails erfolgreich abgerufen.", feedback });
        });
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : "Unbekannter Fehler";
        reject({ status: "error", message: errorMessage, feedback });
      }
    });

    imap.once("error", (err: ImapError) => {
      const errorMessage = err instanceof Error ? err.message : "Unbekannter Fehler";
      console.log("Verbindungsfehler: " + errorMessage);
      reject({ status: "error", message: "Verbindungsfehler.", feedback });
    });

    imap.once("end", () => {
      console.log("Verbindung zum Mailserver beendet.");
    });

    imap.connect();
  });
}

export { fetchMails };
