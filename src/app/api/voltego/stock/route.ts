import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import type { ApiResponse } from "~/types/api/apiType";
import getExchangeElectricityPrice, { COPY_MODE } from "~/server/task/getExchangeElectricityPrice";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";

export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    // Extract copy_mode from query parameters
    const { searchParams } = new URL(request.url);
    let copy_mode: COPY_MODE | undefined = searchParams.get("copy_mode") as COPY_MODE;

    // Validate copy_mode and set default if necessary
    if (copy_mode !== COPY_MODE.FULL && copy_mode !== COPY_MODE.DELTA) {
      copy_mode = COPY_MODE.DELTA;
    }

    // Fetch the stock prices
    const result = await getExchangeElectricityPrice(copy_mode);

    // Return success response
    return NextResponse.json({
      status: "success",
      message: "Successfully imported stock prices from Voltego.",
      payload: result,
    } as ApiResponse<any>);
  } catch (error) {
    // Return error response
    const errorMessage = error instanceof Error ? error.message : "Unknown error";
    return NextResponse.json(
      {
        status: "error",
        message: `Failed to import stock prices: ${errorMessage}`,
      } as ApiResponse<null>,
      { status: 500 },
    );
  }
}
