import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role } from "@prisma/client";
import prisma from "~/server/db/prisma";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";

export async function GET(request: NextRequest) {
  const session = await getServerSession(authOptions);
  
  if (!session || (session?.user?.role !== Role.ADMIN && session?.user?.role !== Role.CPO)) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const categories = await prisma.maintenanceCategory.findMany({
      orderBy: [
        { isDefault: 'desc' },
        { name: 'asc' }
      ],
      include: {
        _count: {
          select: { maintenanceRecords: true }
        }
      }
    });

    return NextResponse.json(categories);
  } catch (error) {
    {
      const msg = "Error fetching maintenance categories:";
      const errorMsg = (error as Error)?.message || String(error);
      Logger(`${msg}: ${errorMsg}`, "API 500", "src/app/api/maintenance/categories/route.ts", LogType.ERROR);
    }
    Logger("API 500 Error", "API 500", "src/app/api/maintenance/categories/route.ts", LogType.ERROR);
    return NextResponse.json(
      { error: "Failed to fetch maintenance categories" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  const session = await getServerSession(authOptions);
  
  if (!session || (session?.user?.role !== Role.ADMIN && session?.user?.role !== Role.CPO)) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const { name, description } = await request.json();

    if (!name) {
      return NextResponse.json(
        { error: "Name is required" },
        { status: 400 }
      );
    }

    // Check if category already exists
    const existingCategory = await prisma.maintenanceCategory.findUnique({
      where: { name }
    });

    if (existingCategory) {
      return NextResponse.json(
        { error: "Category with this name already exists" },
        { status: 409 }
      );
    }

    const category = await prisma.maintenanceCategory.create({
      data: {
        name,
        description,
        isDefault: false
      }
    });

    return NextResponse.json(category, { status: 201 });
  } catch (error) {
    {
      const msg = "Error creating maintenance category:";
      const errorMsg = (error as Error)?.message || String(error);
      Logger(`${msg}: ${errorMsg}`, "API 500", "src/app/api/maintenance/categories/route.ts", LogType.ERROR);
    }
    Logger("API 500 Error", "API 500", "src/app/api/maintenance/categories/route.ts", LogType.ERROR);
    return NextResponse.json(
      { error: "Failed to create maintenance category" },
      { status: 500 }
    );
  }
}
