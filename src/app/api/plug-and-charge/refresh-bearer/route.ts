import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role } from "@prisma/client";
import { env } from "~/env";

export async function POST(request: NextRequest) {
  try {
    // Check authentication and admin role
    const session = await getServerSession(authOptions);
    if (!session?.user || session.user.role !== Role.ADMIN) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Prepare the request body for the auth endpoint
    const authBody = {
      client_id: env.PLUG_AND_CHARGE_CLIENT_ID,
      client_secret: env.PLUG_AND_CHARGE_CLIENT_SECRET,
      audience: "https://solutions.hubject.com",
      grant_type: "client_credentials",
      scope: "pnc-remote-event"
    };

    // Make request to auth endpoint
    const authResponse = await fetch(env.PLUG_AND_CHARGE_AUTH_URL, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(authBody),
    });

    if (!authResponse.ok) {
      const errorText = await authResponse.text();
      return NextResponse.json(
        { 
          error: "Failed to fetch bearer token", 
          details: `${authResponse.status}: ${errorText}` 
        }, 
        { status: 500 }
      );
    }

    const authData = await authResponse.json();
    
    if (!authData.access_token) {
      return NextResponse.json(
        { error: "No access token received from auth endpoint" },
        { status: 500 }
      );
    }

    // Update the runtime environment variable (not the .env file)
    process.env.PLUG_AND_CHARGE_24H_DEV_BEARER = authData.access_token;

    return NextResponse.json({
      success: true,
      message: "Bearer token refreshed successfully (runtime only)",
      token_preview: `${authData.access_token.substring(0, 20)}...`,
      expires_in: authData.expires_in || "unknown"
    });

  } catch (error) {
    console.error("Error refreshing bearer token:", error);
    return NextResponse.json(
      { 
        error: "Internal server error", 
        details: error instanceof Error ? error.message : "Unknown error" 
      }, 
      { status: 500 }
    );
  }
}
