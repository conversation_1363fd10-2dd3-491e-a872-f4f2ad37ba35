import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role } from "@prisma/client";
import { env } from "~/env";

export async function POST(request: NextRequest) {
  try {
    // Check authentication and admin role
    const session = await getServerSession(authOptions);
    if (!session?.user || session.user.role !== Role.ADMIN) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Create test outgoing event data
    const testEventData = {
      location: {
        latitude: 52.5200,
        longitude: 13.4050,
      },
      plugInEventTimestamp: Math.floor(Date.now() / 1000), // Current timestamp in seconds
      evseid: "DE*EUL*E9999*01", // Test EVSE ID
    };

    // Send test request to plug and charge API
    const testResponse = await fetch(
      env.PLUG_AND_CHAGE_DOMAIN + env.PLUG_AND_CHARGE_SEND_EVENT_SLUG,
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${process.env.PLUG_AND_CHARGE_24H_DEV_BEARER}`,
        },
        body: JSON.stringify(testEventData),
      }
    );

    const responseText = await testResponse.text();
    let responseData;
    
    try {
      responseData = JSON.parse(responseText);
    } catch {
      responseData = { raw_response: responseText };
    }

    if (testResponse.ok) {
      // Success (any 2xx status) - check if we got a UUID
      const hasUuid = responseData.uuid && typeof responseData.uuid === 'string';

      return NextResponse.json({
        success: hasUuid,
        status: testResponse.status,
        message: hasUuid ? "✅ Token gültig - UUID erhalten" : "❌ Token gültig aber keine UUID erhalten",
        uuid: responseData.uuid || null,
        response_data: responseData,
      });
    } else if (testResponse.status === 401) {
      // Unauthorized - token invalid
      return NextResponse.json({
        success: false,
        status: 401,
        message: "❌ Token ungültig - 401 Unauthorized",
        response_data: responseData,
      });
    } else {
      // Other error
      return NextResponse.json({
        success: false,
        status: testResponse.status,
        message: `❌ API Fehler - ${testResponse.status}: ${testResponse.statusText}`,
        response_data: responseData,
      });
    }

  } catch (error) {
    console.error("Error testing bearer token:", error);
    return NextResponse.json(
      { 
        success: false,
        error: "Internal server error", 
        details: error instanceof Error ? error.message : "Unknown error" 
      }, 
      { status: 500 }
    );
  }
}
