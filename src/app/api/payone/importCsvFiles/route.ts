import type { NextRequest } from "next/server";

import Logger from "~/server/logger/logger";
import { env } from "~/env";
import { LogType } from "@prisma/client";
import { findAllCsvFiles, importEnerchargeSessionCSV } from "~/utils/csv/enerchargeSessionCSV";
import { importPayoneCSVFiles } from "~/utils/csv/payoneSessionCSV";
export const dynamic = "force-dynamic";
export async function GET(request: NextRequest) {
  //todo auth
  Logger(
    `Look for PayOne CSV Files in ${env.PAYONE_CSV_FOLDER}`,
    "Fetch stock price from voltego",
    "cron",
    LogType.DEBUG,
  );
  try {
    const allCsv = findAllCsvFiles(env.PAYONE_CSV_FOLDER);
    for (const csvpath of allCsv) {
      await importPayoneCSVFiles(csvpath);
    }

    return new Response("ok", { status: 200 });
  } catch (e) {
    Logger("API 500 Error", "API 500", "src/app/api/payone/importCsvFiles/route.ts", LogType.ERROR);
    return new Response("cant import", { status: 500 });
  }
}
