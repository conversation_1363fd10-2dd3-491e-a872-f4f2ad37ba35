import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import prisma from "~/server/db/prisma";
import { getOusBelowOu } from "~/server/model/ou/func";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";


export const dynamic = "force-dynamic";

export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized", message: "Nicht authentifiziert" },
        { status: 401 },
      );
    }

    // Check if OU ID is provided as query parameter
    const { searchParams } = new URL(request.url);
    const queryOuId = searchParams.get("ouId");

    // Determine which OU to use
    let targetOu;
    if (queryOuId) {
      // Verify user has access to the requested OU
      const accessibleOus = await getOusBelowOu(session.user.ou);
      targetOu = accessibleOus.find((ou) => ou.id === queryOuId);
      if (!targetOu) {
        return NextResponse.json(
          { error: "Forbidden", message: "Keine Berechtigung für diese OU" },
          { status: 403 },
        );
      }
    } else {
      targetOu = session.user.selectedOu;
    }

    if (!targetOu) {
      return NextResponse.json(
        { error: "Bad Request", message: "Keine OU zugeordnet" },
        { status: 400 },
      );
    }

    const accessibleOus = await getOusBelowOu(targetOu);
    const ouIds = accessibleOus.map((ou) => ou.id);

    // Fetch charge points from accessible OUs
    const chargePoints = await prisma.chargePoint.findMany({
      where: {
        ouId: {
          in: ouIds,
        },
      },
      select: {
        chargePointId: true,
        displayName: true,
      },
      orderBy: [{ displayName: "asc" }, { chargePointId: "asc" }],
    });

    return NextResponse.json(chargePoints);
  } catch (error) {
    {
      const msg = "Internal Server Error - Fehler beim Laden der Ladepunkte";
      const errorMsg = (error as Error)?.message || String(error);
      Logger(`${msg}: ${errorMsg}`, 'API 500', 'src/app/api/chargepoints/route.ts', LogType.ERROR);
    }
    return NextResponse.json(
      {
        error: "Internal Server Error",
        message: "Fehler beim Laden der Ladepunkte",
      },
      { status: 500 },
    );
  }
}
