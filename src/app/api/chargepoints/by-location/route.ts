import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import prisma from "~/server/db/prisma";
import { getOusBelowOu } from "~/server/model/ou/func";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";


export const dynamic = "force-dynamic";

export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized", message: "Nicht authentifiziert" },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const locationId = searchParams.get("locationId");

    if (!locationId) {
      return NextResponse.json(
        { error: "Bad Request", message: "Location ID ist erforderlich" },
        { status: 400 }
      );
    }

    // Verify user has access to the location
    const accessibleOus = await getOusBelowOu(session.user.selectedOu);
    const ouIds = accessibleOus.map((ou) => ou.id);

    // Check if location exists and user has access
    const location = await prisma.location.findFirst({
      where: {
        id: locationId,
        ouId: {
          in: ouIds,
        },
      },
    });

    if (!location) {
      return NextResponse.json(
        { error: "Forbidden", message: "Keine Berechtigung für diesen Standort" },
        { status: 403 }
      );
    }

    // Get charge points for this location via EVSEs
    const evses = await prisma.evse.findMany({
      where: {
        locationId: locationId,
        chargePointId: {
          not: null,
        },
      },
      include: {
        chargePoint: {
          select: {
            chargePointId: true,
            displayName: true,
          },
        },
      },
    });

    // Extract unique charge points
    const chargePointsMap = new Map();
    evses.forEach((evse) => {
      if (evse.chargePoint) {
        chargePointsMap.set(evse.chargePoint.chargePointId, {
          chargePointId: evse.chargePoint.chargePointId,
          displayName: evse.chargePoint.displayName,
        });
      }
    });

    const chargePoints = Array.from(chargePointsMap.values()).sort((a, b) => {
      return a.displayName.localeCompare(b.displayName);
    });

    return NextResponse.json(chargePoints);
  } catch (error) {
    {
      const msg = "Internal Server Error - Fehler beim Laden der Ladepunkte";
      const errorMsg = (error as Error)?.message || String(error);
      Logger(`${msg}: ${errorMsg}`, 'API 500', 'src/app/api/chargepoints/by-location/route.ts', LogType.ERROR);
    }
    return NextResponse.json(
      {
        error: "Internal Server Error",
        message: "Fehler beim Laden der Ladepunkte",
      },
      { status: 500 }
    );
  }
}
