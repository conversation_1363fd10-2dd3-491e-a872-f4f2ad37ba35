import type { NextRequest } from "next/server";
import prisma from "../../../server/db/prisma";
import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role, StateOfInvoice, KindOfInvoice } from "@prisma/client";
import { getOusBelowOu } from "~/server/model/ou/func";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";

export type MonthlyOutstanding = {
  month: string; // Format: "2024-01"
  monthName: string; // Format: "Januar 2024"
  totalAmount: number;
  invoiceCount: number;
};

export type FinanceDashboardData = {
  emp: {
    totalAmount: number;
    invoices: FinanceDashboardInvoice[];
  };
  cpo: {
    totalAmount: number;
    invoices: FinanceDashboardInvoice[];
  };
  employee: {
    totalAmount: number;
    invoices: FinanceDashboardInvoice[];
  };
  monthlyBreakdown: MonthlyOutstanding[];
};

export type CPOInvoiceDetail = {
  id: string;
  invoiceNumber: string | null;
  invoiceDate: Date | null;
  sumGross: number;
  kindOfInvoice: string;
  isOnboarding: boolean;
  positions: {
    title: string;
    description: string | null;
    amount: number;
    unitPrice: number;
    sumGross: number;
  }[];
};

export type CPORevenueData = {
  cpoId: string;
  cpoName: string;
  totalCreditNotes: number;
  totalInvoiced: number;
  onboardingRevenue: number;
  recurringRevenue: number;
  yearForecast: number;
  monthlyBreakdown: {
    month: string;
    revenue: number;
    creditNotes: number;
    invoiced: number;
    recurringRevenue: number;
  }[];
  invoiceDetails: CPOInvoiceDetail[];
  creditNoteDetails: CPOInvoiceDetail[];
};

export type CPORevenueDashboardData = {
  cpos: CPORevenueData[];
  totalCreditNotes: number;
  totalInvoiced: number;
  totalOnboarding: number;
  totalRecurring: number;
  globalMonthlyBreakdown: {
    month: string;
    totalRevenue: number;
    totalCreditNotes: number;
    totalInvoiced: number;
    totalRecurringRevenue: number;
    totalOnboardingRevenue: number;
  }[];
  globalYearForecast: number;
};

export type FinanceDashboardInvoice = {
  id: string;
  invoiceNumber: string | null;
  sumGross: number;
  invoiceDate: Date | null;
  sentDate: Date | null;
  sentTo: string | null;
  daysSinceSent: number | null;
  contactName: string | null;
  userName: string | null;
  contractName: string | null;
  urgencyLevel: "normal" | "warning" | "urgent" | "critical";
  hasStripePayment: boolean;
};

function calculateUrgencyLevel(
  daysSinceSent: number | null,
): "normal" | "warning" | "urgent" | "critical" {
  if (daysSinceSent === null) return "normal";
  if (daysSinceSent > 60) return "critical";
  if (daysSinceSent > 30) return "urgent";
  if (daysSinceSent > 14) return "warning";
  return "normal";
}

function calculateDaysSinceSent(invoiceDate: Date | null): number | null {
  if (!invoiceDate) return null;
  const now = new Date();
  const diffTime = Math.abs(now.getTime() - invoiceDate.getTime());
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
}

function getMonthKey(date: Date): string {
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, "0")}`;
}

function getMonthName(date: Date): string {
  const monthNames = [
    "Januar",
    "Februar",
    "März",
    "April",
    "Mai",
    "Juni",
    "Juli",
    "August",
    "September",
    "Oktober",
    "November",
    "Dezember",
  ];
  return `${monthNames[date.getMonth()]} ${date.getFullYear()}`;
}

export async function GET(request: NextRequest) {
  const session = await getServerSession(authOptions);

  if (!session || session?.user?.role !== Role.ADMIN) {
    return new Response("no auth", { status: 401 });
  }

  try {
    // Get user's OU and all OUs below it
    const userOuId = session.user.selectedOu?.id;
    let ouIds: string[] = [];

    if (userOuId) {
      const ous = await getOusBelowOu(userOuId);
      ouIds = ous.map((ou) => ou.id).filter((id) => id !== undefined && id !== null);
    }

    // Build where clause
    const whereClause: any = {
      stateOfInvoice: StateOfInvoice.CREATED,
      paidOnDate: null,
      sendAsMail: true,
      // Exclude cancelled invoices and storno invoices
      kindOfInvoice: {
        notIn: [KindOfInvoice.STORNO, KindOfInvoice.CREDIT_STORNO],
      },
      // Exclude invoices that have been cancelled by a storno
      invoiceChilds: {
        none: {
          kindOfInvoice: {
            in: [KindOfInvoice.STORNO, KindOfInvoice.CREDIT_STORNO],
          },
        },
      },
      // Exclude invoices with successful Stripe payments
      AND: [
        {
          OR: [
            {
              paymentIntent: null // No payment intent
            },
            {
              paymentIntent: {
                status: {
                  not: "succeeded"
                }
              }
            }
          ]
        }
      ]
    };

    // Only add OU filter if we have valid OU IDs
    if (ouIds.length > 0) {
      whereClause.AND.push({
        OR: [
          {
            contact: {
              ouId: {
                in: ouIds,
              },
            },
          },
          {
            user: {
              ouId: {
                in: ouIds,
              },
            },
          },
          {
            contract: {
              contact: {
                ouId: {
                  in: ouIds,
                },
              },
            },
          },
        ]
      });
    }

    // Get all unpaid invoices that are CREATED (sent) but not PAID
    const unpaidInvoices = await prisma.invoice.findMany({
      where: whereClause,
      include: {
        contact: true,
        user: true,
        contract: {
          include: {
            contact: true,
          },
        },
        paymentIntent: true,
      },
      orderBy: {
        invoiceDate: "desc",
      },
    });

    const empInvoices: FinanceDashboardInvoice[] = [];
    const cpoInvoices: FinanceDashboardInvoice[] = [];
    const employeeInvoices: FinanceDashboardInvoice[] = [];
    const monthlyMap = new Map<
      string,
      { totalAmount: number; invoiceCount: number; monthName: string }
    >();

    for (const invoice of unpaidInvoices) {
      const daysSinceSent = calculateDaysSinceSent(invoice.invoiceDate);
      const urgencyLevel = calculateUrgencyLevel(daysSinceSent);

      // Determine recipient based on invoice type
      let sentTo: string | null = null;
      if (invoice.userId && invoice.user?.email) {
        sentTo = invoice.user.email;
      } else if (invoice.contactId && invoice.contact?.invoiceMail) {
        sentTo = invoice.contact.invoiceMail;
      }

      const dashboardInvoice: FinanceDashboardInvoice = {
        id: invoice.id,
        invoiceNumber: invoice.invoiceNumber,
        sumGross: invoice.sumGross,
        invoiceDate: invoice.invoiceDate,
        sentDate: invoice.invoiceDate, // Use invoice date as sent date since sendAsMail is true
        sentTo,
        daysSinceSent,
        contactName: invoice.contact?.companyName || invoice.contact?.name || null,
        userName: invoice.user ? `${invoice.user.name} ${invoice.user.lastName}` : null,
        contractName: invoice.contract?.name || null,
        urgencyLevel,
        hasStripePayment: !!invoice.paymentIntent,
      };

      // Add to monthly breakdown
      if (invoice.invoiceDate) {
        const monthKey = getMonthKey(invoice.invoiceDate);
        const monthName = getMonthName(invoice.invoiceDate);

        if (monthlyMap.has(monthKey)) {
          const existing = monthlyMap.get(monthKey)!;
          existing.totalAmount += invoice.sumGross;
          existing.invoiceCount += 1;
        } else {
          monthlyMap.set(monthKey, {
            totalAmount: invoice.sumGross,
            invoiceCount: 1,
            monthName,
          });
        }
      }

      // Categorize by contract type
      if (invoice.userId) {
        // Employee/Club invoice
        employeeInvoices.push(dashboardInvoice);
      } else if (invoice.contactId && invoice.contact?.cpo) {
        // CPO invoice (contact with cpo=true)
        cpoInvoices.push(dashboardInvoice);
      } else if (invoice.contactId && !invoice.contact?.cpo) {
        // EMP invoice (contact but not CPO)
        empInvoices.push(dashboardInvoice);
      }
    }

    // Convert monthly map to sorted array
    const monthlyBreakdown: MonthlyOutstanding[] = Array.from(monthlyMap.entries())
      .map(([month, data]) => ({
        month,
        monthName: data.monthName,
        totalAmount: data.totalAmount,
        invoiceCount: data.invoiceCount,
      }))
      .sort((a, b) => b.month.localeCompare(a.month)); // Sort by month descending (newest first)

    const result: FinanceDashboardData = {
      emp: {
        totalAmount: empInvoices.reduce((sum, inv) => sum + inv.sumGross, 0),
        invoices: empInvoices,
      },
      cpo: {
        totalAmount: cpoInvoices.reduce((sum, inv) => sum + inv.sumGross, 0),
        invoices: cpoInvoices,
      },
      employee: {
        totalAmount: employeeInvoices.reduce((sum, inv) => sum + inv.sumGross, 0),
        invoices: employeeInvoices,
      },
      monthlyBreakdown,
    };

    return NextResponse.json(result);
  } catch (error) {
    {
      const msg = "Error fetching finance dashboard data:";
      const errorMsg = (error as Error)?.message || String(error);
      Logger(`${msg}: ${errorMsg}`, "API 500", "src/app/api/finance-dashboard/route.ts", LogType.ERROR);
    }
    Logger("API 500 Error", "API 500", "src/app/api/finance-dashboard/route.ts", LogType.ERROR);
    return new Response("Internal Server Error", { status: 500 });
  }
}
