import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import prismaMongo from "~/server/db/mongo";


type UpdateBody = {
  energy_price?: number | string;
  session_fee?: number | string;
  tax_rate?: number | string;
  blocking_fee?: number | string;
  blocking_fee_max?: number | string;
  blocking_fee_start?: number | string;
  start?: string | Date;
  end?: string | Date;
};

const asFloat = (v: unknown, fallback: number) =>
  v === undefined || v === null ? fallback : (typeof v === "string" ? parseFloat(v) : (v as number));

const asInt = (v: unknown, fallback: number) =>
  v === undefined || v === null ? fallback : (typeof v === "string" ? parseInt(v, 10) : (v as number));

export async function PATCH(
  req: NextRequest,
  { params }: { params: { id: string } }
) {
  const session = await getServerSession(authOptions);
  if (!session?.user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  const id = params.id;
  if (!id) return NextResponse.json({ error: "Missing id" }, { status: 400 });

  try {
    const body = (await req.json()) as UpdateBody;

    const existing = await prismaMongo.locationPrice.findUnique({
      where: { id },
      select: {
        id: true,
        start: true,
        end: true,
        energy_price: true,
        session_fee: true,
        tax_rate: true,
        blocking_fee: true,
        blocking_fee_max: true,
        blocking_fee_start: true,
        current_type: true,
        location: { select: { id: true } },
        Emp: { select: { id: true } },
      },
    });

    if (!existing) {
      return NextResponse.json({ error: "Nicht gefunden" }, { status: 404 });
    }

    const locationId = existing.location?.id;
    const empId = existing.Emp?.id;
    if (!locationId || !empId) {
      return NextResponse.json(
        { error: "Location oder Emp fehlen am Datensatz." },
        { status: 400 }
      );
    }
    const current_type = existing.current_type;

    // Zeit-Checks (Start standardmäßig heute, falls nicht mitgegeben)
    const now = new Date();
    const ALLOWED_LAG_MINUTES = 10;
    const minStart = new Date(now.getTime() - ALLOWED_LAG_MINUTES * 60 * 1000);

    const newStart = body.start ? new Date(body.start) : new Date(); // Default heute
    const newEnd = body.end ? new Date(body.end) : new Date(existing.end);

    if (isNaN(newStart.getTime()) || isNaN(newEnd.getTime())) {
      return NextResponse.json({ error: "Start/End ist kein valides Datum." }, { status: 400 });
    }
    if (newStart < minStart) {
      return NextResponse.json({ error: "Start-Datum darf nicht in der Vergangenheit liegen." }, { status: 400 });
    }
    if (!(newEnd > newStart)) {
      return NextResponse.json({ error: "End-Datum muss nach dem Start-Datum liegen." }, { status: 400 });
    }

    // Numerik aus Request (Strings -> Zahlen)
    const energy_price = asFloat(body.energy_price, existing.energy_price);
    const session_fee = asFloat(body.session_fee, existing.session_fee);
    const tax_rate = asFloat(body.tax_rate, existing.tax_rate);
    const blocking_fee = asFloat(body.blocking_fee, existing.blocking_fee);
    const blocking_fee_max = asFloat(body.blocking_fee_max, existing.blocking_fee_max);
    const blocking_fee_start = asInt(body.blocking_fee_start, existing.blocking_fee_start);

    const numericInvalid =
      [energy_price, session_fee, tax_rate, blocking_fee, blocking_fee_max].some((n) => Number.isNaN(n)) ||
      Number.isNaN(blocking_fee_start);

    if (numericInvalid) {
      return NextResponse.json({ error: "Ungültige numerische Werte im Request." }, { status: 400 });
    }

    // Overlap-Check mit Geschwistern (gleiche Location/Emp/Typ, sich selbst ausschließen)
    const siblings = await prismaMongo.locationPrice.findMany({
      where: {
        id: { not: id },
        location: { is: { id: locationId } },
        Emp: { is: { id: empId } },
        current_type,
      },
      select: { id: true, start: true, end: true },
    });

    const overlaps = siblings.some((s) => {
      const sStart = new Date(s.start);
      const sEnd = new Date(s.end);
      return newStart < sEnd && newEnd > sStart; // Randberührung erlaubt
    });
    if (overlaps) {
      return NextResponse.json(
        { error: "Zeiträume dürfen sich nicht überlappen (gleiche Location/Emp/Stromtyp)." },
        { status: 400 }
      );
    }

    const updated = await prismaMongo.locationPrice.update({
      where: { id },
      data: {
        energy_price,
        session_fee,
        tax_rate,
        blocking_fee,
        blocking_fee_max,
        blocking_fee_start,
        start: newStart,
        end: newEnd,
      },
    });

    return NextResponse.json(updated, { status: 200 });
  } catch (err) {
    const msg = (err as any)?.message || String(err);
    return NextResponse.json({ error: msg }, { status: 500 });
  }
}


export async function DELETE(
  _req: Request,
  { params }: { params: { id: string } }
) {
  try {

    const session = await getServerSession(authOptions);
    if (!session) return NextResponse.json({ error: "Unauthorized" }, { status: 401 });


    const id = params.id;
    await prismaMongo.locationPrice.delete({ where: { id } });

    // 204 = No Content
    return new NextResponse(null, { status: 204 });
  } catch (err: any) {
    if (err.code === "P2025") {
      // Prisma: Record not found
      return NextResponse.json({ error: "Not found" }, { status: 404 });
    }
    if (err instanceof NextResponse) return err;
    return NextResponse.json({ error: err?.message ?? "Delete failed" }, { status: 500 });
  }
}