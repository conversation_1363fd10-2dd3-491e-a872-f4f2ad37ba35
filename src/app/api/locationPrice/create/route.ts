import { NextRequest, NextResponse } from "next/server";
import prismaMongo from "~/server/db/mongo";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { locationPriceSchema } from "~/app/api/locationPrice/locationPriceSchema";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";
import { cleanPrismaCreateData } from "~/server/utils/prismaCreateDataCleaner";

export async function POST(req: NextRequest) {
  const session = await getServerSession(authOptions);
  if (!session?.user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const raw = await req.json();

    // Create only: id darf nicht mitkommen
    const validData = locationPriceSchema.omit({ id: true }).parse(raw);

    const now = new Date();
    const ALLOWED_LAG_MINUTES = 10;
    const minStart = new Date(now.getTime() - ALLOWED_LAG_MINUTES * 60 * 1000);

    const newStart = new Date(validData.start);
    const newEnd = new Date(validData.end);

    // Basis-Validierungen
    if (newStart < minStart) {
      return NextResponse.json(
        { error: "Start-Datum darf nicht in der Vergangenheit liegen." },
        { status: 400 }
      );
    }
    if (!(newEnd > newStart)) {
      return NextResponse.json(
        { error: "End-Datum muss nach dem Start-Datum liegen." },
        { status: 400 }
      );
    }

    // Bestehende Preise dieser Location/EMP laden
    const siblings = await prismaMongo.locationPrice.findMany({
      where: {
        locationId: validData.locationId,
        empId: validData.empId,
      },
      select: { id: true, current_type: true, start: true, end: true },
    });

    // a) Max. 2 (AC & DC) insgesamt
    const hasAC = siblings.some(s => s.current_type === "AC");
    const hasDC = siblings.some(s => s.current_type === "DC");
    if ((hasAC && hasDC) ||
      (validData.current_type === "AC" && hasAC) ||
      (validData.current_type === "DC" && hasDC)) {
      return NextResponse.json(
        { error: "Für diesen Standort ist dieser Stromtyp bereits vorhanden (oder beide Typen sind schon angelegt)." },
        { status: 400 }
      );
    }

    // b) Keine Zeit-Überlappung innerhalb desselben Typs (Randberührung erlaubt)
    const sameType = siblings.filter(s => s.current_type === validData.current_type);
    const overlaps = sameType.some(s => {
      const sStart = new Date(s.start);
      const sEnd = new Date(s.end);
      return newStart < sEnd && newEnd > sStart;
    });
    if (overlaps) {
      return NextResponse.json(
        { error: "Zeiträume dürfen sich nicht überlappen (gleiche Location/Emp/Stromtyp)." },
        { status: 400 }
      );
    }

    const created = await prismaMongo.locationPrice.create({
      data: cleanPrismaCreateData({
        ...validData,
        start: newStart,
        end: newEnd,
      }),
    });

    return NextResponse.json(created);
  } catch (err) {
    const msg = (err as any)?.message || String(err);
    Logger(`❌ POST /api/locationPrice/create: ${msg}`, "API 500", "src/app/api/locationPrice/create/route.ts", LogType.ERROR);

    if (err && typeof err === "object" && "issues" in (err as any)) {
      return NextResponse.json({ error: "Validation failed", details: err }, { status: 400 });
    }
    return NextResponse.json({ error: "Interner Fehler beim Create" }, { status: 500 });
  }
}
