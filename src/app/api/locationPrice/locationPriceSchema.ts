// src/lib/validation/locationPriceSchema.ts

import { z } from "zod";

export const locationPriceSchema = z.object({
  id:z.string().optional(),
  locationId: z.string().min(1, "LocationId ist erforderlich"),
  empId: z.string().min(1, "EmpId ist erforderlich"),
  energy_price: z.coerce.number().nonnegative(),
  blocking_fee: z.coerce.number().nonnegative(),
  blocking_fee_start: z.coerce.number().int().nonnegative(),
  blocking_fee_max: z.coerce.number().nonnegative(),
  session_fee: z.coerce.number().nonnegative(),
  tax_rate: z.coerce.number().nonnegative(),
  start: z.coerce.date(),
  end: z.coerce.date(),
  current_type: z.enum(["AC", "DC"]),
});

// 💡 daraus wird ein Typ:
export type LocationPriceInput = z.infer<typeof locationPriceSchema>;
