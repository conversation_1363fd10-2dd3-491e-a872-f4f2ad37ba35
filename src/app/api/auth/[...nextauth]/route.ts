import type { NextAuthOptions } from "next-auth";
import NextAuth from "next-auth";
import prisma from "~/server/db/prisma";

import Cred<PERSON><PERSON><PERSON><PERSON>ider from "next-auth/providers/credentials";
import { hasher } from "~/server/hasher/hasher";
import { Role } from "@prisma/client";

export const authOptions: NextAuthOptions = {
  secret: process.env.JWT_SECRET,
  pages: {
    signIn: "/login",
    error: "/error",
  },
  session: {
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  // Configure one or more authentication providers
  providers: [
    CredentialsProvider({
      // The name to display on the sign in form (e.g. "Sign in with...")
      name: "Name / Passwort",
      // `credentials` is used to generate a form on the sign in page.
      // You can specify which fields should be submitted, by adding keys to the `credentials` object.
      // e.g. domain, username, password, 2FA token, etc.
      // You can pass any HTML attribute to the <input> tag through the object.
      credentials: {
        email: { label: "email", type: "text", placeholder: "" },
        password: { label: "password", type: "password" },
      },
      async authorize(credentials, req) {
        // Add logic here to look up the user from the credentials supplied
        if (!credentials?.password || !credentials.email) {
          return null;
        }

        const user = await prisma.user.findUnique({
          where: {
            email: credentials?.email,
          },
          include: {
            ou: true,
            selectedOu: true,
          },
        });
        const hashedPassword = await hasher(credentials?.password);
        if (user && user?.password == hashedPassword) {
          return user;
        }
        return null;
      },
    }),
  ],

  //debug: true,

  callbacks: {
    async session({ session, token, user }) {
      if (session?.user && session?.user?.email && token.ou) {
        const dbUser = await prisma.user.findUnique({
          where: {
            email: session.user.email,
          },
          include: {
            ou: true,
            selectedOu: true,
          },
        });
        //console.log("dbUser.selectedOu:", dbUser?.selectedOu);
        //console.log("dbUser:", dbUser);
        if (!dbUser) {
          return session;
        }
        if (dbUser.role == Role.CARD_HOLDER && !dbUser.emailVerified) {
          return session;
        }
        session.user = {
          ...session.user,
          name: dbUser.name,
          remoteStartToken: dbUser?.remoteStartToken ?? undefined,
          companyName: dbUser?.companyName,
          role: token.role,
          ou: token.ou,
          selectedOu: dbUser.selectedOu,
          lastName: dbUser.lastName,
          id: dbUser.id,
          password: "", // do not keep password in object
        };
        //console.log("Session-Objekt nach setzen:", session);
        // Add role value to user object so it is passed along with session
      }
      return session;
    },
    async jwt({ token, user, account, profile, isNewUser }) {
      // console.log("jwt callback")
      if (user) {
        token.role = user.role;
        token.ou = user.ou;
        token.selectedOu = user.selectedOu;
      }
      return token;
    },
    async signIn({ user, account, profile, email, credentials }) {
      console.log("signIn callback");
      return true;
    },
  },
};

const handler = NextAuth(authOptions);

export { handler as GET, handler as POST };
