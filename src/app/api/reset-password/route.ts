import { NextRequest } from "next/server";
import prisma from "~/server/db/prisma";
import { hasher } from "~/server/hasher/hasher";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";

export async function POST(request: NextRequest) {
  const { token, password, passwordRepeat } = await request.json();

  const user = await prisma.user.findFirst({
    where: { signUpHash: token },
  });

  if (!user) {
    return new Response("Fehler - Token ungültig oder bereits vewendet", { status: 404 });
  }
  if (user?.signUpHashValidUntil && user.signUpHashValidUntil < new Date()) {
    return new Response("Fehler - Token abgelaufen", { status: 401 });
  }
  if (password == passwordRepeat) {
    const hash = await hasher(password);
    try {
      await prisma.user.update({
        where: { id: user.id },
        data: {
          password: hash,
          signUpHash: null,
          signUpHashValidUntil: null,
        },
      });

      return new Response("Passwort wurde geändert", { status: 200 });
    } catch (error) {
      Logger("API 500 Error", "API 500", "src/app/api/reset-password/route.ts", LogType.ERROR);
    return new Response("Fehler beim Ändern", { status: 500 });
    }
  }
  Logger("API 500 Error", "API 500", "src/app/api/reset-password/route.ts", LogType.ERROR);
    return new Response("Passwörter stimmen", { status: 500 });
}
