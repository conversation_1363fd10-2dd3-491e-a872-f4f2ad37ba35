import type { NextRequest } from "next/server";
import prisma from "../../../server/db/prisma";
import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role, KindOfInvoice } from "@prisma/client";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";

export type ContactInvoiceOverview = {
  contactId: string;
  contactName: string;
  companyName: string | null;
  isCPO: boolean;
  monthlyInvoices: {
    [month: string]: {
      invoices: number;
      credits: number;
      hasInvoice: boolean;
      hasCredit: boolean;
    };
  };
};

export type InvoiceOverviewData = {
  cpoContacts: ContactInvoiceOverview[];
  nonCpoContacts: ContactInvoiceOverview[];
  months: string[];
};

// Generate last 12 months including current month
const generateLast12Months = (): string[] => {
  const months: string[] = [];
  const now = new Date();
  
  for (let i = 11; i >= 0; i--) {
    const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
    const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
    months.push(monthKey);
  }
  
  return months;
};

// Format month for display
const formatMonthName = (monthKey: string): string => {
  const [year, month] = monthKey.split('-');
  const date = new Date(parseInt(year), parseInt(month) - 1, 1);
  return date.toLocaleDateString('de-DE', { 
    month: 'short', 
    year: 'numeric' 
  });
};

export async function GET(request: NextRequest) {
  const session = await getServerSession(authOptions);
  
  if (!session || session?.user?.role !== Role.ADMIN) {
    return new Response("Unauthorized", { status: 401 });
  }

  try {
    const months = generateLast12Months();
    const startDate = new Date(months[0] + '-01');
    const endDate = new Date();
    endDate.setMonth(endDate.getMonth() + 1, 0); // Last day of current month

    // Get all contacts with their invoices from the last 12 months
    const contacts = await prisma.contact.findMany({
      select: {
        id: true,
        name: true,
        companyName: true,
        cpo: true,
        invoices: {
          where: {
            invoiceDate: {
              gte: startDate,
              lte: endDate,
            },
            kindOfInvoice: {
              in: [KindOfInvoice.INVOICE, KindOfInvoice.CREDIT],
            },
          },
          select: {
            id: true,
            invoiceDate: true,
            kindOfInvoice: true,
          },
        },
      },
      orderBy: [
        { cpo: 'desc' },
        { companyName: 'asc' },
        { name: 'asc' },
      ],
    });

    // Process contacts and group by CPO status
    const processContacts = (contactList: typeof contacts): ContactInvoiceOverview[] => {
      return contactList.map(contact => {
        const monthlyInvoices: ContactInvoiceOverview['monthlyInvoices'] = {};
        
        // Initialize all months
        months.forEach(month => {
          monthlyInvoices[month] = {
            invoices: 0,
            credits: 0,
            hasInvoice: false,
            hasCredit: false,
          };
        });

        // Count invoices by month
        contact.invoices.forEach(invoice => {
          if (invoice.invoiceDate) {
            const monthKey = `${invoice.invoiceDate.getFullYear()}-${String(invoice.invoiceDate.getMonth() + 1).padStart(2, '0')}`;
            
            if (monthlyInvoices[monthKey]) {
              if (invoice.kindOfInvoice === KindOfInvoice.INVOICE) {
                monthlyInvoices[monthKey].invoices++;
                monthlyInvoices[monthKey].hasInvoice = true;
              } else if (invoice.kindOfInvoice === KindOfInvoice.CREDIT) {
                monthlyInvoices[monthKey].credits++;
                monthlyInvoices[monthKey].hasCredit = true;
              }
            }
          }
        });

        return {
          contactId: contact.id,
          contactName: contact.name || 'Unbekannt',
          companyName: contact.companyName,
          isCPO: contact.cpo,
          monthlyInvoices,
        };
      });
    };

    const cpoContacts = processContacts(contacts.filter(c => c.cpo));
    const nonCpoContacts = processContacts(contacts.filter(c => !c.cpo));

    const response: InvoiceOverviewData = {
      cpoContacts,
      nonCpoContacts,
      months,
    };

    return NextResponse.json(response);
  } catch (error) {
    {
      const msg = 'Error fetching invoice overview:';
      const errorMsg = (error as Error)?.message || String(error);
      Logger(`${msg}: ${errorMsg}`, "API 500", "src/app/api/invoice-overview/route.ts", LogType.ERROR);
    }
    Logger("API 500 Error", "API 500", "src/app/api/invoice-overview/route.ts", LogType.ERROR);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
