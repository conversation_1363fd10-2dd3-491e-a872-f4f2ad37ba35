import type { NextRequest } from "next/server";
import prisma from "../../../server/db/prisma";
import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role, StateOfInvoice, KindOfInvoice } from "@prisma/client";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";
// Local types for CPO Revenue Dashboard with net calculations
type CPOInvoiceDetailNet = {
  id: string;
  invoiceNumber: string | null;
  invoiceDate: Date | null;
  sumGross: number;
  sumNet: number;
  onboardingAmount: number;
  recurringAmount: number;
  otherAmount: number;
  kindOfInvoice: string;
  isOnboarding: boolean;
  positions: {
    title: string;
    description: string | null;
    amount: number;
    unitPrice: number;
    sumGross: number;
    sumNet: number;
    isOnboarding?: boolean;
    isRecurring?: boolean;
  }[];
};

type CPORevenueDataNet = {
  cpoId: string;
  cpoName: string;
  totalCreditNotes: number;
  totalInvoiced: number;
  onboardingRevenue: number;
  recurringRevenue: number;
  yearForecast: number;
  monthlyBreakdown: {
    month: string;
    revenue: number;
    creditNotes: number;
    invoiced: number;
    recurringRevenue: number;
    invoiceToCreditsRatio: number; // Prozent: Rechnungen / Gutschriften * 100
  }[];
  invoiceDetails: CPOInvoiceDetailNet[];
  creditNoteDetails: CPOInvoiceDetailNet[];
};

type CPORevenueDashboardDataNet = {
  cpos: CPORevenueDataNet[];
  totalCreditNotes: number;
  totalInvoiced: number;
  totalOnboarding: number;
  totalRecurring: number;
  globalMonthlyBreakdown: {
    month: string;
    totalRevenue: number;
    totalCreditNotes: number;
    totalInvoiced: number;
    totalRecurringRevenue: number;
    totalOnboardingRevenue: number;
    globalInvoiceToCreditsRatio: number; // Prozent: Rechnungen / Gutschriften * 100
  }[];
  globalYearForecast: number;
};

function getMonthKey(date: Date): string {
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, "0")}`;
}

function isOnboardingFee(title: string, description?: string): boolean {
  const onboardingKeywords = [
    "kartenbestellgebühr",
    "onboarding",
    "einrichtung",
    "setup",
    "aktivierung",
    "ersteinrichtung",
    "Einmaliger Serviceeinsatz",
    "Einmalige Setupgebühr",
  ];

  const searchText = `${title} ${description || ""}`.toLowerCase();
  return onboardingKeywords.some((keyword) => searchText.includes(keyword));
}

function isRecurringFee(title: string, description?: string): boolean {
  const recurringRevenueKeywords = [
    // Bestehende Keywords
    "Transaktionsgebühr",
    "DC Ladepunkte",
    "AC Ladepunkte",
    "Zusatzgebühren GLS",
    "Ladekarten Grundgebühr",
    "Mitarbeiter Laden",
    "Sessiongebühr AC",
    "Sessiongebühr DC",
    "Hotline AC",
    "Hotline DC",
    "Servicegebühr AC Ladepunkt",
    "Servicegebühr DC Ladepunkt",
    "Mitarbeiterladen/Clubladen Gebühr",
    "Sessiongebühr",
  ];
  const searchText = `${title} ${description || ""}`.toLowerCase();
  return recurringRevenueKeywords.some((keyword) => searchText.includes(keyword.toLowerCase()));
}

function calculateYearForecast(
  onboardingRevenue: number,
  recurringRevenue: number,
  monthlyData: { month: string; recurringRevenue: number }[],
): number {
  const currentYear = new Date().getFullYear();
  const currentMonth = new Date().getMonth() + 1;

  // Filter data for current year only
  const currentYearData = monthlyData.filter(
    (item) => item.month.startsWith(currentYear.toString()) && item.recurringRevenue > 0,
  );

  if (currentYearData.length === 0) return onboardingRevenue;

  // Calculate average monthly recurring revenue from actual data
  const totalRecurringRevenue = currentYearData.reduce(
    (sum, item) => sum + item.recurringRevenue,
    0,
  );
  const avgMonthlyRecurring = totalRecurringRevenue / currentYearData.length;

  // Forecast remaining months with recurring revenue only
  const remainingMonths = Math.max(0, 12 - currentMonth);
  const forecastRecurringRevenue = totalRecurringRevenue + avgMonthlyRecurring * remainingMonths;

  // Total forecast = onboarding (already earned, one-time) + forecasted recurring
  return onboardingRevenue + forecastRecurringRevenue;
}

export async function GET(request: NextRequest) {
  const session = await getServerSession(authOptions);

  if (!session || session?.user?.role !== Role.ADMIN) {
    return new Response("no auth", { status: 401 });
  }

  try {
    // Build where clause for CPO invoices - no OU filter, show all CPOs for admins
    const whereClause: any = {
      contact: {
        cpo: true, // Only CPO contacts
      },
      stateOfInvoice: {
        in: [StateOfInvoice.CREATED, StateOfInvoice.PAID], // Include both sent and paid invoices
      },
      // Exclude cancelled invoices and storno invoices
      kindOfInvoice: {
        notIn: [KindOfInvoice.STORNO, KindOfInvoice.CREDIT_STORNO],
      },
      // Exclude invoices that have been cancelled by a storno
      invoiceChilds: {
        none: {
          kindOfInvoice: {
            in: [KindOfInvoice.STORNO, KindOfInvoice.CREDIT_STORNO],
          },
        },
      },
    };

    // Get all CPO invoices (both regular and credit notes)
    const cpoInvoices = await prisma.invoice.findMany({
      where: whereClause,
      include: {
        contact: true,
        invoicePositions: true,
        contract: true,
      },
      orderBy: {
        invoiceDate: "desc",
      },
    });

    // Group invoices by CPO contact
    const cpoMap = new Map<
      string,
      {
        contact: any;
        invoices: any[];
        creditNotes: any[];
        monthlyData: Map<
          string,
          { revenue: number; creditNotes: number; invoiced: number; recurringRevenue: number; invoiceToCreditsRatio: number }
        >;
      }
    >();

    for (const invoice of cpoInvoices) {
      if (!invoice.contact) continue;

      const cpoId = invoice.contact.id;
      if (!cpoMap.has(cpoId)) {
        cpoMap.set(cpoId, {
          contact: invoice.contact,
          invoices: [],
          creditNotes: [],
          monthlyData: new Map(),
        });
      }

      const cpoData = cpoMap.get(cpoId)!;

      if (invoice.kindOfInvoice === KindOfInvoice.CREDIT) {
        cpoData.creditNotes.push(invoice);
      } else {
        cpoData.invoices.push(invoice);
      }

      // Add to monthly breakdown
      if (invoice.invoiceDate) {
        const monthKey = getMonthKey(invoice.invoiceDate);
        if (!cpoData.monthlyData.has(monthKey)) {
          cpoData.monthlyData.set(monthKey, {
            revenue: 0,
            creditNotes: 0,
            invoiced: 0,
            recurringRevenue: 0,
            invoiceToCreditsRatio: 0,
          });
        }

        const monthData = cpoData.monthlyData.get(monthKey)!;
        if (invoice.kindOfInvoice === KindOfInvoice.CREDIT) {
          monthData.creditNotes += invoice.sumNet;
          // Don't subtract credit notes from revenue - they are separate transactions
        } else {
          monthData.invoiced += invoice.sumNet;
          monthData.revenue += invoice.sumNet;

          // Calculate recurring revenue for this invoice
          let invoiceRecurringRevenue = 0;
          for (const position of invoice.invoicePositions) {
            if (isRecurringFee(position.title, position?.description ?? "")) {
              invoiceRecurringRevenue += position.sumNet;
            }
          }
          monthData.recurringRevenue += invoiceRecurringRevenue;
        }
      }
    }

    // Process each CPO's data
    const cpos: CPORevenueDataNet[] = [];
    let totalCreditNotes = 0;
    let totalInvoiced = 0;
    let totalOnboarding = 0;
    let totalRecurring = 0;

    for (const [cpoId, cpoData] of cpoMap.entries()) {
      const cpoRevenue = cpoData.invoices.reduce((sum, inv) => sum + inv.sumNet, 0);
      const cpoCreditNotes = cpoData.creditNotes.reduce((sum, inv) => sum + inv.sumNet, 0);

      // Calculate onboarding vs recurring revenue
      let onboardingRevenue = 0;
      let recurringRevenue = 0;

      for (const invoice of cpoData.invoices) {
        for (const position of invoice.invoicePositions) {
          if (isOnboardingFee(position.title, position.description)) {
            onboardingRevenue += position.sumNet;
          } else if (isRecurringFee(position.title, position.description)) {
            recurringRevenue += position.sumNet;
          }
        }
      }

      // Calculate invoice-to-credits ratio for each month
      for (const [monthKey, monthData] of cpoData.monthlyData.entries()) {
        if (monthData.creditNotes > 0) {
          monthData.invoiceToCreditsRatio = (monthData.invoiced / monthData.creditNotes) * 100;
        } else {
          monthData.invoiceToCreditsRatio = monthData.invoiced > 0 ? 100 : 0; // 100% wenn Rechnungen aber keine Gutschriften
        }
      }

      // Convert monthly data to array
      const monthlyBreakdown = Array.from(cpoData.monthlyData.entries())
        .map(([month, data]) => ({
          month,
          revenue: data.revenue,
          creditNotes: data.creditNotes,
          invoiced: data.invoiced,
          recurringRevenue: data.recurringRevenue,
          invoiceToCreditsRatio: data.invoiceToCreditsRatio,
        }))
        .sort((a, b) => b.month.localeCompare(a.month));

      const yearForecast = calculateYearForecast(
        onboardingRevenue,
        recurringRevenue,
        monthlyBreakdown,
      );
      // Don't subtract credit notes from revenue - they are separate transactions

      // Prepare invoice details
      const invoiceDetails: CPOInvoiceDetailNet[] = cpoData.invoices.map((invoice) => {
        // Calculate breakdown per invoice
        let invoiceOnboardingAmount = 0;
        let invoiceRecurringAmount = 0;
        let invoiceOtherAmount = 0;

        const enhancedPositions = invoice.invoicePositions.map((pos: any) => {
          const isOnboardingPos = isOnboardingFee(pos.title, pos.description);
          const isRecurringPos = isRecurringFee(pos.title, pos.description);

          if (isOnboardingPos) {
            invoiceOnboardingAmount += pos.sumNet;
          } else if (isRecurringPos) {
            invoiceRecurringAmount += pos.sumNet;
          } else {
            invoiceOtherAmount += pos.sumNet;
          }

          return {
            title: pos.title,
            description: pos.description,
            amount: pos.amount,
            unitPrice: pos.unitPrice,
            sumGross: pos.sumGross,
            sumNet: pos.sumNet,
            isOnboarding: isOnboardingPos,
            isRecurring: isRecurringPos,
          };
        });

        return {
          id: invoice.id,
          invoiceNumber: invoice.invoiceNumber,
          invoiceDate: invoice.invoiceDate,
          sumGross: invoice.sumGross,
          sumNet: invoice.sumNet,
          onboardingAmount: invoiceOnboardingAmount,
          recurringAmount: invoiceRecurringAmount,
          otherAmount: invoiceOtherAmount,
          kindOfInvoice: invoice.kindOfInvoice,
          isOnboarding: invoice.invoicePositions.some((pos: any) =>
            isOnboardingFee(pos.title, pos.description),
          ),
          positions: enhancedPositions,
        };
      });

      // Prepare credit note details
      const creditNoteDetails: CPOInvoiceDetailNet[] = cpoData.creditNotes.map((invoice) => {
        // Calculate breakdown per credit note
        let invoiceOnboardingAmount = 0;
        let invoiceRecurringAmount = 0;
        let invoiceOtherAmount = 0;

        const enhancedPositions = invoice.invoicePositions.map((pos: any) => {
          const isOnboardingPos = isOnboardingFee(pos.title, pos.description);
          const isRecurringPos = isRecurringFee(pos.title, pos.description);

          if (isOnboardingPos) {
            invoiceOnboardingAmount += pos.sumNet;
          } else if (isRecurringPos) {
            invoiceRecurringAmount += pos.sumNet;
          } else {
            invoiceOtherAmount += pos.sumNet;
          }

          return {
            title: pos.title,
            description: pos.description,
            amount: pos.amount,
            unitPrice: pos.unitPrice,
            sumGross: pos.sumGross,
            sumNet: pos.sumNet,
            isOnboarding: isOnboardingPos,
            isRecurring: isRecurringPos,
          };
        });

        return {
          id: invoice.id,
          invoiceNumber: invoice.invoiceNumber,
          invoiceDate: invoice.invoiceDate,
          sumGross: invoice.sumGross,
          sumNet: invoice.sumNet,
          onboardingAmount: invoiceOnboardingAmount,
          recurringAmount: invoiceRecurringAmount,
          otherAmount: invoiceOtherAmount,
          kindOfInvoice: invoice.kindOfInvoice,
          isOnboarding: false,
          positions: enhancedPositions,
        };
      });

      cpos.push({
        cpoId,
        cpoName: cpoData.contact.companyName || cpoData.contact.name || "Unbekannt",
        totalCreditNotes: cpoCreditNotes,
        totalInvoiced: cpoRevenue,
        onboardingRevenue,
        recurringRevenue,
        yearForecast,
        monthlyBreakdown,
        invoiceDetails,
        creditNoteDetails,
      });

      totalCreditNotes += cpoCreditNotes;
      totalInvoiced += cpoRevenue;
      totalOnboarding += onboardingRevenue;
      totalRecurring += recurringRevenue;
    }

    // Sort CPOs by total invoiced (descending)
    cpos.sort((a, b) => b.totalInvoiced - a.totalInvoiced);

    // Calculate global monthly breakdown by aggregating all CPO data
    const globalMonthlyMap = new Map<
      string,
      {
        totalRevenue: number;
        totalCreditNotes: number;
        totalInvoiced: number;
        totalRecurringRevenue: number;
        totalOnboardingRevenue: number;
        globalInvoiceToCreditsRatio: number;
      }
    >();

    // Aggregate monthly data from all CPOs
    for (const cpo of cpos) {
      for (const monthData of cpo.monthlyBreakdown) {
        if (!globalMonthlyMap.has(monthData.month)) {
          globalMonthlyMap.set(monthData.month, {
            totalRevenue: 0,
            totalCreditNotes: 0,
            totalInvoiced: 0,
            totalRecurringRevenue: 0,
            totalOnboardingRevenue: 0,
            globalInvoiceToCreditsRatio: 0,
          });
        }

        const globalMonth = globalMonthlyMap.get(monthData.month)!;
        globalMonth.totalRevenue += monthData.revenue;
        globalMonth.totalCreditNotes += monthData.creditNotes;
        globalMonth.totalInvoiced += monthData.invoiced;
        globalMonth.totalRecurringRevenue += monthData.recurringRevenue;

        // Calculate onboarding revenue for this month by subtracting recurring from total
        const monthOnboardingRevenue = monthData.revenue - monthData.recurringRevenue;
        globalMonth.totalOnboardingRevenue += monthOnboardingRevenue;
      }
    }

    // Calculate global invoice-to-credits ratio for each month
    for (const [monthKey, globalMonth] of globalMonthlyMap.entries()) {
      if (globalMonth.totalCreditNotes > 0) {
        globalMonth.globalInvoiceToCreditsRatio = (globalMonth.totalInvoiced / globalMonth.totalCreditNotes) * 100;
      } else {
        globalMonth.globalInvoiceToCreditsRatio = globalMonth.totalInvoiced > 0 ? 100 : 0;
      }
    }

    // Convert to array and sort by month
    const globalMonthlyBreakdown = Array.from(globalMonthlyMap.entries())
      .map(([month, data]) => ({
        month,
        ...data,
      }))
      .sort((a, b) => b.month.localeCompare(a.month));

    // Calculate global year forecast
    // Convert globalMonthlyBreakdown to the format expected by calculateYearForecast
    const globalMonthlyForForecast = globalMonthlyBreakdown.map((month) => ({
      month: month.month,
      recurringRevenue: month.totalRecurringRevenue,
    }));
    const globalYearForecast = calculateYearForecast(
      totalOnboarding,
      totalRecurring,
      globalMonthlyForForecast,
    );

    const result: CPORevenueDashboardDataNet = {
      cpos,
      totalCreditNotes,
      totalInvoiced,
      totalOnboarding,
      totalRecurring,
      globalMonthlyBreakdown,
      globalYearForecast,
    };

    return NextResponse.json(result);
  } catch (error) {
    {
      const msg = "Error fetching CPO revenue dashboard data:";
      const errorMsg = (error as Error)?.message || String(error);
      Logger(`${msg}: ${errorMsg}`, "API 500", "src/app/api/cpo-revenue-dashboard/route.ts", LogType.ERROR);
    }
    Logger("API 500 Error", "API 500", "src/app/api/cpo-revenue-dashboard/route.ts", LogType.ERROR);
    return new Response("Internal Server Error", { status: 500 });
  }
}
