import prisma from "~/server/db/prisma";
import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";
export async function POST(request: NextRequest) {
  const { data } = await request.json();
  if (data) {
    try {
      if (data?.id) {
        await prisma.creditTarif.update({
          where: {
            id: data?.id || 0,
          },
          data: data,
        });
      } else {
        await prisma.creditTarif.create({
          data: {
            ...data,
            sessionCredit: +data.sessionCredit,
            energyCredit: +data.energyCredit,
            blockingCredit: +data.blockingCredit,
            maxBlockingCredit: +data.maxBlockingCredit,
            blockingFeeMinStart: data.blockingFeeMinStart ? +data.blockingFeeMinStart : 0,
          },
        });
      }
      return NextResponse.json(data);
    } catch (e) {
      Logger("API 500 Error", "API 500", "src/app/api/tarif/creditTarif/route.ts", LogType.ERROR);
    return NextResponse.json({ success: false, message: "DB Error" }, { status: 500 });
    }
  } else {
    Logger("API 500 Error", "API 500", "src/app/api/tarif/creditTarif/route.ts", LogType.ERROR);
    return NextResponse.json({ success: false, message: "Invalid data" }, { status: 500 });
  }
}
