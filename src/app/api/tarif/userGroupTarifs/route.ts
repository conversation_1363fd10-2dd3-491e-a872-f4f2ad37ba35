import prisma from "~/server/db/prisma";
import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";

export async function GET(request: NextRequest) {
  const session = await getServerSession(authOptions);
  const user = session?.user;
  
  if (!session || !user) {
    return NextResponse.json("No Login", { status: 404 });
  }

  try {
    // Get user's userGroup
    const userWithGroup = await prisma.user.findUnique({
      where: { id: user.id },
      include: {
        userGroup: {
          include: {
            companyTarifs: {
              include: {
                tarif: true,
              },
            },
          },
        },
      },
    });

    if (!userWithGroup) {
      return NextResponse.json("User not found", { status: 404 });
    }

    // If user has no userGroup, return empty array
    if (!userWithGroup.userGroup) {
      return NextResponse.json([]);
    }

    // Get today's date for validity check
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Filter tariffs that are:
    // 1. Assigned to user's userGroup
    // 2. Valid today
    // 3. Not internal
    const availableTarifs = userWithGroup.userGroup.companyTarifs
      .map(ct => ct.tarif)
      .filter(tarif => {
        const validFrom = new Date(tarif.validFrom);
        const validTo = new Date(tarif.validTo);
        return (
          validFrom <= today &&
          validTo >= today &&
          !tarif.internal
        );
      });

    return NextResponse.json(availableTarifs);
  } catch (error) {
    {
      const msg = "Error fetching user group tarifs:";
      const errorMsg = (error as Error)?.message || String(error);
      Logger(`${msg}: ${errorMsg}`, "API 500", "src/app/api/tarif/userGroupTarifs/route.ts", LogType.ERROR);
    }
    return NextResponse.json("Internal server error", { status: 500 });
  }
}
