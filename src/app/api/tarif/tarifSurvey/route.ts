// /api/tarif/tarifSurvey/route.ts
import type { NextRequest } from "next/server";
import { tarifSurveySchema } from "~/types/schemaZod/tarifSurveyZodSchema";
import prisma from "~/server/db/prisma";

export async function POST(request: NextRequest) {
    try {
        const body = await request.json();
        const safe = tarifSurveySchema.safeParse(body);
        if (!safe.success) {
            return new Response(JSON.stringify(safe.error.format()), { status: 400 });
        }
        const data = safe.data;

        const baseName = typeof body.name === "string" ? body.name.trim() : "";
        if (!baseName) {
            return new Response(JSON.stringify({ message: "name (CPO) ist erforderlich." }), { status: 400 });
        }
        if (!data.currentType) {
            return new Response(JSON.stringify({ message: "currentType (AC|DC) ist erforderlich." }), { status: 400 });
        }

        // vorhandene Suffixe _AC/_DC und optional _survey am Ende entfernen
        const cleanedBase = baseName.replace(/_(AC|DC)(?:_survey)?$/i, "");
        const tarifName = `${cleanedBase}_${data.currentType}_survey`;

        const now = new Date();
        const oneYearLater = new Date(now);
        oneYearLater.setFullYear(now.getFullYear() + 1);

        const upserted = await prisma.tarif.upsert({
            where: { name: tarifName },
            create: {
                name: tarifName,
                sessionFee: data.sessionFee ?? null,
                kwh: data.kwh,
                blockingFee: data.blockingFee ?? 0,
                blockingFeeBeginAtMin: data.blockingFeeBeginAtMin ?? 0,
                blockingFeeMax: data.blockingFeeMax ?? 0,
                currentType: data.currentType,
                description: data.description ?? null,
                minChargingTime: 0,
                minChargingEnergy: 0,
                validFrom: now,
                validTo: oneYearLater,
            },
            update: {
                sessionFee: data.sessionFee ?? null,
                kwh: data.kwh,
                blockingFee: data.blockingFee ?? 0,
                blockingFeeBeginAtMin: data.blockingFeeBeginAtMin ?? 0,
                blockingFeeMax: data.blockingFeeMax ?? 0,
                currentType: data.currentType,
                description: data.description ?? null,
                name: tarifName,
                validTo: oneYearLater,
            },
        });

        return new Response(JSON.stringify(upserted), { status: 201 });
    } catch (e) {
        console.error(e);
        const msg = e instanceof Error ? e.message : "Server error";
        return new Response(JSON.stringify({ message: msg }), { status: 500 });
    }
}
