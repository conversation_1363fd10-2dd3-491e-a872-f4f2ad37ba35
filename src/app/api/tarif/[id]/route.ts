import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role } from "@prisma/client";
import prisma from "~/server/db/prisma";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";

interface Props {
  params: {
    id: string;
  };
}

export async function GET(request: NextRequest, { params }: Props) {
  const session = await getServerSession(authOptions);

  if (!session || ![Role.ADMIN, Role.CARD_MANAGER].includes(session.user.role)) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const tarif = await prisma.tarif.findUnique({
      where: { id: params.id },
      include: {
        validOus: {
          select: {
            id: true,
            name: true,
            code: true,
          },
        },
        contract: {
          select: {
            id: true,
            name: true,
            contact: {
              select: {
                name: true,
              },
            },
          },
        },
      },
    });

    if (!tarif) {
      return NextResponse.json({ error: "Tarif not found" }, { status: 404 });
    }

    return NextResponse.json(tarif);
  } catch (error) {
    {
      const msg = "Error fetching tarif:";
      const errorMsg = (error as Error)?.message || String(error);
      Logger(`${msg}: ${errorMsg}`, "API 500", "src/app/api/tarif/[id]/route.ts", LogType.ERROR);
    }
    Logger("API 500 Error", "API 500", "src/app/api/tarif/[id]/route.ts", LogType.ERROR);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
