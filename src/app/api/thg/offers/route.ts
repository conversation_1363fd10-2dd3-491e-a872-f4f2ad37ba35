import { z } from "zod";
import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import prisma from "~/server/db/prisma";
import { getServerSession } from "next-auth/next";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role, ThgOfferStatus, ThgOfferType } from "@prisma/client";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";


export const dynamic = "force-dynamic";

const CreateThgOfferSchema = z.object({
  pricePerKwh: z.coerce.number().min(0.01, "Preis muss größer als 0 sein"),
  priceUnit: z.enum(["CENT_KWH", "EURO_MWH"]),
  quantityMin: z.coerce.number().min(1, "Mindestmenge muss mindestens 1 kWh sein"),
  quantityMax: z.coerce.number().min(1, "Höchstmenge muss mindestens 1 kWh sein"),
  validUntil: z.string().transform((val) => new Date(val)),
  title: z.string().optional(),
  description: z.string().optional(),
  contactPerson: z.string().min(1, "Ansprechpartner ist erforderlich"),
  paymentTerms: z.string().optional(),
  deliveryTerms: z.string().optional(),
}).refine((data) => data.quantityMax >= data.quantityMin, {
  message: "Höchstmenge muss größer oder gleich der Mindestmenge sein",
  path: ["quantityMax"],
});

// GET - Angebote eines Users abrufen
export async function GET(request: NextRequest) {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json({ error: "Not authenticated" }, { status: 401 });
  }

  if (session.user.role !== Role.THG_BUYER) {
    return NextResponse.json({ error: "Access denied. THG_BUYER role required." }, { status: 403 });
  }

  try {
    const { searchParams } = new URL(request.url);
    const status = searchParams.get("status");

    // Build where clause
    const whereClause: any = { buyerId: session.user.id };

    if (status && Object.values(ThgOfferStatus).includes(status as ThgOfferStatus)) {
      whereClause.status = status as ThgOfferStatus;
    }

    const offers = await prisma.thgOffer.findMany({
      where: whereClause,
      orderBy: { createdAt: "desc" },
    });

    // Sicherstellen, dass Integer-Werte korrekt serialisiert werden
    const serializedOffers = offers.map(offer => ({
      ...offer,
      pricePerKwh: Number(offer.pricePerKwh),
      quantity: Number(offer.quantity),
      quantityMin: offer.quantityMin ? Number(offer.quantityMin) : null,
      quantityMax: offer.quantityMax ? Number(offer.quantityMax) : null,
      totalAmount: offer.totalAmount ? Number(offer.totalAmount) : null,
    }));

    return NextResponse.json(serializedOffers);
  } catch (error) {
    {
      const msg = "Internal server error - fetching THG offers";
      const errorMsg = (error as Error)?.message || String(error);
      Logger(`${msg}: ${errorMsg}`, 'API 500', 'src/app/api/thg/offers/route.ts', LogType.ERROR);
    }
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

// POST - Neues Angebot erstellen
export async function POST(request: NextRequest) {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json({ error: "Not authenticated" }, { status: 401 });
  }

  if (session.user.role !== Role.THG_BUYER) {
    return NextResponse.json({ error: "Access denied. THG_BUYER role required." }, { status: 403 });
  }

  try {
    const body = await request.json();
    const result = CreateThgOfferSchema.safeParse(body);

    if (!result.success) {
      // Zod-Validierungsfehler in benutzerfreundliche Nachricht umwandeln
      const errorMessages = result.error.errors.map(err => {
        if (err.path.length > 0) {
          return `${err.path.join('.')}: ${err.message}`;
        }
        return err.message;
      }).join(', ');

      return NextResponse.json({ error: `Validierungsfehler: ${errorMessages}` }, { status: 400 });
    }

    const data = result.data;

    // Preis in Cent pro kWh umrechnen falls nötig
    let priceInCentPerKwh = data.pricePerKwh;
    if (data.priceUnit === "EURO_MWH") {
      // Euro/MWh zu Cent/kWh: 1 Euro/MWh = 0.1 Cent/kWh
      priceInCentPerKwh = data.pricePerKwh * 0.1;
    }

    // Durchschnittsmenge für quantity berechnen
    // Bei identischen Werten keine Rundung nötig
    const averageQuantity = data.quantityMin === data.quantityMax
      ? data.quantityMin
      : Math.round((data.quantityMin + data.quantityMax) / 2);

    const newOffer = await prisma.thgOffer.create({
      data: {
        buyerId: session.user.id,
        type: ThgOfferType.PURCHASE,
        status: ThgOfferStatus.ACTIVE,
        pricePerKwh: priceInCentPerKwh,
        quantity: averageQuantity,
        validUntil: data.validUntil,
        title: data.title,
        description: data.description,
        contactPerson: data.contactPerson,
        paymentTerms: data.paymentTerms,
        deliveryTerms: data.deliveryTerms,
        // Zusätzliche Felder für Mengenbereich
        quantityMin: data.quantityMin,
        quantityMax: data.quantityMax,
        priceUnit: data.priceUnit,
      },
    });

    return NextResponse.json(newOffer, { status: 201 });
  } catch (error) {
    {
      const msg = "Internal server error - creating THG offer";
      const errorMsg = (error as Error)?.message || String(error);
      Logger(`${msg}: ${errorMsg}`, 'API 500', 'src/app/api/thg/offers/route.ts', LogType.ERROR);
    }
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
