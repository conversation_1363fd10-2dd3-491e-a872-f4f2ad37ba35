import { NextResponse } from "next/server";
import prisma from "~/server/db/prisma";
import { getServerSession } from "next-auth/next";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role, ThgOfferStatus } from "@prisma/client";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";

// GET - Angebots-Statistiken abrufen
export async function GET() {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json({ error: "Not authenticated" }, { status: 401 });
  }

  if (session.user.role !== Role.THG_BUYER) {
    return NextResponse.json({ error: "Access denied. THG_BUYER role required." }, { status: 403 });
  }

  try {
    // Gesamtanzahl der Angebote
    const totalOffers = await prisma.thgOffer.count({
      where: { buyerId: session.user.id },
    });

    // Aktive Angebote
    const activeOffers = await prisma.thgOffer.count({
      where: { 
        buyerId: session.user.id,
        status: ThgOfferStatus.ACTIVE,
      },
    });

    // Abgelaufene/stornierte Angebote
    const expiredOffers = await prisma.thgOffer.count({
      where: { 
        buyerId: session.user.id,
        status: {
          in: [ThgOfferStatus.EXPIRED, ThgOfferStatus.CANCELLED],
        },
      },
    });

    const stats = {
      totalOffers,
      activeOffers,
      expiredOffers,
    };

    return NextResponse.json(stats);
  } catch (error) {
    {
      const msg = "Error fetching THG offer stats:";
      const errorMsg = (error as Error)?.message || String(error);
      Logger(`${msg}: ${errorMsg}`, "API 500", "src/app/api/thg/offers/stats/route.ts", LogType.ERROR);
    }
    Logger("API 500 Error", "API 500", "src/app/api/thg/offers/stats/route.ts", LogType.ERROR);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
