import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import prisma from "~/server/db/prisma";
import { getServerSession } from "next-auth/next";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role, ThgOfferStatus } from "@prisma/client";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";

interface RouteParams {
  params: {
    id: string;
  };
}

// POST - Angebot deaktivieren
export async function POST(request: NextRequest, { params }: RouteParams) {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json({ error: "Not authenticated" }, { status: 401 });
  }

  if (session.user.role !== Role.THG_BUYER) {
    return NextResponse.json({ error: "Access denied. THG_BUYER role required." }, { status: 403 });
  }

  try {
    const offerId = params.id;

    // Check if offer exists and belongs to user
    const existingOffer = await prisma.thgOffer.findUnique({
      where: { id: offerId },
    });

    if (!existingOffer) {
      return NextResponse.json({ error: "Angebot nicht gefunden" }, { status: 404 });
    }

    if (existingOffer.buyerId !== session.user.id) {
      return NextResponse.json({ error: "Keine Berechtigung für dieses Angebot" }, { status: 403 });
    }

    if (existingOffer.status !== ThgOfferStatus.ACTIVE) {
      return NextResponse.json({ error: "Angebot ist bereits deaktiviert" }, { status: 400 });
    }

    // Deactivate offer
    const updatedOffer = await prisma.thgOffer.update({
      where: { id: offerId },
      data: {
        status: ThgOfferStatus.CANCELLED,
        cancelledAt: new Date(),
      },
    });

    return NextResponse.json(updatedOffer);
  } catch (error) {
    {
      const msg = "Error deactivating THG offer:";
      const errorMsg = (error as Error)?.message || String(error);
      Logger(`${msg}: ${errorMsg}`, "API 500", "src/app/api/thg/offers/[id]/deactivate/route.ts", LogType.ERROR);
    }
    Logger("API 500 Error", "API 500", "src/app/api/thg/offers/[id]/deactivate/route.ts", LogType.ERROR);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
