import { z } from "zod";
import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import prisma from "~/server/db/prisma";
import { getServerSession } from "next-auth/next";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role } from "@prisma/client";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";

const ThgCompanyContactSchema = z.object({
  contactPerson: z.string().optional(),
  phone: z.string().optional(),
  website: z.string().optional(),
  notes: z.string().optional(),
});

// GET - Firmendaten abrufen
export async function GET() {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json({ error: "Not authenticated" }, { status: 401 });
  }

  if (session.user.role !== Role.THG_BUYER) {
    return NextResponse.json({ error: "Access denied. THG_BUYER role required." }, { status: 403 });
  }

  try {
    if (!prisma) {
      throw new Error("Prisma client not available");
    }

    const thgCompanyContact = await prisma.thgCompanyContact.findUnique({
      where: { userId: session.user.id },
    });

    if (!thgCompanyContact) {
      return NextResponse.json({ error: "THG company contact not found" }, { status: 404 });
    }

    return NextResponse.json(thgCompanyContact);
  } catch (error) {
    {
      const msg = "Error fetching THG company contact:";
      const errorMsg = (error as Error)?.message || String(error);
      Logger(`${msg}: ${errorMsg}`, "API 500", "src/app/api/thg/company-contact/route.ts", LogType.ERROR);
    }
    Logger("API 500 Error", "API 500", "src/app/api/thg/company-contact/route.ts", LogType.ERROR);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

// PUT - Firmendaten aktualisieren
export async function PUT(request: NextRequest) {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json({ error: "Not authenticated" }, { status: 401 });
  }

  if (session.user.role !== Role.THG_BUYER) {
    return NextResponse.json({ error: "Access denied. THG_BUYER role required." }, { status: 403 });
  }

  try {
    if (!prisma) {
      throw new Error("Prisma client not available");
    }

    const body = await request.json();
    const result = ThgCompanyContactSchema.safeParse(body);

    if (!result.success) {
      return NextResponse.json({ error: result.error.errors }, { status: 400 });
    }

    const data = result.data;

    // Check if THG company contact exists
    const existingContact = await prisma.thgCompanyContact.findUnique({
      where: { userId: session.user.id },
    });

    if (!existingContact) {
      // Create new THG company contact if it doesn't exist
      const newContact = await prisma.thgCompanyContact.create({
        data: {
          userId: session.user.id,
          contactPerson: data.contactPerson,
          phone: data.phone,
          website: data.website,
          notes: data.notes,
        },
      });
      return NextResponse.json(newContact);
    }

    // Update existing THG company contact
    const updatedContact = await prisma.thgCompanyContact.update({
      where: { userId: session.user.id },
      data: {
        contactPerson: data.contactPerson,
        phone: data.phone,
        website: data.website,
        notes: data.notes,
      },
    });

    return NextResponse.json(updatedContact);
  } catch (error) {
    {
      const msg = "Error updating THG company contact:";
      const errorMsg = (error as Error)?.message || String(error);
      Logger(`${msg}: ${errorMsg}`, "API 500", "src/app/api/thg/company-contact/route.ts", LogType.ERROR);
    }
    Logger("API 500 Error", "API 500", "src/app/api/thg/company-contact/route.ts", LogType.ERROR);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
