import { z } from "zod";
import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import prisma from "~/server/db/prisma";

import { PrismaClientKnownRequestError } from "@prisma/client/runtime/library";

import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";

const UpdateUserProfileSchema = z.object({
  name: z.string().optional(),
  lastName: z.string().optional(),
  street: z.string().optional(),
  streetNr: z.string().optional(),
  city: z.string().optional(),
  zip: z.string().optional(),
  country: z.string().optional(),
  addressId: z.string().optional(),
  companyName: z.string().optional(),
  phone: z.string().optional(),
});
export async function PATCH(request: NextRequest) {
  const result = UpdateUserProfileSchema.safeParse(await request.json());

  if (!result.success) {
    return NextResponse.json({ error: result.error }, { status: 400 });
  }
  const session = await getServerSession(authOptions);
  const userId = session?.user?.id;
  if (!userId) {
    return NextResponse.json({ error: "No login" }, { status: 404 });
  }

  try {
    // Handle address updates only if address fields are provided
    if (result.data.addressId && result.data.street) {
      await prisma.contactAddress.update({
        where: { id: result.data.addressId },
        data: {
          street: result.data.street,
          streetNr: result.data.streetNr,
          city: result.data.city,
          zip: result.data.zip,
          country: result.data.country,
        },
      });
    } else if (
      result.data.zip &&
      result.data.city &&
      result.data.street &&
      result.data.streetNr &&
      result.data.country
    ) {
      const address = await prisma.contactAddress.create({
        data: {
          zip: result.data.zip,
          city: result.data.city,
          street: result.data.street,
          streetNr: result.data.streetNr,
          country: result.data.country,
          validFrom: new Date(),
          validTo: new Date(2099, 0, 1), //dummy date für cardholder
          userId: userId,
        },
      });
    }

    // Build update data object with only provided fields
    const updateData: any = {};
    if (result.data.name !== undefined) updateData.name = result.data.name;
    if (result.data.lastName !== undefined) updateData.lastName = result.data.lastName;
    if (result.data.companyName !== undefined) updateData.companyName = result.data.companyName;
    if (result.data.phone !== undefined) updateData.phone = result.data.phone;

    const updatedUser = await prisma.user.update({
      where: { id: userId },
      data: updateData,
      select: {
        id: true,
        name: true,
        email: true,
        ouId: true,
      },
    });

    return NextResponse.json(updatedUser, { status: 200 });
  } catch (e: unknown) {
    if (e instanceof PrismaClientKnownRequestError) {
      if (e.code === "P2016") {
        return NextResponse.json({ error: "User not found." }, { status: 404 });
      } else {
        return NextResponse.json({ error: "Unbekanter Fehler" }, { status: 404 });
      }
    }
    Logger("API 500 Error", "API 500", "src/app/api/user/updateProfile/route.ts", LogType.ERROR);
    return NextResponse.json({ error: "An unexpected error occurred." }, { status: 500 });
  }
}
