import { z } from "zod";
import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import prisma from "~/server/db/prisma";
import { hasher } from "~/server/hasher/hasher";
import { Role } from "@prisma/client";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";

const VerifyThgInviteSchema = z.object({
  email: z.string().email(),
  signUpHash: z.string(),
  password: z.string().min(8),
  passwordRepeat: z.string(),
  contactPerson: z.string(),
  phone: z.string().optional(),
  website: z.string().optional(),
}).refine((data) => data.password === data.passwordRepeat, {
  message: "Passwords don't match",
  path: ["passwordRepeat"],
});

export async function POST(request: NextRequest) {
  try {
    const result = VerifyThgInviteSchema.safeParse(await request.json());

    if (!result.success) {
      return NextResponse.json({ error: result.error }, { status: 400 });
    }

    const { email, signUpHash, password, contactPerson, phone, website } = result.data;

    // Find the user with the signup hash
    const user = await prisma.user.findFirst({
      where: {
        email: email,
        signUpHash: signUpHash,
        role: Role.THG_BUYER,
        emailVerified: null,
      },
    });

    if (!user) {
      return NextResponse.json({ error: "Invalid signup link or user already verified" }, { status: 400 });
    }

    // Hash the password
    const hashedPassword = await hasher(password);

    // Update user and THG company contact in a transaction
    await prisma.$transaction(async (tx) => {
      // Update the user with the new information
      await tx.user.update({
        where: { id: user.id },
        data: {
          password: hashedPassword,
          emailVerified: new Date(),
          signUpHash: null, // Clear the signup hash
          phone: phone,
        },
      });

      // Update THG company contact with additional information
      await tx.thgCompanyContact.update({
        where: { userId: user.id },
        data: {
          contactPerson: contactPerson,
          phone: phone,
          website: website,
        },
      });
    });

    return NextResponse.json({ message: "THG buyer registration completed successfully" }, { status: 200 });
  } catch (error) {
    {
      const msg = "THG verification error:";
      const errorMsg = (error as Error)?.message || String(error);
      Logger(`${msg}: ${errorMsg}`, "API 500", "src/app/api/user/verify-thg-invite/route.ts", LogType.ERROR);
    }
    Logger("API 500 Error", "API 500", "src/app/api/user/verify-thg-invite/route.ts", LogType.ERROR);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
