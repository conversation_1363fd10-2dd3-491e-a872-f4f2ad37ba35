import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role } from "@prisma/client";
import prisma from "~/server/db/prisma";

export async function POST(request: NextRequest) {
  const session = await getServerSession(authOptions);
  if (session?.user?.role != Role.ADMIN) {
    return NextResponse.json({ error: "No permission" }, { status: 400 });
  }
  const { startDate, endDate } = await request.json();
  const tokensUids = await prisma.physicalCard.findMany({
    select: { uid: true, EMPCard: { select: { userId: true, invoice: true } } },
  });
  const flatTokenUids = tokensUids.map((token) => `'${token.uid}'`);

  const foundTokenUids: { Authentication_ID: string }[] = await prisma.$queryRawUnsafe(
    `select distinct Authentication_ID  from Cdr where End_Datetime <= '${endDate}' and invoiceId is null and End_Datetime >= '${startDate}' and Authentication_ID in (${flatTokenUids.join(
      ",",
    )})`,
  );
  const flatFoundTokenUids = foundTokenUids.map((entry) => entry.Authentication_ID.toLowerCase());

  const userIds = tokensUids
    .filter(
      (token) => flatFoundTokenUids.includes(token.uid.toLowerCase()) && token.EMPCard !== null,
    )
    .map((filteredToken) => (filteredToken.EMPCard ? filteredToken.EMPCard.userId : null))
    .filter((userId): userId is string => userId !== null);

  const unpaidCards = tokensUids.map((token) => !token.EMPCard?.invoice);
  if (unpaidCards.length > 0) {
  }
  if (userIds) {
    const users = await prisma.user.findMany({
      where: { role: Role.CARD_HOLDER, id: { in: userIds } },
      select: { ou: { select: { name: true } }, id: true, name: true, lastName: true },
    });
    return NextResponse.json(users, { status: 200 });
  }

  return NextResponse.json([], { status: 200 });
}
