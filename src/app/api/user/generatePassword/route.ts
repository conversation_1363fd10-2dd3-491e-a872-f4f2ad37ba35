import { z } from "zod";
import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";

import generator from "generate-password";
import nodemailer from "nodemailer";
import prisma from "../../../../server/db/prisma";
import { hasher } from "~/server/hasher/hasher";
import { env } from "~/env.js";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";

const CreateUserSchema = z.object({
  id: z.string(),
});

export async function POST(request: NextRequest) {
  const result = CreateUserSchema.safeParse(await request.json());

  if (!result.success) {
    return NextResponse.json({ error: result.error }, { status: 400 });
  }

  const { id } = result.data;

  const existingUser = await prisma.user.findFirst({
    where: {
      id: id,
    },
  });

  if (!existingUser) {
    return NextResponse.json({ error: "User not found" }, { status: 409 });
  }

  try {
    const password = generator.generate({
      length: 10,
      numbers: true,
      symbols: true,
      uppercase: true,
      lowercase: true,
    });

    const user = await prisma.user.update({
      where: { id },
      data: {
        password: await hasher(password),
      },
      select: {
        id: true,
        name: true,
        email: true,
        ouId: true,
      },
    });

    // Create transporter
    const transporter = nodemailer.createTransport({
      port: 465,
      host: env.EMAIL_SERVER_HOST,
      auth: {
        user: env.EMAIL_SERVER_USER,
        pass: env.EMAIL_SERVER_PASSWORD,
      },
    });

    // Send email
    transporter.sendMail({
      from: "<EMAIL>",
      to: existingUser.email,
      subject: "Welcome!",
      text: `Welcome ${existingUser.name}! Your password is: ${password}`,
    });

    return NextResponse.json(user, { status: 201 });
  } catch (e: unknown) {
    if (typeof e === "string") {
      Logger("API 500 Error", "API 500", "src/app/api/user/generatePassword/route.ts", LogType.ERROR);
    return NextResponse.json({ error: e.toUpperCase() }, { status: 500 });
    } else if (e instanceof Error) {
      Logger("API 500 Error", "API 500", "src/app/api/user/generatePassword/route.ts", LogType.ERROR);
    return NextResponse.json({ error: e.message }, { status: 500 });
    }
  }
}
