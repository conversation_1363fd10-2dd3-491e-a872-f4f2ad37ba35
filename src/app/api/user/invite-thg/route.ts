import { z } from "zod";
import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import prisma from "~/server/db/prisma";
import nodemailer from "nodemailer";
import { env } from "~/env.js";
import crypto from "crypto";
import { Role } from "@prisma/client";
import { getServerSession } from "next-auth/next";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { createThgPseudoOu } from "~/utils/user/pseudoThgOu";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";

const InviteThgUserSchema = z.object({
  firstName: z.string(),
  lastName: z.string(),
  email: z.string().email(),
  companyName: z.string(),
});

export async function POST(request: NextRequest) {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json({ error: "Not authenticated" }, { status: 401 });
  }

  if (session.user.role !== Role.ADMIN) {
    return NextResponse.json({ error: "No permission to invite THG buyers" }, { status: 401 });
  }

  const result = InviteThgUserSchema.safeParse(await request.json());

  if (!result.success) {
    return NextResponse.json({ error: result.error }, { status: 400 });
  }

  const { firstName, lastName, email, companyName } = result.data;

  // Check if user already exists
  const existingUser = await prisma.user.findFirst({
    where: {
      email: email,
    },
  });

  if (existingUser) {
    return NextResponse.json({ error: "User with this email already exists" }, { status: 409 });
  }
  const ou = await createThgPseudoOu();
  try {
    const randomSecret = crypto.randomBytes(32).toString("hex");

    // Create user and THG company contact in a transaction
    const result = await prisma.$transaction(async (tx) => {
      const user = await tx.user.create({
        data: {
          name: `${firstName}`,
          lastName: `${lastName}`,
          email: email,
          role: Role.THG_BUYER,
          signUpHash: randomSecret,
          ouId: ou.id,
          selectedOuId: ou.id,
          companyName: companyName,
        },
      });

      // Create THG company contact
      const thgCompanyContact = await tx.thgCompanyContact.create({
        data: {
          userId: user.id,
          contactPerson: `${firstName} ${lastName}`,
        },
      });

      return { user, thgCompanyContact };
    });

    // Create transporter
    const transporter = nodemailer.createTransport({
      port: 465,
      host: env.EMAIL_SERVER_HOST,
      auth: {
        user: env.EMAIL_SERVER_USER,
        pass: env.EMAIL_SERVER_PASSWORD,
      },
    });

    // Send invitation email
    await transporter.sendMail({
      from: "<EMAIL>",
      to: email,
      subject: "Einladung zur THG-Quoten Plattform",
      text: `Hallo ${firstName} ${lastName}!\n\nSie wurden zur THG-Quoten Plattform eingeladen.\n\nIhre Firma: ${companyName}\n\nBitte registrieren Sie sich über folgenden Link:\n${
        env.NEXT_PUBLIC_SITE_URL
      }/signup/thg/${randomSecret}/${encodeURIComponent(email)}\n\nViele Grüße\nIhr Eulektro Team`,
    });

    return NextResponse.json({ message: "THG buyer invited successfully" }, { status: 201 });
  } catch (error) {
    {
      const msg = "Error inviting THG buyer:";
      const errorMsg = (error as Error)?.message || String(error);
      Logger(`${msg}: ${errorMsg}`, "API 500", "src/app/api/user/invite-thg/route.ts", LogType.ERROR);
    }
    Logger("API 500 Error", "API 500", "src/app/api/user/invite-thg/route.ts", LogType.ERROR);
    return NextResponse.json({ error: "Failed to invite THG buyer" }, { status: 500 });
  }
}
