import { z } from "zod";
import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import prisma from "~/server/db/prisma";
import generator from "generate-password";
import nodemailer from "nodemailer";
import { env } from "~/env.js";
import { hasher } from "~/server/hasher/hasher";
import { PrismaClientKnownRequestError } from "@prisma/client/runtime/library";
import { Role } from "@prisma/client";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";

const CreateUserSchema = z.object({
  name: z.string(),
  lastName: z.string(),
  email: z.string().email(),
  ouId: z.string(),
  role: z.enum(["CARD_HOLDER", "CARD_MANAGER", "USER", "CPO", "THG_BUYER", "PNC_REMOTE"]),
});

export async function POST(request: NextRequest) {
  const session = await getServerSession(authOptions);
  if (!session || session?.user?.role != Role.ADMIN) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }
  const result = CreateUserSchema.safeParse(await request.json());

  if (!result.success) {
    return NextResponse.json({ error: result.error }, { status: 400 });
  }

  const { name, email, ouId, role, lastName } = result.data;

  const existingUser = await prisma.user.findFirst({
    where: {
      email: email,
    },
  });

  if (existingUser) {
    return NextResponse.json({ error: "User or Email already exists." }, { status: 409 });
  }

  try {
    const password = generator.generate({
      length: 10,
      numbers: true,
      symbols: true,
      uppercase: true,
      lowercase: true,
    });

    const user = await prisma.user.create({
      data: {
        name,
        lastName,
        email,
        ouId,
        selectedOuId: ouId,
        password: await hasher(password),
        role: role,
      },
    });

    const htmlContent = `
      <!DOCTYPE html> <html>
        <head>
            <title>Zugang zum Ladeportal</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 40px; }
                .container { background-color: #FDFDFD; padding: 20px; border-radius: 10px; }
                h1 { color: #2786b0; }
                p { color: #2786b0; }
                a {font-size: large; color:#2786b0; }
            </style>
        </head>
        <body>
        <div class="container">
            <h1>Willkommen beim Ladeportal!</h1>
            <p>Hallo ${name} ${lastName},</p>
            <p>du hast nun Zugang zu unserem Ladeportal erhalten. Wir freuen uns, dich als neuen Nutzer begrüßen zu dürfen!</p>
            <p>Dein Passwort lautet: <strong>${password}</strong></p>
            <p>Bitte bewahre dein Passwort sicher auf und teile es nicht mit anderen. Du kannst dich in unserem Portal mit deiner E-Mail-Adresse und diesem Passwort einloggen.</p>
            <p>Viel Spaß beim Nutzen unseres Portals!</p>
            <p><a href="${env.NEXT_PUBLIC_SITE_URL}">Hier geht's zum Ladeportal</a></p>
            <p>Beste Grüße <br>Dein Team von Eulektro</p>
        </div>
        </body>
        </html>`;
    // Create transporter
    const transporter = nodemailer.createTransport({
      port: 465,
      host: env.EMAIL_SERVER_HOST,
      auth: {
        user: env.EMAIL_SERVER_USER,
        pass: env.EMAIL_SERVER_PASSWORD,
      },
    });

    // Send email
    await transporter.sendMail({
      from: "<EMAIL>",
      to: email,
      subject: "Welcome!",
      html: htmlContent,
    });

    return NextResponse.json(user, { status: 201 });
  } catch (e: unknown) {
    if (typeof e === "string") {
      {
        const msg = typeof e === 'string' ? e.toUpperCase() : 'UNKNOWN-STRING-ERROR';
        Logger(msg, 'API 500', 'src/app/api/user/route.ts', LogType.ERROR);
      }
      {
        const msg = `API Error: ${String(e).toUpperCase()}`;
        Logger(msg, 'API 500', 'src/app/api/user/route.ts', LogType.ERROR);
      }
      return NextResponse.json({ error: e.toUpperCase() }, { status: 500 });
    } else if (e instanceof Error) {
      {
        const msg = e.message;
        Logger(msg, 'API 500', 'src/app/api/user/route.ts', LogType.ERROR);
      }
      {
        const msg = `API Error: ${e.message}`;
        Logger(msg, 'API 500', 'src/app/api/user/route.ts', LogType.ERROR);
      }
      return NextResponse.json({ error: e.message }, { status: 500 });
    }
  }
}

const UpdateUserSchema = z.object({
  id: z.string(),
  name: z.string(),
  email: z.string().email(),
  ouId: z.string(),
});

export async function PATCH(request: NextRequest) {
  const result = UpdateUserSchema.safeParse(await request.json());

  if (!result.success) {
    return NextResponse.json({ error: result.error }, { status: 400 });
  }

  const { id, name, email, ouId } = result.data;

  try {
    const updatedUser = await prisma.user.update({
      where: { id },
      data: {
        name,
        email,
        ouId,
        selectedOuId: ouId,
      },
      select: {
        id: true,
        name: true,
        email: true,
        ouId: true,
      },
    });

    return NextResponse.json(updatedUser, { status: 200 });
  } catch (e: unknown) {
    if (e instanceof PrismaClientKnownRequestError) {
      if (e.code === "P2016") {
        return NextResponse.json({ error: "User not found." }, { status: 404 });
      }
    }
    {
      const msg = "An unexpected error occurred.";
      Logger(msg, 'API 500', 'src/app/api/user/route.ts', LogType.ERROR);
    }
    {
      const msg = "An unexpected error occurred.";
      Logger(msg, 'API 500', 'src/app/api/user/route.ts', LogType.ERROR);
    }
    return NextResponse.json({ error: "An unexpected error occurred." }, { status: 500 });
  }
}

export async function GET(request: NextRequest) {
  const session = await getServerSession(authOptions);
  if (session?.user?.role != Role.ADMIN) {
    return NextResponse.json({ error: "No permission" }, { status: 400 });
  }
  const users = await prisma.user.findMany({
    where: { role: Role.CARD_HOLDER },
    include: { invoices: true },
  });

  return NextResponse.json(users, { status: 200 });
}
