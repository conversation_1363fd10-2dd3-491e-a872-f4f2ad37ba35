import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import prisma from "../../../../server/db/prisma";
import path from "path";
import { existsSync } from "fs";
import { mkdir, writeFile, unlink } from "fs/promises";
import crypto from "crypto";
import { sniffMime, extForMime } from "~/utils/image/fileTypeValidation";

export const runtime = "nodejs"; // FS-Zugriff: nicht edge

// Limits
const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5 MB




export async function POST(req: NextRequest) {
  try {
    // Auth
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Nicht authentifiziert" }, { status: 401 });
    }
    const userId = session.user.id;

    // Datei holen
    const form = await req.formData();
    const file = form.get("file") as File | null; // FormData-Key: "file"
    if (!file) {
      return NextResponse.json({ error: "Keine Datei übermittelt" }, { status: 400 });
    }
    if (file.size > MAX_FILE_SIZE) {
      return NextResponse.json({ error: "Datei zu groß (max. 5MB)" }, { status: 413 });
    }

    const buf = Buffer.from(await file.arrayBuffer());
    const mime = sniffMime(buf);
    if (!mime) {
      return NextResponse.json({ error: "Ungültiger Bildtyp (erlaubt: PNG, JPG, WEBP, AVIF)" }, { status: 400 });
    }

    // Zielordner unter /public/avatar anlegen
    const baseDir = path.join(process.cwd(), "public", "avatar");
    if (!existsSync(baseDir)) await mkdir(baseDir, { recursive: true });

    // Unguessable Name
    const rand = crypto.randomBytes(6).toString("hex");
    const fileName = `${userId}-${Date.now()}-${rand}${extForMime(mime)}`;
    const absPath = path.join(baseDir, fileName);

    // Schreiben
    await writeFile(absPath, buf);

    // Atomar: alten Key lesen, neuen setzen
    const oldKey = await prisma.$transaction(async (tx) => {
      const prev = await tx.user.findUnique({
        where: { id: userId },
        select: { imageKey: true },
      });
      await tx.user.update({
        where: { id: userId },
        data: {
          imageKey: fileName,
          imageUrl: `/avatar/${fileName}`, // bequem im Frontend
        },
      });
      return prev?.imageKey ?? null;
    });

    // Alten physisch löschen (best-effort)
    if (oldKey && oldKey !== fileName) {
      const oldPath = path.join(baseDir, path.basename(oldKey));
      try {
        if (existsSync(oldPath)) await unlink(oldPath);
      } catch (e) {
        console.warn("Avatar cleanup failed:", e);
      }
    }

    return NextResponse.json({
      success: true,
      imageUrl: `/avatar/${fileName}`,
      message: "Avatar erfolgreich hochgeladen",
    });
  } catch (e) {
    console.error(e);
    return NextResponse.json({ error: "Fehler beim Avatar-Upload" }, { status: 500 });
  }
}
