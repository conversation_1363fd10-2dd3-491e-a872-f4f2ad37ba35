import { z } from "zod";
import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import prisma from "~/server/db/prisma";
import nodemailer from "nodemailer";
import { env } from "~/env.js";
import crypto from "crypto";
import { Role } from "@prisma/client";
import { getServerSession } from "next-auth/next";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";

const InviteUserSchema = z.object({
  firstName: z.string(),
  lastName: z.string(),
  email: z.string().email(),
  identifier: z.string().optional(),
  userGroupId: z.string().optional(),
});

export async function POST(request: NextRequest) {
  const session = await getServerSession(authOptions);

  if (!session) {
    return NextResponse.json({ error: "Not authenticated" }, { status: 401 });
  }

  if (!session.user.selectedOu.id) {
    return NextResponse.json({ error: "No selected ou" }, { status: 400 });
  }

  if (session.user.role !== Role.CARD_MANAGER && session.user.role !== Role.ADMIN) {
    return NextResponse.json({ error: "No permission to invite" }, { status: 401 });
  }

  const result = InviteUserSchema.safeParse(await request.json());

  if (!result.success) {
    return NextResponse.json({ error: result.error }, { status: 400 });
  }

  const { firstName, lastName, email, identifier, userGroupId } = result.data;

  const existingUser = await prisma.user.findUnique({ where: { email } });

  if (existingUser) {
    return NextResponse.json({ error: "Email already exists." }, { status: 409 });
  }

  try {
    // Validate userGroup if provided
    if (userGroupId) {
      const userGroup = await prisma.userGroup.findUnique({
        where: { id: userGroupId },
      });

      if (!userGroup) {
        return NextResponse.json({ error: "Nutzergruppe nicht gefunden" }, { status: 400 });
      }

      if (userGroup.ouId !== session.user.selectedOu.id) {
        return NextResponse.json({ error: "Nutzergruppe gehört nicht zur ausgewählten OU" }, { status: 400 });
      }
    }

    const randomSecret = crypto.randomBytes(32).toString("hex");

    const user = await prisma.user.create({
      data: {
        name: `${firstName}`,
        lastName: `${lastName}`,
        email: email,
        role: Role.CARD_HOLDER,
        signUpHash: randomSecret,
        ouId: session.user.selectedOu.id,
        selectedOuId: session.user.selectedOu.id,
        identifier: identifier,
        userGroupId: userGroupId,
      },
    });

    const transporter = nodemailer.createTransport({
      port: 465,
      host: env.EMAIL_SERVER_HOST,
      auth: {
        user: env.EMAIL_SERVER_USER,
        pass: env.EMAIL_SERVER_PASSWORD,
      },
    });
    // todo oucolor verwenden fürs email template
    await transporter.sendMail({
      from: "<EMAIL>",
      to: email,
      subject: "Einladung zur Registrierung",
      html: `<!DOCTYPE html>
            <html>
            <head>
                <title>Registrierung abschließen</title>
                <style>
                    body { font-family: Arial, sans-serif; margin: 40px; }
                    .container { background-color: #FDFDFD; padding: 20px; border-radius: 10px; }
                    h1 { color: #214958; }
                    p { color: #214958; }
                    a {font-size: large; color:#214958; }
                </style>
            </head>
            <body>
            <div class="container">
                <h1>Registrierung abschließen</h1>
                <p>Hallo ${firstName} ${lastName},</p>
                <p>Bitte klicke <a href="${
                  env.NEXT_PUBLIC_SITE_URL
                }/signup/${randomSecret}/${encodeURIComponent(
                  email,
                )}">hier</a>, um deine Registrierung abzuschließen und Zugang zum Ladeportal zu erhalten:</p>

                <p>Falls du keine Einladung erwartet hast, ignoriere bitte diese E-Mail.</p>
                <p>Beste Grüße <br>Ihr Team von Eulektro</p>
            </div>
            </body>
            </html>
        `,
    });

    return NextResponse.json(user, { status: 201 });
  } catch (e: unknown) {
    if (e instanceof Error) {
      Logger("API 500 Error", "API 500", "src/app/api/user/invite/route.ts", LogType.ERROR);
    return NextResponse.json({ error: e.message }, { status: 500 });
    }
    Logger("API 500 Error", "API 500", "src/app/api/user/invite/route.ts", LogType.ERROR);
    return NextResponse.json({ error: "Unknown error occurred." }, { status: 500 });
  }
}
