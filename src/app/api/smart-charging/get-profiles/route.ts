import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { LongshipHeaders } from "~/utils/longship";
import { env } from "~/env";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";

export const dynamic = "force-dynamic";

export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized", message: "Nicht authentifiziert" },
        { status: 401 }
      );
    }

    // Check if user has appropriate role - only ADMIN allowed
    if (session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Forbidden", message: "Keine Berechtigung für Smart Charging" },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const chargePointId = searchParams.get("chargePointId");

    if (!chargePointId) {
      return NextResponse.json(
        { error: "Bad Request", message: "Ladepunkt ID ist erforderlich" },
        { status: 400 }
      );
    }

    // Send request to Longship API to get charging profiles
    const headers = LongshipHeaders({});
    const longshipUrl = `${env.LONGSHIP_API}chargepoints/${chargePointId}/getchargingprofiles`;

    Logger(
      `Getting charging profiles for ${chargePointId} by user ${session.user.email}`,
      "Smart Charging Profiles Get",
      "SmartCharging",
      LogType.INFO
    );

    const response = await fetch(longshipUrl, {
      method: "POST",
      headers: {
        ...headers,
        "Content-Type": "application/json",
      },
      body: JSON.stringify({}), // Empty body to get all profiles
    });

    if (!response.ok) {
      const errorText = await response.text();
      Logger(
        `Failed to get charging profiles for ${chargePointId}: ${response.status} - ${errorText}`,
        "Smart Charging Profiles Get Failed",
        "SmartCharging",
        LogType.ERROR
      );

      return NextResponse.json(
        {
          error: "Longship API Error",
          message: `Fehler beim Abrufen der Charging Profiles: ${response.status}`,
          details: errorText,
        },
        { status: response.status }
      );
    }

    const result = await response.json();

    Logger(
      `Successfully retrieved charging profiles for ${chargePointId} by user ${session.user.email}`,
      "Smart Charging Profiles Get Success",
      "SmartCharging",
      LogType.INFO
    );

    return NextResponse.json({
      success: true,
      data: result,
      chargePointId,
    });
  } catch (error) {
    {
      const msg = "Error getting charging profiles:";
      const errorMsg = (error as Error)?.message || String(error);
      Logger(`${msg}: ${errorMsg}`, "API 500", "src/app/api/smart-charging/get-profiles/route.ts", LogType.ERROR);
    }
    
    Logger(
      `Error getting charging profiles: ${error}`,
      "Smart Charging Profiles Get Error",
      "SmartCharging",
      LogType.ERROR
    );

    Logger("API 500 Error", "API 500", "src/app/api/smart-charging/get-profiles/route.ts", LogType.ERROR);
    return NextResponse.json(
      {
        error: "Internal Server Error",
        message: "Interner Serverfehler beim Abrufen der Charging Profiles",
      },
      { status: 500 }
    );
  }
}
