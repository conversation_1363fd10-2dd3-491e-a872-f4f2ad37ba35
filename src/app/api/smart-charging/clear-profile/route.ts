import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { LongshipHeaders } from "~/utils/longship";
import { env } from "~/env";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";
import { z } from "zod";
import { ChargingProfilePurposeType, ChargingProfileTargetType } from "~/types/ocpp-smart-charging";
import prisma from "~/server/db/prisma";
import { getOusBelowOu } from "~/server/model/ou/func";

export const dynamic = "force-dynamic";

// Zod schema for validation
const ClearChargingProfileRequestSchema = z.object({
  targetType: z.nativeEnum(ChargingProfileTargetType),
  chargePointId: z.string().optional(),
  locationId: z.string().optional(),
  id: z.coerce.number().optional(),
  connectorId: z.coerce.number().min(0),
  chargingProfilePurpose: z.nativeEnum(ChargingProfilePurposeType).optional(),
  stackLevel: z.coerce.number().min(0).optional(),
});

type ClearChargingProfileRequest = z.infer<typeof ClearChargingProfileRequestSchema>;

interface LongshipClearRequest {
  id?: number;
  connectorId: number;
  chargingProfilePurpose?: ChargingProfilePurposeType;
  stackLevel?: number;
}

export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized", message: "Nicht authentifiziert" },
        { status: 401 },
      );
    }

    // Check if user has appropriate role - only ADMIN allowed
    if (session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Forbidden", message: "Keine Berechtigung für Smart Charging" },
        { status: 403 },
      );
    }

    const body = await request.json();
    const validationResult = ClearChargingProfileRequestSchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: "Bad Request",
          message: "Ungültige Anfrage",
          details: validationResult.error.errors,
        },
        { status: 400 },
      );
    }

    const formData = validationResult.data;

    // Determine target charge points based on target type
    let targetChargePoints: string[] = [];

    switch (formData.targetType) {
      case ChargingProfileTargetType.SingleChargePoint:
        if (!formData.chargePointId) {
          return NextResponse.json(
            { error: "Bad Request", message: "Ladepunkt ID ist erforderlich" },
            { status: 400 },
          );
        }
        targetChargePoints = [formData.chargePointId];
        break;

      case ChargingProfileTargetType.LocationChargePoints:
        if (!formData.locationId) {
          return NextResponse.json(
            { error: "Bad Request", message: "Standort ID ist erforderlich" },
            { status: 400 },
          );
        }

        // Get charge points for this location
        const accessibleOus = await getOusBelowOu(session.user.selectedOu);
        const ouIds = accessibleOus.map((ou) => ou.id);

        // Verify location access
        const location = await prisma.location.findFirst({
          where: {
            id: formData.locationId,
            ouId: { in: ouIds },
          },
        });

        if (!location) {
          return NextResponse.json(
            { error: "Forbidden", message: "Keine Berechtigung für diesen Standort" },
            { status: 403 },
          );
        }

        // Get charge points via EVSEs
        const evses = await prisma.evse.findMany({
          where: {
            locationId: formData.locationId,
            chargePointId: { not: null },
          },
          include: {
            chargePoint: {
              select: { chargePointId: true },
            },
          },
        });

        targetChargePoints = evses
          .filter((evse) => evse.chargePoint)
          .map((evse) => evse.chargePoint!.chargePointId)
          .filter((value, index, self) => self.indexOf(value) === index); // Remove duplicates
        break;

      case ChargingProfileTargetType.AllChargePoints:
        // Get all charge points in accessible OUs
        const allAccessibleOus = await getOusBelowOu(session.user.selectedOu);
        const allOuIds = allAccessibleOus.map((ou) => ou.id);

        const allChargePoints = await prisma.chargePoint.findMany({
          where: {
            ouId: { in: allOuIds },
          },
          select: { chargePointId: true },
        });

        targetChargePoints = allChargePoints.map((cp) => cp.chargePointId);
        break;

      default:
        return NextResponse.json(
          { error: "Bad Request", message: "Ungültiger Zieltyp" },
          { status: 400 },
        );
    }

    if (targetChargePoints.length === 0) {
      return NextResponse.json(
        { error: "Bad Request", message: "Keine Ladepunkte gefunden" },
        { status: 400 },
      );
    }

    // Prepare Longship API request
    const clearRequest: LongshipClearRequest = {
      connectorId: Math.round(Number(formData.connectorId) || 0),
      ...(formData.id && { id: Math.round(Number(formData.id) || 0) }),
      ...(formData.chargingProfilePurpose && {
        chargingProfilePurpose: formData.chargingProfilePurpose,
      }),
      ...(formData.stackLevel !== undefined && { stackLevel: Math.round(Number(formData.stackLevel) || 0) }),
    };

    // Send requests to all target charge points
    const headers = LongshipHeaders({});
    const results: Array<{ chargePointId: string; success: boolean; error?: string }> = [];

    Logger(
      `Clearing charging profile for ${targetChargePoints.length} charge points by user ${session.user.email}`,
      "Smart Charging Profile Clear",
      "SmartCharging",
      LogType.INFO,
    );

    for (const chargePointId of targetChargePoints) {
      try {
        const longshipUrl = `${env.LONGSHIP_API}chargepoints/${chargePointId}/clearchargingprofile`;

        const response = await fetch(longshipUrl, {
          method: "POST",
          headers: headers,
          body: JSON.stringify(clearRequest),
        });

        if (response.ok) {
          results.push({ chargePointId, success: true });
          Logger(
            `Successfully cleared charging profile for ${chargePointId}`,
            "Smart Charging Profile Clear Success",
            "SmartCharging",
            LogType.INFO,
          );
        } else {
          const errorText = await response.text();
          results.push({
            chargePointId,
            success: false,
            error: `${response.status}: ${errorText}`,
          });
          Logger(
            `Failed to clear charging profile for ${chargePointId}: ${response.status} - ${errorText}`,
            "Smart Charging Profile Clear Failed",
            "SmartCharging",
            LogType.ERROR,
          );
        }
      } catch (error) {
        results.push({
          chargePointId,
          success: false,
          error: `Network error: ${error}`,
        });
        Logger(
          `Error clearing charging profile for ${chargePointId}: ${error}`,
          "Smart Charging Profile Clear Error",
          "SmartCharging",
          LogType.ERROR,
        );
      }
    }

    const successCount = results.filter((r) => r.success).length;
    const failureCount = results.filter((r) => !r.success).length;

    return NextResponse.json({
      success: failureCount === 0,
      message:
        failureCount === 0
          ? `Charging Profile erfolgreich von ${successCount} Ladepunkten gelöscht`
          : `Charging Profile von ${successCount} von ${targetChargePoints.length} Ladepunkten gelöscht (${failureCount} Fehler)`,
      data: {
        targetType: formData.targetType,
        totalChargePoints: targetChargePoints.length,
        successCount,
        failureCount,
        results,
        clearRequest,
      },
    });
  } catch (error) {
    {
      const msg = "Error clearing charging profile:";
      const errorMsg = (error as Error)?.message || String(error);
      Logger(`${msg}: ${errorMsg}`, "API 500", "src/app/api/smart-charging/clear-profile/route.ts", LogType.ERROR);
    }

    Logger(
      `Error clearing charging profile: ${error}`,
      "Smart Charging Profile Clear Error",
      "SmartCharging",
      LogType.ERROR,
    );

    return NextResponse.json(
      {
        error: "Internal Server Error",
        message: "Interner Serverfehler beim Löschen des Charging Profiles",
      },
      { status: 500 },
    );
  }
}
