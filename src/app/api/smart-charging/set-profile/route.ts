import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { LongshipHeaders } from "~/utils/longship";
import { env } from "~/env";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";
import {
  ChargingProfileFormData,
  SetChargingProfileRequest,
  ChargingProfile,
  ChargingProfileTargetType,
} from "~/types/ocpp-smart-charging";
import prisma from "~/server/db/prisma";
import { getOusBelowOu } from "~/server/model/ou/func";

/**
 * Converts a datetime-local string to ISO 8601 UTC format with Z suffix
 * @param datetimeLocal - String from datetime-local input (YYYY-MM-DDTHH:mm)
 * @returns ISO 8601 UTC string with Z suffix (YYYY-MM-DDTHH:mm:ssZ)
 */
function formatDatetimeToUTC(datetimeLocal: string): string {
  if (!datetimeLocal) return datetimeLocal;

  // If already has Z suffix, return as is
  if (datetimeLocal.endsWith('Z')) return datetimeLocal;

  // If already has timezone info (+XX:XX or -XX:XX), convert to UTC
  if (datetimeLocal.includes('+') || datetimeLocal.match(/-\d{2}:\d{2}$/)) {
    return new Date(datetimeLocal).toISOString();
  }

  // Assume local datetime-local input is in local timezone, convert to UTC
  // Add seconds if not present
  const withSeconds = datetimeLocal.includes(':') && datetimeLocal.split(':').length === 2
    ? datetimeLocal + ':00'
    : datetimeLocal;

  // Add Z to indicate UTC (assuming the input is meant to be UTC)
  return withSeconds + 'Z';
}

export const dynamic = "force-dynamic";

export async function POST(request: NextRequest): Promise<NextResponse> {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json(
        { error: "Unauthorized", message: "Nicht authentifiziert" },
        { status: 401 },
      );
    }

    // Check if user has appropriate role - only ADMIN allowed
    if (session.user.role !== "ADMIN") {
      return NextResponse.json(
        { error: "Forbidden", message: "Keine Berechtigung für Smart Charging" },
        { status: 403 },
      );
    }

    const formData: ChargingProfileFormData = await request.json();

    // Validate required fields
    if (!formData.schedulePeriods || formData.schedulePeriods.length === 0) {
      return NextResponse.json(
        { error: "Bad Request", message: "Mindestens eine Schedule Period ist erforderlich" },
        { status: 400 },
      );
    }

    // Determine target charge points based on target type
    let targetChargePoints: string[] = [];

    switch (formData.targetType) {
      case ChargingProfileTargetType.SingleChargePoint:
        if (!formData.chargePointId) {
          return NextResponse.json(
            { error: "Bad Request", message: "Ladepunkt ID ist erforderlich" },
            { status: 400 },
          );
        }
        targetChargePoints = [formData.chargePointId];
        break;

      case ChargingProfileTargetType.LocationChargePoints:
        if (!formData.locationId) {
          return NextResponse.json(
            { error: "Bad Request", message: "Standort ID ist erforderlich" },
            { status: 400 },
          );
        }

        // Get charge points for this location
        const accessibleOus = await getOusBelowOu(session.user.selectedOu);
        const ouIds = accessibleOus.map((ou) => ou.id);

        // Verify location access
        const location = await prisma.location.findFirst({
          where: {
            id: formData.locationId,
            ouId: { in: ouIds },
          },
        });

        if (!location) {
          return NextResponse.json(
            { error: "Forbidden", message: "Keine Berechtigung für diesen Standort" },
            { status: 403 },
          );
        }

        // Get charge points via EVSEs
        const evses = await prisma.evse.findMany({
          where: {
            locationId: formData.locationId,
            chargePointId: { not: null },
          },
          include: {
            chargePoint: {
              select: { chargePointId: true },
            },
          },
        });

        targetChargePoints = evses
          .filter((evse) => evse.chargePoint)
          .map((evse) => evse.chargePoint!.chargePointId)
          .filter((value, index, self) => self.indexOf(value) === index); // Remove duplicates
        break;

      case ChargingProfileTargetType.AllChargePoints:
        // Get all charge points in accessible OUs
        const allAccessibleOus = await getOusBelowOu(session.user.selectedOu);
        const allOuIds = allAccessibleOus.map((ou) => ou.id);

        const allChargePoints = await prisma.chargePoint.findMany({
          where: {
            ouId: { in: allOuIds },
          },
          select: { chargePointId: true },
        });

        targetChargePoints = allChargePoints.map((cp) => cp.chargePointId);
        break;

      default:
        return NextResponse.json(
          { error: "Bad Request", message: "Ungültiger Zieltyp" },
          { status: 400 },
        );
    }

    if (targetChargePoints.length === 0) {
      return NextResponse.json(
        { error: "Bad Request", message: "Keine Ladepunkte gefunden" },
        { status: 400 },
      );
    }

    // Convert form data to OCPP charging profile format
    const chargingProfile: ChargingProfile = {
      chargingProfileId: Math.round(Number(formData.chargingProfileId) || 1),
      stackLevel: Math.round(Number(formData.stackLevel) || 0),
      chargingProfilePurpose: formData.chargingProfilePurpose,
      chargingProfileKind: formData.chargingProfileKind,
      chargingSchedule: {
        chargingRateUnit: formData.chargingRateUnit,
        chargingSchedulePeriod: formData.schedulePeriods.map((period) => ({
          startPeriod: Math.round(Number(period.startPeriod) || 0),
          limit: Math.round(Number(period.limit) || 0),
          ...(period.numberPhases && { numberPhases: Math.round(Number(period.numberPhases) || 1) }),
        })),
        ...(formData.duration && { duration: Math.round(Number(formData.duration) || 0) }),
        ...(formData.startSchedule && { startSchedule: formatDatetimeToUTC(formData.startSchedule) }),
        ...(formData.minChargingRate && { minChargingRate: Math.round(Number(formData.minChargingRate) || 0) }),
      },
      ...(formData.transactionId && { transactionId: Math.round(Number(formData.transactionId) || 0) }),
      ...(formData.recurrencyKind && { recurrencyKind: formData.recurrencyKind }),
      ...(formData.validFrom && { validFrom: formatDatetimeToUTC(formData.validFrom) }),
      ...(formData.validTo && { validTo: formatDatetimeToUTC(formData.validTo) }),
    };

    // Prepare Longship API request
    const setChargingProfileRequest: SetChargingProfileRequest = {
      connectorId: Math.round(Number(formData.connectorId) || 0),
      csChargingProfiles: chargingProfile,
    };

    // Send requests to all target charge points
    const headers = LongshipHeaders({});
    const results: Array<{ chargePointId: string; success: boolean; error?: string }> = [];

    Logger(
      `Setting charging profile for ${targetChargePoints.length} charge points by user ${session.user.email}`,
      "Smart Charging Profile Set",
      "SmartCharging",
      LogType.INFO,
    );

    for (const chargePointId of targetChargePoints) {
      try {
        const longshipUrl = `${env.LONGSHIP_API}chargepoints/${chargePointId}/setchargingprofile`;

        const response = await fetch(longshipUrl, {
          method: "POST",
          headers: headers,
          body: JSON.stringify(setChargingProfileRequest),
        });

        if (response.ok) {
          results.push({ chargePointId, success: true });
          Logger(
            `Successfully set charging profile ${formData.chargingProfileId} for ${chargePointId}`,
            "Smart Charging Profile Set Success",
            "SmartCharging",
            LogType.INFO,
          );
        } else {
          const errorText = await response.text();
          results.push({
            chargePointId,
            success: false,
            error: `${response.status}: ${errorText}`,
          });
          Logger(
            `Failed to set charging profile for ${chargePointId}: ${response.status} - ${errorText}`,
            "Smart Charging Profile Set Failed",
            "SmartCharging",
            LogType.ERROR,
          );
        }
      } catch (error) {
        results.push({
          chargePointId,
          success: false,
          error: `Network error: ${error}`,
        });
        Logger(
          `Error setting charging profile for ${chargePointId}: ${error}`,
          "Smart Charging Profile Set Error",
          "SmartCharging",
          LogType.ERROR,
        );
      }
    }

    const successCount = results.filter((r) => r.success).length;
    const failureCount = results.filter((r) => !r.success).length;

    return NextResponse.json({
      success: failureCount === 0,
      message:
        failureCount === 0
          ? `Charging Profile erfolgreich an ${successCount} Ladepunkte gesetzt`
          : `Charging Profile an ${successCount} von ${targetChargePoints.length} Ladepunkten gesetzt (${failureCount} Fehler)`,
      data: {
        targetType: formData.targetType,
        totalChargePoints: targetChargePoints.length,
        successCount,
        failureCount,
        results,
        chargingProfileId: formData.chargingProfileId,
        connectorId: formData.connectorId,
      },
    });
  } catch (error) {
    {
      const msg = "Error setting charging profile:";
      const errorMsg = (error as Error)?.message || String(error);
      Logger(`${msg}: ${errorMsg}`, "API 500", "src/app/api/smart-charging/set-profile/route.ts", LogType.ERROR);
    }

    Logger(
      `Error setting charging profile: ${error}`,
      "Smart Charging Profile Set Error",
      "SmartCharging",
      LogType.ERROR,
    );

    return NextResponse.json(
      {
        error: "Internal Server Error",
        message: "Interner Serverfehler beim Setzen des Charging Profiles",
      },
      { status: 500 },
    );
  }
}
