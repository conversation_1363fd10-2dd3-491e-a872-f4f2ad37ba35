import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import prisma from "~/server/db/prisma";
import { PlugAndChargeEventType } from "@prisma/client";
import { getServerSession } from "next-auth/next";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { getOusBelowOu } from "~/server/model/ou/func";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";

// Zod Schema für die Validierung der eingehenden Daten
const PlugAndChargeEventSchema = z.object({
  emaid: z.string().optional(), // Optional for outgoing events
  evseid: z.string(),
  pcid: z.string().optional(), // Optional for outgoing events
  sessionId: z.string().optional(),
  matchingConfidence: z.number().min(0).max(1).optional(), // Optional for outgoing events
  plugInEventTimestamp: z.string().datetime(),
  locationId: z.string().optional(),
  latitude: z.number().optional(),
  longitude: z.number().optional(),
  type: z.enum(["INCOMING", "OUTGOING"]).default("INCOMING"),
});

// GET - Alle PlugAndChargeEvents abrufen (mit optionalen Filtern)
export async function GET(request: NextRequest) {
  try {
    // Get user session for OU filtering
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ success: false, error: "Not authenticated" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const evseid = searchParams.get("evseid");
    const type = searchParams.get("type");
    const emaid = searchParams.get("emaid");
    const sessionId = searchParams.get("sessionId");
    const showLinked = searchParams.get("showLinked") === "true";
    const includeOus = searchParams.get("includeOus") === "true"; // if true and admin, include OU lists in response
    const limit = searchParams.get("limit");
    const offset = searchParams.get("offset");

    const where: any = {};

    // Always filter by selected OU and all child OUs (for all roles)
    if (!session.user.selectedOu?.id) {
      return NextResponse.json({ success: false, error: "No OU selected" }, { status: 400 });
    }
    const currentOu = await prisma.ou.findUnique({ where: { id: session.user.selectedOu.id } });
    if (currentOu) {
      const ousBelow = await getOusBelowOu(currentOu);
      const ouIds = ousBelow.map((o) => o.id);
      where.ouId = { in: ouIds };
    }

    if (evseid) where.evseid = evseid;
    if (type && (type === "INCOMING" || type === "OUTGOING")) {
      where.type = type as PlugAndChargeEventType;
    }
    if (emaid) where.emaid = emaid;
    if (sessionId) {
      where.OR = [{ sessionId: sessionId }];
    }

    const events = await prisma.plugAndChargeEvent.findMany({
      where,
      orderBy: { createdAt: "desc" },
      take: limit ? parseInt(limit) : undefined,
      skip: offset ? parseInt(offset) : undefined,
    });

    // Optionally include OU lists for admins when requested
    let ous: any = undefined;
    if (includeOus && session.user.role === "ADMIN") {
      const ousWithEvents = await prisma.ou.findMany({
        where: {
          plugAndChargeEvents: { some: {} },
        },
        select: {
          id: true,
          name: true,
          _count: { select: { plugAndChargeEvents: true } },
        },
        orderBy: { name: "asc" },
      });

      const allOus = await prisma.ou.findMany({
        select: { id: true, name: true },
        orderBy: { name: "asc" },
      });

      ous = { ousWithEvents, allOus };
    }

    return NextResponse.json({
      success: true,
      data: events,
      count: events.length,
      ...(ous ? { ous } : {}),
    });
  } catch (error) {
    {
      const msg = "Error fetching PlugAndChargeEvents:";
      const errorMsg = (error as Error)?.message || String(error);
      Logger(
        `${msg}: ${errorMsg}`,
        "API 500",
        "src/app/api/plug-and-charge-events/route.ts",
        LogType.ERROR,
      );
    }
    Logger(
      "API 500 Error",
      "API 500",
      "src/app/api/plug-and-charge-events/route.ts",
      LogType.ERROR,
    );
    return NextResponse.json(
      {
        success: false,
        error: "Failed to fetch PlugAndChargeEvents",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    );
  }
}

// POST - Neues PlugAndChargeEvent erstellen
export async function POST(request: NextRequest) {
  try {
    // Get user session for OU assignment
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ success: false, error: "Not authenticated" }, { status: 401 });
    }

    // For non-admin users, require selectedOu
    if (session.user.role !== "ADMIN" && !session.user.selectedOu?.id) {
      return NextResponse.json({ success: false, error: "No OU selected" }, { status: 400 });
    }

    const rawBody = await request.json();
    console.log("Received PlugAndChargeEvent:", rawBody);

    // Validierung der eingehenden Daten
    const validationResult = PlugAndChargeEventSchema.safeParse(rawBody);

    if (!validationResult.success) {
      return NextResponse.json(
        {
          success: false,
          error: "Validation failed",
          details: validationResult.error.format(),
        },
        { status: 400 },
      );
    }

    const validatedData = validationResult.data;

    // Event in der Datenbank speichern
    const plugAndChargeEvent = await prisma.plugAndChargeEvent.create({
      data: {
        raw: rawBody, // Komplettes JSON als JSON-Typ speichern
        evseid: validatedData.evseid,
        locationId: validatedData.locationId,
        plugInEventTimestamp: new Date(validatedData.plugInEventTimestamp),
        latitude: validatedData.latitude,
        longitude: validatedData.longitude,
        type: validatedData.type as PlugAndChargeEventType,
        emaid: validatedData.emaid || null,
        pcid: validatedData.pcid || null,
        sessionId: validatedData.sessionId || null, // sessionId wird zu sessionId gemappt
        matchingConfidence: validatedData.matchingConfidence || null,
        ouId: session.user.selectedOu?.id || null, // Assign to user's selected OU (null for admin)
      },
    });

    console.log("Created PlugAndChargeEvent:", plugAndChargeEvent.id);

    return NextResponse.json(
      {
        success: true,
        data: plugAndChargeEvent,
        message: "PlugAndChargeEvent created successfully",
      },
      { status: 201 },
    );
  } catch (error) {
    {
      const msg = "Error creating PlugAndChargeEvent:";
      const errorMsg = (error as Error)?.message || String(error);
      Logger(
        `${msg}: ${errorMsg}`,
        "API 500",
        "src/app/api/plug-and-charge-events/route.ts",
        LogType.ERROR,
      );
    }
    Logger(
      "API 500 Error",
      "API 500",
      "src/app/api/plug-and-charge-events/route.ts",
      LogType.ERROR,
    );
    return NextResponse.json(
      {
        success: false,
        error: "Failed to create PlugAndChargeEvent",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    );
  }
}
