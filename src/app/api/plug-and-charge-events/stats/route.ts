import { NextRequest, NextResponse } from "next/server";
import prisma from "~/server/db/prisma";
import { PlugAndChargeEventType } from "@prisma/client";
import { getServerSession } from "next-auth/next";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { getOusBelowOu } from "~/server/model/ou/func";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";

/**
 * GET - Statistiken für PlugAndChargeEvents Dashboard
 */
export async function GET(request: NextRequest) {
  try {
    // Get user session for OU filtering
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ success: false, error: "Not authenticated" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const hours = parseInt(searchParams.get("hours") || "24");

    // Zeitraum für Statistiken
    const since = new Date(Date.now() - hours * 60 * 60 * 1000);

    // Always filter by selected OU and all child OUs
    const baseFilter: any = {};
    if (!session.user.selectedOu?.id) {
      return NextResponse.json({ success: false, error: "No OU selected" }, { status: 400 });
    }
    const currentOu = await prisma.ou.findUnique({ where: { id: session.user.selectedOu.id } });
    if (currentOu) {
      const ousBelow = await getOusBelowOu(currentOu);
      const ouIds = ousBelow.map((o) => o.id);
      baseFilter.ouId = { in: ouIds };
    }

    // Gesamtanzahl Events (filtered by OU)
    const totalEvents = await prisma.plugAndChargeEvent.count({
      where: baseFilter,
    });

    // Events in den letzten X Stunden (filtered by OU)
    const recentEvents = await prisma.plugAndChargeEvent.count({
      where: {
        ...baseFilter,
        createdAt: {
          gte: since,
        },
      },
    });

    // Events nach Typ (filtered by OU)
    const eventsByType = await prisma.plugAndChargeEvent.groupBy({
      by: ["type"],
      where: baseFilter,
      _count: {
        id: true,
      },
    });

    // Events in den letzten X Stunden nach Typ (filtered by OU)
    const recentEventsByType = await prisma.plugAndChargeEvent.groupBy({
      by: ["type"],
      where: {
        ...baseFilter,
        createdAt: {
          gte: since,
        },
      },
      _count: {
        id: true,
      },
    });

    // Verknüpfte Events: UUIDs die genau 2 Events haben (1 INCOMING + 1 OUTGOING)
    const linkedUuids = await prisma.plugAndChargeEvent.groupBy({
      by: ['uuid'],
      where: {
        ...baseFilter,
        uuid: { not: null },
      },
      having: {
        uuid: {
          _count: {
            equals: 2
          }
        }
      },
      _count: {
        uuid: true
      }
    });

    const linkedEventPairs = linkedUuids.length;

    // Unique EVSEs mit Events (filtered by OU)
    const uniqueEvses = await prisma.plugAndChargeEvent.findMany({
      where: baseFilter,
      select: {
        evseid: true,
      },
      distinct: ["evseid"],
    });

    // Letzte Events für Live-Anzeige (filtered by OU)
    const latestEvents = await prisma.plugAndChargeEvent.findMany({
      where: baseFilter,
      orderBy: {
        createdAt: "desc",
      },
      take: 10,
      select: {
        id: true,
        type: true,
        evseid: true,
        createdAt: true,
        emaid: true,
        sessionId: true,
        raw: true,
      },
    });

    // Events pro Stunde für die letzten 24 Stunden (filtered by selected OU + children)
    let hourlyStatsRaw;
    const ouIdsForSql = (baseFilter.ouId?.in as string[]) || [];
    if (ouIdsForSql.length > 0) {
      // Use raw query with IN clause for multiple OU IDs
      const placeholders = ouIdsForSql.map(() => "?").join(",");
      hourlyStatsRaw = (await prisma.$queryRawUnsafe(
        `SELECT DATE_FORMAT(createdAt, '%Y-%m-%d %H:00:00') as hour, type, COUNT(*) as count
         FROM PlugAndChargeEvent
         WHERE createdAt >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
           AND ouId IN (${placeholders})
         GROUP BY DATE_FORMAT(createdAt, '%Y-%m-%d %H:00:00'), type
         ORDER BY hour DESC`,
        ...ouIdsForSql,
      )) as Array<{ hour: string; type: string; count: bigint }>;
    } else {
      // Fallback: no OU ids, should not happen due to earlier check
      hourlyStatsRaw = [] as any;
    }

    // Convert BigInt to number for JSON serialization
    const hourlyStats = hourlyStatsRaw.map((stat) => ({
      hour: stat.hour,
      type: stat.type,
      count: Number(stat.count),
    }));

    // Durchschnittliche Matching Confidence für incoming events (filtered by OU)
    const avgMatchingConfidence = await prisma.plugAndChargeEvent.aggregate({
      where: {
        ...baseFilter,
        type: PlugAndChargeEventType.INCOMING,
        matchingConfidence: {
          not: null,
        },
      },
      _avg: {
        matchingConfidence: true,
      },
    });

    // Format der Antwort
    const stats = {
      total: {
        events: totalEvents,
        incoming:
          eventsByType.find((e) => e.type === PlugAndChargeEventType.INCOMING)?._count.id || 0,
        outgoing:
          eventsByType.find((e) => e.type === PlugAndChargeEventType.OUTGOING)?._count.id || 0,
        linkedPairs: linkedEventPairs,
        uniqueEvses: uniqueEvses.length,
      },
      recent: {
        events: recentEvents,
        incoming:
          recentEventsByType.find((e) => e.type === PlugAndChargeEventType.INCOMING)?._count.id ||
          0,
        outgoing:
          recentEventsByType.find((e) => e.type === PlugAndChargeEventType.OUTGOING)?._count.id ||
          0,
        hours: hours,
      },
      quality: {
        avgMatchingConfidence: avgMatchingConfidence._avg.matchingConfidence || 0,
        linkingRate: totalEvents > 0 ? (linkedEventPairs * 2 / totalEvents) * 100 : 0,
      },
      latest: latestEvents,
      hourly: hourlyStats,
    };

    return NextResponse.json({
      success: true,
      data: stats,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    {
      const msg = "Error fetching PlugAndChargeEvent stats:";
      const errorMsg = (error as Error)?.message || String(error);
      Logger(
        `${msg}: ${errorMsg}`,
        "API 500",
        "src/app/api/plug-and-charge-events/stats/route.ts",
        LogType.ERROR,
      );
    }
    return NextResponse.json(
      {
        success: false,
        error: "Failed to fetch stats",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    );
  }
}
