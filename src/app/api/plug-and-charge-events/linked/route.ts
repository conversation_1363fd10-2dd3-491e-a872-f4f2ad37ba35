import { NextRequest, NextResponse } from "next/server";
import prisma from "~/server/db/prisma";
import { PlugAndChargeEventType } from "@prisma/client";
import { getServerSession } from "next-auth/next";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { getOusBelowOu } from "~/server/model/ou/func";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";

/**
 * GET - Findet verknüpfte PlugAndChargeEvents (incoming + outgoing pairs)
 * Query Parameter:
 * - evseid: Filtert nach EVSE ID
 * - sessionId: Filtert nach Session ID
 * - limit: Anzahl der Ergebnisse begrenzen
 * - offset: Offset für Paginierung
 */
export async function GET(request: NextRequest) {
  try {
    // Get user session for OU filtering
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ success: false, error: "Not authenticated" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const evseid = searchParams.get("evseid");
    const sessionId = searchParams.get("sessionId");
    const limit = searchParams.get("limit");
    const offset = searchParams.get("offset");

    const where: any = {};

    // Always filter by selected OU and all child OUs
    if (!session.user.selectedOu?.id) {
      return NextResponse.json({ success: false, error: "No OU selected" }, { status: 400 });
    }
    const currentOu = await prisma.ou.findUnique({ where: { id: session.user.selectedOu.id } });
    if (currentOu) {
      const ousBelow = await getOusBelowOu(currentOu);
      const ouIds = ousBelow.map((o) => o.id);
      where.ouId = { in: ouIds };
    }

    if (evseid) where.evseid = evseid;
    if (sessionId) {
      where.OR = [{ sessionId: sessionId }];
    }

    // Finde alle UUIDs die genau 2 Events haben (1 INCOMING + 1 OUTGOING)
    const linkedUuids = await prisma.plugAndChargeEvent.groupBy({
      by: ["uuid"],
      where: {
        ...where,
        uuid: { not: null },
      },
      having: {
        uuid: {
          _count: {
            equals: 2,
          },
        },
      },
      _count: {
        uuid: true,
      },
    });

    // Hole alle Events für die gefundenen UUIDs
    const linkedEvents = await prisma.plugAndChargeEvent.findMany({
      where: {
        ...where,
        uuid: {
          in: linkedUuids.map((item) => item.uuid!),
        },
      },
      orderBy: { createdAt: "desc" },
    });

    // Gruppiere Events nach UUID und erstelle Paare
    const eventsByUuid = linkedEvents.reduce(
      (acc, event) => {
        if (!event.uuid) return acc;
        if (!acc[event.uuid]) {
          acc[event.uuid] = [];
        }
        acc[event.uuid].push(event);
        return acc;
      },
      {} as Record<string, typeof linkedEvents>,
    );

    // Erstelle Event-Paare
    const linkedEventPairs = Object.entries(eventsByUuid)
      .map(([uuid, events]) => {
        const incomingEvent = events.find((e) => e.type === PlugAndChargeEventType.INCOMING);
        const outgoingEvents = events.filter((e) => e.type === PlugAndChargeEventType.OUTGOING);

        return {
          incoming: incomingEvent,
          outgoing: outgoingEvents,
          uuid: uuid,
          evseid: incomingEvent?.evseid || outgoingEvents[0]?.evseid,
          linkedCount: outgoingEvents.length,
        };
      })
      .filter((pair) => pair.incoming); // Nur Paare mit incoming event

    // Paginierung anwenden
    const startIndex = offset ? parseInt(offset) : 0;
    const endIndex = limit ? startIndex + parseInt(limit) : undefined;
    const paginatedPairs = linkedEventPairs.slice(startIndex, endIndex);

    // Filtere nur Paare mit mindestens einem outgoing event (falls gewünscht)
    const hasLinkedOnly = searchParams.get("hasLinkedOnly") === "true";
    const filteredPairs = hasLinkedOnly
      ? paginatedPairs.filter((pair) => pair.linkedCount > 0)
      : paginatedPairs;

    return NextResponse.json({
      success: true,
      data: filteredPairs,
      count: filteredPairs.length,
      totalLinked: linkedEventPairs.length,
    });
  } catch (error) {
    {
      const msg = "Error fetching linked PlugAndChargeEvents:";
      const errorMsg = (error as Error)?.message || String(error);
      Logger(
        `${msg}: ${errorMsg}`,
        "API 500",
        "src/app/api/plug-and-charge-events/linked/route.ts",
        LogType.ERROR,
      );
    }
    Logger(
      "API 500 Error",
      "API 500",
      "src/app/api/plug-and-charge-events/linked/route.ts",
      LogType.ERROR,
    );
    return NextResponse.json(
      {
        success: false,
        error: "Failed to fetch linked PlugAndChargeEvents",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    );
  }
}

/**
 * POST - Verknüpft ein outgoing event mit einem incoming event über UUID
 * Body: { outgoingEventId: string, incomingEventId: string, uuid: string }
 */
export async function POST(request: NextRequest) {
  try {
    // Get user session for OU filtering
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ success: false, error: "Not authenticated" }, { status: 401 });
    }

    const { outgoingEventId, incomingEventId, uuid } = await request.json();

    if (!outgoingEventId || !incomingEventId || !uuid) {
      return NextResponse.json(
        {
          success: false,
          error: "Missing required fields: outgoingEventId, incomingEventId and uuid",
        },
        { status: 400 },
      );
    }

    // Überprüfe, ob das outgoing event existiert (und zur OU gehört für non-admin)
    const outgoingEventWhere: any = { id: outgoingEventId };
    if (session.user.role !== "ADMIN" && session.user.selectedOu?.id) {
      outgoingEventWhere.ouId = session.user.selectedOu.id;
    }

    const outgoingEvent = await prisma.plugAndChargeEvent.findUnique({
      where: outgoingEventWhere,
    });

    if (!outgoingEvent) {
      return NextResponse.json(
        { success: false, error: "Outgoing event not found" },
        { status: 404 },
      );
    }

    if (outgoingEvent.type !== PlugAndChargeEventType.OUTGOING) {
      return NextResponse.json(
        { success: false, error: "Event is not an outgoing event" },
        { status: 400 },
      );
    }

    // Überprüfe, ob das incoming event existiert (und zur OU gehört für non-admin)
    const incomingEventWhere: any = {
      id: incomingEventId,
      type: PlugAndChargeEventType.INCOMING,
    };
    if (session.user.role !== "ADMIN" && session.user.selectedOu?.id) {
      incomingEventWhere.ouId = session.user.selectedOu.id;
    }

    const incomingEvent = await prisma.plugAndChargeEvent.findFirst({
      where: incomingEventWhere,
    });

    if (!incomingEvent) {
      return NextResponse.json(
        { success: false, error: "Incoming event not found" },
        { status: 404 },
      );
    }

    // Setze die UUID für beide Events und synchronisiere OU-Zuordnung
    const updateData: any = { uuid: uuid };

    // Falls OUTGOING keine ouId hat, aber INCOMING schon -> übernehme ouId vom incoming
    if (!outgoingEvent.ouId && incomingEvent.ouId) {
      updateData.ouId = incomingEvent.ouId;
    }

    // Update beide Events mit der gleichen UUID
    const [updatedOutgoingEvent, updatedIncomingEvent] = await Promise.all([
      prisma.plugAndChargeEvent.update({
        where: { id: outgoingEventId },
        data: updateData,
      }),
      prisma.plugAndChargeEvent.update({
        where: { id: incomingEventId },
        data: { uuid: uuid },
      }),
    ]);

    // Falls INCOMING keine ouId hat, aber OUTGOING (ggf. soeben gesetzt) -> ouId am incoming nachziehen
    const resultingOuId = updatedOutgoingEvent.ouId || outgoingEvent.ouId || incomingEvent.ouId;
    if (!incomingEvent.ouId && resultingOuId) {
      await prisma.plugAndChargeEvent.update({
        where: { id: incomingEvent.id },
        data: { ouId: resultingOuId },
      });
    }

    return NextResponse.json({
      success: true,
      data: {
        outgoing: updatedOutgoingEvent,
        incoming: updatedIncomingEvent,
      },
      message: "Events successfully linked via UUID (OU synced where missing)",
    });
  } catch (error) {
    {
      const msg = "Error linking PlugAndChargeEvents:";
      const errorMsg = (error as Error)?.message || String(error);
      Logger(
        `${msg}: ${errorMsg}`,
        "API 500",
        "src/app/api/plug-and-charge-events/linked/route.ts",
        LogType.ERROR,
      );
    }
    Logger(
      "API 500 Error",
      "API 500",
      "src/app/api/plug-and-charge-events/linked/route.ts",
      LogType.ERROR,
    );
    return NextResponse.json(
      {
        success: false,
        error: "Failed to link events",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    );
  }
}
