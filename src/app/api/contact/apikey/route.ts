import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { z } from "zod";
import { Role, NotificationType } from "@prisma/client";
import prisma from "~/server/db/prisma";
import { generateUniqueApiKey } from "~/utils/apiAuth/apiKeyUtil";
import { createSystemNotificationForAdmins } from "~/utils/notifications/createSystemNotification";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";

const GenerateApiKeySchema = z.object({
  contactId: z.string(),
});

const DeleteApiKeySchema = z.object({
  contactId: z.string(),
});

/**
 * Generate a new API key for a contact
 */
export async function POST(request: NextRequest) {
  const session = await getServerSession(authOptions);

  if (!session || session?.user?.role !== Role.ADMIN) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const body = await request.json();
    const validationResult = GenerateApiKeySchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        { error: "Invalid request data", details: validationResult.error.errors },
        { status: 400 }
      );
    }

    const { contactId } = validationResult.data;

    // Check if contact exists and is a CPO
    const contact = await prisma.contact.findUnique({
      where: { id: contactId },
    });

    if (!contact) {
      return NextResponse.json({ error: "Contact not found" }, { status: 404 });
    }

    if (!contact.cpo) {
      return NextResponse.json(
        { error: "API Keys können nur für CPO-Contacts generiert werden" },
        { status: 400 }
      );
    }

    // Generate unique API key
    const apiKey = await generateUniqueApiKey();

    // Update contact with new API key
    const updatedContact = await prisma.contact.update({
      where: { id: contactId },
      data: { apiKey },
      select: {
        id: true,
        name: true,
        companyName: true,
        apiKey: true,
      },
    });

    // Create admin notification
    await createSystemNotificationForAdmins({
      nachricht: `🔑 Neuer API Key generiert für CPO-Contact "${contact.name || contact.companyName || 'Unbekannt'}" (ID: ${contact.id})`,
      type: NotificationType.INFO,
    });

    return NextResponse.json({
      message: "API key generated successfully",
      contact: updatedContact,
    });
  } catch (error) {
    {
      const msg = "Error generating API key:";
      const errorMsg = (error as Error)?.message || String(error);
      Logger(`${msg}: ${errorMsg}`, "API 500", "src/app/api/contact/apikey/route.ts", LogType.ERROR);
    }
    Logger("API 500 Error", "API 500", "src/app/api/contact/apikey/route.ts", LogType.ERROR);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

/**
 * Delete/remove API key from a contact
 */
export async function DELETE(request: NextRequest) {
  const session = await getServerSession(authOptions);

  if (!session || session?.user?.role !== Role.ADMIN) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const body = await request.json();
    const validationResult = DeleteApiKeySchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        { error: "Invalid request data", details: validationResult.error.errors },
        { status: 400 }
      );
    }

    const { contactId } = validationResult.data;

    // Check if contact exists and is a CPO
    const contact = await prisma.contact.findUnique({
      where: { id: contactId },
    });

    if (!contact) {
      return NextResponse.json({ error: "Contact not found" }, { status: 404 });
    }

    if (!contact.cpo) {
      return NextResponse.json(
        { error: "API Keys können nur von CPO-Contacts entfernt werden" },
        { status: 400 }
      );
    }

    // Remove API key from contact
    const updatedContact = await prisma.contact.update({
      where: { id: contactId },
      data: { apiKey: null },
      select: {
        id: true,
        name: true,
        companyName: true,
        apiKey: true,
      },
    });

    // Create admin notification
    await createSystemNotificationForAdmins({
      nachricht: `🗑️ API Key entfernt für CPO-Contact "${contact.name || contact.companyName || 'Unbekannt'}" (ID: ${contact.id})`,
      type: NotificationType.WARNING,
    });

    return NextResponse.json({
      message: "API key removed successfully",
      contact: updatedContact,
    });
  } catch (error) {
    {
      const msg = "Error removing API key:";
      const errorMsg = (error as Error)?.message || String(error);
      Logger(`${msg}: ${errorMsg}`, "API 500", "src/app/api/contact/apikey/route.ts", LogType.ERROR);
    }
    Logger("API 500 Error", "API 500", "src/app/api/contact/apikey/route.ts", LogType.ERROR);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

/**
 * Get all contacts with their API key status
 */
export async function GET(request: NextRequest) {
  const session = await getServerSession(authOptions);

  if (!session || session?.user?.role !== Role.ADMIN) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    let contacts;
    try {
      contacts = await prisma.contact.findMany({
        select: {
          id: true,
          name: true,
          companyName: true,
          apiKey: true,
          cpo: true,
          ou: {
            select: {
              id: true,
              name: true,
              code: true,
            },
          },
        },
        orderBy: {
          name: "asc",
        },
      });
    } catch (selectError) {
      // Fallback if apiKey field is not available yet
      console.warn("apiKey field not available, using fallback");
      const contactsWithoutApiKey = await prisma.contact.findMany({
        select: {
          id: true,
          name: true,
          companyName: true,
          cpo: true,
          ou: {
            select: {
              id: true,
              name: true,
              code: true,
            },
          },
        },
        orderBy: {
          name: "asc",
        },
      });

      contacts = contactsWithoutApiKey.map(contact => ({
        ...contact,
        apiKey: null,
      }));
    }

    return NextResponse.json({ contacts });
  } catch (error) {
    {
      const msg = "Error fetching contacts with API keys:";
      const errorMsg = (error as Error)?.message || String(error);
      Logger(`${msg}: ${errorMsg}`, "API 500", "src/app/api/contact/apikey/route.ts", LogType.ERROR);
    }
    Logger("API 500 Error", "API 500", "src/app/api/contact/apikey/route.ts", LogType.ERROR);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
