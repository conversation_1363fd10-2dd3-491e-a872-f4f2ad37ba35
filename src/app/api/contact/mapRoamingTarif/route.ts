import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import prisma from "~/server/db/prisma";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";

export async function POST(request: NextRequest) {
  const { contact, tarif, subscribed } = await request.json();

  try {
    if (subscribed) {
      await prisma.contact.update({
        where: {
          id: contact.id,
        },
        data: {
          tarifs: {
            create: {
              tarifId: tarif.id,
            },
          },
        },
      });
    } else {
      await prisma.tarifOnContacts.delete({
        where: {
          tarifId_contactId: {
            tarifId: tarif.id,
            contactId: contact.id,
          },
        },
      });
    }
    return NextResponse.json(contact);
  } catch (err) {
    return NextResponse.json(err, { status: 500 });
  }
}
