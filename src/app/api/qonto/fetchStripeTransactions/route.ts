import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import { fetchStripeTransactionsFromQonto } from "../../../../utils/qonto/transactions";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";

export const dynamic = "force-dynamic";

export async function GET(request: NextRequest) {
  const result = await fetchStripeTransactionsFromQonto();
  if (result) {
    return NextResponse.json("success");
  }
  return NextResponse.json("error", { status: 500 });
}
