import type { NextRequest } from "next/server";
import prisma from "~/server/db/prisma";
import { NextResponse } from "next/server";

export const dynamic = "force-dynamic";

import { z } from "zod";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";
import { uploadInvoiceToTransaction } from "~/utils/qonto/uploader";
import { env } from "~/env.js";

const MappingSchema = z.object({
  invoiceIds: z.array(z.string()).min(1),
  transactionIds: z.array(z.string()).min(1),
  multipleInvoicesFlag: z.boolean(),
  manualInvoiceAmounts: z.record(z.coerce.number()).nullable(),
});

const mapMultipleTransactionsToInvoice = async (invoiceId: string, transactionIds: string[]) => {
  const data = transactionIds.map((transactionId: string) => {
    return { invoiceId: invoiceId, qontoTransaction_id: transactionId };
  });

  try {
    await prisma.invoiceToQontoTransaction.createMany({
      data: data,
      skipDuplicates: true,
    });

    const aggregationResult = await prisma.qontoTransaction.aggregate({
      where: {
        transaction_id: {
          in: transactionIds,
        },
      },
      _sum: {
        amount: true, // Specify the fields you want to sum
      },
      _max: {
        emitted_at: true, // Specify the field for which you want the latest date
      },
    });

    await prisma.invoice.update({
      where: {
        id: invoiceId,
      },
      data: {
        paidAmount: aggregationResult._sum.amount,
        paidOnDate: aggregationResult._max.emitted_at,
      },
    });

    return NextResponse.json("okay", { status: 200 });
  } catch (error) {
    {
      const msg = "Error updating DB";
      const errorMsg = (error as Error)?.message || String(error);
      Logger(`${msg}: ${errorMsg}`,'API 500','src/app/api/qonto/mapInvoices/route.ts', LogType.ERROR);
    }
    return NextResponse.json("Error updating DB", { status: 500 });
  }
};

export const mapMultipleInvoicesToTransaction = async (
  invoiceIds: string[],
  transactionId: string,
  manualInvoiceAmounts: Record<string, number> | null = null,
) => {
  const data = invoiceIds.map((invoiceId: string) => {
    return { invoiceId: invoiceId, qontoTransaction_id: transactionId };
  });

  try {
    try {
      // Get the amount of the specified transaction
      const transaction = await prisma.qontoTransaction.findUnique({
        where: {
          transaction_id: transactionId,
        },
      });

      // Insert the respective corresponding amount into the 'paidAmount' field of each invoice
      for (const invoiceId of invoiceIds) {
        try {
          if (transaction && transaction.emitted_at) {
            await prisma.invoiceToQontoTransaction.createMany({
              data: data,
              skipDuplicates: true,
            });
            let amount;

            // check if manualInvoiceAmount is provided
            //if yes use it in prisma update query
            if (manualInvoiceAmounts) {
              amount = manualInvoiceAmounts[invoiceId]?.toString();
              if (!amount) {
                amount = "sumGross";
                Logger(
                  "Invoice Amount not found, using sumGross as backup",
                  "Invoice Amount not found",
                  "Finance",
                  LogType.WARN,
                );
              }
            } else {
              amount = "sumGross";
            }
            await prisma.$executeRawUnsafe(`UPDATE Invoice
                                                        SET paidAmount = ${amount},
                                                            paidOnDate = '${
                                                              new Date(transaction.emitted_at)
                                                                .toISOString()
                                                                .split("T")[0]
                                                            }'
                                                        WHERE id = '${invoiceId}'`);
            if (!env.DEBUG) {
              Logger(
                `Trying to upload to Qonto ${invoiceId} ${transactionId}`,
                "QontoUpload",
                "Finance",
                LogType.DEBUG,
              );
              await uploadInvoiceToTransaction(invoiceId, transactionId);
            } else {
              Logger(
                `Document not Uploaded because dev env.DEBUG = ${env.DEBUG}`,
                "QontoUpload",
                "Finance",
                LogType.DEBUG,
              );
            }
          } else {
            Logger(
              `Cannot upload file because transaction object or emitted_at is undefined`,
              "Invoice Update",
              "Finance",
              LogType.ERROR,
            );
          }
        } catch (error) {
          const errorMsg = (error as Error).message;
          Logger(
            `Error updating invoice with ID: ${invoiceId}. Error: ${errorMsg}`,
            "Invoice Update",
            "src/app/api/qonto/mapInvoices/route.ts",
            LogType.ERROR,
          );
          Logger(`Error updating invoice with ID: ${invoiceId}.`, 'API 500', 'src/app/api/qonto/mapInvoices/route.ts', LogType.ERROR);
          return NextResponse.json(`Error updating invoice with ID: ${invoiceId}.`, {
            status: 500,
          });
        }
      }

      Logger(
        `All invoices with IDs: ${invoiceIds.join(", ")} have been updated.`,
        "Invoice Update",
        "Finance",
        LogType.INFO,
      );
      return NextResponse.json("success", { status: 200 });
    } catch (error) {
      const errorMsg = (error as Error).message;
      Logger(
        `Error performing transaction verification. Error: ${errorMsg}`,
        "Transaction Verification",
        "src/app/api/qonto/mapInvoices/route.ts",
        LogType.ERROR,
      );
      Logger(`Error performing transaction verification.`, 'API 500', 'src/app/api/qonto/mapInvoices/route.ts', LogType.ERROR);
      return NextResponse.json(`Error performing transaction verification.`, {
        status: 500,
      });
    }
  } catch (error) {
    const errorMsg = (error as Error).message;
    Logger(
      `Error creating mapping. Error: ${errorMsg}`,
      "Transaction Invoice Mapping",
      "src/app/api/qonto/mapInvoices/route.ts",
      LogType.ERROR,
    );
    Logger(`Error Transaction Invoice Mapping.`, 'API 500', 'src/app/api/qonto/mapInvoices/route.ts', LogType.ERROR);
    return NextResponse.json(`Error Transaction Invoice Mapping.`, {
      status: 500,
    });
  }
};

/**
 * Endpoint will map multiple invoices to 1 transaction or multiple transactions to 1  invoice
 * @param request invoiceId(s), transactionId(s), multipleInvoicesFlag
 * @constructor
 */
export async function POST(request: NextRequest) {
  const zodCheckedPayload = MappingSchema.safeParse(await request.json());
  if (zodCheckedPayload.success) {
    const invoiceIds = zodCheckedPayload.data.invoiceIds;
    const transactionIds = zodCheckedPayload.data.transactionIds;
    const multipleInvoicesFlag = zodCheckedPayload.data.multipleInvoicesFlag;
    const manualInvoiceAmounts = zodCheckedPayload.data.manualInvoiceAmounts;
    if (multipleInvoicesFlag) {
      const response = await mapMultipleInvoicesToTransaction(
        invoiceIds,
        transactionIds[0] ?? "",
        manualInvoiceAmounts, // don't know why this could be undefined in zod.success if..
      );
      return response;
    } else {
      const response = await mapMultipleTransactionsToInvoice(
        invoiceIds[0] ?? "", // don't know why this could be undefined in zod.success if..
        transactionIds,
      );
      return response;
    }
  }
  {
    const msg = "ZOD error expected array.min(1),array.min(1),string";
    Logger(msg, 'API 500', 'src/app/api/qonto/mapInvoices/route.ts', LogType.ERROR);
  }
  return NextResponse.json("ZOD error expected array.min(1),array.min(1),string", {
    status: 500,
  });
}
