import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import { fetchMainTransactionsFromQonto } from "../../../../utils/qonto/transactions";
import { autoUploadInvoices } from "~/utils/qonto/uploader";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";

export const dynamic = "force-dynamic";

export async function GET(request: NextRequest) {
  const result = await fetchMainTransactionsFromQonto();

  if (result) {
    return NextResponse.json("success");
  }
  return NextResponse.json("error", { status: 500 });
}
