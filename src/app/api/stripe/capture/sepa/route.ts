import { type NextRequest, NextResponse } from "next/server";

import { stripe } from "~/utils/stripe/stripe";
import prisma from "~/server/db/prisma";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { KindOfInvoice, LogType, Role } from "@prisma/client";
import Logger from "~/server/logger/logger";
export async function POST(request: NextRequest) {
  const { invoiceId } = await request.json();
  const session = await getServerSession(authOptions);

  if (!session || session?.user?.role != Role.ADMIN) {
    return new Response("no auth", { status: 401 });
  }

  // Get PhysicalCard uid by its number
  const invoiceToCapture = await prisma?.invoice.findUnique({
    where: {
      id: invoiceId,
    },
    include: {
      user: true,
      paymentIntent: true,
      contact: true,
    },
  });

  if (!invoiceToCapture) {
    return new Response("Rechnung nicht gefunden", { status: 404 });
  }

  if (invoiceToCapture.paymentIntent) {
    Logger("API 500 Error", "API 500", "src/app/api/stripe/capture/sepa/route.ts", LogType.ERROR);
    return new Response("Invoice already captured", { status: 500 });
  }

  if (invoiceToCapture?.kindOfInvoice == KindOfInvoice.CREDIT) {
    Logger("API 500 Error", "API 500", "src/app/api/stripe/capture/sepa/route.ts", LogType.ERROR);
    return new Response("Credit can't be captured", { status: 500 });
  }
  let stripeUserId;
  stripeUserId = invoiceToCapture?.user?.stripeCustomerId;
  if (!stripeUserId) {
    stripeUserId = invoiceToCapture?.contact?.stripeCustomerId;
  }

  if (stripeUserId) {
    const stripeUser = await stripe.customers.retrieve(stripeUserId);
    if (!stripeUser.deleted) {
      const paymentMethodId = stripeUser?.invoice_settings?.default_payment_method;
      if (paymentMethodId && typeof paymentMethodId == "string") {
        const paymentIntent = await stripe.paymentIntents.create({
          customer: stripeUserId,
          payment_method: paymentMethodId,
          amount: Math.floor(invoiceToCapture.sumGross * 100),
          currency: "EUR",
          capture_method: "automatic",
          off_session: true,
          confirm: true,
          description: `Eulektro Rechnung ${invoiceToCapture?.invoiceNumber}`,
          statement_descriptor: `${invoiceToCapture?.invoiceNumber}`,
          payment_method_types: ["sepa_debit"],
          metadata: { eulektroInvoice: `${invoiceToCapture?.invoiceNumber}` },
        });
        try {
          await prisma.paymentIntent.create({
            data: {
              id: paymentIntent.id,
              status: paymentIntent.status,
              invoiceId: invoiceToCapture.id,
            },
          });
          return new Response("Capture triggert", { status: 200 });
        } catch (e) {
          const errorMsg = (e as Error).message;
          Logger(
            `Error creating db payment intent for ${invoiceToCapture.id} prisma: ${errorMsg}`,
            "Creating DB PaymentIntent failed",
            "Invoice",
            LogType.ERROR,
          );
        }
      }
    }
  } else {
    Logger("API 500 Error", "API 500", "src/app/api/stripe/capture/sepa/route.ts", LogType.ERROR);
    return new Response("Keinen Stripe User gefunden", { status: 500 });
  }

  Logger("API 500 Error", "API 500", "src/app/api/stripe/capture/sepa/route.ts", LogType.ERROR);
    return new Response("Fehler beim capture", { status: 500 });
}
