import { type NextRequest, NextResponse } from "next/server";

import { stripe } from "~/utils/stripe/stripe";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { LogType, Role } from "@prisma/client";
import Logger from "~/server/logger/logger";
export async function GET(request: NextRequest) {
  const paymentMethodId = request.nextUrl.searchParams.get("paymentMethodId");
  const session = await getServerSession(authOptions);

  if (!session || session?.user?.role != Role.ADMIN) {
    return new Response("no auth", { status: 401 });
  }
  try {
    if (!paymentMethodId) {
      return new Response("No payment method provided", { status: 404 });
    }
    const paymentMethod = await stripe.paymentMethods.detach(paymentMethodId);

    Logger(
      `Detached ${paymentMethodId} from user`,
      "Detached Payment Method",
      "Stripe",
      LogType.INFO,
    );
    return new Response("Payment Method Detached", { status: 200 });
  } catch (e) {
    Logger(
      `Error Detaching ${paymentMethodId} from user`,
      "Error Detaching Payment Method",
      "Stripe",
      LogType.ERROR,
    );
  }

  Logger("API 500 Error", "API 500", "src/app/api/stripe/detachPaymentMethodFromUser/route.ts", LogType.ERROR);
    return new Response("Fehler beim detach", { status: 500 });
}
