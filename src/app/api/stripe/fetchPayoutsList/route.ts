import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import prisma from "../../../../server/db/prisma";
import { stripe } from "../../../../utils/stripe/stripe";
import { Prisma } from "@prisma/client";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";

export const revalidate = 0;

export async function GET(request: NextRequest) {
  const payouts = await stripe.payouts.list();

  if (payouts.data.length > 0) {
    const payoutIds = payouts.data.map((payout) => payout.id);
    const existingPayouts = await prisma.stripePayout.findMany({
      where: { id: { in: payoutIds } },
      select: { id: true },
    });
    const existingIds = existingPayouts.map((payout) => payout.id);
    const newPayouts = payouts.data.filter((payout) => !existingIds.includes(payout.id));

    const newPayoutsTransformed = newPayouts.map((payout) => {
      return {
        id: payout.id,
        amount: payout.amount,
        arrival_date: new Date(payout.arrival_date * 1000),
        created: new Date(payout.created * 1000),
        automatic: payout.automatic,
      };
    });
    try {
      await prisma.stripePayout.createMany({
        data: newPayoutsTransformed,
      });
    } catch (e) {
      if (e instanceof Prisma.PrismaClientKnownRequestError) {
        NextResponse.json(e.stack ?? "No stacktrace available.. stripePayout.createMany error", {
          status: 500,
        });
      }
    }

    return NextResponse.json(payouts.data);
  }

  return NextResponse.json([]);
}
