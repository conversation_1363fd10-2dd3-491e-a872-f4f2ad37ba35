import type { NextRequest } from "next/server";
import fs, { readFileSync } from "fs";
import { NextResponse } from "next/server";
import prisma from "~/server/db/prisma";
import { env } from "~/env";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";

export async function GET(request: NextRequest) {
  const payoutId = request.nextUrl.searchParams.get("payoutId");

  if (payoutId) {
    // Pfade zur Datei und zum Dateinamen festlegen
    let fileRef;
    try {
      fileRef = await prisma?.fileRef.findFirst({
        where: { stripePayoutId: payoutId },
      });
    } catch (e) {
      return NextResponse.json("error - prisma error", { status: 500 });
    }

    if (!fileRef || !fileRef.path) {
      return NextResponse.json("error - file reference not found", {
        status: 404,
      });
    }
    // Voller Pfad zur Datei erstellen
    const fullPath = fileRef.path;

    // Prüfen, ob die Datei existiert
    if (fs.existsSync(fullPath)) {
      // Datei-Header für den Download setzen

      // Die Datei als Stream lesen und als Response senden
      const fileStream = readFileSync(fullPath);
      return new NextResponse(fileStream, {
        headers: {
          "Content-Type": "text/csv",
          "Content-Disposition": `attachment; filename="${
            env.NODE_ENV != "production" ? "_DEV_" : ""
          }$${fileRef.name}"`,
        },
      });
    } else {
      // Fehlermeldung, wenn die Datei nicht gefunden wurde
      NextResponse.json({ status: 404 });
    }
  } else {
    NextResponse.json("no payoutId provided", { status: 500 });
  }
}
