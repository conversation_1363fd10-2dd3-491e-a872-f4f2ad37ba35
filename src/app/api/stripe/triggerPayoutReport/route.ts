import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import { stripe } from "../../../../utils/stripe/stripe";
import prisma from "../../../../server/db/prisma";
import { LogType, PayoutStatus } from "@prisma/client";
import Logger from "~/server/logger/logger";

export async function GET(request: NextRequest) {
  const payoutId = request.nextUrl.searchParams.get("payoutId");

  if (payoutId) {
    const reportFileref = await prisma.fileRef.findFirst({
      where: {
        stripePayoutId: payoutId,
      },
    });
    if (!reportFileref) {
      try {
        await stripe.reporting.reportRuns.create({
          report_type: "payout_reconciliation.by_id.itemized.4",
          parameters: {
            payout: payoutId,
            columns: [
              "reporting_category",
              "automatic_payout_id",
              "payment_intent_id",
              "gross",
              "net",
              "fee",
              "customer_facing_amount",
              "payment_metadata[evse]",
              "payment_metadata[cdr_id]",
              "payment_metadata[eulektroInvoice]",
            ],
          },
        });
        await prisma.stripePayout.update({
          where: { id: payoutId },
          data: {
            status: PayoutStatus.STRIPE_REPORT_TRIGGERED,
          },
        });
      } catch (e) {
        Logger("prisma error", "Trigger Report", "stripe", LogType.ERROR);
        Logger("error triggering report", "API 500", "src/app/api/stripe/triggerPayoutReport/route.ts", LogType.ERROR);
        return NextResponse.json("error triggering report", { status: 500 });
      }
    } else {
      Logger("report already triggered before", "Trigger Report", "stripe", LogType.WARN);
      return NextResponse.json("report already triggered before", {
        status: 500,
      });
    }
  } else {
    Logger("missing payoutId parameter", "Trigger Report", "stripe", LogType.ERROR);
    return NextResponse.json("missing payoutId parameter", { status: 500 });
  }

  return NextResponse.json("success");
}
