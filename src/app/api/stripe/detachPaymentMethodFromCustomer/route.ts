import { type NextRequest, NextResponse } from "next/server";

import { stripe } from "~/utils/stripe/stripe";
import prisma from "~/server/db/prisma";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";

export const revalidate = 0;

export async function GET(request: NextRequest) {
  const session = await getServerSession(authOptions);

  if (!session) {
    Logger("Unauthorized", "Detach payment method", "ERROR");
    return NextResponse.json({ success: false, message: "Unauthorized" }, { status: 404 });
  }

  const payment_method_id = request.nextUrl.searchParams.get("paymentMethodId");

  if (!payment_method_id) {
    Logger("No PaymentMethodId provided", "Detach payment method", "ERROR");
    return NextResponse.json(
      { success: false, message: "No PaymentMethodId provided" },
      { status: 401 },
    );
  }
  if (!payment_method_id.startsWith("pm_")) {
    Logger("PaymentMethodId invalid format", "Detach payment method", "ERROR");
    return NextResponse.json(
      { success: false, message: "PaymentMethodId invalid format" },
      { status: 500 },
    );
  }

  const id = session?.user.id;

  if (!id) {
    Logger("No user id found in session", "Detach payment method", "ERROR");
    return NextResponse.json(
      { success: false, message: "No user id found in session" },
      { status: 404 },
    );
  }
  const user = await prisma.user.findUnique({
    where: {
      id: id,
    },
  });
  if (!user) {
    Logger("User not found", "Detach payment method", "ERROR");
    return NextResponse.json({ success: false, message: "User not found" }, { status: 404 });
  }
  if (user.stripeCustomerId) {
    const paymentMethodCheck = await stripe.paymentMethods.retrieve(payment_method_id);
    if (paymentMethodCheck.customer) {
      if (paymentMethodCheck.customer != user.stripeCustomerId) {
        Logger("Connected user does not match", "Detach payment method", "ERROR");
        return NextResponse.json(
          { success: false, message: "Connected user does not match" },
          { status: 404 },
        );
      }
    } else {
      Logger("No connected user", "Detach payment method", "ERROR");
      return NextResponse.json({ success: false, message: "No connected user" }, { status: 404 });
    }

    const paymentMethod = await stripe.paymentMethods.detach(payment_method_id);
    if (!paymentMethod.customer) {
      return NextResponse.json(
        { success: true, message: "Payment Medhod detached" },
        { status: 200 },
      );
    }

    Logger("Error detaching payment method from user", "Detach payment method", "ERROR");
    return NextResponse.json(
      { success: false, message: "Error detaching payment method from user" },
      { status: 500 },
    );
  }

  Logger("User has no stripe customerId", "Detach payment method", "ERROR");
  return NextResponse.json(
    { success: false, message: "Error changing default payment method" },
    { status: 500 },
  );
}
