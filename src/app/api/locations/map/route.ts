import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import prisma from "~/server/db/prisma";
import { getOusBelowOu } from "~/server/model/ou/func";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";


export async function GET(request: NextRequest) {
  const session = await getServerSession(authOptions);

  if (!session || !session?.user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    // Get all child OUs for filtering
    const childOus = await getOusBelowOu(session.user.selectedOu);
    const ouIds = childOus.map((ou) => ou.id);
    // Fetch locations with coordinates for the selected OU and its children
    const locations = await prisma.location.findMany({
      where: {
        ouId: { in: ouIds },
        coordinates: {
          isNot: null,
        },
      },
      select: {
        id: true,
        name: true,
        street: true,
        houseNumber: true,
        city: true,
        postal_code: true,
        country: true,
        coordinates: {
          select: {
            latitude: true,
            longitude: true,
          },
        },
        evses: {
          select: {
            uid: true,
            evse_id: true,
            status: true,
          },
        },
        ou: {
          select: {
            id: true,
            name: true,
          },
        },
      },
      orderBy: {
        name: "asc",
      },
    });

    // Transform data for map display
    const mapLocations = locations.map((location) => ({
      id: location.id,
      name: location.name,
      address: `${location.street} ${location.houseNumber}, ${location.city} ${location.postal_code}`,
      city: location.city,
      country: location.country,
      coordinates: {
        lat: location.coordinates!.latitude,
        lng: location.coordinates!.longitude,
      },
      evseCount: location.evses.length,
      ou: location.ou,
    }));

    return NextResponse.json(mapLocations);
  } catch (error) {
    {
      const msg = "Failed to fetch locations";
      const errorMsg = (error as Error)?.message || String(error);
      Logger(`${msg}: ${errorMsg}`, 'API 500', 'src/app/api/locations/map/route.ts', LogType.ERROR);
    }
    return NextResponse.json({ error: "Failed to fetch locations" }, { status: 500 });
  }
}
