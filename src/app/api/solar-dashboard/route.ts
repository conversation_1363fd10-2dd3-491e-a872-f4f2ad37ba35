import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '~/app/api/auth/[...nextauth]/route';
import prisma from '~/server/db/prisma';
import { CreateSolarDashboardRequest, SolarDashboardListItem } from '~/app/(app)/solar-dashboard/types';
import { generatePublicToken } from '~/app/(app)/solar-dashboard/utils/tokens';
import { getOusBelowOu } from '~/server/model/ou/func';
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";

/**
 * Check if a user's OU has access to a location based on OU hierarchy
 * Returns true if:
 * 1. User's OU is the same as location's OU, OR
 * 2. User's OU is a parent/ancestor of location's OU
 */
async function hasOuAccessToLocation(userOuId: string, locationOuId: string): Promise<boolean> {
  // Direct match - user's OU is the same as location's OU
  if (userOuId === locationOuId) {
    return true;
  }

  // Check if user's OU is a parent of location's OU
  // Get the user's OU and all its child OUs
  const userOu = await prisma.ou.findUnique({ where: { id: userOuId } });
  if (!userOu) {
    return false;
  }

  const accessibleOus = await getOusBelowOu(userOu);
  const accessibleOuIds = accessibleOus.map(ou => ou.id);

  // Check if the location's OU is in the list of accessible OUs
  return accessibleOuIds.includes(locationOuId);
}

// GET /api/solar-dashboard - List all dashboards for user's OU
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get user's OU
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      include: { ou: true }
    });

    if (!user?.ou) {
      return NextResponse.json({ error: 'User has no OU assigned' }, { status: 400 });
    }

    // Get all dashboards for the user's OU
    const dashboards = await prisma.solarDashboard.findMany({
      where: { ouId: user.ouId },
      include: {
        location: {
          select: {
            id: true,
            name: true,
            city: true
          }
        },
        ou: {
          select: {
            id: true,
            name: true
          }
        }
      },
      orderBy: { createdAt: 'desc' }
    });

    const dashboardList: SolarDashboardListItem[] = dashboards.map(dashboard => ({
      id: dashboard.id,
      name: dashboard.name,
      description: dashboard.description,
      isActive: dashboard.isActive,
      isPublic: dashboard.isPublic,
      location: dashboard.location,
      ou: dashboard.ou,
      createdAt: dashboard.createdAt,
      updatedAt: dashboard.updatedAt
    }));

    return NextResponse.json(dashboardList);
  } catch (error) {
    {
      const msg = 'Error fetching solar dashboards:';
      const errorMsg = (error as Error)?.message || String(error);
      Logger(`${msg}: ${errorMsg}`, "API 500", "src/app/api/solar-dashboard/route.ts", LogType.ERROR);
    }
    Logger("API 500 Error", "API 500", "src/app/api/solar-dashboard/route.ts", LogType.ERROR);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// POST /api/solar-dashboard - Create new dashboard
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body: CreateSolarDashboardRequest = await request.json();
    const { name, description, locationId, solarEdgeApiKey, solarEdgeSiteId, settings } = body;

    // Validate required fields
    if (!name || !locationId) {
      return NextResponse.json({
        error: 'Missing required fields: name, locationId'
      }, { status: 400 });
    }

    // Get user's OU from session
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      include: { ou: true }
    });

    console.log('Debug - User lookup:', {
      userId: session.user.id,
      userFound: !!user,
      userOuId: user?.ouId,
      ouName: user?.ou?.name
    });

    if (!user?.ou) {
      return NextResponse.json({ error: 'User has no OU assigned' }, { status: 400 });
    }

    const ouId = user.ouId;

    // Check if location exists and user has access based on OU hierarchy
    const location = await prisma.location.findUnique({
      where: { id: locationId }
    });

    console.log('Debug - Location lookup:', {
      locationId,
      userOuId: ouId,
      locationFound: !!location,
      locationOuId: location?.ouId,
      locationName: location?.name
    });

    if (!location) {
      return NextResponse.json({ error: 'Location not found' }, { status: 404 });
    }

    // Check OU hierarchy access
    const hasAccess = await hasOuAccessToLocation(ouId, location.ouId);

    console.log('Debug - OU hierarchy check:', {
      userOuId: ouId,
      locationOuId: location.ouId,
      hasAccess,
      accessType: ouId === location.ouId ? 'direct' : 'hierarchical'
    });

    if (!hasAccess) {
      return NextResponse.json({
        error: `Access denied. Location OU: ${location.ouId}, User OU: ${ouId}. User's OU must be the same or a parent of the location's OU.`
      }, { status: 403 });
    }

    // Check if dashboard already exists for this OU-Location combination
    const existingDashboard = await prisma.solarDashboard.findUnique({
      where: {
        ouId_locationId: {
          ouId,
          locationId
        }
      }
    });

    if (existingDashboard) {
      return NextResponse.json({
        error: 'Dashboard already exists for this location'
      }, { status: 409 });
    }

    // Create the dashboard
    const dashboard = await prisma.solarDashboard.create({
      data: {
        name,
        description,
        ouId,
        locationId,
        solarEdgeApiKey,
        solarEdgeSiteId,
        settings: settings || {},
        isActive: false, // Start inactive
        isPublic: false
      },
      include: {
        location: {
          select: {
            id: true,
            name: true,
            city: true
          }
        },
        ou: {
          select: {
            id: true,
            name: true
          }
        }
      }
    });

    return NextResponse.json(dashboard, { status: 201 });
  } catch (error) {
    {
      const msg = 'Error creating solar dashboard:';
      const errorMsg = (error as Error)?.message || String(error);
      Logger(`${msg}: ${errorMsg}`, "API 500", "src/app/api/solar-dashboard/route.ts", LogType.ERROR);
    }
    Logger("API 500 Error", "API 500", "src/app/api/solar-dashboard/route.ts", LogType.ERROR);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
