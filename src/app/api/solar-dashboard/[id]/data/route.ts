import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '~/app/api/auth/[...nextauth]/route';
import prisma from '~/server/db/prisma';
import { SolarDashboardData, SolarDashboardError } from '~/app/(app)/solar-dashboard/types';
import { getOusBelowOu } from '~/server/model/ou/func';
import { getSolarData, getCachedData, SolarEdgeDataType, getTodayHourlyEnergy } from '~/services/solarEdge/api';
import { getCachedChargingPower, ChargingStationData } from '~/services/charging/chargingStationService';
import { getCachedSunriseSunsetData, SunriseSunsetData } from '~/services/weather/sunriseSunsetService';
import { getCachedWeatherData, getLocationCoordinates, WeatherData } from '~/services/weather/openMeteoService';
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";

/**
 * Check if a user's OU has access to a location based on OU hierarchy
 */
async function hasOuAccessToLocation(userOuId: string, locationOuId: string): Promise<boolean> {
  if (userOuId === locationOuId) {
    return true;
  }

  const userOu = await prisma.ou.findUnique({ where: { id: userOuId } });
  if (!userOu) {
    return false;
  }

  const accessibleOus = await getOusBelowOu(userOu);
  const accessibleOuIds = accessibleOus.map(ou => ou.id);

  return accessibleOuIds.includes(locationOuId);
}

/**
 * Get real solar and charging data from APIs or cache, with proper error handling
 */
async function getRealSolarData(dashboard: any): Promise<{
  data: SolarDashboardData | null;
  dataSource: 'api' | 'cache' | 'error';
  errors: SolarDashboardError[];
}> {
  let dataSource: 'api' | 'cache' | 'error' = 'error';
  let solarEdgeData = null;
  let hourlyEnergyData = null;
  let chargingData: ChargingStationData | null = null;
  let sunData: SunriseSunsetData | null = null;
  let weatherData: WeatherData | null = null;
  let apiCallsRemaining = 0;
  const errors: SolarDashboardError[] = [];

  // Try to get real SolarEdge data if API credentials are configured
  if (dashboard.solarEdgeApiKey && dashboard.solarEdgeSiteId) {
    try {
      console.log(`🌞 Fetching SolarEdge data for dashboard ${dashboard.id}`);

      solarEdgeData = await getSolarData(
        dashboard.id,
        dashboard.solarEdgeApiKey,
        dashboard.solarEdgeSiteId
      );

      apiCallsRemaining = solarEdgeData.apiCallsRemaining;

      // Determine primary data source
      if (solarEdgeData.currentPower?.source === 'api' ||
          solarEdgeData.todayEnergy?.source === 'api' ||
          solarEdgeData.yearEnergy?.source === 'api') {
        dataSource = 'api';
      } else if (solarEdgeData.currentPower || solarEdgeData.todayEnergy || solarEdgeData.yearEnergy) {
        dataSource = 'cache';
      }

      console.log(`📊 SolarEdge data source: ${dataSource}, API calls remaining: ${apiCallsRemaining}`);

      // Try to get hourly energy data for today (for the chart)
      try {
        console.log(`📊 Fetching hourly energy data for dashboard ${dashboard.id}`);
        hourlyEnergyData = await getTodayHourlyEnergy(
          dashboard.id,
          dashboard.solarEdgeApiKey,
          dashboard.solarEdgeSiteId
        );

        if (hourlyEnergyData) {
          console.log(`📊 Hourly energy data: ${hourlyEnergyData.value.length} hours, Source: ${hourlyEnergyData.source}`);
        }
      } catch (error) {
        {
      const msg = `❌ Error fetching hourly energy data for dashboard ${dashboard.id}:`;
      const errorMsg = (error as Error)?.message || String(error);
      Logger(`${msg}: ${errorMsg}`, "API 500", "src/app/api/solar-dashboard/[id]/data/route.ts", LogType.ERROR);
    }
        // Don't add to errors array as this is optional data
      }

    } catch (error) {
      {
      const msg = `❌ Error fetching SolarEdge data for dashboard ${dashboard.id}:`;
      const errorMsg = (error as Error)?.message || String(error);
      Logger(`${msg}: ${errorMsg}`, "API 500", "src/app/api/solar-dashboard/[id]/data/route.ts", LogType.ERROR);
    }
      errors.push({
        type: 'api',
        message: 'SolarEdge API Fehler',
        details: error instanceof Error ? error.message : 'Unbekannter Fehler',
        timestamp: new Date()
      });
    }
  } else {
    errors.push({
      type: 'data',
      message: 'SolarEdge API nicht konfiguriert',
      details: 'API-Schlüssel oder Site-ID fehlen',
      timestamp: new Date()
    });
  }

  // Get real charging station data for the dashboard's location
  try {
    console.log(`🔌 Fetching charging data for location: ${dashboard.locationId}`);

    chargingData = await getCachedChargingPower(dashboard.locationId);

    console.log(`🔋 Charging data: ${chargingData.totalChargingPower} kW from ${chargingData.activeSessionsCount} active sessions`);
  } catch (error) {
    {
      const msg = `❌ Error fetching charging data for dashboard ${dashboard.id}:`;
      const errorMsg = (error as Error)?.message || String(error);
      Logger(`${msg}: ${errorMsg}`, "API 500", "src/app/api/solar-dashboard/[id]/data/route.ts", LogType.ERROR);
    }
    errors.push({
      type: 'api',
      message: 'Ladestations-Daten nicht verfügbar',
      details: error instanceof Error ? error.message : 'Unbekannter Fehler',
      timestamp: new Date()
    });
  }

  // Get location coordinates for weather data
  let coordinates = { latitude: 53.1199176, longitude: 8.5714827 }; // Bremen fallback
  try {
    coordinates = await getLocationCoordinates(dashboard.locationId);
    console.log(`🗺️ Using coordinates: ${coordinates.latitude}, ${coordinates.longitude}`);
  } catch (error) {
    {
      const msg = `❌ Error getting coordinates for dashboard ${dashboard.id}:`;
      const errorMsg = (error as Error)?.message || String(error);
      Logger(`${msg}: ${errorMsg}`, "API 500", "src/app/api/solar-dashboard/[id]/data/route.ts", LogType.ERROR);
    }
  }

  // Get sunrise/sunset data
  try {
    console.log(`🌅 Fetching sunrise/sunset data for dashboard ${dashboard.id}`);
    sunData = await getCachedSunriseSunsetData(coordinates.latitude, coordinates.longitude);
    console.log(`🌞 Sun data: Sunrise ${sunData.sunrise}, Sunset ${sunData.sunset}, Current ${sunData.currentTime}, Position: ${sunData.sunPosition.toFixed(3)}, Daytime: ${sunData.isDaytime}`);
  } catch (error) {
    {
      const msg = `❌ Error fetching sun data for dashboard ${dashboard.id}:`;
      const errorMsg = (error as Error)?.message || String(error);
      Logger(`${msg}: ${errorMsg}`, "API 500", "src/app/api/solar-dashboard/[id]/data/route.ts", LogType.ERROR);
    }
    errors.push({
      type: 'api',
      message: 'Sonnenzeiten nicht verfügbar',
      details: error instanceof Error ? error.message : 'Unbekannter Fehler',
      timestamp: new Date()
    });
  }

  // Get weather data
  try {
    console.log(`🌤️ Fetching weather data for dashboard ${dashboard.id}`);
    weatherData = await getCachedWeatherData(coordinates.latitude, coordinates.longitude);
    console.log(`🌤️ Weather data: ${weatherData.temperature}°C, ${weatherData.description}, Source: ${weatherData.source}`);

    // Add error if weather data has an error
    if (weatherData.source === 'error') {
      errors.push({
        type: 'api',
        message: 'Wetterdaten nicht verfügbar',
        details: weatherData.error || 'Unbekannter Fehler',
        timestamp: new Date()
      });
    }
  } catch (error) {
    {
      const msg = `❌ Error fetching weather data for dashboard ${dashboard.id}:`;
      const errorMsg = (error as Error)?.message || String(error);
      Logger(`${msg}: ${errorMsg}`, "API 500", "src/app/api/solar-dashboard/[id]/data/route.ts", LogType.ERROR);
    }
    errors.push({
      type: 'api',
      message: 'Wetterdaten nicht verfügbar',
      details: error instanceof Error ? error.message : 'Unbekannter Fehler',
      timestamp: new Date()
    });
  }

  // Check if we have sufficient data to create a dashboard
  const hasSolarData = solarEdgeData && (solarEdgeData.currentPower || solarEdgeData.todayEnergy || solarEdgeData.yearEnergy);

  if (!hasSolarData) {
    // Return error state if no solar data is available
    return {
      data: null,
      dataSource: 'error',
      errors: [
        ...errors,
        {
          type: 'data',
          message: 'Keine Solardaten verfügbar',
          details: 'SolarEdge API liefert keine Daten oder ist nicht konfiguriert',
          timestamp: new Date()
        }
      ]
    };
  }

  // Convert SolarEdge data to our dashboard structure
  // Note: SolarEdge API already returns power in kW, so no conversion needed
  const currentPowerKw = solarEdgeData.currentPower ? solarEdgeData.currentPower.value : 0;
  const todayEnergyKwh = solarEdgeData.todayEnergy ? solarEdgeData.todayEnergy.value / 1000 : 0; // Energy is in Wh, convert to kWh
  const yearEnergyKwh = solarEdgeData.yearEnergy ? solarEdgeData.yearEnergy.value / 1000 : 0; // Energy is in Wh, convert to kWh

  // Get grid and load power from metadata if available
  // Note: Grid and load power are also in kW from SolarEdge API
  const gridPowerKw = solarEdgeData.currentPower?.metadata?.gridPower || 0;
  const loadPowerKw = solarEdgeData.currentPower?.metadata?.loadPower || 0;

  // Get real charging power from charging stations
  const chargingPowerKw = chargingData ? chargingData.totalChargingPower : 0;

  // Calculate corrected office consumption (SolarEdge load - charging power)
  // SolarEdge "load" includes both office and charging station consumption
  const totalLoadPowerKw = loadPowerKw;
  const correctedOfficePowerKw = Math.max(0, totalLoadPowerKw - chargingPowerKw);

  // Get configurable formulas from dashboard settings
  const settings = dashboard.settings || {};
  const fuelSavingsFormula = settings.fuelSavingsFormula || 0.25; // Default: 0.25 liters per kWh
  const co2Formula = settings.co2SavingsFormula || {
    electricityGridFactor: 0.4, // kg CO2/kWh (German grid)
    gasolineEmissionFactor: 2.3, // kg CO2/liter gasoline
    efficiencyFactor: 0.25 // liters saved per kWh
  };

  // Calculate fuel savings based on total charged energy (all-time sum)
  // Get total charged energy from charging stations (all-time)
  const totalChargedEnergyKwh = yearEnergyKwh; // Using year energy as proxy for total charged energy
  const fuelSavedLitersTotal = totalChargedEnergyKwh * fuelSavingsFormula;

  // Calculate CO2 savings based on total produced energy (all-time sum)
  // CO2 saved = total produced energy * grid factor (simplified)
  const totalCo2SavedKg = yearEnergyKwh * co2Formula.electricityGridFactor;

  console.log(`🔍 Dashboard data conversion:`, {
    originalPvPower: solarEdgeData.currentPower?.value,
    convertedPvPower: currentPowerKw,
    originalGridPower: solarEdgeData.currentPower?.metadata?.gridPower,
    convertedGridPower: gridPowerKw,
    originalLoadPower: solarEdgeData.currentPower?.metadata?.loadPower,
    totalLoadPower: totalLoadPowerKw,
    chargingPower: chargingPowerKw,
    correctedOfficePower: correctedOfficePowerKw,
    chargingActiveSessions: chargingData?.activeSessionsCount || 0,
    fuelSavedLitersTotal: fuelSavedLitersTotal.toFixed(2),
    co2SavedKgTotal: totalCo2SavedKg.toFixed(2)
  });

  // Create data structure with real values only
  const dashboardData: SolarDashboardData = {
    id: dashboard.id,
    name: dashboard.name,
    description: dashboard.description,
    logoUrl: dashboard.logoUrl,
      ouId: dashboard.ouId,
      locationId: dashboard.locationId,
      isActive: dashboard.isActive,
      isPublic: dashboard.isPublic,
      publicToken: dashboard.publicToken,
      solarEdgeApiKey: dashboard.solarEdgeApiKey,
      solarEdgeSiteId: dashboard.solarEdgeSiteId,
      settings: dashboard.settings,
      createdAt: dashboard.createdAt,
      updatedAt: dashboard.updatedAt,

      // Real SolarEdge and Charging data with corrected office consumption
      currentPower: {
        pv: currentPowerKw,
        consumption: correctedOfficePowerKw, // Office consumption = Total load - Charging
        charging: chargingPowerKw, // Real charging power from charging stations
        grid: gridPowerKw
      },

      energy: {
        todayProduction: todayEnergyKwh,
        monthProduction: yearEnergyKwh * 0.083, // Rough estimate: year/12
        yearProduction: yearEnergyKwh,
        totalProduction: yearEnergyKwh * 2.5, // Rough estimate: assuming 2.5 years operation
        todayHourlyProduction: hourlyEnergyData ? hourlyEnergyData.value : undefined
      },

      efficiency: {
        selfConsumptionToday: 0, // Not available without additional calculations
        selfConsumptionMonth: 0, // Not available without additional calculations
        selfConsumptionYear: 0, // Not available without additional calculations
        selfConsumptionTotal: 0, // Not available without additional calculations
        systemEfficiency: 0 // Not available without additional calculations
      },

      environmental: {
        co2SavedToday: Math.round(todayEnergyKwh * co2Formula.electricityGridFactor * 10) / 10,
        co2SavedMonth: 0, // Not available without monthly data
        co2SavedYear: Math.round(yearEnergyKwh * co2Formula.electricityGridFactor),
        co2SavedTotal: Math.round(totalCo2SavedKg * 10) / 10,
        treesEquivalent: Math.round(totalCo2SavedKg * 50) // rough estimate
      },

      weather: weatherData ? {
        temperature: weatherData.temperature,
        windSpeed: weatherData.windSpeed,
        windDirection: weatherData.windDirection,
        weatherCode: weatherData.weatherCode,
        isDay: weatherData.isDay,
        description: weatherData.description,
        lastUpdated: weatherData.lastUpdated,
        source: weatherData.source,
        error: weatherData.error
      } : {
        temperature: 0,
        windSpeed: 0,
        windDirection: 0,
        weatherCode: 0,
        isDay: true,
        description: 'Wetterdaten nicht verfügbar',
        lastUpdated: new Date(),
        source: 'error' as const,
        error: 'Keine Wetterdaten verfügbar'
      },

      lastUpdate: 0, // seconds since last update

      // Enhanced metadata
      dataSource,
      apiCallsRemaining,
      solarEdgeMetadata: {
        currentPowerSource: solarEdgeData.currentPower?.source || 'cache',
        todayEnergySource: solarEdgeData.todayEnergy?.source || 'cache',
        yearEnergySource: solarEdgeData.yearEnergy?.source || 'cache',
        lastApiCall: new Date(),
        nextScheduledUpdate: new Date(Date.now() + 10 * 60 * 1000) // Next update in 10 minutes
      },

      // Charging station metadata
      chargingMetadata: chargingData ? {
        totalChargingPower: chargingData.totalChargingPower,
        activeSessionsCount: chargingData.activeSessionsCount,
        chargingStations: chargingData.chargingStations,
        lastUpdated: chargingData.lastUpdated,
        dataSource: 'realtime'
      } : undefined,

      // Calculated metrics for info screen display
      calculatedMetrics: {
        fuelSavedLitersTotal: Math.round(fuelSavedLitersTotal * 100) / 100,
        co2SavedKgTotal: Math.round(totalCo2SavedKg * 100) / 100,
        correctedOfficeConsumption: correctedOfficePowerKw,
        totalLoadConsumption: totalLoadPowerKw,
        formulas: {
          fuelSavingsFormula,
          co2Formula
        }
      },

      // Sunrise/sunset data for info screen
      sunData: sunData || undefined
    };

    return { data: dashboardData, dataSource, errors };

  // This should not be reached since we check for solar data above
  return {
    data: null,
    dataSource: 'error',
    errors: [
      ...errors,
      {
        type: 'data',
        message: 'Keine Daten verfügbar',
        details: 'Weder SolarEdge noch andere Datenquellen sind verfügbar',
        timestamp: new Date()
      }
    ]
  };
}

interface RouteParams {
  params: {
    id: string;
  };
}

// GET /api/solar-dashboard/[id]/data - Get dashboard data
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const { searchParams } = new URL(request.url);
    const publicToken = searchParams.get('token');

    let dashboard;
    let isPublicAccess = false;

    // First, try to find a public dashboard (no token required)
    dashboard = await prisma.solarDashboard.findFirst({
      where: {
        id: params.id,
        isPublic: true,
        isActive: true
      },
      include: {
        location: {
          select: {
            id: true,
            name: true,
            city: true
          }
        },
        ou: {
          select: {
            id: true,
            name: true
          }
        }
      }
    });

    if (dashboard) {
      // Public dashboard found - check if token is provided and valid (optional security)
      if (publicToken && dashboard.publicToken && dashboard.publicToken !== publicToken) {
        return NextResponse.json({ error: 'Invalid token' }, { status: 403 });
      }
      isPublicAccess = true;
    } else {
      // Regular authenticated access
      const session = await getServerSession(authOptions);
      if (!session?.user?.id) {
        return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
      }

      dashboard = await prisma.solarDashboard.findUnique({
        where: { id: params.id },
        include: {
          location: {
            select: {
              id: true,
              name: true,
              city: true
            }
          },
          ou: {
            select: {
              id: true,
              name: true
            }
          }
        }
      });

      if (!dashboard) {
        return NextResponse.json({ error: 'Dashboard not found' }, { status: 404 });
      }

      // Check if user has access to this dashboard's OU (including hierarchy)
      const user = await prisma.user.findUnique({
        where: { id: session.user.id },
        include: { ou: true }
      });

      if (!user) {
        return NextResponse.json({ error: 'User not found' }, { status: 403 });
      }

      // Check OU hierarchy access
      const hasAccess = await hasOuAccessToLocation(user.ouId, dashboard.ouId);

      console.log('Debug - OU hierarchy check (data access):', {
        userOuId: user.ouId,
        dashboardOuId: dashboard.ouId,
        hasAccess,
        accessType: user.ouId === dashboard.ouId ? 'direct' : 'hierarchical'
      });

      if (!hasAccess) {
        return NextResponse.json({ error: 'Access denied - OU hierarchy check failed' }, { status: 403 });
      }
    }

    // Check if dashboard is active
    if (!dashboard.isActive) {
      return NextResponse.json({ error: 'Dashboard is not active' }, { status: 403 });
    }

    // Get real SolarEdge data with intelligent caching and error handling
    console.log(`🔍 Getting solar data for dashboard ${dashboard.id} (${dashboard.name})`);

    const { data: dashboardData, dataSource, errors } = await getRealSolarData(dashboard);

    // If no data is available, return error response
    if (!dashboardData) {
      console.log(`❌ No data available for dashboard ${dashboard.id}`);
      return NextResponse.json({
        error: 'Keine Daten verfügbar',
        details: 'Dashboard-Daten können derzeit nicht abgerufen werden',
        errors: errors.map(e => ({
          type: e.type,
          message: e.message,
          details: e.details
        }))
      }, { status: 503 });
    }

    // Don't expose sensitive data in public access
    if (isPublicAccess) {
      dashboardData.publicToken = undefined;
      dashboardData.solarEdgeApiKey = undefined;
    }

    console.log(`📊 Dashboard ${dashboard.id} data source: ${dataSource}`);
    if (dashboardData.apiCallsRemaining !== undefined) {
      console.log(`🔢 API calls remaining today: ${dashboardData.apiCallsRemaining}`);
    }

    // Log any errors that occurred during data fetching
    if (errors.length > 0) {
      console.log(`⚠️ Dashboard ${dashboard.id} has ${errors.length} errors:`, errors.map(e => e.message));
    }

    // Add cache headers for better performance
    const response = NextResponse.json(dashboardData);
    response.headers.set('Cache-Control', 'public, max-age=30, stale-while-revalidate=60');

    return response;
  } catch (error) {
    {
      const msg = 'Error fetching solar dashboard data:';
      const errorMsg = (error as Error)?.message || String(error);
      Logger(`${msg}: ${errorMsg}`, "API 500", "src/app/api/solar-dashboard/[id]/data/route.ts", LogType.ERROR);
    }
    Logger("API 500 Error", "API 500", "src/app/api/solar-dashboard/[id]/data/route.ts", LogType.ERROR);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// POST /api/solar-dashboard/[id]/data/refresh - Force refresh dashboard data
export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const dashboard = await prisma.solarDashboard.findUnique({
      where: { id: params.id }
    });

    if (!dashboard) {
      return NextResponse.json({ error: 'Dashboard not found' }, { status: 404 });
    }

    // Check if user has access to this dashboard's OU
    const user = await prisma.user.findUnique({
      where: { id: session.user.id }
    });

    if (!user || user.ouId !== dashboard.ouId) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // TODO: Implement real SolarEdge API data refresh
    // For now, just return success
    return NextResponse.json({
      message: 'Dashboard data refresh initiated',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    {
      const msg = 'Error refreshing solar dashboard data:';
      const errorMsg = (error as Error)?.message || String(error);
      Logger(`${msg}: ${errorMsg}`, "API 500", "src/app/api/solar-dashboard/[id]/data/route.ts", LogType.ERROR);
    }
    Logger("API 500 Error", "API 500", "src/app/api/solar-dashboard/[id]/data/route.ts", LogType.ERROR);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
