import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '~/app/api/auth/[...nextauth]/route';
import prisma from '~/server/db/prisma';
import { getEnergyAnalytics } from '~/services/solarEdge/historicalDataService';
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";

/**
 * Fast analytics API for Solar Dashboard historical data
 * GET /api/solar-dashboard/[id]/analytics?period=month&year=2024&month=1
 * GET /api/solar-dashboard/[id]/analytics?startDate=2024-01-01&endDate=2024-01-31
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const period = searchParams.get('period'); // 'day', 'week', 'month', 'year', 'custom'
    const year = parseInt(searchParams.get('year') || new Date().getFullYear().toString());
    const month = parseInt(searchParams.get('month') || (new Date().getMonth() + 1).toString());
    const week = parseInt(searchParams.get('week') || '1');
    const startDateParam = searchParams.get('startDate');
    const endDateParam = searchParams.get('endDate');

    // Verify dashboard access
    const dashboard = await prisma.solarDashboard.findFirst({
      where: {
        id: params.id,
        ou: {
          users: {
            some: {
              userId: session.user.id
            }
          }
        }
      }
    });

    if (!dashboard) {
      return NextResponse.json({ error: 'Dashboard not found or access denied' }, { status: 404 });
    }

    let startDate: Date;
    let endDate: Date;

    // Calculate date range based on period
    switch (period) {
      case 'day':
        startDate = new Date(year, month - 1, parseInt(searchParams.get('day') || '1'));
        endDate = new Date(startDate);
        endDate.setDate(endDate.getDate() + 1);
        break;

      case 'week':
        startDate = new Date(year, 0, 1 + (week - 1) * 7);
        endDate = new Date(startDate);
        endDate.setDate(endDate.getDate() + 7);
        break;

      case 'month':
        startDate = new Date(year, month - 1, 1);
        endDate = new Date(year, month, 0); // Last day of month
        break;

      case 'year':
        startDate = new Date(year, 0, 1);
        endDate = new Date(year, 11, 31);
        break;

      case 'custom':
        if (!startDateParam || !endDateParam) {
          return NextResponse.json({ 
            error: 'Custom period requires startDate and endDate parameters' 
          }, { status: 400 });
        }
        startDate = new Date(startDateParam);
        endDate = new Date(endDateParam);
        break;

      default:
        // Default to current month
        const now = new Date();
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0);
    }

    console.log(`📊 Analytics request for dashboard ${params.id}: ${startDate.toISOString().split('T')[0]} to ${endDate.toISOString().split('T')[0]}`);

    // Get analytics data
    const analytics = await getEnergyAnalytics(params.id, startDate, endDate);

    // Get daily breakdown for charts
    const dailyData = await prisma.solarEdgeHistoricalData.findMany({
      where: {
        dashboardId: params.id,
        date: {
          gte: startDate,
          lte: endDate
        }
      },
      orderBy: {
        date: 'asc'
      },
      select: {
        date: true,
        energyProduced: true,
        energyConsumed: true,
        gridFeedIn: true,
        gridConsumption: true,
        chargingConsumption: true,
        dataSource: true,
        dataQuality: true
      }
    });

    // Calculate charging data from charging stations if not in historical data
    const chargingData = await prisma.chargePoint.findMany({
      where: {
        locationId: dashboard.locationId,
        dateDeleted: null
      },
      include: {
        historyPowerByCharger: {
          where: {
            timestamp: {
              gte: startDate,
              lte: endDate
            }
          }
        }
      }
    });

    // Aggregate charging consumption by date
    const chargingByDate = new Map<string, number>();
    chargingData.forEach(chargePoint => {
      chargePoint.historyPowerByCharger.forEach(history => {
        const dateKey = history.timestamp.toISOString().split('T')[0];
        const currentTotal = chargingByDate.get(dateKey) || 0;
        chargingByDate.set(dateKey, currentTotal + (history.power / 1000)); // Convert W to kWh
      });
    });

    // Enhance daily data with charging information
    const enhancedDailyData = dailyData.map(day => {
      const dateKey = day.date.toISOString().split('T')[0];
      const chargingFromStations = chargingByDate.get(dateKey) || 0;
      
      return {
        ...day,
        chargingConsumption: day.chargingConsumption || chargingFromStations,
        date: dateKey
      };
    });

    const response = {
      period: {
        type: period || 'month',
        startDate: startDate.toISOString().split('T')[0],
        endDate: endDate.toISOString().split('T')[0],
        year,
        month,
        week
      },
      analytics,
      dailyData: enhancedDailyData,
      metadata: {
        totalDataPoints: dailyData.length,
        dataQuality: {
          good: dailyData.filter(d => d.dataQuality === 'good').length,
          partial: dailyData.filter(d => d.dataQuality === 'partial').length,
          estimated: dailyData.filter(d => d.dataQuality === 'estimated').length
        },
        dataSources: {
          api: dailyData.filter(d => d.dataSource === 'api').length,
          manual: dailyData.filter(d => d.dataSource === 'manual').length,
          estimated: dailyData.filter(d => d.dataSource === 'estimated').length
        },
        generatedAt: new Date().toISOString()
      }
    };

    return NextResponse.json(response);

  } catch (error) {
    {
      const msg = '❌ Analytics API error:';
      const errorMsg = (error as Error)?.message || String(error);
      Logger(`${msg}: ${errorMsg}`, "API 500", "src/app/api/solar-dashboard/[id]/analytics/route.ts", LogType.ERROR);
    }
    Logger("API 500 Error", "API 500", "src/app/api/solar-dashboard/[id]/analytics/route.ts", LogType.ERROR);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * Store manual historical data
 * POST /api/solar-dashboard/[id]/analytics
 */
export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await request.json();
    const { date, energyProduced, energyConsumed, gridFeedIn, gridConsumption, chargingConsumption } = body;

    if (!date) {
      return NextResponse.json({ error: 'Date is required' }, { status: 400 });
    }

    // Verify dashboard access
    const dashboard = await prisma.solarDashboard.findFirst({
      where: {
        id: params.id,
        ou: {
          users: {
            some: {
              userId: session.user.id
            }
          }
        }
      }
    });

    if (!dashboard) {
      return NextResponse.json({ error: 'Dashboard not found or access denied' }, { status: 404 });
    }

    // Store manual data
    const historicalData = await prisma.solarEdgeHistoricalData.upsert({
      where: {
        dashboardId_date: {
          dashboardId: params.id,
          date: new Date(date)
        }
      },
      update: {
        energyProduced: energyProduced || undefined,
        energyConsumed: energyConsumed || undefined,
        gridFeedIn: gridFeedIn || undefined,
        gridConsumption: gridConsumption || undefined,
        chargingConsumption: chargingConsumption || undefined,
        dataSource: 'manual',
        dataQuality: 'good',
        updatedAt: new Date()
      },
      create: {
        dashboardId: params.id,
        date: new Date(date),
        energyProduced: energyProduced || undefined,
        energyConsumed: energyConsumed || undefined,
        gridFeedIn: gridFeedIn || undefined,
        gridConsumption: gridConsumption || undefined,
        chargingConsumption: chargingConsumption || undefined,
        dataSource: 'manual',
        dataQuality: 'good'
      }
    });

    console.log(`💾 Manual historical data stored for dashboard ${params.id} on ${date}`);

    return NextResponse.json({
      success: true,
      data: historicalData
    });

  } catch (error) {
    {
      const msg = '❌ Manual data storage error:';
      const errorMsg = (error as Error)?.message || String(error);
      Logger(`${msg}: ${errorMsg}`, "API 500", "src/app/api/solar-dashboard/[id]/analytics/route.ts", LogType.ERROR);
    }
    Logger("API 500 Error", "API 500", "src/app/api/solar-dashboard/[id]/analytics/route.ts", LogType.ERROR);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
