import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '~/app/api/auth/[...nextauth]/route';
import prisma from '~/server/db/prisma';
import { UpdateSolarDashboardRequest } from '~/app/(app)/solar-dashboard/types';
import { generatePublicToken } from '~/app/(app)/solar-dashboard/utils/tokens';
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";

interface RouteParams {
  params: {
    id: string;
  };
}

// GET /api/solar-dashboard/[id] - Get specific dashboard
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const dashboard = await prisma.solarDashboard.findUnique({
      where: { id: params.id },
      include: {
        location: {
          select: {
            id: true,
            name: true,
            city: true,
            street: true,
            houseNumber: true,
            postal_code: true
          }
        },
        ou: {
          select: {
            id: true,
            name: true
          }
        }
      }
    });

    if (!dashboard) {
      return NextResponse.json({ error: 'Dashboard not found' }, { status: 404 });
    }

    // Check if user has access to this dashboard's OU
    const user = await prisma.user.findUnique({
      where: { id: session.user.id }
    });

    if (!user || user.ouId !== dashboard.ouId) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    return NextResponse.json(dashboard);
  } catch (error) {
    {
      const msg = 'Error fetching solar dashboard:';
      const errorMsg = (error as Error)?.message || String(error);
      Logger(`${msg}: ${errorMsg}`, "API 500", "src/app/api/solar-dashboard/[id]/route.ts", LogType.ERROR);
    }
    Logger("API 500 Error", "API 500", "src/app/api/solar-dashboard/[id]/route.ts", LogType.ERROR);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// PUT /api/solar-dashboard/[id] - Update dashboard
export async function PUT(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body: UpdateSolarDashboardRequest = await request.json();
    const { name, description, isActive, isPublic, solarEdgeApiKey, solarEdgeSiteId, logoUrl, settings } = body;

    // Get existing dashboard
    const existingDashboard = await prisma.solarDashboard.findUnique({
      where: { id: params.id }
    });

    if (!existingDashboard) {
      return NextResponse.json({ error: 'Dashboard not found' }, { status: 404 });
    }

    // Check if user has access to this dashboard's OU
    const user = await prisma.user.findUnique({
      where: { id: session.user.id }
    });

    if (!user || user.ouId !== existingDashboard.ouId) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Generate public token if making dashboard public for the first time
    let publicToken = existingDashboard.publicToken;
    if (isPublic && !publicToken) {
      publicToken = generatePublicToken();
    }

    // Update the dashboard
    const updatedDashboard = await prisma.solarDashboard.update({
      where: { id: params.id },
      data: {
        ...(name !== undefined && { name }),
        ...(description !== undefined && { description }),
        ...(isActive !== undefined && { isActive }),
        ...(isPublic !== undefined && { isPublic }),
        ...(publicToken && { publicToken }),
        ...(solarEdgeApiKey !== undefined && { solarEdgeApiKey }),
        ...(solarEdgeSiteId !== undefined && { solarEdgeSiteId }),
        ...(logoUrl !== undefined && { logoUrl }),
        ...(settings !== undefined && { settings }),
        updatedAt: new Date()
      },
      include: {
        location: {
          select: {
            id: true,
            name: true,
            city: true
          }
        },
        ou: {
          select: {
            id: true,
            name: true
          }
        }
      }
    });

    return NextResponse.json(updatedDashboard);
  } catch (error) {
    {
      const msg = 'Error updating solar dashboard:';
      const errorMsg = (error as Error)?.message || String(error);
      Logger(`${msg}: ${errorMsg}`, "API 500", "src/app/api/solar-dashboard/[id]/route.ts", LogType.ERROR);
    }
    Logger("API 500 Error", "API 500", "src/app/api/solar-dashboard/[id]/route.ts", LogType.ERROR);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

// DELETE /api/solar-dashboard/[id] - Delete dashboard
export async function DELETE(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get existing dashboard
    const existingDashboard = await prisma.solarDashboard.findUnique({
      where: { id: params.id }
    });

    if (!existingDashboard) {
      return NextResponse.json({ error: 'Dashboard not found' }, { status: 404 });
    }

    // Check if user has access to this dashboard's OU
    const user = await prisma.user.findUnique({
      where: { id: session.user.id }
    });

    if (!user || user.ouId !== existingDashboard.ouId) {
      return NextResponse.json({ error: 'Access denied' }, { status: 403 });
    }

    // Delete the dashboard
    await prisma.solarDashboard.delete({
      where: { id: params.id }
    });

    return NextResponse.json({ message: 'Dashboard deleted successfully' });
  } catch (error) {
    {
      const msg = 'Error deleting solar dashboard:';
      const errorMsg = (error as Error)?.message || String(error);
      Logger(`${msg}: ${errorMsg}`, "API 500", "src/app/api/solar-dashboard/[id]/route.ts", LogType.ERROR);
    }
    Logger("API 500 Error", "API 500", "src/app/api/solar-dashboard/[id]/route.ts", LogType.ERROR);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
