import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import prisma from "~/server/db/prisma";
import { writeFile, mkdir } from "fs/promises";
import { existsSync } from "fs";
import path from "path";
import { v4 as uuidv4 } from "uuid";
import { getOusBelowOu } from "~/server/model/ou/func";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";

// Maximum file size: 5MB
const MAX_FILE_SIZE = 5 * 1024 * 1024;

// Allowed file types
const ALLOWED_TYPES = ["image/svg+xml", "image/png"];
const ALLOWED_EXTENSIONS = [".svg", ".png"];

/**
 * Check if a user's OU has access to a dashboard's OU based on OU hierarchy
 */
async function hasOuAccessToDashboard(userOuId: string, dashboardOuId: string): Promise<boolean> {
  if (userOuId === dashboardOuId) {
    return true;
  }

  const userOu = await prisma.ou.findUnique({ where: { id: userOuId } });
  if (!userOu) {
    return false;
  }

  const accessibleOus = await getOusBelowOu(userOu);
  const accessibleOuIds = accessibleOus.map((ou) => ou.id);

  return accessibleOuIds.includes(dashboardOuId);
}

/**
 * Upload logo for solar dashboard
 */
export async function POST(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    const dashboardId = params.id;

    // Get dashboard first
    const dashboard = await prisma.solarDashboard.findUnique({
      where: { id: dashboardId },
      include: { ou: true },
    });

    if (!dashboard) {
      return NextResponse.json({ error: "Dashboard nicht gefunden" }, { status: 404 });
    }

    // Check if dashboard is public - if so, allow access without authentication
    if (dashboard.isPublic && dashboard.isActive) {
      // Skip authentication for public dashboards
    } else {
      // Private dashboard - require authentication
      const session = await getServerSession(authOptions);
      if (!session?.user) {
        return NextResponse.json({ error: "Nicht authentifiziert" }, { status: 401 });
      }

      // Check if user has access to this dashboard's OU
      const user = await prisma.user.findUnique({
        where: { id: session.user.id },
        include: { ou: true },
      });

      if (!user) {
        return NextResponse.json({ error: "Benutzer nicht gefunden" }, { status: 403 });
      }

      // Check OU hierarchy access or admin role
      const hasAccess =
        (await hasOuAccessToDashboard(user.ouId, dashboard.ouId)) || session.user.role === "ADMIN";

      if (!hasAccess) {
        return NextResponse.json({ error: "Keine Berechtigung" }, { status: 403 });
      }
    }

    // Parse form data
    const formData = await request.formData();
    const file = formData.get("logo") as File;

    if (!file) {
      return NextResponse.json({ error: "Keine Datei hochgeladen" }, { status: 400 });
    }

    // Validate file type
    if (!ALLOWED_TYPES.includes(file.type)) {
      return NextResponse.json(
        {
          error: "Ungültiger Dateityp. Nur SVG und PNG sind erlaubt.",
        },
        { status: 400 },
      );
    }

    // Validate file extension
    const fileExtension = path.extname(file.name).toLowerCase();
    if (!ALLOWED_EXTENSIONS.includes(fileExtension)) {
      return NextResponse.json(
        {
          error: "Ungültige Dateiendung. Nur .svg und .png sind erlaubt.",
        },
        { status: 400 },
      );
    }

    // Validate file size
    if (file.size > MAX_FILE_SIZE) {
      return NextResponse.json(
        {
          error: "Datei zu groß. Maximum 5MB erlaubt.",
        },
        { status: 400 },
      );
    }

    // Create upload directory if it doesn't exist
    const uploadDir = path.join(process.cwd(), "public", "uploads", "solar-dashboard-logos");
    if (!existsSync(uploadDir)) {
      await mkdir(uploadDir, { recursive: true });
    }

    // Generate unique filename
    const uniqueId = uuidv4();
    const fileName = `${dashboardId}-${uniqueId}${fileExtension}`;
    const filePath = path.join(uploadDir, fileName);
    const publicUrl = `/api/public/uploads/solar-dashboard-logos/${fileName}`;

    // Save file
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);
    await writeFile(filePath, buffer);

    // Update dashboard with logo URL
    const updatedDashboard = await prisma.solarDashboard.update({
      where: { id: dashboardId },
      data: { logoUrl: publicUrl },
    });

    return NextResponse.json({
      success: true,
      logoUrl: publicUrl,
      message: "Logo erfolgreich hochgeladen",
    });
  } catch (error) {
    return NextResponse.json(
      {
        error: "Fehler beim Hochladen des Logos",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    );
  }
}

/**
 * Delete logo for solar dashboard
 */
export async function DELETE(request: NextRequest, { params }: { params: { id: string } }) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session?.user) {
      return NextResponse.json({ error: "Nicht authentifiziert" }, { status: 401 });
    }

    const dashboardId = params.id;

    // Get dashboard and check permissions
    const dashboard = await prisma.solarDashboard.findUnique({
      where: { id: dashboardId },
      include: { ou: true },
    });

    if (!dashboard) {
      return NextResponse.json({ error: "Dashboard nicht gefunden" }, { status: 404 });
    }

    // Check if user has access to this dashboard's OU
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      include: { ou: true },
    });

    if (!user) {
      return NextResponse.json({ error: "Benutzer nicht gefunden" }, { status: 403 });
    }

    // Check OU hierarchy access or admin role
    const hasAccess =
      (await hasOuAccessToDashboard(user.ouId, dashboard.ouId)) || session.user.role === "ADMIN";

    if (!hasAccess) {
      return NextResponse.json({ error: "Keine Berechtigung" }, { status: 403 });
    }

    // Remove logo URL from dashboard
    await prisma.solarDashboard.update({
      where: { id: dashboardId },
      data: { logoUrl: null },
    });

    return NextResponse.json({
      success: true,
      message: "Logo erfolgreich entfernt",
    });
  } catch (error) {
    return NextResponse.json(
      {
        error: "Fehler beim Entfernen des Logos",
      },
      { status: 500 },
    );
  }
}
