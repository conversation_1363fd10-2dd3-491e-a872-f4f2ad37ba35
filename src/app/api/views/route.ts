import { z } from "zod";
import { NextRequest, NextResponse } from "next/server";
import prisma from "~/server/db/prisma";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { createSystemNotificationForAdmins } from "~/utils/notifications/createSystemNotification";
import { NotificationType } from "@prisma/client";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";

// Definieren Sie ein Schema für die Validierung der Anforderungsdaten
const AgGridViewSchema = z
  .object({
    gridId: z.string().min(1),
    viewId: z.string().optional(),
    overwrite: z.boolean().optional(),
    columnState: z.string(),
    filterModel: z.string(),
    chartModels: z.string(),
    name: z.string(),
    description: z.string().optional(),
  })
  .refine(
    (data) => {
      // If overwrite is true, then viewId must be defined
      return !(data.overwrite === true && data.viewId === undefined);
    },
    {
      message: "viewId must be defined if overwrite is set",
      path: ["viewId"], // Path of the field that the error message is associated with
    },
  );

export async function POST(request: NextRequest) {
  // Zod-Validierung
  const session = await getServerSession(authOptions);
  if (!session) {
    return NextResponse.json("No session found", { status: 401 });
  }
  const zodCheckedPayload = AgGridViewSchema.safeParse(await request.json());

  if (zodCheckedPayload.success) {
    // Extrahieren Sie die Daten aus dem validierten Payload
    const { gridId, viewId, columnState, overwrite, filterModel, name, description, chartModels } =
      zodCheckedPayload.data;

    const userId = session.user.id;

    if (overwrite && viewId) {
      try {
        // Speichern Sie die Daten in der Datenbank
        await prisma.agGridView.update({
          where: { id: viewId },
          data: {
            userId: userId,
            gridId: gridId,
            columnState: columnState,
            filterModel: filterModel,
            description: description,
            chartModels: chartModels,
          },
        });

        // Geben Sie die erstellte Ressource zurück
        return NextResponse.json("Update success", { status: 200 });
      } catch (error) {
        // Fehlerbehandlung
        {
      const msg = error;
      Logger(msg, "API 500", "src/app/api/views/route.ts", LogType.ERROR);
    }

        return NextResponse.json("Ein Fehler ist aufgetreten", { status: 500 });
      }
    }

    try {
      // Speichern Sie die Daten in der Datenbank
      await prisma.agGridView.create({
        data: {
          userId,
          gridId,
          columnState,
          filterModel,
          name,
          description,
          chartModels,
        },
      });

      await createSystemNotificationForAdmins({
        nachricht: `Neue View erstellt von ${session.user.name} ${session.user.lastName}: ${name} (ID: ${viewId})`,
        type: NotificationType.INFO,
      });
      // Geben Sie die erstellte Ressource zurück
      return NextResponse.json("okay", { status: 200 });
    } catch (error) {
      // Fehlerbehandlung
      {
      const msg = error;
      Logger(msg, "API 500", "src/app/api/views/route.ts", LogType.ERROR);
    }

      return NextResponse.json("Ein Fehler ist aufgetreten", { status: 500 });
    }
  }

  return NextResponse.json("ZOD error: Ungültige POST Daten", { status: 400 });
}

export async function GET(request: NextRequest) {
  const gridId = request.nextUrl.searchParams.get("gridId");
  const session = await getServerSession(authOptions);
  if (!session) {
    return NextResponse.json({ message: "id fehlt" }, { status: 400 });
  }
  if (gridId) {
    const views = await prisma.agGridView.findMany({
      where: { gridId: gridId, deleted: false, userId: session.user.id },
    });
    return NextResponse.json(views);
  }
  return NextResponse.json("No grid id ", { status: 404 });
}
export async function DELETE(request: NextRequest) {
  // Extrahieren der gridId aus der URL
  const id = request.nextUrl.searchParams.get("id");
  const session = await getServerSession(authOptions);
  if (!session) {
    return NextResponse.json({ message: "id fehlt" }, { status: 400 });
  }
  const userId = session.user.id;

  // Überprüfen, ob eine gridId vorhanden ist
  if (!id) {
    return NextResponse.json({ message: "id fehlt" }, { status: 400 });
  }

  try {
    const view = await prisma.agGridView.findFirst({ where: { id: id, userId: session.user.id } });
    if (!view) {
      Logger("API 500 Error", "API 500", "src/app/api/views/route.ts", LogType.ERROR);
    return NextResponse.json({ message: "Löschen fehlgeschlagen" }, { status: 500 });
    }
    // Aktualisieren des Eintrags in der Datenbank
    const updateResult = await prisma.agGridView.update({
      where: {
        id: id,
      },
      data: {
        deleted: true,
      },
    });

    // Überprüfen, ob das Update erfolgreich war
    if (updateResult) {
      const views = await prisma.agGridView.findMany({
        where: { gridId: updateResult.gridId, deleted: false, userId: userId },
      });
      return NextResponse.json(views);
    } else {
      return NextResponse.json({ message: "Eintrag nicht gefunden" }, { status: 404 });
    }
  } catch (error) {
    // Fehlerbehandlung
    {
      const msg = error;
      Logger(msg, "API 500", "src/app/api/views/route.ts", LogType.ERROR);
    }

    Logger("API 500 Error", "API 500", "src/app/api/views/route.ts", LogType.ERROR);
    return NextResponse.json({ message: "Ein Fehler ist aufgetreten" }, { status: 500 });
  }
}
