import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";
import { getAllData } from "~/utils/longship";
import { endpoints } from "~/server/api/longship/constants";
import prisma from "../../../../server/db/prisma";
import type { ApiResponse } from "~/types/api/apiType"; // Make sure this path is correct.

export const revalidate = 0;

export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    const ou = await getAllData(endpoints.ORGANIZATIONAL_UNITS);
    let successCount = 0; // Counter for successful creations
    let existingCount = 0; // Counter for existing OUs

    for (const ouData of ou) {
      for (const key in ouData) {
        if (ouData[key] === null) {
          ouData[key] = undefined; // Convert null to undefined
        }
      }
      try {
        const existingOU = await prisma.ou.findUnique({
          where: {
            // Assuming 'id' is the unique identifier for OUs
            id: ouData.id,
          },
        });

        if (existingOU) {
          existingCount++; // Increment counter for existing OUs
        } else {
          await prisma.ou.create({
            data: ouData,
          });
          successCount++; // Increment counter for successful creation
        }
      } catch (e) {
        if (e instanceof Error) {
          Logger(`Error while saving ou: ${e.message}`, "Ou Importer", "cron", LogType.ERROR);
          // On error, directly send a failed response
          return NextResponse.json({
            status: "error",
            errorCode: "DATABASE_ERROR",
            message: "Failed to save some organizational units.",
            errorDetails: e.message,
          } as ApiResponse<null>);
        }
      }
    }

    // Successful response after processing all OU data
    Logger("Import ou from longship successful", "Ou Importer", "cron", LogType.INFO);
    return NextResponse.json({
      status: "success",
      payload: {
        totalFetched: ou.length,
        newlyCreated: successCount,
        alreadyExisting: existingCount,
      },
      message: `Successfully imported ${successCount} organizational units. ${existingCount} already existed.`,
    } as ApiResponse<{ totalFetched: number; newlyCreated: number; alreadyExisting: number }>);
  } catch (e) {
    if (e instanceof Error) {
      Logger(`Failed to import OU: ${e.message}`, "Ou Importer", "cron", LogType.ERROR);
    }
    // General error, such as communication with external services
    return NextResponse.json({
      status: "error",
      errorCode: "FETCH_ERROR",
      message: "Failed to fetch organizational units from Longship.",
      errorDetails: e instanceof Error ? e.message : e,
    } as ApiResponse<null>);
  }
}
