import type { ImportStats } from "~/server/task/importCdr";
import importCdr, { COPY_MODE } from "../../../../server/task/importCdr";
import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";
import type { ApiResponse } from "~/types/api/apiType";

export interface ImportResult {
  totalRecords: number;
  processedRecords: number;
  errors: string[];
}

export const revalidate = 0;

export async function GET(request: NextRequest): Promise<NextResponse> {
  const copy_mode = request.nextUrl.searchParams.get("copy_mode") || COPY_MODE.FULL;

  if (copy_mode !== COPY_MODE.FULL && copy_mode !== COPY_MODE.DELTA) {
    Logger("Invalid copy mode", "CDR Importer", "cron", LogType.WARN);
    return NextResponse.json({
      status: "error",
      errorCode: "INVALID_COPY_MODE",
      message: "Invalid 'copy_mode' parameter. Please use 'FULL' or 'DELTA'.",
      errorDetails: { requestedMode: copy_mode },
    } as ApiResponse<null>);
  }

  const numDayFromGet = request.nextUrl.searchParams.get("numDays");
  let numDays = 10; // default to 10 if not specified
  if (numDayFromGet) {
    numDays = parseInt(numDayFromGet, 10);
    if (isNaN(numDays)) {
      return NextResponse.json({
        status: "error",
        errorCode: "INVALID_NUM_DAYS",
        message: "The 'numDays' parameter must be a valid number.",
        errorDetails: { provided: numDayFromGet },
      } as ApiResponse<null>);
    }
  }

  try {
    const result = await importCdr(copy_mode, numDays);
    Logger(
      `CDR import success: Processed ${result.payload?.successfullyImported} records out of ${result.payload?.totalProcessed}`,
      "CDR Importer",
      "cron",
      LogType.INFO,
    );
    return NextResponse.json({
      status: "success",
      payload: result.payload,
      message: `CDR import ${copy_mode} successful for the past ${numDays} days. Processed ${result.payload?.successfullyImported} of ${result.payload?.totalProcessed}.`,
    } as ApiResponse<ImportStats>);
  } catch (error: unknown) {
    const errorMessage = error instanceof Error ? error.message : "An unknown error occurred";
    Logger(`Failed to import CDR: ${errorMessage}`, "CDR Importer", "cron", LogType.ERROR);
    return NextResponse.json({
      status: "error",
      errorCode: "IMPORT_FAILED",
      message: "Failed to import CDR data.",
      errorDetails: errorMessage,
    } as ApiResponse<null>);
  }
}
