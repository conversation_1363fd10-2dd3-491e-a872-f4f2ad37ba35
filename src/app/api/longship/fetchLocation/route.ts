import type { NextRequest } from "next/server";
import getLocation from "../../../../server/task/location";
import { NextResponse } from "next/server";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";
import type { ApiResponse } from "~/types/api/apiType"; // <PERSON><PERSON><PERSON>, dass dieser Pfad korrekt ist.

export const revalidate = 0;

export async function GET(request: NextRequest): Promise<NextResponse> {
  try {
    await getLocation();
    Logger("Import locations from longship", "Location Importer", "cron", LogType.INFO);
    // Erfolgreiche Antwort
    return NextResponse.json({
      status: "success",
      message: "Successfully imported locations from longship.",
      payload: null, // Keine spezifischen Nutzdaten zu senden
    } as ApiResponse<null>);
  } catch (error) {
    Logger(
      `Failed to import locations: ${error instanceof Error ? error.message : "Unknown error"}`,
      "Location Importer",
      "cron",
      LogType.ERROR,
    );
    // Fehlerantwort
    return NextResponse.json({
      status: "error",
      errorCode: "IMPORT_FAILED",
      message: "Failed to import locations from longship.",
      errorDetails: error instanceof Error ? error.message : "Unknown error",
    } as ApiResponse<null>);
  }
}
