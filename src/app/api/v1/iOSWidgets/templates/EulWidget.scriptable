{"always_run_in_app": false, "icon": {"color": "teal", "glyph": "magic"}, "name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "script": "// Eulektro Charging Power Widget für Scriptable\n// Konfiguration\nconst API_URL = \"https://app.eulektro.de/api/v1/ou/charging-power\";\nconst BEARER_TOKEN = \"EUL_7ddaad1075da2f68ebb657d3f8ad60f7\"; // Ersetze XXX mit deinem echten Token\n\n// Widget erstellen\nlet widget = new ListWidget();\nwidget.backgroundColor = new Color(\"#1a1a1a\");\n\ntry {\n  // API Request\n  let request = new Request(API_URL);\n  request.headers = {\n    \"Accept\": \"application/json\",\n    \"Authorization\": `Bearer ${BEARER_TOKEN}`\n  };\n  \n  let response = await request.loadJSON();\n  \n  if (response.success) {\n    // Titel\n    let titleText = widget.addText(\"⚡ Eulektro\");\n    titleText.textColor = Color.white();\n    titleText.font = Font.boldSystemFont(16);\n    \n    widget.addSpacer(8);\n    \n    // Aktuelle Leistung\n    let powerValue = parseFloat(response.chargingPower.totalCurrentKw);\n    let powerText = widget.addText(`${powerValue.toFixed(2)} kW`);\n    powerText.textColor = powerValue > 0 ? new Color(\"#00ff00\") : Color.white();\n    powerText.font = Font.boldSystemFont(24);\n    \n    // Power Label\n    let powerLabel = widget.addText(\"Aktuelle Leistung\");\n    powerLabel.textColor = new Color(\"#888888\");\n    powerLabel.font = Font.systemFont(12);\n    \n    widget.addSpacer(12);\n    \n    // Aktive Sessions\n    let sessionsValue = response.chargingPower.totalActiveSessions;\n    let sessionsText = widget.addText(`${sessionsValue}`);\n    sessionsText.textColor = sessionsValue > 0 ? new Color(\"#00ff00\") : Color.white();\n    sessionsText.font = Font.boldSystemFont(20);\n    \n    // Sessions Label\n    let sessionsLabel = widget.addText(\"Aktive Sessions\");\n    sessionsLabel.textColor = new Color(\"#888888\");\n    sessionsLabel.font = Font.systemFont(12);\n    \n    widget.addSpacer(8);\n    \n    // Timestamp\n    let timestamp = new Date(response.timestamp);\n    let timeText = widget.addText(`Stand: ${timestamp.toLocaleTimeString(\"de-DE\", {hour: '2-digit', minute:'2-digit'})}`);\n    timeText.textColor = new Color(\"#666666\");\n    timeText.font = Font.systemFont(10);\n    \n  } else {\n    // Fehler anzeigen\n    let errorText = widget.addText(\"❌ Fehler beim Laden\");\n    errorText.textColor = Color.red();\n    errorText.font = Font.boldSystemFont(14);\n  }\n  \n} catch (error) {\n  // Netzwerk- oder andere Fehler\n  let errorTitle = widget.addText(\"⚠️ Verbindungsfehler\");\n  errorTitle.textColor = Color.orange();\n  errorTitle.font = Font.boldSystemFont(14);\n  \n  widget.addSpacer(4);\n  \n  let errorDetail = widget.addText(\"Prüfe Internetverbindung\");\n  errorDetail.textColor = new Color(\"#888888\");\n  errorDetail.font = Font.systemFont(12);\n  \n  console.log(\"Fehler:\", error);\n}\n\n// Widget anzeigen\nif (config.runsInWidget) {\n  Script.setWidget(widget);\n} else {\n  widget.presentSmall();\n}\n\nScript.complete();", "share_sheet_inputs": []}