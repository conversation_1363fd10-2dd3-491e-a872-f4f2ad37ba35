import { NextResponse } from "next/server";
import { openApiSpec } from "~/utils/swagger/openApiSpec";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";

export const dynamic = "force-dynamic";

/**
 * API-Endpunkt für die OpenAPI JSON-Spezifikation
 * Stellt die Swagger/OpenAPI-Spezifikation als JSON bereit
 * URL: GET /api/v1/docs/swagger.json
 */
export async function GET() {
  try {
    return NextResponse.json(openApiSpec, {
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'no-cache, no-store, must-revalidate', // Kein <PERSON>ache während Entwicklung
        'Pragma': 'no-cache',
        'Expires': '0',
      },
    });
  } catch (error) {
    {
      const msg = 'Error generating OpenAPI spec:';
      const errorMsg = (error as Error)?.message || String(error);
      Logger(`${msg}: ${errorMsg}`, "API 500", "src/app/api/v1/docs/swagger.json/route.ts", LogType.ERROR);
    }
    Logger("API 500 Error", "API 500", "src/app/api/v1/docs/swagger.json/route.ts", LogType.ERROR);
    return NextResponse.json(
      { 
        error: 'Internal server error',
        message: 'Fehler beim Generieren der OpenAPI-Spezifikation'
      },
      { status: 500 }
    );
  }
}
