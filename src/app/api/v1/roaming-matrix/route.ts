import { NextRequest, NextResponse } from "next/server";
import { validate<PERSON><PERSON><PERSON><PERSON> } from "~/utils/apiAuth/apiKeyUtil";
import prisma from "~/server/db/prisma";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";

export const dynamic = "force-dynamic";

/**
 * @swagger
 * /api/v1/roaming-matrix:
 *   get:
 *     tags:
 *       - Roaming Matrix
 *     summary: Roaming Matrix abrufen
 *     description: Gibt die aktuelle Roaming Matrix als JSON zurück. Enthält alle Contacts mit visibleInRoamingMatrix=true und ihre gültigen zugeordneten Tarife, inklusive providerIds.
 *     security:
 *       - ApiKeyAuth: []
 *     responses:
 *       200:
 *         description: Roaming Matrix erfolgreich abgerufen
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/RoamingMatrixResponse'
 *       401:
 *         description: API-Key ungültig oder fehlt
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       403:
 *         description: Unzureichende Berechtigung
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Interner Serverfehler
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */

/**
 * API-Endpunkt für die Roaming Matrix
 * Gibt alle Contacts mit visibleInRoamingMatrix=true und ihre gültigen Tarife zurück
 * Verwendet API-Key-Authentifizierung statt Session-Authentifizierung
 * URL: GET /api/v1/roaming-matrix
 */
export async function GET(request: NextRequest) {
  try {
    // API-Key Validierung
    const validation = await validateApiKey(request);

    if (!validation.isValid) {
      return NextResponse.json(
        {
          error: "API key validation failed",
          message: validation.error,
        },
        { status: 401 },
      );
    }

    const contact = validation.contact;

    // Berechtigungsprüfung - nur ADMIN, CPO und CARD_MANAGER haben Zugriff
    if (!contact?.cpo) {
      return NextResponse.json(
        {
          error: "Insufficient permissions",
          message: "Nur CPO Contacts haben Zugriff auf die Roaming Matrix",
        },
        { status: 403 },
      );
    }

    // Get current date for validity check
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Fetch contacts with visibleInRoamingMatrix flag and their tariffs
    const contacts = await prisma.contact.findMany({
      where: {
        visibleInRoamingMatrix: true,
      },
      select: {
        companyName: true,
        tarifs: {
          select: {
            tarif: {
              select: {
                id: true,
                kwh: true,
                sessionFee: true,
                minChargingTime: true,
                minChargingEnergy: true,
                blockingFee: true,
                blockingFeeBeginAtMin: true,
                blockingFeeMax: true,
                validFrom: true,
                validTo: true,
                currentType: true,
              },
            },
          },
        },
        providers: {
          select: {
            providerId: true,
            providerCountryId: true,
          },
        },
      },
      orderBy: {
        companyName: "asc",
      },
    });

    // Process contacts to include only valid tariffs
    const roamingMatrixData = contacts.map((contact) => {
      // Filter valid roaming tariffs (Tarif model)
      const allValidRoamingTariffs = contact.tarifs
        .map((tarifOnContact) => tarifOnContact.tarif)
        .filter((tarif) => {
          const validFrom = new Date(tarif.validFrom);
          const validTo = new Date(tarif.validTo);
          return validFrom <= today && validTo >= today;
        });

      // Separate AC and DC tariffs
      const acTariffs = allValidRoamingTariffs.filter((tarif) => tarif.currentType === "AC");
      const dcTariffs = allValidRoamingTariffs.filter((tarif) => tarif.currentType === "DC");

      // Check for multiple valid tariffs of the same type (should not happen)
      if (acTariffs.length > 1) {
        const tariffIds = acTariffs.map((t) => t.id).join(", ");
        throw new Error(
          `Contact "${contact.companyName}" has multiple valid AC tariffs: ${tariffIds}. Please ensure only one tariff per currentType is valid at any time.`,
        );
      }

      if (dcTariffs.length > 1) {
        const tariffIds = dcTariffs.map((t) => t.id).join(", ");
        throw new Error(
          `Contact "${contact.companyName}" has multiple valid DC tariffs: ${tariffIds}. Please ensure only one tariff per currentType is valid at any time.`,
        );
      }

      return {
        companyName: contact.companyName,
        providers: contact.providers.map((provider) => ({
          providerId: provider.providerId,
          providerCountryId: provider.providerCountryId,
        })),
        acTariff: acTariffs[0]
          ? {
              id: acTariffs[0].id,
              kwh: acTariffs[0].kwh,
              sessionFee: acTariffs[0].sessionFee,
              minChargingTime: acTariffs[0].minChargingTime,
              minChargingEnergy: acTariffs[0].minChargingEnergy,
              blockingFee: acTariffs[0].blockingFee,
              blockingFeeBeginAtMin: acTariffs[0].blockingFeeBeginAtMin,
              blockingFeeMax: acTariffs[0].blockingFeeMax,
              validFrom: acTariffs[0].validFrom,
              validTo: acTariffs[0].validTo,
            }
          : null,
        dcTariff: dcTariffs[0]
          ? {
              id: dcTariffs[0].id,
              kwh: dcTariffs[0].kwh,
              sessionFee: dcTariffs[0].sessionFee,
              minChargingTime: dcTariffs[0].minChargingTime,
              minChargingEnergy: dcTariffs[0].minChargingEnergy,
              blockingFee: dcTariffs[0].blockingFee,
              blockingFeeBeginAtMin: dcTariffs[0].blockingFeeBeginAtMin,
              blockingFeeMax: dcTariffs[0].blockingFeeMax,
              validFrom: dcTariffs[0].validFrom,
              validTo: dcTariffs[0].validTo,
            }
          : null,
      };
    });

    return NextResponse.json(roamingMatrixData, {
      headers: {
        "Content-Type": "application/json",
        "Cache-Control": "no-cache", // Roaming Matrix sollte immer aktuell sein
      },
    });
  } catch (error) {
    {
      const msg = "Error fetching roaming matrix data:";
      const errorMsg = (error as Error)?.message || String(error);
      Logger(`${msg}: ${errorMsg}`, "API 500", "src/app/api/v1/roaming-matrix/route.ts", LogType.ERROR);
    }
    return NextResponse.json(
      {
        error: "Internal server error",
        message: "Fehler beim Abrufen der Roaming Matrix",
      },
      { status: 500 },
    );
  }
}
