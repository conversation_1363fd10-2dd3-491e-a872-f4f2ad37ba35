import { NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { validateApi<PERSON><PERSON> } from "~/utils/apiAuth/apiKeyUtil";
import prisma from "~/server/db/prisma";
import { LongshipHeaders } from "~/utils/longship";
import { env } from "~/env.js";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";

export const dynamic = "force-dynamic";

// Zod Schema für Token-Objekte
const TokenObjectSchema = z.object({
  name: z.string().min(1, "Name ist erforderlich"),
  uid: z.string().min(1, "UID ist erforderlich"),
  note: z.string().optional().default(""),
});

// Schema für das Array von Token-Objekten
const TokenArraySchema = z.object({
  tokens: z.array(TokenObjectSchema).min(1, "Mindestens ein Token ist erforderlich"),
});

// Interface für Longship LocalTokenGroup
interface LocalTokenGroup {
  id: string;
  oucode: string;
  tokenGroupName: string;
  targetOUCodes: string[];
  tokens: {
    isValid: boolean;
    name: string;
    uid: string;
    contractId: string;
    normalizedContractId: string;
  }[];
  targetChargepointIds: string[];
  overrideTariffId: string;
  created: string;
  updated: string;
  isPrefix: boolean;
}

/**
 * @swagger
 * /api/v1/contact/tokens:
 *   post:
 *     tags:
 *       - Token Management
 *     summary: Tokens erstellen/aktualisieren
 *     description: |
 *       Erstellt oder aktualisiert Tokens für einen Contact mit automatischer Longship-Synchronisation.
 *
 *       **Longship-Verhalten:**
 *       - Sucht nach allen TokenGroups mit passendem OU-Code
 *       - Prüft ob eine Gruppe mit "(API)" Suffix existiert
 *       - Falls nicht: Erstellt neue LocalTokenGroup mit OU-Name + "(API)" Suffix
 *       - Falls ja: Aktualisiert die gesamte TokenGroup mit nur den neuen Tokens
 *
 *       **Ergebnis:** Die API-TokenGroup enthält ausschließlich die übermittelten Tokens.
 *     security:
 *       - ApiKeyAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/TokenArray'
 *           example:
 *             tokens:
 *               - name: "Mitarbeiter Token 1"
 *                 uid: "TOKEN_UID_12345"
 *                 note: "Token für Mitarbeiter Max Mustermann"
 *               - name: "Mitarbeiter Token 2"
 *                 uid: "TOKEN_UID_67890"
 *                 note: "Token für Mitarbeiter Anna Schmidt"
 *     responses:
 *       200:
 *         description: Tokens erfolgreich erstellt/aktualisiert
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/TokenManagementResponse'
 *       400:
 *         description: Ungültige Eingabedaten
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: API-Key ungültig oder fehlt
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Interner Serverfehler
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */

/**
 * API-Endpunkt für Token-Management über API-Key
 * Akzeptiert ein Array von Token-Objekten mit name, uid und note
 * URL: POST /api/v1/contact/tokens
 */
export async function POST(request: NextRequest) {
  try {
    // API-Key Validierung
    const validation = await validateApiKey(request);

    if (!validation.isValid) {
      return NextResponse.json(
        {
          error: "API key validation failed",
          message: validation.error,
        },
        { status: 401 },
      );
    }

    // Request Body parsen
    const body = await request.json();

    // Zod Validierung
    const validationResult = TokenArraySchema.safeParse(body);

    if (!validationResult.success) {
      return NextResponse.json(
        {
          error: "Validation failed",
          details: validationResult.error.errors,
          message: "Die übermittelten Token-Daten sind ungültig",
        },
        { status: 400 },
      );
    }

    const { tokens } = validationResult.data;

    // Contact und OU Informationen
    const contact = validation.contact;
    const ouName = contact?.ou?.name || "Unknown OU";

    // TokenGroup für den Contact finden oder erstellen
    let tokenGroup = await prisma.tokenGroup.findFirst({
      where: {
        contactId: contact?.id,
      },
    });

    // Wenn keine TokenGroup existiert, erstelle eine neue
    if (!tokenGroup) {
      tokenGroup = await prisma.tokenGroup.create({
        data: {
          name: ouName,
          contactId: contact?.id,
        },
      });
    }

    // Aktuelle Zeit für die Description
    const currentDate = new Date().toLocaleString("de-DE", {
      timeZone: "Europe/Berlin",
    });

    // Tokens erstellen
    const createdTokens = [];
    const errors = [];

    for (const tokenData of tokens) {
      try {
        // Prüfen ob Token mit dieser authenticationId bereits existiert
        const existingToken = await prisma.token.findFirst({
          where: {
            authenticationId: tokenData.uid,
          },
        });

        if (existingToken) {
          // Token aktualisieren
          const updatedToken = await prisma.token.update({
            where: { id: existingToken.id },
            data: {
              name: tokenData.name,
              description: `API Token - Letztes Update: ${currentDate}${
                tokenData.note ? ` - ${tokenData.note}` : ""
              }`,
            },
          });
          createdTokens.push({ ...updatedToken, action: "updated" });
        } else {
          // Neuen Token erstellen
          const newToken = await prisma.token.create({
            data: {
              authenticationId: tokenData.uid,
              plateNumber: "", // Leer wie gewünscht
              name: tokenData.name,
              description: `API Token - Erstellt: ${currentDate}${
                tokenData.note ? ` - ${tokenData.note}` : ""
              }`,
              tokenGroupId: tokenGroup.id,
            },
          });
          createdTokens.push({ ...newToken, action: "created" });
        }
      } catch (error) {
        {
      const msg = `Error processing token ${tokenData.uid}:`;
      const errorMsg = (error as Error)?.message || String(error);
      Logger(`${msg}: ${errorMsg}`, "API 500", "src/app/api/v1/contact/tokens/route.ts", LogType.ERROR);
    }
        errors.push({
          uid: tokenData.uid,
          name: tokenData.name,
          error: "Failed to create/update token",
        });
      }
    }

    // Tokens zu Longship pushen
    const longshipErrors = [];
    const longshipSuccess = [];

    try {
      // Longship LocalTokenGroups abrufen
      const headers = LongshipHeaders({});
      const fetchRequest = await fetch(`${env.LONGSHIP_API}localtokengroups?skip=0&take=100`, {
        method: "GET",
        headers: headers,
      });

      if (fetchRequest.ok) {
        const longshipTokenGroups = (await fetchRequest.json()) as LocalTokenGroup[];
        const ouCode = contact?.ou?.code;

        if (!ouCode) {
          longshipErrors.push("OU Code nicht gefunden für Longship Push");
        } else {
          // Nach allen TokenGroups mit passendem OU-Code suchen
          const matchingTokenGroups = longshipTokenGroups.filter(
            (group) => group.oucode === ouCode,
          );

          // Nach API-TokenGroup suchen (mit "(API)" Suffix)
          let apiTokenGroup = matchingTokenGroups.find((group) =>
            group.tokenGroupName.endsWith("(API)"),
          );

          // Falls keine API-TokenGroup existiert, erstelle eine neue
          if (!apiTokenGroup) {
            const apiGroupName = `${ouName} (API)`;

            try {
              const createGroupRequest = await fetch(`${env.LONGSHIP_API}localtokengroups`, {
                method: "POST",
                headers: headers,
                body: JSON.stringify({
                  oucode: ouCode,
                  tokenGroupName: apiGroupName,
                  targetOUCodes: [ouCode],
                  targetChargepointIds: [],
                  overrideTariffId: "",
                  isPrefix: false,
                }),
              });

              if (createGroupRequest.ok) {
                apiTokenGroup = (await createGroupRequest.json()) as LocalTokenGroup;
                longshipSuccess.push({
                  action: "group_created",
                  groupName: apiGroupName,
                  groupId: apiTokenGroup.id,
                });
              } else {
                const errorText = await createGroupRequest.text();
                longshipErrors.push(`Failed to create API TokenGroup: ${errorText}`);
                return NextResponse.json(
                  {
                    success: false,
                    message: "Failed to create API TokenGroup in Longship",
                    errors: longshipErrors,
                  },
                  { status: 500 },
                );
              }
            } catch (error) {
              {
      const msg = "Error creating API TokenGroup:";
      const errorMsg = (error as Error)?.message || String(error);
      Logger(`${msg}: ${errorMsg}`, "API 500", "src/app/api/v1/contact/tokens/route.ts", LogType.ERROR);
    }
              longshipErrors.push("Failed to create API TokenGroup: Network error");
              return NextResponse.json(
                {
                  success: false,
                  message: "Failed to create API TokenGroup in Longship",
                  errors: longshipErrors,
                },
                { status: 500 },
              );
            }
          }

          // TokenGroup mit neuen Tokens aktualisieren (ersetzt alle bestehenden Tokens)
          if (apiTokenGroup) {
            try {
              // Neue Token-Liste für die TokenGroup erstellen
              const newTokens = tokens.map((tokenData) => ({
                isValid: true,
                name: tokenData.name,
                uid: tokenData.uid,
                contractId: `Token via API - ${tokenData.uid}`,
              }));

              const updateGroupRequest = await fetch(
                `${env.LONGSHIP_API}localtokengroups/${apiTokenGroup.id}`,
                {
                  method: "PUT",
                  headers: headers,
                  body: JSON.stringify({
                    oucode: apiTokenGroup.oucode,
                    tokenGroupName: apiTokenGroup.tokenGroupName,
                    targetOUCodes: apiTokenGroup.targetOUCodes,
                    overrideTariffId: apiTokenGroup.overrideTariffId,
                    tokens: newTokens,
                    targetChargepointIds: apiTokenGroup.targetChargepointIds,
                    isPrefix: apiTokenGroup.isPrefix,
                  }),
                },
              );

              if (updateGroupRequest.ok) {
                // Alle neuen Tokens als erfolgreich hinzugefügt markieren
                for (const tokenData of tokens) {
                  longshipSuccess.push({
                    uid: tokenData.uid,
                    name: tokenData.name,
                    action: "token_added",
                  });
                }

                // Anzahl der ersetzten Tokens tracken
                const replacedTokensCount = apiTokenGroup.tokens.length;
                if (replacedTokensCount > 0) {
                  longshipSuccess.push({
                    action: "tokens_replaced",
                    count: replacedTokensCount,
                    groupId: apiTokenGroup.id,
                  });
                }
              } else {
                const errorText = await updateGroupRequest.text();
                longshipErrors.push(`Failed to update API TokenGroup: ${errorText}`);
              }
            } catch (error) {
              {
      const msg = "Error updating API TokenGroup:";
      const errorMsg = (error as Error)?.message || String(error);
      Logger(`${msg}: ${errorMsg}`, "API 500", "src/app/api/v1/contact/tokens/route.ts", LogType.ERROR);
    }
              longshipErrors.push("Failed to update API TokenGroup: Network error");
            }
          }
        }
      } else {
        const errorText = await fetchRequest.text();
        longshipErrors.push(`Failed to fetch Longship TokenGroups: ${errorText}`);
      }
    } catch (error) {
      {
      const msg = "Error communicating with Longship:";
      const errorMsg = (error as Error)?.message || String(error);
      Logger(`${msg}: ${errorMsg}`, "API 500", "src/app/api/v1/contact/tokens/route.ts", LogType.ERROR);
    }
      longshipErrors.push("Failed to communicate with Longship API");
    }

    return NextResponse.json({
      success: true,
      message: `${createdTokens.length} Token(s) erfolgreich verarbeitet`,
      processedTokens: createdTokens,
      errors: errors,
      longship: {
        success: longshipSuccess,
        errors: longshipErrors,
        apiTokenGroup: longshipSuccess.find((item) => item.action === "group_created")
          ? {
              created: true,
              groupName: longshipSuccess.find((item) => item.action === "group_created")?.groupName,
            }
          : {
              created: false,
              existing: true,
            },
        statistics: {
          pushed: longshipSuccess.filter((item) => item.action === "token_added").length,
          failed: longshipErrors.length,
          groupCreated: longshipSuccess.some((item) => item.action === "group_created"),
          tokensReplaced:
            longshipSuccess.find((item) => item.action === "tokens_replaced")?.count || 0,
        },
      },
      statistics: {
        total: tokens.length,
        processed: createdTokens.length,
        errors: errors.length,
        longshipPushed: longshipSuccess.length,
        longshipErrors: longshipErrors.length,
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    {
      const msg = "Error processing token request:";
      const errorMsg = (error as Error)?.message || String(error);
      Logger(`${msg}: ${errorMsg}`, "API 500", "src/app/api/v1/contact/tokens/route.ts", LogType.ERROR);
    }
    return NextResponse.json(
      {
        error: "Internal server error",
        message: "Ein unerwarteter Fehler ist aufgetreten",
      },
      { status: 500 },
    );
  }
}
