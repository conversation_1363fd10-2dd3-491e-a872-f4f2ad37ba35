import { NextRequest, NextResponse } from "next/server";
import { validate<PERSON><PERSON><PERSON><PERSON> } from "~/utils/apiAuth/apiKeyUtil";
import { getOusBelowOu } from "~/server/model/ou/func";
import {
  loadSessionData,
  loadChargepointstatus,
  loadLocation,
  getCurrentKw,
} from "~/pages/api/realtime2";
import { todayEnd, yesterdayEnd } from "~/utils/date/date";
import prisma from "~/server/db/prisma";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";


export const dynamic = "force-dynamic";

/**
 * Lädt die heutigen CDRs für die angegebenen OUs und berechnet die Summe der kWh
 * @param ouIds Array von OU-IDs
 * @returns Summe der kWh von heute
 */
const loadTodayKwhFromCdrs = async (ouIds: string[]): Promise<number> => {
  try {
    // Alle OUs laden um die OU-Codes zu bekommen
    const ous = await prisma.ou.findMany({
      where: {
        id: { in: ouIds },
        deleted: { equals: null },
      },
      select: {
        code: true,
      },
    });

    const ouCodes = ous.map((ou) => ou.code);

    if (ouCodes.length === 0) {
      return 0;
    }

    // Heutige CDRs laden (Start_datetime zwischen gestern Ende und heute Ende)
    const todayCdrs = await prisma.cdr.findMany({
      where: {
        OU_Code: { in: ouCodes },
        Start_datetime: {
          gt: yesterdayEnd(),
          lte: todayEnd(),
        },
        Volume: {
          not: null,
        },
      },
      select: {
        Volume: true,
      },
    });

    // Summe der kWh berechnen
    const totalKwh = todayCdrs.reduce((sum, cdr) => {
      return sum + (cdr.Volume || 0);
    }, 0);

    return totalKwh;
  } catch (error) {
    {
      const msg = "Error loading today's kWh from CDRs";
      const errorMsg = (error as Error)?.message || String(error);
      Logger(`${msg}: ${errorMsg}`, "API 500", "src/app/api/v1/ou/charging-power/route.ts", LogType.ERROR);
    }
    return 0;
  }
};

/**
 * @swagger
 * /api/v1/ou/charging-power:
 *   get:
 *     tags:
 *       - Charging Power
 *     summary: Aktuelle Ladeleistung Zusammenfassung
 *     description: Gibt eine Zusammenfassung der aktuellen Ladeleistung für eine OU zurück - Gesamtleistung in kW, Anzahl aktiver Sessions und heutige kWh-Summe aus CDR-Daten.
 *     security:
 *       - ApiKeyAuth: []
 *     responses:
 *       200:
 *         description: Ladeleistungsdaten erfolgreich abgerufen
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/ChargingPowerResponse'
 *       400:
 *         description: Fehlerhafte Anfrage
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       401:
 *         description: API-Key ungültig oder fehlt
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 *       500:
 *         description: Interner Serverfehler
 *         content:
 *           application/json:
 *             schema:
 *               $ref: '#/components/schemas/Error'
 */

/**
 * API-Endpunkt für Ladeleistung-Zusammenfassung einer OU
 * Berechnet die Gesamtladeleistung basierend auf aktiven Sessions (wie auf der Realtime-Seite)
 * Zusätzlich wird die Summe der heutigen kWh aus der CDR-Tabelle berechnet
 * Gibt nur Zusammenfassungsdaten zurück, keine einzelnen Ladepunkte
 * URL: GET /api/v1/ou/charging-power
 */
export async function GET(request: NextRequest) {
  try {
    // API-Key Validierung
    const validation = await validateApiKey(request);

    if (!validation.isValid) {
      return NextResponse.json(
        {
          error: "API key validation failed",
          message: validation.error,
        },
        { status: 401 },
      );
    }

    const contact = validation.contact;
    const contactOu = contact?.ou;

    if (!contactOu) {
      return NextResponse.json(
        {
          error: "No OU found for contact",
          message: "Contact ist keiner OU zugeordnet",
        },
        { status: 400 },
      );
    }

    // Alle OUs unterhalb der Contact-OU laden (inklusive der OU selbst)
    const ous = await getOusBelowOu(contactOu);
    const ouIds = ous.map((ou) => ou.id);

    // Session-Daten und Ladepunkt-Status laden
    const activeChargingSessions = await loadSessionData(ouIds);

    // Gesamte aktuelle Ladeleistung berechnen
    const totalCurrentKw = getCurrentKw(activeChargingSessions);

    // Heutige kWh aus CDRs berechnen
    const todayTotalKwh = await loadTodayKwhFromCdrs(ouIds);

    return NextResponse.json({
      success: true,
      timestamp: new Date().toISOString(),
      contact: {
        name: contact?.name,
      },
      chargingPower: {
        totalCurrentKw: (totalCurrentKw / 1000).toFixed(2), // Watt zu kW, 2 Dezimalstellen
        totalActiveSessions: activeChargingSessions.length,
        todayTotalKwh: todayTotalKwh.toFixed(2), // Heutige kWh aus CDRs, 2 Dezimalstellen
      },
      statistics: {
        totalOusChecked: ouIds.length,
        totalActiveSessions: activeChargingSessions.length,
      },
    });
  } catch (error) {
    {
      const msg = "Internal server error - fetching charging power";
      const errorMsg = (error as Error)?.message || String(error);
      Logger(`${msg}: ${errorMsg}`, 'API 500', 'src/app/api/v1/ou/charging-power/route.ts', LogType.ERROR);
    }
    return NextResponse.json(
      {
        error: "Internal server error",
        message: "Fehler beim Abrufen der Ladeleistung",
      },
      { status: 500 },
    );
  }
}
