import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import prisma from "~/server/db/prisma";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { z } from "zod";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";

const CPOContractSchema = z.object({
  id: z.string().optional(),
  numACCharger: z.coerce.number().int().min(0),
  numDCCharger: z.coerce.number().int().min(0),
  numACHotline: z.coerce.number().int().min(0),
  numDCHotline: z.coerce.number().int().min(0),
  priceACCharger: z.coerce.number().min(0),
  priceDCCharger: z.coerce.number().min(0),
  start: z.string().refine((date) => !isNaN(Date.parse(date)), {
    message: "Invalid date format",
  }),
  end: z.string().refine((date) => !isNaN(Date.parse(date)), {
    message: "Invalid date format",
  }),
  contactId: z.string().optional(),
  kwhFeeCent: z.coerce.number().min(0),
  kwhFeeCentAC: z.coerce.number().min(0),
  kwhFeeCentDC: z.coerce.number().min(0),
  sessionFeeCent: z.coerce.number().min(0),
  sessionFeeCentAC: z.coerce.number().min(0),
  sessionFeeCentDC: z.coerce.number().min(0),
  monthlyEmployeeClubCharging: z.coerce.number().min(0),
  directPaymentFeePercent: z.coerce.number().min(0).max(100),
  adhocPaymentFeePercent: z.coerce.number().min(0).max(100),
  directPaymentToken: z.string(),
  serviceFeePerAC: z.coerce.number().min(0),
  serviceFeePerDC: z.coerce.number().min(0),
  priceACHotline: z.coerce.number().min(0),
  priceDCHotline: z.coerce.number().min(0),
  name: z.string(),
});

export async function POST(request: NextRequest) {
  const { data } = await request.json();
  const validated = CPOContractSchema.safeParse(data);
  const session = await getServerSession(authOptions);
  if (!session || !session?.user) {
    return NextResponse.json("No Login", { status: 404 });
  }

  if (validated.success) {
    try {
      const dbentry = await prisma.cPOContract.upsert({
        where: { id: validated.data?.id || "" },
        update: {
          numACCharger: +validated.data.numACCharger,
          numDCCharger: +validated.data.numDCCharger,
          numACHotline: +validated.data.numACHotline,
          numDCHotline: +validated.data.numDCHotline,
          priceACCharger: +validated.data.priceACCharger,
          priceDCCharger: +validated.data.priceDCCharger,
          priceACHotline: +validated.data.priceACHotline,
          priceDCHotline: +validated.data.priceDCHotline,
          start: new Date(validated.data.start),
          end: new Date(validated.data.end),
          kwhFeeCent: +validated.data.kwhFeeCent,
          kwhFeeCentAC: +validated.data.kwhFeeCentAC,
          kwhFeeCentDC: +validated.data.kwhFeeCentDC,
          sessionFeeCent: +validated.data.sessionFeeCent,
          sessionFeeCentAC: +validated.data.sessionFeeCentAC,
          sessionFeeCentDC: +validated.data.sessionFeeCentDC,
          monthlyEmployeeClubCharging: +validated.data.monthlyEmployeeClubCharging,
          directPaymentFeePercent: +validated.data.directPaymentFeePercent,
          adhocPaymentFeePercent: +validated.data.adhocPaymentFeePercent,
          contactId: validated.data.contactId,
          directPaymentToken: validated.data.directPaymentToken,
          serviceFeePerAC: +validated.data.serviceFeePerAC,
          serviceFeePerDC: +validated.data.serviceFeePerDC,
          name: validated.data.name,
        },
        create: {
          numACCharger: +validated.data.numACCharger,
          numDCCharger: +validated.data.numDCCharger,
          numACHotline: +validated.data.numACHotline,
          numDCHotline: +validated.data.numDCHotline,
          priceACHotline: +validated.data.priceACHotline,
          priceDCHotline: +validated.data.priceDCHotline,
          priceACCharger: +validated.data.priceACCharger,
          priceDCCharger: +validated.data.priceDCCharger,
          start: new Date(validated.data.start),
          end: new Date(validated.data.end),
          kwhFeeCent: +validated.data.kwhFeeCent,
          kwhFeeCentAC: +validated.data.kwhFeeCentAC,
          kwhFeeCentDC: +validated.data.kwhFeeCentDC,
          sessionFeeCent: +validated.data.sessionFeeCent,
          sessionFeeCentAC: +validated.data.sessionFeeCentAC,
          sessionFeeCentDC: +validated.data.sessionFeeCentDC,
          monthlyEmployeeClubCharging: +validated.data.monthlyEmployeeClubCharging,
          directPaymentFeePercent: +validated.data.directPaymentFeePercent,
          adhocPaymentFeePercent: +validated.data.adhocPaymentFeePercent,
          contact: { connect: { id: validated.data.contactId } },
          directPaymentToken: validated.data.directPaymentToken,
          serviceFeePerAC: +validated.data.serviceFeePerAC,
          serviceFeePerDC: +validated.data.serviceFeePerDC,
          name: validated.data.name,
        },
      });

      return NextResponse.json(dbentry);
    } catch (e) {
      Logger("API 500 Error", "API 500", "src/app/api/cpoContract/route.ts", LogType.ERROR);
    return NextResponse.json({ success: false, message: "DB Error" }, { status: 500 });
    }
  } else {
    Logger("API 500 Error", "API 500", "src/app/api/cpoContract/route.ts", LogType.ERROR);
    return NextResponse.json({ success: false, message: "Invalid data" }, { status: 500 });
  }
}
