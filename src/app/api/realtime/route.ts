import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import type { KwhProps, MspProps } from "~/pages/api/cdr/cdrRealtimeOverview";

import prisma from "~/server/db/prisma";
import { Cdr, Role } from "@prisma/client";
import {
  calcKwhFromCdr,
  findLocationByChargePointId,
  getCurrentKw,
  loadChargepointstatus,
  loadCharger,
  loadLocation,
  loadSessionData,
} from "~/pages/api/realtime2";
import { getOusBelowOu } from "~/server/model/ou/func";
import { todayEnd, yesterdayEnd, yesterdayStart } from "~/utils/date/date";
import type { Ou } from "@prisma/client";
import { getChargepointErrors } from "~/utils/monitoring/chargepointErrors";
import { countOperationalStatus } from "~/app/api/realtime/helper/helper";
import { getOcppMessagesFromToday } from "~/utils/monitoring/ocppMessages";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";

export const revalidate = 60;

export interface ChargeByEvseProps {
  evse: string;
  current_wh: number;
}

export interface ConnectorStatus {
  operationalStatus: string;
  evseId: string;
  connector: string;
  chargePointId: string;
  last_update: Date;
}

export interface RealTimeChargepointData {
  id: string;
  place: string;
  displayName: string;
  operationalStatus: string;
  lastStateChange: Date;
  kwhInCurrentSession: number;
  currentKw: number;
  parkingSensor: number | undefined;
  kWhToday: number;
  emp: string;
}

async function getCurrentHourStockPrice() {
  const currentHour = new Date().getHours();
  const currentMonth = new Date().getMonth() + 1;
  const currentDay = new Date().getDate();
  const currentYear = new Date().getFullYear();

  const stockPrice = await prisma.energyStockPrice.findFirst({
    where: {
      hour: currentHour,
      day: currentDay,
      month: currentMonth,
      year: currentYear,
    },
    orderBy: {
      timestamp: "desc",
    },
  });

  if (!stockPrice) {
    return "";
    throw new Error("No stock price found for the current hour");
  }

  // 2. Holen Sie den vorherigen Börsenpreis basierend auf dem Timestamp des aktuellen Börsenpreises
  const previousStockPrice = await prisma.energyStockPrice.findFirst({
    where: {
      timestamp: {
        lt: stockPrice.timestamp,
      },
    },
    orderBy: {
      timestamp: "desc",
    },
  });

  // 3. Holen Sie den nächsten Börsenpreis basierend auf dem Timestamp des aktuellen Börsenpreises
  const nextStockPrice = await prisma.energyStockPrice.findFirst({
    where: {
      timestamp: {
        gt: stockPrice.timestamp,
      },
    },
    orderBy: {
      timestamp: "asc",
    },
  });

  return [previousStockPrice, stockPrice, nextStockPrice];
}

export async function POST(request: NextRequest) {
  const session = await getServerSession(authOptions);
  if (!session) {
    return NextResponse.json(
      {
        error: "No session",
      },
      { status: 404 },
    );
  }
  const { ouId } = await request.json();

  if (!ouId) {
    return NextResponse.json(
      {
        error: "No ouId provided",
      },
      { status: 400 },
    );
  }

  const ou = await prisma.ou.findUnique({
    where: {
      id: ouId,
    },
  });

  if (!ou) {
    return NextResponse.json(
      {
        error: "No ou found",
      },
      { status: 400 },
    );
  }

  const ous = await getOusBelowOu(ou);

  if (!ous) {
    return NextResponse.json(
      {
        error: "No ou found",
      },
      { status: 400 },
    );
  }

  const query = {
    where: {
      End_datetime: {
        gte: yesterdayStart(),
        lte: todayEnd(),
      },
      OR: ous.map((ou: Ou) => ({
        OU_Code: `'${ou.code}'`,
      })),
    },
  };

  const cdrs = await prisma.cdr.findMany(query);

  let yesterday_cdr = 0;
  let yesterday_invalid_cdr = 0;
  let today_cdr = 0;
  let today_invalid_cdr = 0;
  let yesterday_valid_kWh = 0;
  let yesterday_invalid_kWh = 0;
  let today_valid_kWh = 0;
  let today_invalid_kWh = 0;
  let yesterday_msp: MspProps = {};
  let today_msp: MspProps = {};
  let today_kWh_by_evse: KwhProps = {
    evse: {},
    location: {},
  };
  try {
    const tokens = await prisma.token.findMany({
      include: {
        tokenGroup: {
          include: {
            contact: true,
          },
        },
      },
    });

    cdrs.map((cdr: Cdr) => {
      let providerPartyId = cdr?.Service_Provider_ID;
      if (!providerPartyId) {
        const token = tokens.find(
          (token) => token.authenticationId?.toUpperCase() == cdr.Authentication_ID?.toUpperCase(),
        );
        if (token) {
          providerPartyId = "Local";
        } else {
          providerPartyId = "unknown";
        }
      }

      if (!cdr.Volume) {
        cdr.Volume = 0;
      }
      if (
        new Date(cdr.End_datetime).getTime() > yesterdayStart().getTime() &&
        new Date(cdr.End_datetime).getTime() < yesterdayEnd().getTime()
      ) {
        if (cdr.Volume > 0.2) {
          yesterday_cdr++;
          yesterday_valid_kWh += cdr.Volume;
        } else {
          yesterday_invalid_cdr++;
          yesterday_invalid_kWh += cdr.Volume;
        }
        const mspData = yesterday_msp[providerPartyId];
        if (mspData) {
          yesterday_msp = {
            ...yesterday_msp,
            [providerPartyId]: {
              sessions: mspData.sessions + 1,
              kwh: mspData.kwh + cdr.Volume,
            },
          };
        } else {
          yesterday_msp = {
            ...yesterday_msp,
            [providerPartyId]: {
              sessions: 1,
              kwh: cdr.Volume,
            },
          };
        }
      }

      if (
        new Date(cdr.End_datetime).getTime() > yesterdayEnd().getTime() &&
        new Date(cdr.End_datetime).getTime() < todayEnd().getTime()
      ) {
        if (cdr.Volume > 0.2) {
          today_cdr++;
          today_valid_kWh += cdr.Volume;
        } else {
          today_invalid_cdr++;
          today_invalid_kWh += cdr.Volume;
        }
        const todayMspData = today_msp[providerPartyId];
        if (providerPartyId && todayMspData) {
          today_msp = {
            ...today_msp,
            [providerPartyId]: {
              sessions: todayMspData.sessions + 1,
              kwh: todayMspData.kwh + cdr.Volume,
            },
          };
        } else {
          today_msp = {
            ...today_msp,
            [providerPartyId]: {
              sessions: 1,
              kwh: cdr.Volume,
            },
          };
        }

        if (!cdr.Charge_Point_ID || !cdr.Charge_Point_Address) {
          return;
        }

        if (cdr.Charge_Point_ID in today_kWh_by_evse.evse) {
          today_kWh_by_evse = {
            ...today_kWh_by_evse,
            evse: {
              ...today_kWh_by_evse.evse,
              [cdr.Charge_Point_ID]:
                (today_kWh_by_evse.evse[cdr.Charge_Point_ID] || 0) + cdr.Volume,
            },
          };
        } else {
          today_kWh_by_evse = {
            ...today_kWh_by_evse,
            evse: {
              ...today_kWh_by_evse.evse,
              [cdr.Charge_Point_ID]: cdr.Volume,
            },
          };
        }
        if (cdr.Charge_Point_Address in today_kWh_by_evse.location) {
          today_kWh_by_evse = {
            ...today_kWh_by_evse,
            location: {
              ...today_kWh_by_evse.location,
              [cdr.Charge_Point_Address]:
                (today_kWh_by_evse.location[cdr?.Charge_Point_Address] || 0) + cdr.Volume,
            },
          };
        } else {
          today_kWh_by_evse = {
            ...today_kWh_by_evse,
            location: {
              ...today_kWh_by_evse.location,
              [cdr.Charge_Point_Address]: cdr.Volume,
            },
          };
        }
      }
    });

    const ouIds = ous.map((ou) => ou.id);
    const activeChargingSessions = await loadSessionData(ouIds);

    const allChargePoints = await loadCharger(ouIds);
    const chargePointIds = allChargePoints.map((chargePoint) => chargePoint.id);
    const chargepointStatusList = await loadChargepointstatus(undefined, chargePointIds);
    const locations = await loadLocation(ouIds);
    const overallKw = getCurrentKw(activeChargingSessions);

    const connector_status: Record<string, any> = {};

    // Führen Sie eine Prisma-Query aus, um alle Evse-Elemente zu erhalten, die diesen chargePointIds entsprechen
    const evseIds = await prisma.evse.findMany({
      where: {
        chargePointId: {
          in: chargePointIds,
        },
      },
      select: { evse_id: true, chargePointId: true, connector: true },
    });
    const offlineConnectors: ConnectorStatus[] = [];
    const connectorStatusList: ConnectorStatus[] = [];
    let targetList: ConnectorStatus[];
    for (const chargePointStatusObject of chargepointStatusList) {
      if (
        chargePointStatusObject.connectors.length == 0 ||
        !allChargePoints.find((chargePoint) => chargePoint.id == chargePointStatusObject.id)
      ) {
        continue;
      }

      //const current_status: any = status[i]?.connectors[0].operationalStatus;

      if (chargePointStatusObject?.connectivityStatus == "OFFLINE") {
        targetList = offlineConnectors;
      } else {
        targetList = connectorStatusList;
      }
      for (const connector of chargePointStatusObject.connectors) {
        targetList.push({
          last_update: connector.timestamp,
          operationalStatus: connector.operationalStatus,
          chargePointId: chargePointStatusObject?.id,
          connector: connector.connectorNumber,
          evseId:
            evseIds.find(
              (element) =>
                element.chargePointId == chargePointStatusObject?.id &&
                element.connector == connector.connectorNumber,
            )?.evse_id ?? `${chargePointStatusObject?.id}/${connector.connectorNumber}`,
        });
      }
    }
    const parkingSensorStates = await prisma.parkingSensor.findMany({});
    const realtime_chargepoint_data: RealTimeChargepointData[] = [];
    const cardHolderTokens = await prisma.physicalCard.findMany({
      select: { uid: true, visualNumber: true },
    });
    for (const status_from_current_charger of connectorStatusList) {
      let emp = "";
      const current_session = activeChargingSessions.find(
        (session: any) =>
          session.chargePointId == status_from_current_charger.chargePointId &&
          session.connectorId == status_from_current_charger.connector,
      );
      let currentKw = 0;
      if (current_session) {
        const currentWh = current_session.chargingMeterValues
          .slice()
          .reverse()
          .find((item: any) => item.measurand == "Power.Active.Import");
        currentKw = parseFloat(currentWh?.value || 0);
        if (currentKw > 0 && currentWh?.unit != "kW") {
          currentKw = currentKw / 1000;
        }
        emp = `${current_session?.startedByInfo?.tokenInfo?.providerCountryCode}*${current_session?.startedByInfo?.tokenInfo?.providerPartyId}`;

        //if no roaming check local tokens from longship or mitarbeiterladen
        if (!current_session?.startedByInfo?.tokenInfo?.providerCountryCode) {
          emp = current_session?.startedByInfo?.tokenInfo?.contractId ?? "Unknown";
          const token = tokens.find(
            (token) =>
              token.authenticationId?.toUpperCase() == current_session?.idTag?.toUpperCase(),
          );
          if (token) {
            if (token?.tokenGroup?.contact?.name) {
              emp = token?.tokenGroup?.contact?.name;
            } else if (token?.tokenGroup?.name) {
              emp = token?.tokenGroup?.name;
            }
          } else {
            const cardHolderToken = cardHolderTokens.find(
              (token) => token.uid.toUpperCase() == current_session?.idTag.toUpperCase(),
            );
            if (cardHolderToken) {
              emp = cardHolderToken.visualNumber;
            }
          }
        }
      }

      const cp = allChargePoints.find(
        (chargePoint: any) =>
          chargePoint.chargePointId == status_from_current_charger.chargePointId,
      );
      let locationName = "No Roaming";
      if (cp?.locationId) {
        const location_of_current_charger = findLocationByChargePointId(
          locations,
          cp?.chargePointId,
        );
        locationName = location_of_current_charger?.name || "Location not found";
      }

      //const current_evse = findEvseFromChargePointId(location, location?.chargePointId);

      const kWhToday = calcKwhFromCdr(cdrs, status_from_current_charger.evseId);

      realtime_chargepoint_data.push({
        id: status_from_current_charger.evseId,
        place: locationName,
        displayName: status_from_current_charger.evseId,
        operationalStatus: status_from_current_charger.operationalStatus,
        lastStateChange: status_from_current_charger.last_update,
        kwhInCurrentSession: current_session?.totalEnergyInKwh || 0,
        kWhToday: kWhToday + (current_session?.totalEnergyInKwh || 0),
        currentKw: currentKw,
        parkingSensor: parkingSensorStates.find(
          (sensor) => sensor.evseId === status_from_current_charger.evseId,
        )?.state,
        emp: emp,
      });
    }

    let chargePointErrors = await getChargepointErrors();
    if (session?.user?.role !== Role.ADMIN) {
      chargePointErrors = chargePointErrors?.filter((error) =>
        chargePointIds.includes(error.chargePointId),
      );
    }
    let ocppMessages = await getOcppMessagesFromToday();
    if (session?.user.role !== Role.ADMIN) {
      ocppMessages = ocppMessages?.filter((message) =>
        chargePointIds.includes(message.chargePointId),
      );
    }
    const twoDaysAgo = new Date();
    twoDaysAgo.setDate(twoDaysAgo.getDate() - 2);

    const events = await prisma.monitoringEvent.groupBy({
      where: { createdAt: { gte: twoDaysAgo }, resolved: false },
      by: ["evseId", "message"],
      _count: {
        message: true,
      },
      _max: {
        createdAt: true,
        type: true,
        href: true,
        lastevent: true,
      },
    });

    const monitoringEvents = events.map((event) => {
      const le = event?._max?.lastevent ?? event?._max?.lastevent ?? null;
      return {
        evseId: event.evseId,
        message: event.message,
        count: event._count.message,
        type: event._max.type,
        lastevent: le == null ? null : (typeof le === "bigint" ? le.toString() : le),
        href: event?._max?.href ?? null,
      };
    });
    return NextResponse.json({
      yesterday_valid_kWh,
      yesterday_invalid_kWh,
      yesterday_cdr,
      yesterday_invalid_cdr,
      today_cdr,
      yesterday_msp,
      today_msp,
      today_invalid_cdr,
      today_valid_kWh,
      today_invalid_kWh,
      today_kWh_by_evse,
      connector_status: countOperationalStatus(connectorStatusList),
      realtime_chargepoint_data,
      current_kw_charging: overallKw.toFixed(1),
      stockPrice: await getCurrentHourStockPrice(),
      chargePointErrors: chargePointErrors,
      offlineConnectors: offlineConnectors,
      ocppMessages: ocppMessages,
      monitoringEvents: monitoringEvents,
    });
  } catch (e: unknown) {
    let message;
    if (typeof e === "string") {
      message = e.toUpperCase(); // works, `e` narrowed to string
    } else if (e instanceof Error) {
      message = e.message; // works, `e` narrowed to Error
    }
    NextResponse.json({ error: message });
  }
}
