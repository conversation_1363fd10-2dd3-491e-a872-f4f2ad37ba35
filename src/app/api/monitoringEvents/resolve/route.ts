import { NextResponse } from "next/server";
import prisma from "~/server/db/prisma";

export async function POST(req: Request) {
  const { evseId, type, lastevent } = await req.json();
  try {
  const result = await prisma.monitoringEvent.update({
    where: { lastevent_evseId_type:  { evseId: evseId, type: type, lastevent: BigInt(lastevent) }},
    data: { resolved: true },
  });} catch (e) {
    return NextResponse.json({ error: "Error resolving monitoring event" }, { status: 500 });
  }

  return NextResponse.json({ status: 200 });
}
