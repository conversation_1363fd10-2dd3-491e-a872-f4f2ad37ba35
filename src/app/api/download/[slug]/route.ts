import type { NextRequest } from "next/server";
import prisma from "../../../../server/db/prisma";
import { NextResponse } from "next/server";
import { readFileSync } from "fs";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role } from "@prisma/client";
import { env } from "~/env";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";

interface Props {
  params: {
    slug: string;
  };
}

export async function GET(request: NextRequest, { params }: Props) {
  const session = await getServerSession(authOptions);
  if (!session || !session.user) {
    return NextResponse.next({ status: 401, statusText: "not authorized" });
  }

  const invoiceId = params.slug;

  const fileRef = await prisma.fileRef.findUnique({
    where: {
      id: invoiceId,
    },
  });

  if (fileRef) {
    if (fileRef.stripePayoutId && session.user.role !== Role.ADMIN) {
      return NextResponse.next({ status: 401, statusText: "not authorized" });
    }
  }
  if (!fileRef || !fileRef?.path || !fileRef.contentType) {
    return NextResponse.next({ status: 500, statusText: "no file found" });
  }
  let myfile;
  try {
    myfile = readFileSync(fileRef.path);
  } catch (e) {
    return NextResponse.json(
      { success: false, message: "file not found" },
      { status: 404, statusText: "file not found" },
    );
  }

  if (session.user.role == Role.ADMIN) {
    return new NextResponse(myfile, {
      headers: {
        "Content-Type": fileRef.contentType,
        "Content-Disposition": `attachment; filename="${
          env.NODE_ENV != "production" ? "_DEV_" : ""
        }${fileRef.name}`,
      },
    });
  }

  if (fileRef.invoiceId) {
    const invoice = await prisma.invoice.findUnique({
      where: { id: fileRef.invoiceId },
      include: { contact: { include: { ou: true } } },
    });
    if (session.user.role == Role.CARD_HOLDER && invoice?.userId === session.user.id) {
      return new NextResponse(myfile, {
        headers: {
          "Content-Type": fileRef.contentType,
          "Content-Disposition": `attachment; filename="${
            env.NODE_ENV != "production" ? "_DEV_" : ""
          }$${fileRef.name}"`,
        },
      });
    }

    if (invoice?.contact?.ou?.id == session.user.ou.id) {
      return new NextResponse(myfile, {
        headers: {
          "Content-Type": fileRef.contentType,
          "Content-Disposition": `attachment; filename="${
            env.NODE_ENV != "production" ? "_DEV_" : ""
          }$${fileRef.name}"`,
        },
      });
    }
  }
  return NextResponse.next({ status: 401, statusText: "not authorized" });
}
