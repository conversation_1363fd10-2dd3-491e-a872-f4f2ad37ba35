import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role, NotificationType } from "@prisma/client";
import prisma from "~/server/db/prisma";
import { generateUniqueApiKey } from "~/utils/apiAuth/apiKeyUtil";
import { createSystemNotificationForAdmins } from "~/utils/notifications/createSystemNotification";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";

/**
 * Get API key for the user's OU contact
 */
export async function GET(request: NextRequest) {
  const session = await getServerSession(authOptions);

  if (!session || !session?.user?.role || ![Role.CPO, Role.CARD_MANAGER, Role.ADMIN].includes(session.user.role)) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const userOuId = session.user.selectedOu?.id || session.user.ou?.id;
    
    if (!userOuId) {
      return NextResponse.json({ error: "Keine OU zugeordnet" }, { status: 400 });
    }

    // Find CPO contact for this OU
    const contact = await prisma.contact.findFirst({
      where: {
        ouId: userOuId,
        cpo: true,
      },
      select: {
        id: true,
        name: true,
        companyName: true,
        apiKey: true,
        ou: {
          select: {
            id: true,
            name: true,
            code: true,
          },
        },
      },
    });

    if (!contact) {
      return NextResponse.json({ 
        error: "Kein CPO-Contact für Ihre OU gefunden" 
      }, { status: 404 });
    }

    return NextResponse.json({ contact });
  } catch (error) {
    {
      const msg = "Error fetching API key:";
      const errorMsg = (error as Error)?.message || String(error);
      Logger(`${msg}: ${errorMsg}`, "API 500", "src/app/api/my-api-key/route.ts", LogType.ERROR);
    }
    Logger("API 500 Error", "API 500", "src/app/api/my-api-key/route.ts", LogType.ERROR);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

/**
 * Generate or regenerate API key for the user's OU contact
 */
export async function POST(request: NextRequest) {
  const session = await getServerSession(authOptions);

  if (!session || !session?.user?.role || ![Role.CPO, Role.CARD_MANAGER, Role.ADMIN].includes(session.user.role)) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const userOuId = session.user.selectedOu?.id || session.user.ou?.id;
    
    if (!userOuId) {
      return NextResponse.json({ error: "Keine OU zugeordnet" }, { status: 400 });
    }

    // Find CPO contact for this OU
    const contact = await prisma.contact.findFirst({
      where: {
        ouId: userOuId,
        cpo: true,
      },
    });

    if (!contact) {
      return NextResponse.json({ 
        error: "Kein CPO-Contact für Ihre OU gefunden" 
      }, { status: 404 });
    }

    // Generate unique API key
    const apiKey = await generateUniqueApiKey();

    // Update contact with new API key
    const updatedContact = await prisma.contact.update({
      where: { id: contact.id },
      data: { apiKey },
      select: {
        id: true,
        name: true,
        companyName: true,
        apiKey: true,
        ou: {
          select: {
            id: true,
            name: true,
            code: true,
          },
        },
      },
    });

    // Create admin notification
    const roleName = session.user.role === Role.ADMIN ? 'Administrator' :
                     session.user.role === Role.CPO ? 'CPO' : 'Card Manager';
    const ouName = session.user.selectedOu?.name || session.user.ou?.name || 'Unbekannte OU';

    await createSystemNotificationForAdmins({
      nachricht: `🔑 API Key ${contact.apiKey ? 'regeneriert' : 'generiert'} für CPO-Contact "${contact.name || contact.companyName || 'Unbekannt'}" durch ${roleName} "${session.user.name} ${session.user.lastName}" (OU: ${ouName})`,
      type: NotificationType.INFO,
    });

    return NextResponse.json({
      message: contact.apiKey ? "API key regenerated successfully" : "API key generated successfully",
      contact: updatedContact,
    });
  } catch (error) {
    {
      const msg = "Error generating API key:";
      const errorMsg = (error as Error)?.message || String(error);
      Logger(`${msg}: ${errorMsg}`, "API 500", "src/app/api/my-api-key/route.ts", LogType.ERROR);
    }
    Logger("API 500 Error", "API 500", "src/app/api/my-api-key/route.ts", LogType.ERROR);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
