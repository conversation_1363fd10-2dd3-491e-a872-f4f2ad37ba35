import { NextRequest, NextResponse } from "next/server";
import { validateWebhook<PERSON><PERSON><PERSON><PERSON> } from "~/utils/apiAuth/apiKeyUtil";
import prisma from "~/server/db/prisma";
import { LogType } from "@prisma/client";
import Logger from "~/server/logger/logger";
export const dynamic = "force-dynamic";

interface WebhookSessionUpdateData {
  totalenergyinkwh: number;
  totalduration: string;
  totalcosts: number;
  chargepointid: string;
  connectornumber: number;
  locationid: string;
  evseid: string;
  transactionid: string;
  stateofcharge?: number;
}

export async function POST(request: NextRequest) {
  // API-Key Validierung
  const validation = await validateWebhookApiKey(request);

  if (!validation.isValid) {
    return NextResponse.json(
      {
        error: "API key validation failed",
        message: validation.error,
      },
      { status: 401 },
    );
  }

  try {
    const all = await request.json();
    const {
      type,
      time,
      subject,
      data,
    }: { type: string; time: string; subject: string; data: WebhookSessionUpdateData } = all;

    if (type == "SessionStart") {
      await prisma.chargePointRealtimeData.upsert({
        where: {
          chargePointId_connectorNumber: {
            chargePointId: data.chargepointid,
            connectorNumber: data.connectornumber,
          },
        },
        update: {
          kW: 0, //todo hope longship updates webhook
          kWh: data.totalenergyinkwh,
          lastUpdate: new Date(time),
          soc: data?.stateofcharge ?? 0,
        },
        create: {
          chargePointId: data.chargepointid,
          connectorNumber: data.connectornumber,
          kWh: 0,
          kW: 0,
          currentSessionStart: new Date(time),
          sessionId: subject,
          lastUpdate: new Date(time),
          emp: "", //todo hope longship updates webhook
          locationId: data.locationid,
          authenticationId: "", //todo hope longship updates webhook
          evseId: data.evseid,
          status: "",
          soc: 0,
        },
      });
    } else if (type == "SessionUpdate") {
      await prisma.chargePointRealtimeData.upsert({
        where: {
          chargePointId_connectorNumber: {
            chargePointId: data.chargepointid,
            connectorNumber: data.connectornumber,
          },
        },
        update: {
          kW: undefined, //todo hope longship updates webhook
          kWh: data.totalenergyinkwh,
          lastUpdate: new Date(time),
          soc: data?.stateofcharge ?? undefined,
        },
        create: {
          chargePointId: data.chargepointid,
          connectorNumber: data.connectornumber,
          kWh: data.totalenergyinkwh,
          kW: 0,
          lastUpdate: new Date(time),
          emp: "",
          authenticationId: "",
          locationId: data.locationid,
          currentSessionStart: new Date(time),
          sessionId: subject,
          evseId: data.evseid,
          status: "",
          soc: data?.stateofcharge ?? undefined,
        },
      });
    } else if (type == "SessionStop") {
      await prisma.chargePointRealtimeData.upsert({
        where: {
          chargePointId_connectorNumber: {
            chargePointId: data.chargepointid,
            connectorNumber: data.connectornumber,
          },
        },
        update: {
          kW: 0, //todo hope longship updates webhook
          kWh: 0,
          lastUpdate: new Date(time),
          soc: 0,
          lastSessionEnd: new Date(time),
          sessionId: null,
          locationId: data.locationid,
          evseId: data.evseid,
        },
        create: {
          chargePointId: data.chargepointid,
          connectorNumber: data.connectornumber,
          kWh: 0,
          kW: 0,
          lastUpdate: new Date(time),
          emp: "",
          authenticationId: "",
          locationId: data.locationid,
          currentSessionStart: null,
          lastSessionEnd: new Date(time),
          sessionId: null,
          evseId: data.evseid,
          status: "",
          soc: 0,
        },
      });
    }
  } catch (e) {
    if (e instanceof Error) {
      Logger(`error on session webhook ${e.message}`, "session webhook", "webhook", LogType.ERROR);
    }
    return NextResponse.json(
      {
        error: "error getting webhook data",
        message: "error getting webhook data",
      },
      { status: 500 },
    );
  }
  return NextResponse.json("success", { status: 200 });
}
