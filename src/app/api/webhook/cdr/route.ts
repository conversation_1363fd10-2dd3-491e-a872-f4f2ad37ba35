import { NextRequest, NextResponse } from "next/server";
import { validate<PERSON><PERSON><PERSON><PERSON>, validateWebhookApi<PERSON>ey } from "~/utils/apiAuth/apiKeyUtil";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";
import prisma from "~/server/db/prisma";
import { triggerPlugAndChargeEvent } from "~/utils/plugAndCharge/plugAndChargeUtil";
import { DateTime } from "luxon";
export const dynamic = "force-dynamic";

interface WebhookOCPPStatusUpdateData {
  status: string;
  errorcode: string;
  vendorid: number;
  chargepointid: string;
  connectornumber: number;
  locationid: string;
  evseid: string;
  ocppTimestamp: string;
}

export async function POST(request: NextRequest) {
  console.log("POST"); // API-Key Validierung
  const validation = await validateApiKey(request);

  if (!validation.isValid) {
    return NextResponse.json(
      {
        error: "API key validation failed",
        message: validation.error,
      },
      { status: 401 },
    );
  }

  try {
    const all = await request.json();
    const {
      type,
      time,
      data,
    }: { type: string; time: string; subject: string; data: WebhookOCPPStatusUpdateData } = all;
    console.log(all);
    if (type == "OperationalStatusChanged") {
      const unixTimestampInS = DateTime.fromISO("2025-08-07T20:04:11.584Z").toSeconds();
      if (data.status == "Preparing" && data?.locationid) {
        // kein await, soll nicht warten
        triggerPlugAndChargeEvent(
          data.evseid,
          data.chargepointid,
          data.connectornumber,
          data.ocppTimestamp,
          data.locationid,
        );
      }
      await prisma.chargePointRealtimeData.upsert({
        where: {
          chargePointId_connectorNumber: {
            chargePointId: data.chargepointid,
            connectorNumber: data.connectornumber,
          },
        },
        update: {
          status: data.status,
          lastUpdate: new Date(data.ocppTimestamp),
        },
        create: {
          chargePointId: data.chargepointid,
          connectorNumber: data.connectornumber,
          kWh: 0,
          kW: 0,
          currentSessionStart: undefined,
          sessionId: "",
          lastUpdate: new Date(data.ocppTimestamp),
          emp: "", //todo hope longship updates webhook
          locationId: data.locationid,
          authenticationId: "", //todo hope longship updates webhook
          evseId: data.evseid,
          status: data.status,
          soc: 0,
        },
      });
    }
  } catch (e) {
    if (e instanceof Error) {
      Logger(String(e), "API 500", "src/app/api/webhook/ocppStatus/route.ts", LogType.ERROR);
    }
    return NextResponse.json(
      {
        error: "error getting webhook data",
        message: "error getting webhook data",
      },
      { status: 500 },
    );
  }
  return NextResponse.json("success", { status: 200 });
}
