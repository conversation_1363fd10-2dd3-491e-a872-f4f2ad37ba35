import { NextRequest, NextResponse } from "next/server";

import {
  processIncomingPlugAndChargeEvent,
  updatePlugAndChargeEvent,
} from "~/utils/plugAndCharge/plugAndChargeUtil";
import { IncomingPlugAndChargeEventSchema } from "~/types/plugAndCharge";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";
import { LongshipHeaders } from "~/utils/longship";
import { env } from "~/env";
import prisma from "~/server/db/prisma";

// Zod Schema für eingehende PlugAndCharge Webhook Events

/**
 * Webhook endpoint for receiving PlugAndCharge events from external systems
 * This endpoint handles incoming events and stores them in the database
 */
export async function POST(request: NextRequest) {
  const incomingRequestTimestamp = new Date();
  try {
    let rawBody = await request.json();
    rawBody = { ...rawBody, incomingRequestTimestamp: incomingRequestTimestamp };

    //rawBody = { ...rawBody, evseid: "DE*EUL*EPNCR*01" };
    //rawBody = { ...rawBody, evseid: "DE*EUL*EPNCRVW01*01" };
    Logger(
      `Received PlugAndCharge webhook: ${JSON.stringify(rawBody)}`,
      "PlugAndCharge",
      "Webhook",
      LogType.INFO,
    );

    // Validate the incoming data
    const validationResult = IncomingPlugAndChargeEventSchema.safeParse(rawBody);

    if (!validationResult.success) {
      Logger(
        `Invalid PlugAndCharge webhook data: ${JSON.stringify(validationResult.error.format())}`,
        "PlugAndCharge",
        "Webhook",
        LogType.ERROR,
      );

      return NextResponse.json(
        {
          success: false,
          error: "Invalid webhook data",
          details: validationResult.error.format(),
        },
        { status: 400 },
      );
    }

    const eventData = validationResult.data;

    const storedEvent = await processIncomingPlugAndChargeEvent(eventData);
    Logger(
      `PlugAndCharge webhook for EVSE ${eventData.evseid}, stored as ${storedEvent.id}`,
      "PlugAndCharge",
      "Webhook",
      LogType.INFO,
    );

    // EMAID Authorization Request to Longship API
    try {
      // First get chargePointId from evseId
      let orignalEveseId;
      if (eventData?.uuid) {
        let matchedOutgoing;
        try {
          matchedOutgoing = await prisma.plugAndChargeEvent.findFirstOrThrow({
            where: { uuid: eventData.uuid, type: "OUTGOING" },
          });
        } catch (error) {
          Logger(
            `No matching outgoing event found for uuid ${eventData.uuid}`,
            "PlugAndCharge",
            "Webhook",
            LogType.ERROR,
          );
          return NextResponse.json(
            {
              success: false,
              error: "No matching outgoing event found",
            },
            { status: 400 },
          );
        }

        orignalEveseId = matchedOutgoing?.evseid;
        if (!orignalEveseId) {
          return NextResponse.json(
            {
              success: false,
              error: "No original evseid found",
            },
            { status: 400 },
          );
        }
      }
      if (!orignalEveseId) {
        orignalEveseId = "DE*EUL*EPNCR*01";
      }

      const { chargePointId, ouId } = await getChargePointIdFromEvseId(orignalEveseId);
      if (!chargePointId) {
        Logger(
          `Cannot request authorization for EMAID ${eventData.emaid}: chargePointId not found for EVSEID ${orignalEveseId}`,
          "PlugAndCharge",
          "Authorization",
          LogType.ERROR,
        );

        // Continue with webhook processing even if authorization fails
      } else {
        updatePlugAndChargeEvent(storedEvent.id, { ouId: ouId });

        const authorizationResponse = await requestLongshipAuthorization(
          eventData.emaid,
          chargePointId,
        );

        if (
          authorizationResponse.success &&
          authorizationResponse.authorizationId &&
          authorizationResponse.authorizationData
        ) {
          // Create authorization record in database
          const authRecord = await createAuthorizationRecord(
            authorizationResponse.authorizationId,
            eventData.emaid,
            chargePointId,
            orignalEveseId,
            authorizationResponse.authorizationData,
            storedEvent.id,
          );

          Logger(
            `Longship authorization SUCCESS for EMAID ${eventData.emaid} - Authorization ID: ${authorizationResponse.authorizationId} - DB Record: ${authRecord.id}`,
            "PlugAndCharge",
            "Authorization",
            LogType.INFO,
          );

          // Start polling authorization status in background (no await to not block webhook response)
          pollAuthorizationStatus(
            authRecord.id,
            orignalEveseId,
            authorizationResponse.authorizationId,
            eventData.emaid,
          ).catch((error) => {
            Logger(
              `Authorization polling failed for EMAID ${eventData.emaid}, Auth ID ${authorizationResponse.authorizationId}: ${error instanceof Error ? error.message : "Unknown error"}`,
              "PlugAndCharge",
              "Authorization",
              LogType.ERROR,
            );
          });
        } else {
          Logger(
            `Longship authorization FAILED for EMAID ${eventData.emaid} - ${authorizationResponse.message}`,
            "PlugAndCharge",
            "Authorization",
            LogType.WARN,
          );
        }
      }
    } catch (error) {
      Logger(
        `Failed to request Longship authorization for EMAID ${eventData.emaid}: ${error instanceof Error ? error.message : "Unknown error"}`,
        "PlugAndCharge",
        "Authorization",
        LogType.ERROR,
      );
    }

    return NextResponse.json(
      {
        success: true,
        message: "PlugAndCharge event processed successfully",
        eventId: storedEvent.id,
      },
      { status: 200 },
    );
  } catch (error) {
    Logger(
      `Failed to process PlugAndCharge webhook: ${error instanceof Error ? error.message : "Unknown error"}`,
      "PlugAndCharge",
      "Webhook",
      LogType.ERROR,
    );

    Logger(
      "API 500 Error",
      "API 500",
      "src/app/api/webhook/plug-and-charge/route.ts",
      LogType.ERROR,
    );
    return NextResponse.json(
      {
        success: false,
        error: "Failed to process webhook",
        message: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 },
    );
  }
}

/**
 * GET endpoint to check webhook status
 */
export async function GET() {
  return NextResponse.json({
    success: true,
    message: "PlugAndCharge webhook endpoint is active",
    timestamp: new Date().toISOString(),
  });
}

// Interface für Longship Authorization Response
interface LongshipAuthorizationResponse {
  id: string;
  transactionId?: string;
  chargePointId: string;
  idTag: string;
  idTagType: string;
  status: "Created" | "Pending" | "Approved" | "Rejected" | "Timeout" | "NoApprovals";
  trigger:
    | "StartTransaction"
    | "TransactionEventStarted"
    | "TransactionEventUpdated"
    | "TransactionEventEnded"
    | "PlugAndCharge"
    | "Authorize"
    | "Manual";
  scenario?: "Create" | "Authorization" | "Billing";
  created: string;
  modified?: string;
  deleted?: string;
  validTo?: string;
}

/**
 * Request authorization from Longship API for EMAID
 */
async function requestLongshipAuthorization(
  emaid: string,
  chargePointId: string,
): Promise<{
  success: boolean;
  message: string;
  authorizationId?: string;
  authorizationData?: LongshipAuthorizationResponse;
}> {
  try {
    const headers = LongshipHeaders({});

    const authorizationRequest = {
      idTag: emaid,
      idTagType: "EMAID",
      chargePointId: chargePointId,
    };

    const response = await fetch(`${env.LONGSHIP_AUTHORIZATIONS_API}authorizations`, {
      method: "POST",
      headers: headers,
      body: JSON.stringify(authorizationRequest),
    });

    if (response.ok) {
      const responseData: LongshipAuthorizationResponse = await response.json();
      return {
        success: true,
        message: `Authorization created with ID ${responseData.id}, status: ${responseData.status}`,
        authorizationId: responseData.id,
        authorizationData: responseData,
      };
    } else {
      const errorText = await response.text();
      return {
        success: false,
        message: `Authorization failed with status ${response.status}: ${errorText}`,
      };
    }
  } catch (error) {
    return {
      success: false,
      message: `Authorization request failed: ${error instanceof Error ? error.message : "Unknown error"}`,
    };
  }
}

/**
 * Get chargePointId from EVSEID
 */
async function getChargePointIdFromEvseId(
  evseId: string,
): Promise<{ chargePointId: string | null; ouId: string | null }> {
  try {
    const evse = await prisma.evse.findUnique({
      where: { evse_id: evseId },
      select: { chargePointId: true, ouId: true },
    });

    return { chargePointId: evse?.chargePointId || null, ouId: evse?.ouId || null };
  } catch (error) {
    Logger(
      `Failed to get chargePointId for EVSEID ${evseId}: ${error instanceof Error ? error.message : "Unknown error"}`,
      "PlugAndCharge",
      "Database",
      LogType.ERROR,
    );
    return { chargePointId: null, ouId: null };
  }
}

/**
 * Create authorization record in database
 */
async function createAuthorizationRecord(
  longshipAuthId: string,
  emaid: string,
  chargePointId: string,
  evseId: string,
  authorizationData: LongshipAuthorizationResponse,
  plugAndChargeEventId: string,
) {
  try {
    // Parse Longship timestamps
    const longshipCreated = authorizationData.created ? new Date(authorizationData.created) : null;
    const longshipModified = authorizationData.modified
      ? new Date(authorizationData.modified)
      : null;
    const longshipDeleted = authorizationData.deleted ? new Date(authorizationData.deleted) : null;
    const longshipValidTo = authorizationData.validTo ? new Date(authorizationData.validTo) : null;

    // Create initial status history entry
    const initialHistoryEntry = {
      status: authorizationData.status,
      timestamp: new Date().toISOString(),
      response: JSON.parse(JSON.stringify(authorizationData)), // Ensure JSON compatibility
      source: "initial_request",
    };

    const authRecord = await prisma.longshipAuthorization.create({
      data: {
        longshipAuthId,
        emaid,
        chargePointId,
        evseId,
        status: authorizationData.status as any, // Cast to enum
        idTag: authorizationData.idTag,
        idTagType: authorizationData.idTagType as any, // Cast to enum
        trigger: authorizationData.trigger as any, // Cast to enum
        scenario: authorizationData.scenario as any, // Cast to enum (optional)
        longshipCreated,
        longshipModified,
        longshipDeleted,
        longshipValidTo,
        transactionId: authorizationData.transactionId,
        statusHistory: [initialHistoryEntry],
        pollingStarted: null, // Will be set when polling starts
        pollingAttempts: 0,
        plugAndChargeEventId,
      },
    });

    Logger(
      `Created authorization record ${authRecord.id} for Longship Auth ID ${longshipAuthId}`,
      "PlugAndCharge",
      "Database",
      LogType.INFO,
    );

    return authRecord;
  } catch (error) {
    Logger(
      `Failed to create authorization record for ${longshipAuthId}: ${error instanceof Error ? error.message : "Unknown error"}`,
      "PlugAndCharge",
      "Database",
      LogType.ERROR,
    );
    throw error;
  }
}

/**
 * Poll authorization status until it's final or timeout
 */
async function pollAuthorizationStatus(
  authRecordId: string,
  evseId: string,
  authorizationId: string,
  emaid: string,
): Promise<void> {
  const { chargePointId, ouId } = await getChargePointIdFromEvseId(evseId);

  if (!chargePointId) {
    Logger(
      `Cannot poll authorization ${authorizationId} for EMAID ${emaid}: chargePointId not found for EVSEID ${evseId}`,
      "PlugAndCharge",
      "Authorization",
      LogType.ERROR,
    );
    return;
  }

  // Update polling started timestamp
  await updateAuthorizationPollingStarted(authRecordId);

  Logger(
    `Starting authorization polling for EMAID ${emaid}, Auth ID ${authorizationId}, ChargePoint ${chargePointId}, DB Record ${authRecordId}`,
    "PlugAndCharge",
    "Authorization",
    LogType.INFO,
  );

  const maxAttempts = 30; // 30 attempts = 60 seconds with 2-second intervals
  const pollInterval = 500; // 0.5s try to be fast
  let attempts = 0;
  let lastStatus = "";

  while (attempts < maxAttempts) {
    try {
      const status = await checkAuthorizationStatus(chargePointId, authorizationId);

      if (status) {
        // Update database with current status and increment polling attempts
        await updateAuthorizationStatus(authRecordId, status, attempts + 1);

        // Log status changes
        if (status.status !== lastStatus) {
          Logger(
            `Authorization ${authorizationId} status changed: ${lastStatus} → ${status.status} (EMAID: ${emaid})`,
            "PlugAndCharge",
            "Authorization",
            LogType.INFO,
          );
          lastStatus = status.status;
        }

        // Check if status is final
        if (["Approved", "Rejected", "Timeout", "NoApprovals"].includes(status.status)) {
          // Mark polling as completed
          await updateAuthorizationPollingCompleted(authRecordId);

          Logger(
            `Authorization ${authorizationId} FINAL STATUS: ${status.status} for EMAID ${emaid}`,
            "PlugAndCharge",
            "Authorization",
            status.status === "Approved" ? LogType.INFO : LogType.WARN,
          );

          // If approved, start charging automatically
          if (status.status === "Approved") {
            try {
              await startRemoteChargingAfterAuthorization(evseId, emaid, authorizationId);
            } catch (error) {
              Logger(
                `Failed to start remote charging for EMAID ${emaid} after authorization ${authorizationId}: ${error instanceof Error ? error.message : "Unknown error"}`,
                "PlugAndCharge",
                "RemoteStart",
                LogType.ERROR,
              );
            }
          }

          return;
        }
      }

      attempts++;
      if (attempts < maxAttempts) {
        await new Promise((resolve) => setTimeout(resolve, pollInterval));
      }
    } catch (error) {
      Logger(
        `Error polling authorization ${authorizationId} (attempt ${attempts + 1}): ${error instanceof Error ? error.message : "Unknown error"}`,
        "PlugAndCharge",
        "Authorization",
        LogType.ERROR,
      );
      attempts++;
      if (attempts < maxAttempts) {
        await new Promise((resolve) => setTimeout(resolve, pollInterval));
      }
    }
  }

  Logger(
    `Authorization polling TIMEOUT for ${authorizationId} (EMAID: ${emaid}) after ${maxAttempts} attempts`,
    "PlugAndCharge",
    "Authorization",
    LogType.WARN,
  );
}

/**
 * Check authorization status via Longship API
 */
async function checkAuthorizationStatus(
  chargePointId: string,
  authorizationId: string,
): Promise<LongshipAuthorizationResponse | null> {
  try {
    const headers = LongshipHeaders({});

    const response = await fetch(
      `${env.LONGSHIP_AUTHORIZATIONS_API}chargepoints/${chargePointId}/authorizations/${authorizationId}`,
      {
        method: "GET",
        headers: headers,
      },
    );

    if (response.ok) {
      const responseData: LongshipAuthorizationResponse = await response.json();
      return responseData;
    } else {
      Logger(
        `Failed to check authorization status ${authorizationId}: HTTP ${response.status}`,
        "PlugAndCharge",
        "Authorization",
        LogType.ERROR,
      );
      return null;
    }
  } catch (error) {
    Logger(
      `Error checking authorization status ${authorizationId}: ${error instanceof Error ? error.message : "Unknown error"}`,
      "PlugAndCharge",
      "Authorization",
      LogType.ERROR,
    );
    return null;
  }
}

/**
 * Update authorization polling started timestamp
 */
async function updateAuthorizationPollingStarted(authRecordId: string): Promise<void> {
  try {
    await prisma.longshipAuthorization.update({
      where: { id: authRecordId },
      data: { pollingStarted: new Date() },
    });
  } catch (error) {
    Logger(
      `Failed to update polling started for auth record ${authRecordId}: ${error instanceof Error ? error.message : "Unknown error"}`,
      "PlugAndCharge",
      "Database",
      LogType.ERROR,
    );
  }
}

/**
 * Update authorization polling completed timestamp
 */
async function updateAuthorizationPollingCompleted(authRecordId: string): Promise<void> {
  try {
    await prisma.longshipAuthorization.update({
      where: { id: authRecordId },
      data: { pollingCompleted: new Date() },
    });
  } catch (error) {
    Logger(
      `Failed to update polling completed for auth record ${authRecordId}: ${error instanceof Error ? error.message : "Unknown error"}`,
      "PlugAndCharge",
      "Database",
      LogType.ERROR,
    );
  }
}

/**
 * Update authorization status and add to history
 */
async function updateAuthorizationStatus(
  authRecordId: string,
  statusData: LongshipAuthorizationResponse,
  pollingAttempts: number,
): Promise<void> {
  try {
    // Get current record to append to history
    const currentRecord = await prisma.longshipAuthorization.findUnique({
      where: { id: authRecordId },
      select: { statusHistory: true },
    });

    if (!currentRecord) {
      Logger(
        `Authorization record ${authRecordId} not found for status update`,
        "PlugAndCharge",
        "Database",
        LogType.ERROR,
      );
      return;
    }

    // Parse existing history and add new entry
    const existingHistory = Array.isArray(currentRecord.statusHistory)
      ? currentRecord.statusHistory
      : [];

    const newHistoryEntry = {
      status: statusData.status,
      timestamp: new Date().toISOString(),
      response: JSON.parse(JSON.stringify(statusData)), // Ensure JSON compatibility
      source: "polling",
      attempt: pollingAttempts,
    };

    const updatedHistory = [...existingHistory, newHistoryEntry];

    // Parse Longship timestamps
    const longshipModified = statusData.modified ? new Date(statusData.modified) : null;
    const longshipDeleted = statusData.deleted ? new Date(statusData.deleted) : null;
    const longshipValidTo = statusData.validTo ? new Date(statusData.validTo) : null;

    await prisma.longshipAuthorization.update({
      where: { id: authRecordId },
      data: {
        status: statusData.status as any, // Cast to enum
        longshipModified,
        longshipDeleted,
        longshipValidTo,
        transactionId: statusData.transactionId,
        statusHistory: updatedHistory,
        pollingAttempts,
      },
    });
  } catch (error) {
    Logger(
      `Failed to update authorization status for record ${authRecordId}: ${error instanceof Error ? error.message : "Unknown error"}`,
      "PlugAndCharge",
      "Database",
      LogType.ERROR,
    );
  }
}

/**
 * Start remote charging after successful authorization
 */
async function startRemoteChargingAfterAuthorization(
  evseId: string,
  emaid: string,
  authorizationId: string,
): Promise<void> {
  try {
    // Get EVSE data including chargePointId and connector
    const evse = await prisma.evse.findUnique({
      where: { evse_id: evseId },
      select: {
        chargePointId: true,
        connector: true,
        evse_id: true,
      },
    });

    if (!evse || !evse.chargePointId || !evse.connector) {
      Logger(
        `Cannot start remote charging: EVSE data incomplete for ${evseId} (chargePointId: ${evse?.chargePointId}, connector: ${evse?.connector})`,
        "PlugAndCharge",
        "RemoteStart",
        LogType.ERROR,
      );
      return;
    }

    Logger(
      `Starting remote charging for EMAID ${emaid} on EVSE ${evseId} (ChargePoint: ${evse.chargePointId}, Connector: ${evse.connector})`,
      "PlugAndCharge",
      "RemoteStart",
      LogType.INFO,
    );

    // Prepare Longship API request
    const headers = LongshipHeaders({});
    const remoteStartRequest = {
      connectorId: evse.connector,
      idTag: emaid, // Use EMAID as idTag for the remote start
    };

    const response = await fetch(
      `${env.LONGSHIP_API}chargepoints/${evse.chargePointId}/remotestart`,
      {
        method: "POST",
        headers: headers,
        body: JSON.stringify(remoteStartRequest),
      },
    );

    if (response.ok) {
      Logger(
        `Remote start SUCCESS for EMAID ${emaid} on EVSE ${evseId} after authorization ${authorizationId}`,
        "PlugAndCharge",
        "RemoteStart",
        LogType.INFO,
      );
    } else {
      const errorText = await response.text();
      Logger(
        `Remote start FAILED for EMAID ${emaid} on EVSE ${evseId}: HTTP ${response.status} - ${errorText}`,
        "PlugAndCharge",
        "RemoteStart",
        LogType.ERROR,
      );
    }
  } catch (error) {
    Logger(
      `Exception during remote start for EMAID ${emaid} on EVSE ${evseId}: ${error instanceof Error ? error.message : "Unknown error"}`,
      "PlugAndCharge",
      "RemoteStart",
      LogType.ERROR,
    );
    throw error;
  }
}
