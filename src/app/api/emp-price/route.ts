// /api/emp-price/route.ts
import { NextRequest, NextResponse } from "next/server";
import prismaMongo from "~/server/db/mongo";
import { empPriceSchema } from "src/app/api/emp-price/emp-priceSchema";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import prisma from "~/server/db/prisma";
import { Ou } from "@prisma/client";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";
import { cleanPrismaCreateData } from "~/server/utils/prismaCreateDataCleaner";

export async function POST(req: NextRequest) {
  try {
    const json = await req.json();
    console.log('POST emp-price received:', json); // Debug-Log
    //const body = empPriceSchema.parse(json); // ✅ Validierung

    const session = await getServerSession(authOptions);
    const ou = session?.user?.selectedOu;
    let  empId  = session?.user?.selectedOu?.adhocEmpId;

      if (!ou) {
          return NextResponse.json(
              { error: "OU (Organisationseinheit) fehlt in der Session." },
              { status: 400 }
          );
      }

      if (empId == null || empId == undefined ) {
      try {
        empId = await createEmp(ou);
      }
    catch {
    console.log("wtf")
    }}

    const body2 = empPriceSchema.parse({ ...json, empId });

    const { start, end } = body2;
    const startDate = new Date(start);
    const endDate =  new Date(end);
    const ALLOWED_LAG_MINUTES = 10;
    const now = new Date();
    const minStart = new Date(now.getTime() - ALLOWED_LAG_MINUTES * 60 * 1000);
    function toYMD(date: Date) {
      return date.toISOString().slice(0, 10); // Gibt "YYYY-MM-DD" zurück
    }

    if (toYMD(startDate) < toYMD(now)) {
      return NextResponse.json(
        { error: "Start-Datum darf nicht in der Vergangenheit liegen." },
        { status: 400 }
      );
    }



    if( startDate.getTime() > endDate.getTime()){
      return NextResponse.json(
        { error: "Start-Datum darf nicht vor dem enddatum liegen." },
        { status: 400 }
      );}

    const newEmpPrice = await prismaMongo.empPrice.create({ data: cleanPrismaCreateData({...body2})});

//in diese klasse muss emp eersttelt werden und geprüft werden ob eine empId mitgeliefert ist.
    return NextResponse.json(newEmpPrice, { status: 201 });
  } catch (err) {
    {
      const msg = "❌ Fehler beim Erstellen von EmpPrice:";
      const errorMsg = (err as Error)?.message || String(err);
      Logger(`${msg}: ${errorMsg}`, "API 500", "src/app/api/emp-price/route.ts", LogType.ERROR);
    }

    if (err instanceof Error && "issues" in err) {
      return NextResponse.json({ error: "Ungültige Eingabe", details: err }, { status: 400 });
    }

    Logger("API 500 Error", "API 500", "src/app/api/emp-price/route.ts", LogType.ERROR);
    return NextResponse.json({ error: "Konnte EmpPrice nicht erstellen" }, { status: 500 });
  }
}




const createEmp = async ( ou: Ou): Promise<string> => {

  const contryCode = ou.operatorId?.slice(0, 2);
  const partyId = ou.operatorId?.slice(2);


  const newEmp = await prismaMongo.emp.create({
    data: {
      name: ou.name || 'Unbenannt',
      party_id: partyId || 'XXX',
      country_code:  contryCode ||  ou.country || 'DE',
      amount_to_block: 5000,
      min_kwh_in_kwh: 0.2,
      min_time_in_min: 2

    },
  });

  await prisma.ou.update({
    where: { id: ou.id },
    data: { adhocEmpId: newEmp.id },
  });
  return newEmp.id; // <<-- Klar zurückgeben!
};
