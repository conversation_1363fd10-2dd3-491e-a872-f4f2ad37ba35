// ~/server/validation/empPrice.ts
import { z } from "zod";

export const empPriceSchema = z.object({
  start: z.coerce.date(),
  end: z.coerce.date(),
  energy_price: z.coerce.number().nonnegative(),
  blocking_fee: z.coerce.number().nonnegative(),
  blocking_fee_start: z.coerce.number().int().nonnegative(),
  blocking_fee_max: z.coerce.number().nonnegative(),
  session_fee: z.coerce.number().nonnegative(),
  tax_rate: z.coerce.number().min(0).max(100),
  current_type: z.enum(["AC", "DC"]),
  empId: z.string().min(1),
});

// Für PATCH: alle Felder optional machen
export const empPricePartialSchema = empPriceSchema.partial();
