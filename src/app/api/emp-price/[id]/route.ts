// /api/emp-price/[id]/route.ts
import { NextRequest, NextResponse } from "next/server";
import prismaMongo from "~/server/db/mongo";
import { empPriceSchema, empPricePartialSchema } from "src/app/api/emp-price/emp-priceSchema";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";

export async function PATCH(req: NextRequest, { params }: { params: { id: string } }) {
  try {
    const json = await req.json();
    console.log('PATCH emp-price received:', json); // Debug-Log
    const body = empPricePartialSchema.parse(json); //
    const { start, end } = body;

    if (!start) {
      // Fehler ausgeben oder anders handeln
      throw new Error('Start-Datum fehlt!');
    }
    if (!end) {

      throw new Error('end-Datum fehlt!');
    }

                                                            // Verwende partial schema für PATCH
    const startDate = new Date(start);

    const endDate =  new Date(end);
    const ALLOWED_LAG_MINUTES = 10; // frei wählbar
    const now = new Date();
    const minStart = new Date(now.getTime() - ALLOWED_LAG_MINUTES * 60 * 1000);

    const preUpdated = await prismaMongo.empPrice.findUnique({
      where: { id: params.id },
    })

    if(preUpdated?.start.getTime() !== startDate.getTime() ||  preUpdated?.end.getTime() !== endDate.getTime() ){

      if ( startDate.getTime() < (preUpdated?.start?.getTime() ?? 0)){
        return NextResponse.json(
        { error: "Start-Datum darf nicht in der Vergangenheit liegen." },
        { status: 400 }
      );}

      if ( startDate.getTime() > endDate.getTime()){
        return NextResponse.json(
          { error: "end datum darf nicht vor dem start-Datum liegen." },
          { status: 400 }
        );}

      if ( startDate.getTime() <= minStart.getTime()){
        return NextResponse.json(
          { error: "end datum darf nicht vor dem start-Datum liegen." },
          { status: 400 }
        );}




    }

    const updated = await prismaMongo.empPrice.update({
      where: { id: params.id },
      data: body,
    });

    return NextResponse.json(updated);
  } catch (err) {
    {
      const msg = "❌ Fehler beim PATCH:";
      const errorMsg = (err as Error)?.message || String(err);
      Logger(`${msg}: ${errorMsg}`, "API 500", "src/app/api/emp-price/[id]/route.ts", LogType.ERROR);
    }

    if (err instanceof Error && "issues" in err) {
      return NextResponse.json({ error: "Ungültige Eingabe", details: err }, { status: 400 });
    }

    Logger("API 500 Error", "API 500", "src/app/api/emp-price/[id]/route.ts", LogType.ERROR);
    return NextResponse.json({ error: "Konnte EmpPrice nicht updaten" }, { status: 500 });
  }
}
