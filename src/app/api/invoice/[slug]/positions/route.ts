import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import prisma from "~/server/db/prisma";
import { Role, StateOfInvoice } from "@prisma/client";
import { z } from "zod";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";

interface Props {
  params: {
    slug: string;
  };
}

const PositionSchema = z.object({
  id: z.string().optional(),
  pos: z.coerce.number(),
  title: z.string().min(1, "Titel ist erforderlich"),
  unit: z.string().optional(),
  unitPrice: z.coerce.number(),
  amount: z.coerce.number(),
  description: z.string().optional(),
  taxRate: z.coerce.number().min(0).max(100, "Steuersatz muss zwischen 0 und 100 liegen"),
});

const UpdatePositionsSchema = z.object({
  positions: z.array(PositionSchema),
});

export async function PUT(request: NextRequest, { params }: Props) {
  const session = await getServerSession(authOptions);

  if (!session?.user?.role || session?.user?.role !== Role.ADMIN) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  const invoiceId = params.slug;

  try {
    // Validate request body
    const body = await request.json();
    const validatedData = UpdatePositionsSchema.parse(body);

    // Check if invoice exists and is in PREVIEW state
    const invoice = await prisma.invoice.findUnique({
      where: { id: invoiceId },
      include: { invoicePositions: true },
    });

    if (!invoice) {
      return NextResponse.json({ error: "Invoice not found" }, { status: 404 });
    }

    if (invoice.stateOfInvoice !== StateOfInvoice.PREVIEW) {
      return NextResponse.json(
        { error: "Invoice can only be edited in PREVIEW state" },
        { status: 400 },
      );
    }

    // Calculate totals
    let sumNet = 0;
    let sumTax = 0;
    let sumGross = 0;

    const positionsWithCalculations = validatedData.positions.map((pos) => {
      const netAmount = pos.unitPrice * pos.amount;
      const taxAmount = netAmount * (pos.taxRate / 100);
      const grossAmount = netAmount + taxAmount;

      sumNet += netAmount;
      sumTax += taxAmount;
      sumGross += grossAmount;

      return {
        pos: pos.pos,
        title: pos.title,
        unit: pos.unit || "",
        unitPrice: pos.unitPrice,
        amount: pos.amount,
        description: pos.description || "",
        taxRate: pos.taxRate,
        sumNet: netAmount,
        sumTax: taxAmount,
        sumGross: grossAmount,
      };
    });

    // Update invoice in transaction
    await prisma.$transaction(async (tx) => {
      // Delete existing positions
      await tx.invoicePosition.deleteMany({
        where: { invoiceId: invoiceId },
      });

      // Create new positions
      await tx.invoicePosition.createMany({
        data: positionsWithCalculations.map((pos) => ({
          ...pos,
          invoiceId: invoiceId,
        })),
      });

      // Update invoice totals
      await tx.invoice.update({
        where: { id: invoiceId },
        data: {
          sumNet: sumNet,
          sumTax: sumTax,
          sumGross: sumGross,
          history: invoice.history
            ? `${invoice.history}\nInvoice positions updated at ${new Date().toISOString()}`
            : `Invoice positions updated at ${new Date().toISOString()}`,
        },
      });
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    {
      const msg = "Error updating invoice positions:";
      const errorMsg = (error as Error)?.message || String(error);
      Logger(`${msg}: ${errorMsg}`, "API 500", "src/app/api/invoice/[slug]/positions/route.ts", LogType.ERROR);
    }

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Validation error", details: error.errors },
        { status: 400 },
      );
    }

    Logger("API 500 Error", "API 500", "src/app/api/invoice/[slug]/positions/route.ts", LogType.ERROR);
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}
