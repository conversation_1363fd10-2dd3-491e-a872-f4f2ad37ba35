import type { NextRequest } from "next/server";
import prisma from "../../../../server/db/prisma";
import { NextResponse } from "next/server";
import { z } from "zod";
import { StateOfInvoice } from "@prisma/client";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";

interface Props {
  params: {
    slug: string;
  };
}

export async function GET(request: NextRequest, { params }: Props) {
  const invoiceId = params.slug;

  const invoice = await prisma.invoice.findUnique({
    where: {
      id: invoiceId,
    },
    include: {
      contact: {
        include: {
          contactAddress: true,
        },
      },
      user: { include: { address: true } },
      invoiceChilds: true,
      invoiceParent: true,
      invoicePositions: {
        include: {
          tarif: true,
        },
      },
      files: true,
      creditCdrs: { include: { creditPayout: true } },
      cdrs: {
        include: {
          cost: true,
        },
      },
      bankTransactions: true,
      contract: true,
    },
  });
  return NextResponse.json(invoice);
}

const PatchSchema = z.object({
  paidOnDate: z.date(),
  paidAmount: z.number(),
});

export const PATCH = async (request: NextRequest, { params }: Props) => {
  const invoiceId = params.slug;
  const rawData = await request.json();
  const data = PatchSchema.safeParse({
    ...rawData,
    paidOnDate: new Date(rawData.paidOnDate),
  });

  if (data.success) {
    const invoice = await prisma.invoice.update({
      where: {
        id: invoiceId,
      },
      data: {
        paidOnDate: data.data.paidOnDate,
        paidAmount: data.data.paidAmount,
      },
    });
    return NextResponse.json({ ok: true });
  }

  Logger("API 500 Error", "API 500", "src/app/api/invoice/[slug]/route.ts", LogType.ERROR);
    return NextResponse.json({ ok: true }, { status: 500 });
};

export const DELETE = async (request: NextRequest, { params }: Props) => {
  const invoiceId = params.slug;
  const invoice = await prisma.invoice.findUnique({
    where: {
      id: invoiceId,
    },
  });

  if (!invoice) {
    Logger("API 500 Error", "API 500", "src/app/api/invoice/[slug]/route.ts", LogType.ERROR);
    return NextResponse.json({ message: "no invoice" }, { status: 500 });
  }

  if (invoice.stateOfInvoice === StateOfInvoice.PREVIEW) {
    void (await prisma.invoice.delete({
      where: {
        id: invoiceId,
      },
    }));
  }

  return NextResponse.json({ ok: true });
};
