import type { NextRequest } from "next/server";
import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import prisma from "~/server/db/prisma";
import { Role, KindOfInvoice, StateOfInvoice } from "@prisma/client";
import nodemailer from "nodemailer";
import { env } from "~/env";
import fs from "fs";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";

export async function POST(request: NextRequest) {
  const session = await getServerSession(authOptions);

  if (!session || session?.user?.role !== Role.ADMIN) {
    return new Response("not authorized", { status: 401 });
  }

  try {
    const { invoiceId, customMessage } = await request.json();

    if (!invoiceId) {
      return new Response("Invoice ID is required", { status: 400 });
    }

    // Fetch invoice with all necessary data
    const invoice = await prisma.invoice.findUnique({
      where: { id: invoiceId },
      include: {
        contact: {
          include: {
            contactAddress: true,
          },
        },
        user: true,
        files: true,
      },
    });

    if (!invoice) {
      return new Response("Invoice not found", { status: 404 });
    }

    // Validate that this invoice can receive a reminder
    if (
      invoice.paidOnDate ||
      invoice.stateOfInvoice !== StateOfInvoice.CREATED ||
      invoice.kindOfInvoice !== KindOfInvoice.INVOICE
    ) {
      return new Response("Invoice is not eligible for reminder", { status: 400 });
    }

    // Check if invoice is actually overdue
    if (!invoice.invoiceDate) {
      return new Response("Invoice date is missing", { status: 400 });
    }

    const invoiceDate = new Date(invoice.invoiceDate);
    const dueDate = new Date(invoiceDate);
    dueDate.setDate(dueDate.getDate() + 14); // 14 days payment term

    if (new Date() <= dueDate) {
      return new Response("Invoice is not overdue yet", { status: 400 });
    }

    // Calculate days overdue
    const today = new Date();
    const diffTime = today.getTime() - dueDate.getTime();
    const daysOverdue = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

    // Get PDF attachment
    const pdfFile = invoice.files.find((file) => file.contentType === "application/pdf");
    if (!pdfFile) {
      return new Response("No PDF file found for invoice", { status: 400 });
    }

    // Setup email transporter
    const transporter = nodemailer.createTransport({
      port: 465,
      host: env.EMAIL_SERVER_HOST,
      auth: {
        user: env.EMAIL_SERVER_USER,
        pass: env.EMAIL_SERVER_PASSWORD,
      },
      secure: true,
    });

    // Generate reminder email content based on language
    const [subject, message] = generateReminderMessage(invoice, daysOverdue, customMessage);

    // Determine recipient
    const recipientEmail = invoice.contact?.invoiceMail || invoice.user?.email;
    if (!recipientEmail) {
      return new Response("No recipient email found", { status: 400 });
    }

    // Prepare email data
    const emailData = {
      from: env.EMAIL_FROM,
      to: env.NODE_ENV === "production" ? recipientEmail : "<EMAIL>",
      replyTo: invoice.contact ? "<EMAIL>" : "<EMAIL>",
      bcc: "<EMAIL>",
      subject: subject,
      text: message,
      attachments: [
        {
          filename: pdfFile.name,
          content: fs.createReadStream(pdfFile.path),
          contentType: pdfFile.contentType || "application/octet-stream",
        },
      ],
    };

    // Send email

    transporter.sendMail(emailData, (err: any, info: any) => {
      if (err) {
        console.error("Error sending reminder email:", err);
      } else {
        console.log("Reminder email sent:", info);
      }
    });

    // Update invoice history
    const now = new Date().toLocaleString("de-DE");
    const historyEntry = `\n\n${now}\nMahnung versendet an ${recipientEmail} (${daysOverdue} Tage überfällig)`;

    await prisma.invoice.update({
      where: { id: invoiceId },
      data: {
        history: (invoice.history || "") + historyEntry,
      },
    });

    Logger(
      `Reminder sent for invoice ${invoice.invoiceNumber} to ${recipientEmail}`,
      "Invoice Reminder",
      "api/invoice/sendReminder",
      LogType.INFO,
    );

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error sending reminder:", error);
    Logger(
      `Error sending reminder: ${error}`,
      "Invoice Reminder Error",
      "api/invoice/sendReminder",
      LogType.ERROR,
    );
    return new Response("Internal server error", { status: 500 });
  }
}

function generateReminderMessage(
  invoice: any,
  daysOverdue: number,
  customMessage?: string,
): [string, string] {
  const bankName = `Olinda Zweigniederlassung Deutschland, Potsdamer Platz 1, 10785 Berlin`;
  const bankAccountOwner = `Eulektro GmbH`;
  const bankAccountIBAN = `**********************`;
  const bankAccountBIC = `QNTODEB2XXX`;
  const bankRef = `Invoice ${invoice.invoiceNumber}`;

  const language = invoice.contact?.invoiceLanguageCode || "DE";

  if (language === "EN") {
    const subject = `Payment Reminder - Invoice ${invoice.invoiceNumber}`;

    let message = `Dear ${invoice.contact?.name || "Customer"},\n\n`;
    message += `We hope this message finds you well.\n\n`;
    message += `We would like to kindly remind you that the payment for the following invoice is now overdue:\n\n`;
    message += `Invoice Number: ${invoice.invoiceNumber}\n`;
    message += `Invoice Date: ${invoice.invoiceDate?.toLocaleDateString("en-EN")}\n`;
    message += `Amount: ${invoice.sumGross.toLocaleString("en-EN", { style: "currency", currency: "EUR" })}\n`;
    message += `Days Overdue: ${daysOverdue}\n\n`;

    if (customMessage) {
      message += `${customMessage}\n\n`;
    }

    message += `Please settle the outstanding amount as soon as possible to the following account:\n\n`;
    message += `Bank: ${bankName}\n`;
    message += `Account Holder: ${bankAccountOwner}\n`;
    message += `IBAN: ${bankAccountIBAN}\n`;
    message += `BIC: ${bankAccountBIC}\n`;
    message += `Reference: ${bankRef}\n\n`;
    message += `If you have already made the payment, please disregard this reminder.\n\n`;
    message += `If you have any questions or concerns, please don't hesitate to contact us.\n\n`;
    message += `Thank you for your prompt attention to this matter.\n\n`;
    message += `Best regards,\n`;
    message += `Team Eulektro (DE*EUL)\n\n`;
    message += `Werderstraße 69\n`;
    message += `28199 Bremen\n`;
    message += `<EMAIL>`;

    return [subject, message];
  } else {
    // German version
    const subject = `Zahlungserinnerung - Rechnung ${invoice.invoiceNumber}`;

    let message = `Sehr geehrte Damen und Herren,\n\n`;
    message += `wir hoffen, es geht Ihnen gut.\n\n`;
    message += `Wir möchten Sie freundlich daran erinnern, dass die Zahlung für folgende Rechnung überfällig ist:\n\n`;
    message += `Rechnungsnummer: ${invoice.invoiceNumber}\n`;
    message += `Rechnungsdatum: ${invoice.invoiceDate?.toLocaleDateString("de-DE")}\n`;
    message += `Betrag: ${invoice.sumGross.toLocaleString("de-DE", { style: "currency", currency: "EUR" })}\n`;
    message += `Tage überfällig: ${daysOverdue}\n\n`;

    if (customMessage) {
      message += `${customMessage}\n\n`;
    }

    message += `Bitte begleichen Sie den ausstehenden Betrag schnellstmöglich auf folgendes Konto:\n\n`;
    message += `Bank: ${bankName}\n`;
    message += `Kontoinhaber: ${bankAccountOwner}\n`;
    message += `IBAN: ${bankAccountIBAN}\n`;
    message += `BIC: ${bankAccountBIC}\n`;
    message += `Verwendungszweck: ${bankRef}\n\n`;
    message += `Falls Sie die Zahlung bereits geleistet haben, betrachten Sie diese Erinnerung bitte als gegenstandslos.\n\n`;
    message += `Bei Fragen oder Anliegen stehen wir Ihnen gerne zur Verfügung.\n\n`;
    message += `Vielen Dank für Ihre prompte Bearbeitung.\n\n`;
    message += `Mit freundlichen Grüßen,\n`;
    message += `Team Eulektro (DE*EUL)\n\n`;
    message += `Werderstraße 69\n`;
    message += `28199 Bremen\n`;
    message += `<EMAIL>`;

    return [subject, message];
  }
}
