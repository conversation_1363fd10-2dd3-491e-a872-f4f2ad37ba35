import type { NextRequest } from "next/server";
import prisma from "../../../../server/db/prisma";

import { NextResponse } from "next/server";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";
import { getOusBelowOu } from "~/server/model/ou/func";

export async function POST(request: NextRequest) {
  const { ouId } = await request.json();

  const startDate = new Date();
  const endDate = new Date();
  startDate.setHours(0, 0, 0, 0);
  endDate.setHours(23, 59, 59, 999);

  const out = await prisma.ou.findUnique({
    where: {
      id: ouId,
    },
  });
  if (!out) {
    return NextResponse.json(
      {
        error: "No ou found",
      },
      { status: 400 },
    );
  }

  const ouIds = await getOusBelowOu(out);

  try {
    const today_cdrs = await prisma.cdr.findMany({
      where: {
        End_datetime: {
          gte: startDate,
          lte: endDate,
        },
        OR: ouIds.map((ou) => ({
          OU_Code: `'${ou.code}'`,
        })),
      },
      include: {
        cost: true,
        tarif: true,
      },
    });

    //const today_calculatedCdrs = await computeRoamingCostByEmpPrice(today_cdrs);

    const todayRevenue = today_cdrs.reduce((acc, cdr) => {
      return acc + (cdr.Calculated_Cost || 0);
    }, 0);
    const todayEnergyCost = today_cdrs.reduce((acc, cdr) => acc + (cdr?.cost?.cost || 0), 0);

    const yesterday = new Date(
      startDate.getFullYear(),
      startDate.getMonth(),
      startDate.getDate() - 1,
    );

    const startDatetime = new Date(
      yesterday.getFullYear(),
      yesterday.getMonth(),
      yesterday.getDate(),
      0,
      0,
      0,
      0,
    );
    const endDatetime = new Date(
      yesterday.getFullYear(),
      yesterday.getMonth(),
      yesterday.getDate(),
      23,
      59,
      59,
      999,
    );

    const yesterday_cdrs = await prisma.cdr.findMany({
      where: {
        End_datetime: {
          gte: startDatetime,
          lte: endDatetime,
        },
        OR: ouIds.map((ou) => ({
          OU_Code: `'${ou.code}'`,
        })),
      },
      include: {
        cost: true,
        tarif: true,
      },
    });

    //const yesterday_calculatedCdrs = await computeRoamingCostByEmpPrice(yesterday_cdrs);

    const yesterdayRevenue = yesterday_cdrs.reduce(
      (acc, cdr) => acc + (cdr.Calculated_Cost || 0),
      0,
    );
    const yesterdayEnergyCost = yesterday_cdrs.reduce(
      (acc, cdr) => acc + (cdr?.cost?.cost || 0),
      0,
    );

    return NextResponse.json({
      todayRevenue: todayRevenue.toFixed(2),
      yesterdayRevenue: yesterdayRevenue.toFixed(2),
      todayEnergyCost: todayEnergyCost.toFixed(2),
      todayGrossMargin: (todayRevenue - todayEnergyCost).toFixed(2),
      yesterdayEnergyCost: yesterdayEnergyCost.toFixed(2),
      yesterdayGrossMargin: (yesterdayRevenue - yesterdayEnergyCost).toFixed(2),
    });
  } catch (e) {
    if (e instanceof Error) {
      Logger(
        `error on realtime request ${e.message}`,
        "realtime request",
        "realtime",
        LogType.ERROR,
      );
    }
  }
}
