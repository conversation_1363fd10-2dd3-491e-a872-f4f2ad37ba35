import type { NextRequest } from "next/server";
import fs from "fs";

import nodemailer from "nodemailer";
import prisma from "~/server/db/prisma";
import type { Prisma } from "@prisma/client";
import { defaultMailFooter, getCdrMailMessage, getRoamingInvoiceMailMessage } from "./InvoiceMail";
import { KindOfInvoice, Role } from "@prisma/client";
import { env } from "~/env.js";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";

export type InvoiceWithFilesContactUser = Prisma.InvoiceGetPayload<{
  include: {
    files: true;
    contact: true;
    user?: true;
  };
}>;

export async function POST(request: NextRequest) {
  const session = await getServerSession(authOptions);

  if (!session || session?.user?.role !== Role.ADMIN) {
    return new Response("no auth", { status: 401 });
  }

  const { invoiceId, customMailText, onlySetSendFlag = false } = await request.json();

  const invoice = await prisma.invoice.findUnique({
    where: {
      id: invoiceId,
    },
    include: {
      files: true,
      contact: true,
      user: true,
    },
  });
  if (onlySetSendFlag && invoiceId) {
    const markTest = "Only marked as sent";
    await prisma.invoice.update({
      where: {
        id: invoice?.id,
      },
      data: {
        sendAsMail: true,
        history: invoice?.history ? invoice.history + `\n${markTest}` : markTest,
      },
    });

    return new Response("ok", { status: 200 });
  }

  if (!invoice || !invoice.files.find((x) => x.contentType == "application/pdf")) {
    Logger("API 500 Error", "API 500", "src/app/api/invoice/sendMail/route.ts", LogType.ERROR);
    return new Response("no invoice found", { status: 500 });
  }

  if (!invoice?.contact) {
    Logger("API 500 Error", "API 500", "src/app/api/invoice/sendMail/route.ts", LogType.ERROR);
    return new Response("Invoic has no contact", { status: 500 });
  }

  const transporter = nodemailer.createTransport({
    port: 465,
    host: env.EMAIL_SERVER_HOST,
    auth: {
      user: env.EMAIL_SERVER_USER,
      pass: env.EMAIL_SERVER_PASSWORD,
    },
    secure: true,
  });

  const attachedFiles = invoice.files.map((file) => {
    return {
      filename: file.name,
      content: fs.createReadStream(file.path),
      contentType: file?.contentType || "application/octet-stream",
    };
  });
  const attachedPDFs = attachedFiles.filter((x) => x.contentType == "application/pdf");
  const attachedCSVs = attachedFiles.filter((x) => x.contentType == "text/csv");

  let subject = "";
  let message = "";
  //invoice oder credit je nach kindOfInvoice

  if (customMailText && customMailText != "") {
    subject = `${invoice.invoiceNumber}`;
    message = customMailText + defaultMailFooter;
  } else {
    [subject, message] = getRoamingInvoiceMailMessage({
      invoice: invoice,
    });
  }

  if (subject == "" || message == "") {
    Logger("API 500 Error", "API 500", "src/app/api/invoice/sendMail/route.ts", LogType.ERROR);
    return new Response("Warning - no subject or message - mail not sent", { status: 500 });
  }

  const mailAddress = invoice.contact.invoiceMail;

  // if cdrMail is filled, then only send PDF to the default mail address
  const sendBothFiles = invoice?.contact.cdrMail ? false : true;

  const invoiceMailData = {
    from: env.EMAIL_FROM,
    to: env.NODE_ENV == "production" ? mailAddress : "<EMAIL>",
    replyTo:
      invoice.kindOfInvoice == KindOfInvoice.CREDIT ? `<EMAIL>` : `<EMAIL>`,
    bcc: "<EMAIL>",
    subject: subject,
    text: customMailText && customMailText != "" ? customMailText : message,
    attachments: sendBothFiles ? attachedFiles : attachedPDFs,
  };
  let sendStatus = "prepare";
  let history = "";
  transporter.sendMail(invoiceMailData, (err: any, info: any) => {
    if (err) {
      console.log(err);
      history += `\n\n${now}\nFail to send Invoice to ${mailAddress}`;
      sendStatus = "fail";
    } else {
      console.log(info);
    }
    return;
  });

  const now = new Date().toLocaleString();
  // send cdr mail if exists, invoice or credit is not a storno, and there are CSV attachments
  if (
    (invoice.kindOfInvoice == KindOfInvoice.INVOICE ||
      invoice.kindOfInvoice == KindOfInvoice.CREDIT) &&
    invoice?.contact?.cdrMail &&
    attachedCSVs.length > 0
  ) {
    const [subject, mailMessage] = getCdrMailMessage({
      invoice: invoice,
    });

    const invoiceMailData = {
      from: env.EMAIL_FROM,
      to: env.NODE_ENV == "production" ? invoice.contact.cdrMail : "<EMAIL>",
      replyTo: `<EMAIL>`,
      bcc: "<EMAIL>",
      subject: subject,
      text: mailMessage,
      attachments: attachedCSVs,
    };

    transporter.sendMail(invoiceMailData, (err: any, info: any) => {
      if (err) {
        history += `\n\n${now}\nFail to send CDR to ${invoice?.contact?.cdrMail}`;
        sendStatus = "fail";
        console.log(err);
      } else {
        console.log(info);
      }
      return;
    });
    if (sendStatus != "fail") {
      history += `\n\n${now}\nCDRs are send to ${invoice.contact.cdrMail}`;
      history += `\n\n${now}\nInvoice are send to ${invoice.contact.invoiceMail}`;
    }
  } else {
    if (sendStatus != "fail") {
      history += `\n\n${now}\nInvoice and CDRs are send to ${invoice?.contact?.invoiceMail}`;
    }
  }

  await prisma.invoice.update({
    where: {
      id: invoice.id,
    },
    data: {
      sendAsMail: true,
      history: invoice.history ? invoice.history + history : history,
    },
  });

  return new Response("ok", { status: 200 });
}
