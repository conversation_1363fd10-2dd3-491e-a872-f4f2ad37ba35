import type { NextRequest } from "next/server";
import prisma from "~/server/db/prisma";
import { NextResponse } from "next/server";
import { Role, StateOfInvoice } from "@prisma/client";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";

export async function GET(request: NextRequest) {
  const session = await getServerSession(authOptions);
  const userRole = session?.user?.role;

  if (!userRole || userRole !== Role.ADMIN) {
    return new Response("not authorized", { status: 401 });
  }

  const { searchParams } = new URL(request.url);
  const contactId = searchParams.get('contactId');
  const userId = searchParams.get('userId');

  try {
    const whereClause: any = {
      stateOfInvoice: {
        not: StateOfInvoice.PREVIEW
      }
    };

    // Filter by contactId or userId if provided
    if (contactId) {
      whereClause.contactId = contactId;
    } else if (userId) {
      whereClause.userId = userId;
    }

    const recentInvoices = await prisma.invoice.findMany({
      where: whereClause,
      select: {
        id: true,
        invoiceNumber: true,
        sumGross: true,
        invoiceDate: true,
        kindOfInvoice: true,
        stateOfInvoice: true,
        contact: {
          select: {
            name: true,
          },
        },
        user: {
          select: {
            name: true,
            lastName: true,
          },
        },
      },
      orderBy: {
        invoiceDate: "desc",
      },
      take: 20, // Fetch 20 to allow filtering out current invoice and still have enough
    });

    return NextResponse.json(recentInvoices);
  } catch (error) {
    console.error("Error fetching recent invoices:", error);
    return new Response("Internal server error", { status: 500 });
  }
}
