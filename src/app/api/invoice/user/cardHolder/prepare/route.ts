import type { NextRequest } from "next/server";

import { NextResponse } from "next/server";
import { prepareUserInvoice } from "~/server/invoice/invoiceUtils";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role } from "@prisma/client";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";

export async function POST(request: NextRequest) {
  const session = await getServerSession(authOptions);
  if (!session || session?.user?.role != Role.ADMIN) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }
  const { userId, startDate, endDate } = await request.json();
  //const userId = "cls9j5cuc0001wtiytckxdmnd";
  if (!userId) {
    return new Response("no user found", { status: 404 });
  }

  try {
    const userInvoice = await prepareUserInvoice(userId, startDate, endDate);
    if (userInvoice && userInvoice?.id) {
      return NextResponse.json({ invoiceId: userInvoice?.id });
    }
  } catch (err) {
    const message = (err as Error).message;
    return NextResponse.json(message, { status: 500 });
  }
}
