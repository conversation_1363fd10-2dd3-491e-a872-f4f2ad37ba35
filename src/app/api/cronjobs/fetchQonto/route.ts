import { <PERSON><PERSON><PERSON><PERSON> } from "quirrel/next-app";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";
import {
  fetchMainTransactionsFromQonto,
  fetchStripeTransactionsFromQonto,
} from "~/utils/qonto/transactions";
import { autoUploadInvoices } from "~/utils/qonto/uploader";

export const dynamic = "force-dynamic";

const job = CronJob(
  "api/cronjobs/fetchQonto",
  "*/30 * * * *", // (see https://crontab.guru/)
  async () => {
    Logger("fetch qonto transactions", "Qonto transactions", "cron", LogType.INFO);
    await fetchMainTransactionsFromQonto();
    await fetchStripeTransactionsFromQonto();
    await autoUploadInvoices();
    await autoUploadInvoices(true);
  },
);

export const POST = job;
