import React from 'react';
import { Inter } from 'next/font/google';
import '~/styles/globals.css';

const inter = Inter({ subsets: ['latin'] });

export const metadata = {
  title: 'Solar Dashboard - Öffentlicher Zugang',
  description: 'Öffentliches Solar Energy Dashboard',
};

export default function PublicSolarLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="de">
      <body className={inter.className}>
        {children}
      </body>
    </html>
  );
}
