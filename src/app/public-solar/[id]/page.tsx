"use client";

import React, { useEffect, useState } from 'react';
import { useParams, useSearchParams } from 'next/navigation';
import SolarDashboard from '~/app/(app)/solar-dashboard/components/SolarDashboard';
import { SolarDashboardData } from '~/app/(app)/solar-dashboard/types';

export default function PublicSolarDashboardPage() {
  const params = useParams();
  const searchParams = useSearchParams();
  const dashboardId = params.id as string;
  const token = searchParams.get('token');

  const [dashboardData, setDashboardData] = useState<SolarDashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Build URL - token is optional for public dashboards
        const url = token
          ? `/api/solar-dashboard/${dashboardId}/data?token=${token}`
          : `/api/solar-dashboard/${dashboardId}/data`;

        const response = await fetch(url);

        if (!response.ok) {
          if (response.status === 404) {
            setError('Dashboard nicht gefunden');
          } else if (response.status === 403) {
            setError('Dashboard ist nicht öffentlich zugänglich oder nicht aktiv');
          } else {
            setError('Fehler beim Laden der Dashboard-Daten');
          }
          return;
        }

        const data = await response.json();
        setDashboardData(data);
      } catch (err) {
        console.error('Error fetching public dashboard data:', err);
        setError('Netzwerkfehler beim Laden der Daten');
      } finally {
        setLoading(false);
      }
    };

    if (dashboardId) {
      fetchDashboardData();

      // Set up auto-refresh every 5 minutes (300 seconds)
      const interval = setInterval(fetchDashboardData, 300000);

      return () => clearInterval(interval);
    }
  }, [dashboardId, token]);

  if (loading) {
    return (
      <div className="h-screen bg-gradient-to-br from-gray-50 to-gray-200 flex items-center justify-center overflow-hidden">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-green-600 mx-auto mb-4"></div>
          <p className="text-gray-600 text-lg">Dashboard wird geladen...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="h-screen bg-gradient-to-br from-gray-50 to-gray-200 flex items-center justify-center overflow-hidden">
        <div className="text-center bg-white rounded-2xl p-8 shadow-lg max-w-md">
          <div className="text-red-500 text-6xl mb-4">🔒</div>
          <h2 className="text-2xl font-bold text-gray-800 mb-4">Zugriff verweigert</h2>
          <p className="text-gray-600 mb-6">{error}</p>
          <p className="text-sm text-gray-500">
            Bitte überprüfen Sie den Link oder wenden Sie sich an den Administrator.
          </p>
        </div>
      </div>
    );
  }

  if (!dashboardData) {
    return (
      <div className="h-screen bg-gradient-to-br from-gray-50 to-gray-200 flex items-center justify-center overflow-hidden">
        <div className="text-center bg-white rounded-2xl p-8 shadow-lg max-w-md">
          <div className="text-gray-400 text-6xl mb-4">📊</div>
          <h2 className="text-2xl font-bold text-gray-800 mb-4">Keine Daten</h2>
          <p className="text-gray-600">Dashboard-Daten konnten nicht geladen werden.</p>
        </div>
      </div>
    );
  }

  return <SolarDashboard data={dashboardData} isPublic={true} />;
}
