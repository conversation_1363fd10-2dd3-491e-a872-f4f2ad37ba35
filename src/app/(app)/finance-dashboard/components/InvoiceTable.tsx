"use client";

import React from 'react';
import { formatEuro } from '~/utils/format/numbers';
import type { FinanceDashboardInvoice } from '~/app/api/finance-dashboard/route';
import { FaStripeS } from 'react-icons/fa';

interface InvoiceTableProps {
  invoices: FinanceDashboardInvoice[];
}

const getUrgencyColor = (urgencyLevel: string) => {
  switch (urgencyLevel) {
    case 'warning':
      return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
    case 'urgent':
      return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
    case 'critical':
      return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
    default:
      return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
  }
};

const getUrgencyText = (urgencyLevel: string, daysSinceSent: number | null) => {
  if (daysSinceSent === null) return 'Nicht versendet';
  
  switch (urgencyLevel) {
    case 'warning':
      return `${daysSinceSent} Tage (Überfällig)`;
    case 'urgent':
      return `${daysSinceSent} Tage (Dringend)`;
    case 'critical':
      return `${daysSinceSent} Tage (Kritisch)`;
    default:
      return `${daysSinceSent} Tage`;
  }
};

const InvoiceTable: React.FC<InvoiceTableProps> = ({ invoices }) => {
  const handleInvoiceClick = (invoiceId: string) => {
    window.open(`/invoice/${invoiceId}`, '_blank');
  };

  return (
    <div className="overflow-x-auto">
      <table className="w-full table-auto">
        <thead>
          <tr className="border-b border-gray-200 dark:border-gray-700">
            <th className="px-3 py-2 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
              Rechnung
            </th>
            <th className="px-3 py-2 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
              Kunde/Nutzer
            </th>
            <th className="px-3 py-2 text-right text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
              Betrag
            </th>
            <th className="px-3 py-2 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
              Versendet an
            </th>
            <th className="px-3 py-2 text-center text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
              Status
            </th>
          </tr>
        </thead>
        <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
          {invoices.map((invoice) => (
            <tr
              key={invoice.id}
              className="cursor-pointer transition-colors hover:bg-gray-50 dark:hover:bg-gray-800"
              onClick={() => handleInvoiceClick(invoice.id)}
            >
              <td className="px-3 py-3">
                <div>
                  <div className="flex items-center gap-2">
                    <div className="text-sm font-medium text-gray-900 dark:text-white">
                      {invoice.invoiceNumber || 'Ohne Nummer'}
                    </div>
                    {invoice.hasStripePayment && (
                      <FaStripeS
                        className="text-blue-600"
                        size={12}
                        title="Stripe Payment Intent vorhanden"
                      />
                    )}
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    {invoice.invoiceDate ? new Date(invoice.invoiceDate).toLocaleDateString('de-DE') : 'Kein Datum'}
                  </div>
                </div>
              </td>
              <td className="px-3 py-3">
                <div className="text-sm text-gray-900 dark:text-white">
                  {invoice.contactName || invoice.userName || invoice.contractName || 'Unbekannt'}
                </div>
              </td>
              <td className="px-3 py-3 text-right">
                <div className="text-sm font-medium text-gray-900 dark:text-white">
                  {formatEuro(invoice.sumGross)}
                </div>
              </td>
              <td className="px-3 py-3">
                <div>
                  <div className="text-sm text-gray-900 dark:text-white">
                    {invoice.sentTo || 'Nicht versendet'}
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400">
                    {invoice.sentDate ? new Date(invoice.sentDate).toLocaleDateString('de-DE') : ''}
                  </div>
                </div>
              </td>
              <td className="px-3 py-3 text-center">
                <span
                  className={`inline-flex rounded-full px-2 py-1 text-xs font-semibold ${getUrgencyColor(
                    invoice.urgencyLevel
                  )}`}
                >
                  {getUrgencyText(invoice.urgencyLevel, invoice.daysSinceSent)}
                </span>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default InvoiceTable;
