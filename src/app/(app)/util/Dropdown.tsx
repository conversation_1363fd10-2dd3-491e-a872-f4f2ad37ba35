"use client";
import React, { useEffect, useRef, useState } from "react";
import Button from "~/component/button";
import { FaEye } from "react-icons/fa";
import { RxCross1 } from "react-icons/rx";
import { FiSearch } from "react-icons/fi";
import { ouColors } from "~/styles/oucolors/oucolors";

// Definieren Sie einen Typ für eine einzelne Option
export interface Option {
  id: string;
  label: string;
  selected: boolean;
  color?: string;
}

// Definieren Sie einen Typ für die Props der Dropdown-Komponente
interface DropdownProps {
  title?: string;
  options: Option[] | undefined;
  onChange: (id: string) => void;
  onDelete: (id: string) => void;
  canDelete?: boolean;
  icon?: React.ReactNode;
  className?: string;
  placeHolder?: string;
  small?: boolean;
  searchable?: boolean;
  searchPlaceholder?: string;
}

const Dropdown = ({
  title,
  options,
  onChange,
  onDelete,
  canDelete = true,
  icon,
  className,
  placeHolder = "--------",
  small = false,
  searchable = false,
  searchPlaceholder = "Suchen...",
}: DropdownProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const dropdownRef = useRef<HTMLUListElement>(null);
  const ref = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);

  // Filter options based on search term
  const filteredOptions = searchable && searchTerm
    ? options?.filter(option =>
        option.label.toLowerCase().includes(searchTerm.toLowerCase())
      )
    : options;

  // will register events in order to close the opened
  // value list if a click is done somewhere else or ESC key
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (!ref?.current?.contains(event.target as Node)) {
        setIsOpen(false);
        setSearchTerm(""); // Reset search when closing
      }
    };
    const handleEscape = (event: KeyboardEvent) => {
      // on ESC close list
      if (event.key === "Escape") {
        setIsOpen(false);
        setSearchTerm(""); // Reset search when closing
      }
    };

    // Event-Listener hinzufügen, wenn das Komponenten gemountet wird
    document.addEventListener("mouseup", handleClickOutside);
    document.addEventListener("keydown", handleEscape);

    // Event-Listener entfernen, wenn das Komponenten unmountet wird
    return () => {
      document.removeEventListener("mouseup", handleClickOutside);
      document.removeEventListener("keydown", handleEscape);
    };
  }, [isOpen]);

  // Focus search input when dropdown opens
  useEffect(() => {
    if (isOpen && searchable && searchInputRef.current) {
      // Small delay to ensure the dropdown is rendered
      setTimeout(() => {
        searchInputRef.current?.focus();
      }, 100);
    }
  }, [isOpen, searchable]);

  const handleOptionClick = (id: string) => {
    setIsOpen(false); // schließt das Dropdown-Menü
    setSearchTerm(""); // Reset search when selecting
    onChange(id); // ruft die übergebene onChange-Funktion auf
  };

  const handleDelete = (id: string) => {
    setIsOpen(false);
    setSearchTerm(""); // Reset search when deleting
    onDelete(id);
  };

  return (
    <div className={`relative ${className ?? ""}`} ref={ref}>
      <Button
        small={small}
        title={title ?? "Auswahl"}
        onClick={() => {
          setIsOpen(!isOpen);
        }}
        aria-expanded={isOpen}
        type="button"
        className={"w-full items-center truncate"}
      >
        {icon ?? <FaEye size={18} className={"mr-1"} />}
        {options?.find((option) => option.selected)?.label || <span>{placeHolder}</span>}
        {isOpen ? (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="ml-2 h-5 w-5"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M5 15l7-7 7 7" // Pfeil nach oben
            />{" "}
          </svg>
        ) : (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="ml-2 h-5 w-5"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
          </svg>
        )}
      </Button>
      <div
        className={`before:font-awesome pointer-events-none absolute left-0 top-0 z-50 m-0 ${
          small ? "" : "mt-2"
        } w-full sm:w-auto min-w-full sm:min-w-56 md:min-w-64 max-w-full sm:max-w-sm md:max-w-md overflow-hidden rounded-lg border border-gray-200 bg-white bg-clip-padding text-left text-sm text-slate-500 opacity-0 shadow-lg transition-all duration-250 transform-dropdown-show before:absolute before:left-auto before:right-7 before:top-0 before:z-40 before:text-5.5 before:text-white before:transition-all before:duration-350 before:ease-soft before:content-['\f0d8'] lg:shadow-soft-3xl ${
          isOpen ? "pointer-events-auto opacity-100" : "pointer-events-none opacity-0"
        }`}
      >
        {searchable && (
          <div className="sticky top-0 bg-white border-b border-gray-200 p-2">
            <div className="relative">
              <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" size={14} />
              <input
                ref={searchInputRef}
                type="text"
                placeholder={searchPlaceholder}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-8 pr-3 py-1.5 w-full text-sm border border-gray-300 rounded focus:shadow-soft-primary-outline focus:outline-none"
                onClick={(e) => e.stopPropagation()} // Prevent dropdown from closing when clicking input
              />
            </div>
          </div>
        )}
        <ul
          ref={dropdownRef}
          className="max-h-60 sm:max-h-64 md:max-h-72 overflow-y-auto overscroll-y-auto list-none px-0 py-2 scrollbar-thin scrollbar-thumb-gray-300 scrollbar-track-gray-100"
        >
          {filteredOptions?.length === 0 && searchTerm ? (
            <li className="px-4 py-2 text-gray-500 text-center">
              Keine Ergebnisse gefunden
            </li>
          ) : (
            filteredOptions?.map((option) => (
              <li
                key={option.id}
                className={`${
                  option.selected ? "bg-blue-50" : "bg-transparent"
                } flew-row flex  items-center hover:bg-gray-200 `}
              >
                {option.color && (
                  <span
                    className={"h-[2rem] w-2"}
                    style={{
                      backgroundColor: option.color,
                    }}
                  ></span>
                )}

                <button
                  type={"button"}
                  className={`clear-both block w-full whitespace-nowrap border-0  px-4 py-1.2 text-left font-normal text-slate-500 hover:bg-gray-200 hover:text-slate-700 dark:text-white dark:hover:bg-gray-200/80 dark:hover:text-slate-700 lg:transition-colors lg:duration-300 lg:ease-soft`}
                  onClick={() => handleOptionClick(option.id)}
                >
                  {option.label}
                </button>
                {canDelete && (
                  <RxCross1
                    className={`mr-2 transition-colors duration-75 hover:scale-150 hover:cursor-pointer hover:text-red-500`}
                    onClick={() => handleDelete(option.id)}
                  />
                )}
              </li>
            ))
          )}
        </ul>
      </div>
    </div>
  );
};

export default Dropdown;
