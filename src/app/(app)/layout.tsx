import Image from "next/image";
import Link from "next/link";
import { Source_Sans_3 } from "next/font/google";
import "~/styles/globals.css";

import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { ouColors, ouLogos } from "~/styles/oucolors/oucolors";
import OUSelect from "~/component/bar/OUSelect";
import React from "react";
import { getOusBelowUserOu } from "~/server/model/ou/func";
import MobileBar from "~/component/top/MobileBar";
import NotFound from "~/app/(app)/not-found";
import Menu from "~/component/menu";
import ProvidersWrapper from "~/app/(app)/ProvidersWrapper";
import SidebarWrapper from "~/component/sidebarWrapper";
import Bar from "~/component/top/bar";
import { env } from "~/env";
import Login from "./Login";

export const metadata = {
  title: `${env.NODE_ENV != "production" ? "⚙️_DEV_⚙️ " : ""}Ladeportal`,
  icons: {
    icon: "/favicon/favicon-32x32.png",
    apple: "/favicon/favicon-32x32.png",
    shortcut: "/favicon/android-chrome-192x192.png",
  },
  description: "Ladeportal",
};

const sansnew = Source_Sans_3({ display: "swap", variable: "--font-sansnew", subsets: ["latin"] });

const RootLayout = async ({ children }: { children: React.ReactNode }) => {
  const session = await getServerSession(authOptions);
  const ouName = session?.user?.selectedOu?.name ?? "Eulektro GmbH";

  if (!session) {
    return <NotFound />;
  }
  const ou = await getOusBelowUserOu(session?.user?.email);
  const logo = ouLogos[ouName]?.logo ?? "/logo/EULEKTRO_schwarz_C84_M40_Y36_K21.svg";
  const logo_darkmode =
    ouLogos[ouName]?.logo_darkmode ?? "/logo/EULEKTRO_weiss_C84_M40_Y36_K21.svg";
  return (
    <html lang="de" className={`${sansnew.variable}`}>
      <body
        style={ouColors[ouName] ?? ouColors.Default}
        className={
          "m-0 min-h-screen bg-eul-main text-left font-sansnew text-base font-normal leading-default text-primary text-slate-700 antialiased dark:bg-slate-950 dark:text-white"
        }
      >
        <ProvidersWrapper>
          <SidebarWrapper>
            <div className="h-20">
              <Link
                href={"/"}
                className="m-0 block whitespace-nowrap px-8 py-6 text-sm dark:text-white"
              >
                <>
                  <Image
                    src={logo}
                    priority={false}
                    width={300}
                    height={150}
                    alt={`${ouName} Logo`}
                    className={
                      "inline-block h-full max-h-12 max-w-full transition-all duration-200 ease-soft-in-out dark:hidden"
                    }
                  />
                  <Image
                    src={logo_darkmode}
                    width={200}
                    priority={false}
                    height={100}
                    alt={`${ouName} Logo`}
                    className={
                      "hidden h-full max-h-12 max-w-full transition-all duration-200 ease-soft-in-out dark:inline-block"
                    }
                  />
                </>
              </Link>

              <div
                className={` ${
                  ou.length == 1 ? "hidden" : "active"
                } mx-4 my-0 flex items-center whitespace-nowrap px-4 py-2.7 md:hidden`}
              >
                <OUSelect ous={ou} defaultOuId={session?.user?.selectedOu?.id} />
              </div>

              <hr className="mt-0 h-px bg-transparent bg-gradient-to-r from-transparent via-black/40 to-transparent dark:bg-gradient-to-r dark:from-transparent dark:via-white dark:to-transparent" />
              <Menu />
              <div className="ml-4 block sm:hidden">
              <Login />
              </div>
            </div>
          </SidebarWrapper>

          <main className="relative h-full rounded-xl transition-all duration-200 ease-soft-in-out xl:ml-68">
            <Bar />
            <MobileBar />
            <div className={"mx-auto my-4 w-full p-6"}>{children}</div>
          </main>
        </ProvidersWrapper>
      </body>
    </html>
  );
};

export default RootLayout;
