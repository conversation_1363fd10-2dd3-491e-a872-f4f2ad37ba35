"use client";
import React, { useState, useEffect } from "react";
import { Ou } from "@prisma/client";

interface TarifValidOusSelectorProps {
  tarifId?: string;
  selectedOuIds: string[];
  onSelectionChange: (ouIds: string[]) => void;
  disabled?: boolean;
}

interface OuData {
  assigned: Ou[];
  available: Ou[];
}

export const TarifValidOusSelector: React.FC<TarifValidOusSelectorProps> = ({
  tarifId,
  selectedOuIds,
  onSelectionChange,
  disabled = false,
}) => {
  const [ouData, setOuData] = useState<OuData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");

  useEffect(() => {
    const fetchOus = async () => {
      if (!tarifId) {
        // For new tarifs, just fetch all OUs
        try {
          setLoading(true);
          const response = await fetch("/api/admin/ous");
          if (response.ok) {
            const allOus = await response.json();
            setOuData({
              assigned: [],
              available: allOus,
            });
          }
        } catch (err) {
          setError("Fehler beim Laden der OUs");
        } finally {
          setLoading(false);
        }
        return;
      }

      // For existing tarifs, fetch current assignments
      try {
        setLoading(true);
        const response = await fetch(`/api/tarif/${tarifId}/validOus`);
        if (response.ok) {
          const data = await response.json();
          setOuData(data);
        } else {
          setError("Fehler beim Laden der OU-Zuordnungen");
        }
      } catch (err) {
        setError("Fehler beim Laden der OU-Zuordnungen");
      } finally {
        setLoading(false);
      }
    };

    fetchOus();
  }, [tarifId]);

  const handleOuToggle = (ouId: string, checked: boolean) => {
    let newSelection: string[];

    if (checked) {
      newSelection = [...selectedOuIds, ouId];
    } else {
      newSelection = selectedOuIds.filter((id) => id !== ouId);
    }

    onSelectionChange(newSelection);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center p-4">
        <div className="text-sm text-gray-500">Lade OUs...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 bg-red-50 border border-red-200 rounded-md">
        <div className="text-sm text-red-600">{error}</div>
      </div>
    );
  }

  if (!ouData) {
    return null;
  }

  return (
    <div className="space-y-4">
      <div>
        <label className="block text-sm font-medium text-gray-700 dark:text-white mb-2">
          Gültige Organisationseinheiten
        </label>
        <div className="text-xs text-gray-500 dark:text-gray-400 mb-3">
          Wählen Sie die OUs aus, für die dieser Tarif gültig sein soll. Wenn keine OU ausgewählt ist, gilt der Tarif für alle OUs.
        </div>
      </div>

      <div className="max-h-60 overflow-y-auto border border-gray-300 dark:border-gray-600 rounded-md">
        {ouData.available.length === 0 ? (
          <div className="p-4 text-sm text-gray-500 text-center">
            Keine OUs verfügbar
          </div>
        ) : (
          <div className="divide-y divide-gray-200 dark:divide-gray-600">
            {ouData.available.map((ou) => (
              <div key={ou.id} className="p-3 hover:bg-gray-50 dark:hover:bg-gray-700">
                <label className="flex items-center cursor-pointer">
                  <input
                    type="checkbox"
                    checked={selectedOuIds.includes(ou.id)}
                    onChange={(e) => handleOuToggle(ou.id, e.target.checked)}
                    disabled={disabled}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded disabled:opacity-50"
                  />
                  <div className="ml-3 flex-1">
                    <div className="text-sm font-medium text-gray-900 dark:text-white">
                      {ou.name}
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      Code: {ou.code}
                    </div>
                  </div>
                </label>
              </div>
            ))}
          </div>
        )}
      </div>

      {selectedOuIds.length > 0 && (
        <div className="mt-3">
          <div className="text-sm font-medium text-gray-700 dark:text-white mb-2">
            Ausgewählte OUs ({selectedOuIds.length}):
          </div>
          <div className="flex flex-wrap gap-2">
            {selectedOuIds.map((ouId) => {
              const ou = ouData.available.find((o) => o.id === ouId);
              return ou ? (
                <span
                  key={ouId}
                  className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
                >
                  {ou.code}
                </span>
              ) : null;
            })}
          </div>
        </div>
      )}
    </div>
  );
};
