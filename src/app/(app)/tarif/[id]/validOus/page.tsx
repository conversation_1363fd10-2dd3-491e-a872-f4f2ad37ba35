"use client";
import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Card from "~/component/card";
import Headline from "~/component/Headline";
import Button from "~/component/button";
import { FaSave, FaArrowLeft } from "react-icons/fa";
import { Ou, Tarif } from "@prisma/client";

interface TarifWithValidOus extends Tarif {
  validOus: Ou[];
}

interface OuData {
  assigned: Ou[];
  available: Ou[];
}

interface Props {
  params: {
    id: string;
  };
}

const TarifValidOusPage: React.FC<Props> = ({ params }) => {
  const router = useRouter();
  const [tarif, setTarif] = useState<TarifWithValidOus | null>(null);
  const [ouData, setOuData] = useState<OuData | null>(null);
  const [selectedOuIds, setSelectedOuIds] = useState<string[]>([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState("");
  const [successMessage, setSuccessMessage] = useState("");

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        
        // Fetch tarif details
        const tarifResponse = await fetch(`/api/tarif/${params.id}`);
        if (!tarifResponse.ok) {
          throw new Error("Tarif nicht gefunden");
        }
        const tarifData = await tarifResponse.json();
        setTarif(tarifData);

        // Fetch OU assignments
        const ouResponse = await fetch(`/api/tarif/${params.id}/validOus`);
        if (!ouResponse.ok) {
          throw new Error("Fehler beim Laden der OU-Zuordnungen");
        }
        const ouAssignments = await ouResponse.json();
        setOuData(ouAssignments);
        setSelectedOuIds(ouAssignments.assigned.map((ou: Ou) => ou.id));
      } catch (err) {
        setError(err instanceof Error ? err.message : "Unbekannter Fehler");
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [params.id]);

  const handleOuToggle = (ouId: string, checked: boolean) => {
    let newSelection: string[];

    if (checked) {
      newSelection = [...selectedOuIds, ouId];
    } else {
      newSelection = selectedOuIds.filter((id) => id !== ouId);
    }

    setSelectedOuIds(newSelection);
  };

  const handleSave = async () => {
    try {
      setSaving(true);
      setError("");
      setSuccessMessage("");

      const response = await fetch(`/api/tarif/${params.id}/validOus`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ ouIds: selectedOuIds }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Fehler beim Speichern");
      }

      const result = await response.json();
      setSuccessMessage(result.message || "OU-Zuordnungen erfolgreich aktualisiert");

      // Refresh data
      const ouResponse = await fetch(`/api/tarif/${params.id}/validOus`);
      if (ouResponse.ok) {
        const ouAssignments = await ouResponse.json();
        setOuData(ouAssignments);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Unbekannter Fehler");
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <Card>
        <div className="flex items-center justify-center p-8">
          <div className="text-lg">Lade Daten...</div>
        </div>
      </Card>
    );
  }

  if (error && !ouData) {
    return (
      <Card>
        <div className="p-6">
          <div className="bg-red-50 border border-red-200 rounded-md p-4">
            <div className="text-red-600">{error}</div>
          </div>
          <div className="mt-4">
            <Button onClick={() => router.back()}>
              <FaArrowLeft className="mr-2" />
              Zurück
            </Button>
          </div>
        </div>
      </Card>
    );
  }

  return (
    <Card>
      <div className="flex items-center justify-between mb-6">
        <Headline title={`OU-Zuordnungen für Tarif: ${tarif?.name || "Unbekannt"}`} />
        <Button onClick={() => router.back()} variant="secondary">
          <FaArrowLeft className="mr-2" />
          Zurück
        </Button>
      </div>

      {error && (
        <div className="mb-4 bg-red-50 border border-red-200 rounded-md p-4">
          <div className="text-red-600">{error}</div>
        </div>
      )}

      {successMessage && (
        <div className="mb-4 bg-green-50 border border-green-200 rounded-md p-4">
          <div className="text-green-600">{successMessage}</div>
        </div>
      )}

      <div className="space-y-6">
        <div>
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            Gültige Organisationseinheiten
          </h3>
          <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
            Wählen Sie die OUs aus, für die dieser Tarif gültig sein soll. Wenn keine OU ausgewählt ist, gilt der Tarif für alle OUs.
          </p>
        </div>

        {ouData && (
          <div className="max-h-96 overflow-y-auto border border-gray-300 dark:border-gray-600 rounded-md">
            {ouData.available.length === 0 ? (
              <div className="p-4 text-sm text-gray-500 text-center">
                Keine OUs verfügbar
              </div>
            ) : (
              <div className="divide-y divide-gray-200 dark:divide-gray-600">
                {ouData.available.map((ou) => (
                  <div key={ou.id} className="p-4 hover:bg-gray-50 dark:hover:bg-gray-700">
                    <label className="flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={selectedOuIds.includes(ou.id)}
                        onChange={(e) => handleOuToggle(ou.id, e.target.checked)}
                        disabled={saving}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded disabled:opacity-50"
                      />
                      <div className="ml-3 flex-1">
                        <div className="text-sm font-medium text-gray-900 dark:text-white">
                          {ou.name}
                        </div>
                        <div className="text-xs text-gray-500 dark:text-gray-400">
                          Code: {ou.code}
                        </div>
                      </div>
                    </label>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {selectedOuIds.length > 0 && ouData && (
          <div>
            <h4 className="text-sm font-medium text-gray-700 dark:text-white mb-2">
              Ausgewählte OUs ({selectedOuIds.length}):
            </h4>
            <div className="flex flex-wrap gap-2">
              {selectedOuIds.map((ouId) => {
                const ou = ouData.available.find((o) => o.id === ouId);
                return ou ? (
                  <span
                    key={ouId}
                    className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200"
                  >
                    {ou.code}
                  </span>
                ) : null;
              })}
            </div>
          </div>
        )}

        <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200 dark:border-gray-600">
          <Button
            onClick={handleSave}
            disabled={saving}
            className="flex items-center"
          >
            <FaSave className="mr-2" />
            {saving ? "Speichere..." : "Speichern"}
          </Button>
        </div>
      </div>
    </Card>
  );
};

export default TarifValidOusPage;
