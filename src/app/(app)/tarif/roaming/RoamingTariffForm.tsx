"use client";
import { <PERSON><PERSON><PERSON><PERSON>t, KindOfTarif, type Prisma, Tarif } from "@prisma/client";
import type { SubmitHandler } from "react-hook-form";
import { useForm } from "react-hook-form";
import React, { startTransition, useContext, useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Button from "~/component/button";
import { FaClock, FaParking, FaPlug, FaSave } from "react-icons/fa";
import { BsLightningChargeFill } from "react-icons/bs";
import { TbClockPause } from "react-icons/tb";
import { LiaFileContractSolid } from "react-icons/lia";
import { TarifValidOusSelector } from "~/app/(app)/tarif/components/TarifValidOusSelector";

type RoamingTariffType = Omit<Tarif, "validFrom" | "validTo"> & {
  validFrom: string;
  validTo: string;
};

type RoamingTariffTypeErrorType = {
  [K in keyof RoamingTariffType]?: {
    message: string;
  };
};
export type CPOContractWithCPO = Prisma.CPOContractGetPayload<{
  include: {
    contact: true;
  };
}>;

interface Props {
  tarif: RoamingTariffType | boolean;
  cpoContracts: CPOContractWithCPO[];
}

const RoamingTariffForm = ({ tarif, cpoContracts }: Props) => {
  const router = useRouter();
  const [apiErrorMessage, setApiErrorMessage] = useState<string>("");
  const [selectedValidOuIds, setSelectedValidOuIds] = useState<string[]>([]);

  let defaultValues = {};
  if (typeof tarif != "boolean") {
    defaultValues = {
      ...tarif,
      validFrom: tarif.validFrom.substring(0, 10),
      validTo: tarif.validTo.substring(0, 10),
    };
  }

  // Initialize selectedValidOuIds for existing tarif
  useEffect(() => {
    if (typeof tarif !== "boolean" && tarif.validOus && Array.isArray(tarif.validOus)) {
      setSelectedValidOuIds(tarif.validOus.map((ou: any) => ou.id));
    }
  }, [tarif]);

  const {
    getValues,
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<RoamingTariffType, RoamingTariffTypeErrorType>({ defaultValues: defaultValues });

  const onSubmit: SubmitHandler<RoamingTariffType> = async (data) => {
    // Mutate external data source
    const response = await fetch(`/api/tarif/roamingTarif`, {
      method: "POST",
      body: JSON.stringify({
        data: {
          ...data,
          validOuIds: selectedValidOuIds
        }
      }),
    });

    if (response.ok) {
      router.push("/tarif");
      router.refresh();
    } else {
      const message = await response.json();
      setApiErrorMessage(message?.message ?? "Fehler beim Speichern");
    }
  };

  return (
    <>
      <form onSubmit={handleSubmit(onSubmit)}>
        <input disabled={!getValues("id")} type={"text"} {...register("id")} className={"hidden"} />
        <div className="mb-0 flex flex-row justify-between rounded-t-2xl p-6">
          <h5 id={"basic-data"} className="dark:text-white">
            Neuer Roaming/Direct Tarif
          </h5>
          {Object.keys(errors).length > 0 && (
            <ul className={"text-red-500"}>
              {Object.keys(errors).map((fieldName, index) => (
                <li key={index}>
                  {errors[fieldName as keyof RoamingTariffTypeErrorType]?.message}
                </li>
              ))}
            </ul>
          )}

          <Button type={"submit"}>
            <FaSave className={"mr-1"} />
            Tarif Speichern
          </Button>
        </div>
        <div className="flex-auto p-6 pt-0">
          <div className="-mx-3 flex flex-wrap">
            <div className="w-6/12 max-w-full flex-0 px-3">
              <label
                className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80"
                htmlFor="Name"
              >
                Name
              </label>
              <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                <input
                  {...register("name", { required: "Ein Name ist erforderlich" })}
                  type="text"
                  className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
                />
              </div>
            </div>
            <div className="w-3/12 max-w-full flex-0 px-3">
              <label
                className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80"
                htmlFor="gültig ab"
              >
                Gültig ab
              </label>
              <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                <input
                  {...register("validFrom", { valueAsDate: true })}
                  type="date"
                  className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
                />
              </div>
            </div>
            <div className="w-3/12 max-w-full flex-0 px-3">
              <label
                className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80"
                htmlFor="Gültig bis"
              >
                Gültig bis
              </label>
              <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                <input
                  {...register("validTo", { valueAsDate: true })}
                  name="validTo"
                  type="date"
                  className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
                />
              </div>
            </div>
          </div>
          <div className="-mx-3 flex flex-wrap">
            <div className="w-3/12 max-w-full flex-0 px-3">
              <BsLightningChargeFill size={10} color={"orange"} />
              <label
                className="mb-2 ml-1 mt-6 text-xs font-bold text-slate-700 dark:text-white/80"
                htmlFor="Firmen Name"
              >
                Preis pro kWh in Euro
              </label>
              <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                <input
                  {...register("kwh")}
                  type="number"
                  step={"0.01"}
                  className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
                />
              </div>
            </div>
            <div className="w-3/12 max-w-full flex-0 px-3">
              <FaPlug size={10} />
              <label
                className="mb-2 ml-1 mt-6 text-xs font-bold text-slate-700 dark:text-white/80"
                htmlFor="Firmen Name"
              >
                Preis pro Session in Euro
              </label>
              <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                <input
                  {...register("sessionFee")}
                  type="number"
                  step={"0.01"}
                  className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
                />
              </div>
            </div>
            <div className="w-3/12 max-w-full flex-0 px-3">
              <FaClock size={10} />
              <label
                className="mb-2 ml-1 mt-6 text-xs font-bold text-slate-700 dark:text-white/80"
                htmlFor="mindest Lademenge in kWh"
              >
                Mindest-Lademenge in kWh
              </label>
              <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                <input
                  {...register("minChargingEnergy")}
                  type="number"
                  step={"0.01"}
                  className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
                />
              </div>
            </div>
            <div className="w-3/12 max-w-full flex-0 px-3">
              <FaClock size={10} />
              <label
                className="mb-2 ml-1 mt-6 text-xs font-bold text-slate-700 dark:text-white/80"
                htmlFor="mindest Ladezeit in sek"
              >
                Mindest-Ladezeit in Sekunden
              </label>
              <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                <input
                  {...register("minChargingTime")}
                  type="number"
                  step={"1"}
                  className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
                />
              </div>
            </div>
            <div className="w-3/12 max-w-full flex-0 px-3">
              <BsLightningChargeFill size={10} color={"orange"} />
              <label
                className="mb-2 ml-1 mt-6 text-xs font-bold text-slate-700 dark:text-white/80"
                htmlFor="Name"
              >
                LadesäulenType (AC / DC)
              </label>
              <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                <input
                  {...register("currentType")}
                  type=""
                  className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
                />
              </div>
            </div>
            <div className="w-3/12 max-w-full flex-0 px-3">
              <TbClockPause size={10} />
              <label
                className="mb-2 ml-1 mt-6 text-xs font-bold text-slate-700 dark:text-white/80"
                htmlFor="blockingFee"
              >
                Blockiergebühr €/min
              </label>
              <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                <input
                  {...register("blockingFee")}
                  type={"number"}
                  step="0.01"
                  className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
                />
              </div>
            </div>
            <div className="w-3/12 max-w-full flex-0 px-3">
              <TbClockPause size={10} />
              <label
                className="mb-2 ml-1 mt-6 text-xs font-bold text-slate-700 dark:text-white/80"
                htmlFor="blockingFeeBeginAtMin"
              >
                Blockiergebühr ab min
              </label>
              <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                <input
                  {...register("blockingFeeBeginAtMin")}
                  type={"number"}
                  className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
                />
              </div>
            </div>
            <div className="w-3/12 max-w-full flex-0 px-3">
              <TbClockPause size={10} />
              <label
                className="mb-2 ml-1 mt-6 text-xs font-bold text-slate-700 dark:text-white/80"
                htmlFor="blockingFeeMax"
              >
                Blockiergebühr Max €
              </label>
              <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                <input
                  {...register("blockingFeeMax")}
                  type={"number"}
                  step="0.01"
                  className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
                />
              </div>
            </div>
            <div className="w-3/12 max-w-full flex-0 px-3">
              <LiaFileContractSolid size={10} />
              <label
                className="mb-2 ml-1 mt-6 text-xs font-bold text-slate-700 dark:text-white/80"
                htmlFor="kindOfTarif"
              >
                Tariftyp
              </label>
              <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                <select
                  defaultValue={KindOfTarif.ROAMING}
                  {...register("kindOfTarif")}
                  className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
                >
                  <option value={KindOfTarif.ROAMING}>Roaming</option>
                  <option value={KindOfTarif.DIRECT}>Direct Payment</option>
                </select>
              </div>
            </div>
            <div className="w-3/12 max-w-full flex-0 px-3">
              <LiaFileContractSolid size={10} />
              <label
                className="mb-2 ml-1 mt-6 text-xs font-bold text-slate-700 dark:text-white/80"
                htmlFor="kindOfTarif"
              >
                CPO Vertrag
              </label>
              <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                <select
                  {...register("contractId")}
                  className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
                >
                  {cpoContracts.map((cpoContract, index) => (
                    <option key={index} value={cpoContract.id}>
                      {`${cpoContract?.contact?.name ?? "Kein CPO Verknüpft"} - ${
                        cpoContract.name
                      }- ${cpoContract.start.toLocaleDateString("de")}`}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>

          {/* OU Assignment Section */}
          <div className="-mx-3 flex flex-wrap mt-6">
            <div className="w-full max-w-full flex-0 px-3">
              <TarifValidOusSelector
                tarifId={typeof tarif !== "boolean" ? tarif?.id : undefined}
                selectedOuIds={selectedValidOuIds}
                onSelectionChange={setSelectedValidOuIds}
                disabled={false}
              />
            </div>
          </div>
        </div>
      </form>

      {apiErrorMessage && (
        <div className="relative mx-4  rounded-lg border border-solid bg-red-400 p-4 pr-12 text-white">
          {apiErrorMessage}
          <button
            type="button"
            onClick={() => setApiErrorMessage("")}
            className="absolute right-0 top-0 z-2 box-content h-4 w-4 rounded border-0 bg-transparent p-4 text-sm text-white"
          >
            <span aria-hidden="true" className="cursor-pointer text-center">
              &#10005;
            </span>
          </button>
        </div>
      )}
    </>
  );
};

export default RoamingTariffForm;
