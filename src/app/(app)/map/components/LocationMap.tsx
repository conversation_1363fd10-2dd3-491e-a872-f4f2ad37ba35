"use client";
import React, { useEffect, useState, useCallback, useRef } from "react";
import { GoogleMap, use<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, InfoWindow } from "@react-google-maps/api";
import Card from "~/component/card";
import { userStore } from "~/server/zustand/store";
import { ouColors } from "~/styles/oucolors/oucolors";

interface LocationData {
  id: string;
  name: string;
  address: string;
  city: string;
  country: string;
  coordinates: {
    lat: number;
    lng: number;
  };
  evseCount: number;
  ou: {
    id: string;
    name: string;
  };
}

const mapContainerStyle = {
  width: "100%",
  height: "600px",
};

const defaultCenter = {
  lat: 51.1657, // Germany center
  lng: 10.4515,
};

const LocationMap: React.FC = () => {
  const [locations, setLocations] = useState<LocationData[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedLocation, setSelectedLocation] = useState<LocationData | null>(null);
  const [map, setMap] = useState<google.maps.Map | null>(null);
  const [isReloadingForOu, setIsReloadingForOu] = useState(false);
  const [hoveredLocation, setHoveredLocation] = useState<string | null>(null);
  const [currentZoom, setCurrentZoom] = useState<number>(6);

  // Get current OU from store
  const { selectedOuId } = userStore();
  const previousOuId = useRef<string>(selectedOuId);
  const reloadTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Check if Google Maps API key is available
  const apiKey = process.env.NEXT_PUBLIC_GOOGLE_MAPS_KEY;

  // Load Google Maps API
  const { isLoaded, loadError } = useJsApiLoader({
    id: "google-map-script",
    googleMapsApiKey: apiKey || "",
    libraries: ["places"],
  });

  // Function to fetch locations
  const fetchLocations = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch("/api/locations/map");

      if (!response.ok) {
        const errorText = await response.text();
        console.error("API Error response:", errorText);
        let errorData;
        try {
          errorData = JSON.parse(errorText);
        } catch {
          errorData = { error: errorText };
        }
        throw new Error(errorData.error || `HTTP ${response.status}: Failed to fetch locations`);
      }

      const data = await response.json();

      setLocations(data);

      // Auto-fit map bounds will be handled after map loads
    } catch (err) {
      console.error("Fetch error:", err);
      setError(err instanceof Error ? err.message : "Unknown error");
    } finally {
      setLoading(false);
    }
  }, [selectedOuId]);

  // Load locations from API on mount
  useEffect(() => {
    // Initialize the previous OU ID
    previousOuId.current = selectedOuId;
    fetchLocations();
  }, []); // Only run once on mount

  // Watch for OU changes and reload with delay
  useEffect(() => {
    // Skip on initial mount
    if (previousOuId.current === selectedOuId) {
      return;
    }

    // Clear any existing timeout
    if (reloadTimeoutRef.current) {
      clearTimeout(reloadTimeoutRef.current);
    }

    // Close any open InfoWindow when OU changes to prevent map jumping
    setSelectedLocation(null);

    // Set loading state immediately to show user that something is happening
    setLoading(true);
    setError(null);
    setIsReloadingForOu(true);

    // Wait for server to process OU change, then reload locations
    reloadTimeoutRef.current = setTimeout(() => {
      fetchLocations().finally(() => {
        setIsReloadingForOu(false);
      });
      previousOuId.current = selectedOuId;
    }, 1500); // 1.5 second delay

    // Cleanup timeout on unmount
    return () => {
      if (reloadTimeoutRef.current) {
        clearTimeout(reloadTimeoutRef.current);
      }
    };
  }, [selectedOuId, fetchLocations]);

  const onLoad = useCallback((map: google.maps.Map) => {
    setMap(map);

    // Set initial zoom level
    setCurrentZoom(map.getZoom() || 6);

    // Listen for zoom changes
    const zoomListener = map.addListener("zoom_changed", () => {
      const newZoom = map.getZoom() || 6;
      setCurrentZoom(newZoom);
    });

    // Store listener for cleanup
    map.set("zoomListener", zoomListener);
  }, []);

  const onUnmount = useCallback((map: google.maps.Map) => {
    // Clean up zoom listener
    const zoomListener = map.get("zoomListener");
    if (zoomListener) {
      window.google.maps.event.removeListener(zoomListener);
    }
    setMap(null);
  }, []);

  // Auto-fit map bounds when locations change and map is loaded
  useEffect(() => {
    if (locations.length > 0 && map && window.google) {
      const bounds = new window.google.maps.LatLngBounds();
      locations.forEach((location) => {
        bounds.extend(
          new window.google.maps.LatLng(location.coordinates.lat, location.coordinates.lng),
        );
      });
      map.fitBounds(bounds);
    }
  }, [locations, map]);

  const handleMarkerClick = (location: LocationData) => {
    setSelectedLocation(location);
  };

  const handleInfoWindowClose = () => {
    setSelectedLocation(null);
  };

  // Auto-fit map bounds when locations change
  useEffect(() => {
    if (map && locations.length > 0) {
      const bounds = new window.google.maps.LatLngBounds();
      locations.forEach((location) => {
        bounds.extend(location.coordinates);
      });
      map.fitBounds(bounds);

      // Set a reasonable zoom limit and update zoom state
      const listener = window.google.maps.event.addListenerOnce(map, "bounds_changed", () => {
        const currentZoom = map.getZoom() || 6;
        if (currentZoom > 15) {
          map.setZoom(15);
          setCurrentZoom(15);
        } else {
          setCurrentZoom(currentZoom);
        }
      });
    }
  }, [map, locations]);

  // Helper function to get OU color
  const getOuColor = useCallback((ouName: string) => {
    // Check if OU has explicit color defined
    if (ouColors[ouName]?.["--color-primary"]) {
      return ouColors[ouName]["--color-primary"] ?? "#4B5563";
    }
    // Use dark gray as neutral color for OUs without explicit colors
    return "#4B5563"; // Dark gray (gray-600)
  }, []);

  // Helper function to darken a color for stroke
  const darkenColor = useCallback((color: string, amount: number = 0.2) => {
    // Convert hex to RGB
    const hex = color.replace("#", "");
    const r = parseInt(hex.substr(0, 2), 16);
    const g = parseInt(hex.substr(2, 2), 16);
    const b = parseInt(hex.substr(4, 2), 16);

    // Darken each component
    const newR = Math.max(0, Math.floor(r * (1 - amount)));
    const newG = Math.max(0, Math.floor(g * (1 - amount)));
    const newB = Math.max(0, Math.floor(b * (1 - amount)));

    // Convert back to hex
    return `#${newR.toString(16).padStart(2, "0")}${newG.toString(16).padStart(2, "0")}${newB
      .toString(16)
      .padStart(2, "0")}`;
  }, []);

  // Helper function to calculate marker size based on zoom level
  const getMarkerSize = useCallback(
    (zoom: number, isSelected: boolean = false, isHovered: boolean = false) => {
      // Base size calculation based on zoom level (all sizes reduced)
      let baseSize = 12; // Minimum size

      if (zoom >= 15) {
        baseSize = 32; // Very close zoom - large markers
      } else if (zoom >= 12) {
        baseSize = 28; // Close zoom - medium-large markers
      } else if (zoom >= 10) {
        baseSize = 24; // Medium zoom - medium markers
      } else if (zoom >= 8) {
        baseSize = 20; // Far zoom - small-medium markers
      } else if (zoom >= 6) {
        baseSize = 16; // Very far zoom - small markers
      } else {
        baseSize = 14; // Extremely far zoom - very small markers
      }

      // Apply state modifiers (also reduced)
      if (isSelected) {
        return baseSize + 6; // Selected markers are bigger
      } else if (isHovered) {
        return baseSize + 3; // Hovered markers are slightly bigger
      }

      return baseSize;
    },
    [],
  );

  // Create custom marker icon with lightning bolt
  const createMarkerIcon = useCallback(
    (ouName: string, isSelected: boolean = false, isHovered: boolean = false) => {
      if (!window.google) return undefined;

      const size = getMarkerSize(currentZoom, isSelected, isHovered);
      const baseColor = getOuColor(ouName);

      // Adjust color based on state
      let color = baseColor;
      if (isSelected) {
        color = darkenColor(baseColor, 0.3); // Darker when selected
      } else if (isHovered) {
        color = darkenColor(baseColor, 0.15); // Slightly darker when hovered
      }

      const strokeColor = darkenColor(color, 0.2);

      const svgIcon = `
      <svg width="${size}" height="${size}" viewBox="0 0 32 32" xmlns="http://www.w3.org/2000/svg">
        <defs>
          <filter id="shadow${
            isSelected ? "Selected" : ""
          }" x="-50%" y="-50%" width="200%" height="200%">
            <feDropShadow dx="0" dy="2" stdDeviation="3" flood-color="rgba(0,0,0,0.4)"/>
          </filter>
          <linearGradient id="gradient${
            isSelected ? "Selected" : ""
          }" x1="0%" y1="0%" x2="0%" y2="100%">
            <stop offset="0%" style="stop-color:${color};stop-opacity:1" />
            <stop offset="100%" style="stop-color:${strokeColor};stop-opacity:1" />
          </linearGradient>
        </defs>
        <!-- Outer circle with gradient -->
        <circle cx="16" cy="16" r="14" fill="url(#gradient${
          isSelected ? "Selected" : ""
        })" stroke="${strokeColor}" stroke-width="2" filter="url(#shadow${
          isSelected ? "Selected" : ""
        })"/>
        <!-- Inner highlight circle -->
        <circle cx="16" cy="16" r="11" fill="rgba(255,255,255,0.15)" stroke="rgba(255,255,255,0.3)" stroke-width="1"/>
        <!-- Lightning bolt icon - more detailed -->
        <g transform="translate(16,16)">
          <path d="M-3,-6 L3,0 L0,0 L3,6 L-3,0 L0,0 Z" fill="white" stroke="rgba(255,255,255,0.9)" stroke-width="0.5" stroke-linejoin="round"/>
          <!-- Small highlight on bolt -->
          <path d="M-2,-5 L2,-1 L0,-1 L2,5" fill="rgba(255,255,255,0.6)" stroke="none" stroke-width="0.3"/>
        </g>
      </svg>
    `;

      return {
        url: "data:image/svg+xml;charset=UTF-8," + encodeURIComponent(svgIcon),
        scaledSize: new window.google.maps.Size(size, size),
        anchor: new window.google.maps.Point(size / 2, size / 2),
      };
    },
    [getOuColor, darkenColor, getMarkerSize, currentZoom],
  );

  if (loading || !isLoaded) {
    return (
      <Card>
        <div className="flex h-96 items-center justify-center">
          <div className="text-center">
            <div className="border-primary mx-auto mb-4 h-12 w-12 animate-spin rounded-full border-b-2"></div>
            <p className="text-gray-600">
              {loading
                ? isReloadingForOu
                  ? "Lade Standorte für neue OU..."
                  : "Lade Standorte..."
                : "Lade Google Maps..."}
            </p>
          </div>
        </div>
      </Card>
    );
  }

  if (loadError) {
    return (
      <Card>
        <div className="flex h-96 items-center justify-center">
          <div className="text-center">
            <p className="mb-4 text-red-600">Fehler beim Laden von Google Maps</p>
            <p className="text-gray-600">{loadError.message}</p>
            <button
              onClick={() => window.location.reload()}
              className="hover:bg-primary/80 mt-4 rounded bg-primary px-4 py-2 text-white"
            >
              Neu laden
            </button>
          </div>
        </div>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <div className="flex h-96 items-center justify-center text-red-500">
          <div className="text-center">
            <p className="font-semibold">Fehler beim Laden der Standorte</p>
            <p className="text-sm">{error}</p>
            <button
              onClick={() => window.location.reload()}
              className="hover:bg-primary/80 mt-4 rounded bg-primary px-4 py-2 text-white"
            >
              Neu laden
            </button>
          </div>
        </div>
      </Card>
    );
  }

  // Check if API key is missing
  if (!apiKey) {
    return (
      <Card>
        <div className="flex h-96 items-center justify-center">
          <div className="text-center">
            <p className="mb-4 text-red-600">Google Maps API Key fehlt</p>
            <p className="text-gray-600">NEXT_PUBLIC_GOOGLE_MAPS_KEY ist nicht konfiguriert</p>
          </div>
        </div>
      </Card>
    );
  }

  return (
    <Card>
      <div className="mb-4">
        <h2 className="mb-2 text-xl font-bold text-primary">Standorte Kartenansicht</h2>
        <p className="text-gray-600">{locations.length} Standorte gefunden</p>
      </div>

      <GoogleMap
        mapContainerStyle={mapContainerStyle}
        center={defaultCenter}
        zoom={6}
        onLoad={onLoad}
        onUnmount={onUnmount}
        options={{
          zoomControl: true,
          streetViewControl: false,
          mapTypeControl: true,
          fullscreenControl: true,
          mapId: process.env.NEXT_PUBLIC_GOOGLE_MAPS_MAP_ID,
        }}
      >
        {locations.map((location) => {
          const isSelected = selectedLocation?.id === location.id;
          const isHovered = hoveredLocation === location.id;
          return (
            <Marker
              key={location.id}
              position={location.coordinates}
              onClick={() => handleMarkerClick(location)}
              onMouseOver={() => setHoveredLocation(location.id)}
              onMouseOut={() => setHoveredLocation(null)}
              title={`${location.name} (${location.evseCount} Ladepunkte)`}
              icon={createMarkerIcon(location.ou.name, isSelected, isHovered)}
            />
          );
        })}

        {selectedLocation && (
          <InfoWindow position={selectedLocation.coordinates} onCloseClick={handleInfoWindowClose}>
            <div className="max-w-xs p-3">
              <div className="mb-3 flex items-center">
                <div
                  className="mr-2 flex h-6 w-6 items-center justify-center rounded-full"
                  style={{ backgroundColor: getOuColor(selectedLocation.ou.name) }}
                >
                  <span className="text-sm text-white">⚡</span>
                </div>
                <h3
                  className="text-lg font-bold"
                  style={{ color: getOuColor(selectedLocation.ou.name) }}
                >
                  {selectedLocation.name}
                </h3>
              </div>
              <p className="mb-3 text-sm text-gray-600">{selectedLocation.address}</p>
              <div className="space-y-2 text-sm">
                <div className="flex items-center justify-between rounded bg-gray-50 p-2">
                  <span className="text-gray-700">Organisation:</span>
                  <span
                    className="font-medium"
                    style={{ color: getOuColor(selectedLocation.ou.name) }}
                  >
                    {selectedLocation.ou.name}
                  </span>
                </div>
                <div
                  className="flex items-center justify-between rounded p-2"
                  style={{ backgroundColor: `${getOuColor(selectedLocation.ou.name)}15` }}
                >
                  <span className="text-gray-700">Ladepunkte:</span>
                  <span
                    className="font-bold"
                    style={{ color: getOuColor(selectedLocation.ou.name) }}
                  >
                    {selectedLocation.evseCount}
                  </span>
                </div>
              </div>
            </div>
          </InfoWindow>
        )}
      </GoogleMap>
    </Card>
  );
};

export default LocationMap;
