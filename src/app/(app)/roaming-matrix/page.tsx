"use client";

import React, { useState, useEffect } from "react";
import { FaChevronDown, FaChevronRight } from "react-icons/fa";
import { Chip } from "~/app/(app)/component/chip";

interface TariffData {
  id: string;
  name: string;
  kwh: number;
  sessionFee: number;
  minChargingTime: number;
  minChargingEnergy: number;
  blockingFee: number;
  blockingFeeBeginAtMin: number;
  blockingFeeMax: number;
  kindOfTarif: string;
  validFrom: string;
  validTo: string;
  validOus: Array<{
    id: string;
    name: string;
    code: string;
  }>;
}

interface RoamingMatrixData {
  id: string;
  name: string;
  companyName: string;
  customerNumber: string;
  supplierNumber: string;
  cpo: boolean;
  noRoaming: boolean;
  ou: {
    id: string;
    name: string;
    code: string;
  } | null;
  providers: Array<{
    providerId: string;
    providerCountryId: string;
  }>;
  acTariff: TariffData | null;
  dcTariff: TariffData | null;
  validCreditTariffs: Array<{
    id: string;
    name: string;
    tarifType: string;
    powerType: string;
    sessionCredit: number;
    energyCredit: number;
    kindOfTarif: string;
    validFrom: string;
    validTo: string | null;
  }>;
}

const RoamingMatrixPage = () => {
  const [data, setData] = useState<RoamingMatrixData[]>([]);
  const [filteredData, setFilteredData] = useState<RoamingMatrixData[]>([]);
  const [loading, setLoading] = useState(true);
  const [expandedProviders, setExpandedProviders] = useState<Set<string>>(new Set());
  const [filters, setFilters] = useState({
    contactName: "",
    hasAC: "all",
    hasDC: "all",
    hasSessionFee: "all",
    hasBlockingFee: "all",
    priceRange: [0, 1], // Min and max kWh price range
  });

  useEffect(() => {
    fetchRoamingMatrixData();
  }, []);

  const fetchRoamingMatrixData = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/roaming-matrix");
      if (response.ok) {
        const data = await response.json();
        setData(data);
        setFilteredData(data);
      } else {
        console.error("Failed to fetch roaming matrix data");
      }
    } catch (error) {
      console.error("Error fetching roaming matrix data:", error);
    } finally {
      setLoading(false);
    }
  };

  // Get price range from data
  const getPriceRange = () => {
    if (data.length === 0) return { min: 0, max: 1 };

    const prices = [];
    data.forEach((contact) => {
      if (contact.acTariff) prices.push(contact.acTariff.kwh);
      if (contact.dcTariff) prices.push(contact.dcTariff.kwh);
    });

    if (prices.length === 0) return { min: 0, max: 1 };

    return {
      min: Math.min(...prices),
      max: Math.max(...prices),
    };
  };

  const priceRange = getPriceRange();

  // Initialize price range filter when data loads
  useEffect(() => {
    if (data.length > 0 && filters.priceRange[0] === 0 && filters.priceRange[1] === 1) {
      setFilters((prev) => ({
        ...prev,
        priceRange: [priceRange.min, priceRange.max],
      }));
    }
  }, [data, priceRange.min, priceRange.max]);

  // Filter data based on current filters
  useEffect(() => {
    let filtered = data;

    // Contact name filter (searches in both name and companyName)
    if (filters.contactName) {
      filtered = filtered.filter((contact) => {
        const name = contact.name?.toLowerCase() || "";
        const companyName = contact.companyName?.toLowerCase() || "";
        const searchTerm = filters.contactName.toLowerCase();
        return name.includes(searchTerm) || companyName.includes(searchTerm);
      });
    }

    // AC tariff filter
    if (filters.hasAC !== "all") {
      filtered = filtered.filter((contact) =>
        filters.hasAC === "true" ? contact.acTariff !== null : contact.acTariff === null,
      );
    }

    // DC tariff filter
    if (filters.hasDC !== "all") {
      filtered = filtered.filter((contact) =>
        filters.hasDC === "true" ? contact.dcTariff !== null : contact.dcTariff === null,
      );
    }

    // Session fee filter (checks both AC and DC tariffs)
    if (filters.hasSessionFee !== "all") {
      filtered = filtered.filter((contact) => {
        const hasSessionFee = contact.acTariff?.sessionFee > 0 || contact.dcTariff?.sessionFee > 0;
        return filters.hasSessionFee === "true" ? hasSessionFee : !hasSessionFee;
      });
    }

    // Blocking fee filter (checks both AC and DC tariffs)
    if (filters.hasBlockingFee !== "all") {
      filtered = filtered.filter((contact) => {
        const hasBlockingFee =
          contact.acTariff?.blockingFee > 0 || contact.dcTariff?.blockingFee > 0;
        return filters.hasBlockingFee === "true" ? hasBlockingFee : !hasBlockingFee;
      });
    }

    // Price range filter (checks both AC and DC tariffs)
    filtered = filtered.filter((contact) => {
      const acPrice = contact.acTariff?.kwh;
      const dcPrice = contact.dcTariff?.kwh;

      const acInRange = acPrice
        ? acPrice >= filters.priceRange[0] && acPrice <= filters.priceRange[1]
        : false;
      const dcInRange = dcPrice
        ? dcPrice >= filters.priceRange[0] && dcPrice <= filters.priceRange[1]
        : false;

      // Show contact if either AC or DC tariff is in range (or if no tariffs exist)
      return acInRange || dcInRange || (!contact.acTariff && !contact.dcTariff);
    });

    setFilteredData(filtered);
  }, [data, filters]);

  const toggleProviderExpanded = (contactId: string) => {
    const newExpanded = new Set(expandedProviders);
    if (newExpanded.has(contactId)) {
      newExpanded.delete(contactId);
    } else {
      newExpanded.add(contactId);
    }
    setExpandedProviders(newExpanded);
  };

  // CSV Export function - only exports visible data
  const exportToCSV = () => {
    const csvHeaders = [
      "EMP Name",
      "AC kWh Price",
      "AC Session Fee",
      "AC Blocking Fee Begin At Min",
      "AC Blocking Fee",
      "AC Blocking Fee Max",
      "DC kWh Price",
      "DC Session Fee",
      "DC Blocking Fee Begin At Min",
      "DC Blocking Fee",
      "DC Blocking Fee Max",
    ];

    const csvData = filteredData.map((contact) => {
      const row = [contact.companyName || contact.name || ""];

      // AC Tariff data - only add what's visible
      if (contact.acTariff) {
        row.push(contact.acTariff.kwh.toFixed(2));
        row.push(contact.acTariff.sessionFee > 0 ? contact.acTariff.sessionFee.toFixed(2) : "");
        row.push(
          contact.acTariff.blockingFee > 0 && contact.acTariff.blockingFeeBeginAtMin > 0
            ? contact.acTariff.blockingFeeBeginAtMin.toString()
            : "",
        );
        row.push(contact.acTariff.blockingFee > 0 ? contact.acTariff.blockingFee.toFixed(2) : "");
        row.push(
          contact.acTariff.blockingFee > 0 && contact.acTariff.blockingFeeMax > 0
            ? contact.acTariff.blockingFeeMax.toFixed(2)
            : "",
        );
      } else {
        row.push("", "", "", "", ""); // Empty AC tariff fields
      }

      // DC Tariff data - only add what's visible
      if (contact.dcTariff) {
        row.push(contact.dcTariff.kwh.toFixed(2));
        row.push(contact.dcTariff.sessionFee > 0 ? contact.dcTariff.sessionFee.toFixed(2) : "");
        row.push(
          contact.dcTariff.blockingFee > 0 && contact.dcTariff.blockingFeeBeginAtMin > 0
            ? contact.dcTariff.blockingFeeBeginAtMin.toString()
            : "",
        );
        row.push(contact.dcTariff.blockingFee > 0 ? contact.dcTariff.blockingFee.toFixed(2) : "");
        row.push(
          contact.dcTariff.blockingFee > 0 && contact.dcTariff.blockingFeeMax > 0
            ? contact.dcTariff.blockingFeeMax.toFixed(2)
            : "",
        );
      } else {
        row.push("", "", "", "", ""); // Empty DC tariff fields
      }

      return row;
    });

    const csvContent = [
      csvHeaders.join(","),
      ...csvData.map((row) => row.map((field) => `"${field}"`).join(",")),
    ].join("\n");

    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const link = document.createElement("a");
    const url = URL.createObjectURL(blob);
    link.setAttribute("href", url);
    link.setAttribute("download", `roaming-matrix-${new Date().toISOString().split("T")[0]}.csv`);
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const ContactCard = ({ contact }: { contact: RoamingMatrixData }) => {
    const isProviderExpanded = expandedProviders.has(contact.id);

    return (
      <div className="rounded-lg border border-gray-200 bg-white p-4 shadow-md dark:border-gray-700 dark:bg-gray-800">
        {/* Contact Header */}
        <div className="mb-4 flex items-center justify-between">
          <div className="flex-1">
            <div className="mb-1 flex items-center space-x-3">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                {contact.companyName || contact.name || "Unbekannter Contact"}
              </h3>
              {/* Provider IDs - show first 3, then expandable */}
              {contact.providers.length > 0 && (
                <div className="flex items-center space-x-2">
                  {contact.providers.slice(0, 3).map((provider, index) => (
                    <span
                      key={index}
                      className="font-mono rounded bg-blue-100 px-2 py-1 text-xs text-blue-800 dark:bg-blue-800 dark:text-blue-100"
                    >
                      {provider.providerCountryId}*{provider.providerId}
                    </span>
                  ))}
                  {contact.providers.length > 3 && (
                    <button
                      onClick={() => toggleProviderExpanded(contact.id)}
                      className="flex items-center space-x-1 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
                    >
                      {isProviderExpanded ? (
                        <FaChevronDown className="h-3 w-3" />
                      ) : (
                        <FaChevronRight className="h-3 w-3" />
                      )}
                      <span>+{contact.providers.length - 3}</span>
                    </button>
                  )}
                </div>
              )}
            </div>
            <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400">
              {contact.ou && <span>OU: {contact.ou.name}</span>}
            </div>
            {/* Expanded Provider IDs */}
            {isProviderExpanded && contact.providers.length > 3 && (
              <div className="mt-2 flex flex-wrap gap-2">
                {contact.providers.slice(3).map((provider, index) => (
                  <span
                    key={index + 3}
                    className="font-mono rounded bg-blue-100 px-2 py-1 text-xs text-blue-800 dark:bg-blue-800 dark:text-blue-100"
                  >
                    {provider.providerCountryId}*{provider.providerId}
                  </span>
                ))}
              </div>
            )}
          </div>
          <div className="flex items-center space-x-2">
            {contact.cpo && <Chip label="CPO" className="bg-purple-100 text-xs text-purple-800" />}
            {contact.noRoaming && (
              <Chip label="Kein Roaming" className="bg-red-100 text-xs text-red-800" />
            )}
          </div>
        </div>

        {/* Tariff Information */}
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          {/* AC Tariff */}
          <div
            className={`rounded-lg border-2 p-3 ${
              contact.acTariff
                ? "border-blue-200 bg-blue-50 dark:border-blue-700 dark:bg-blue-900/20"
                : "border-gray-200 bg-gray-50 dark:border-gray-600 dark:bg-gray-700"
            }`}
          >
            <div className="mb-3 flex items-center justify-center">
              <span
                className={`rounded px-2 py-1 text-sm font-medium ${
                  contact.acTariff
                    ? "bg-blue-100 text-blue-800 dark:bg-blue-800 dark:text-blue-100"
                    : "bg-gray-100 text-gray-800 dark:bg-gray-600 dark:text-gray-100"
                }`}
              >
                AC
              </span>
            </div>
            {contact.acTariff ? (
              <div className="space-y-1 text-xs">
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-300">kWh:</span>
                  <span className="font-semibold">{contact.acTariff.kwh.toFixed(2)}€</span>
                </div>
                {contact.acTariff.sessionFee > 0 && (
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-300">Session:</span>
                    <span className="font-semibold">{contact.acTariff.sessionFee.toFixed(2)}€</span>
                  </div>
                )}
                {contact.acTariff.blockingFee > 0 && (
                  <>
                    {contact.acTariff.blockingFeeBeginAtMin > 0 && (
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-300">Blockier ab:</span>
                        <span className="font-semibold">
                          {contact.acTariff.blockingFeeBeginAtMin}min
                        </span>
                      </div>
                    )}
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-300">Blockiergebühr:</span>
                      <span className="font-semibold">
                        {contact.acTariff.blockingFee.toFixed(2)}€/min
                      </span>
                    </div>
                    {contact.acTariff.blockingFeeMax > 0 && (
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-300">Max. Blockier:</span>
                        <span className="font-semibold">
                          {contact.acTariff.blockingFeeMax.toFixed(2)}€
                        </span>
                      </div>
                    )}
                  </>
                )}
              </div>
            ) : (
              <div className="py-4 text-center text-gray-400">Kein AC-Tarif</div>
            )}
          </div>

          {/* DC Tariff */}
          <div
            className={`rounded-lg border-2 p-3 ${
              contact.dcTariff
                ? "border-green-200 bg-green-50 dark:border-green-700 dark:bg-green-900/20"
                : "border-gray-200 bg-gray-50 dark:border-gray-600 dark:bg-gray-700"
            }`}
          >
            <div className="mb-3 flex items-center justify-center">
              <span
                className={`rounded px-2 py-1 text-sm font-medium ${
                  contact.dcTariff
                    ? "bg-green-100 text-green-800 dark:bg-green-800 dark:text-green-100"
                    : "bg-gray-100 text-gray-800 dark:bg-gray-600 dark:text-gray-100"
                }`}
              >
                DC
              </span>
            </div>
            {contact.dcTariff ? (
              <div className="space-y-1 text-xs">
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-300">kWh:</span>
                  <span className="font-semibold">{contact.dcTariff.kwh.toFixed(2)}€</span>
                </div>
                {contact.dcTariff.sessionFee > 0 && (
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-300">Session:</span>
                    <span className="font-semibold">{contact.dcTariff.sessionFee.toFixed(2)}€</span>
                  </div>
                )}
                {contact.dcTariff.blockingFee > 0 && (
                  <>
                    {contact.dcTariff.blockingFeeBeginAtMin > 0 && (
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-300">Blockier ab:</span>
                        <span className="font-semibold">
                          {contact.dcTariff.blockingFeeBeginAtMin}min
                        </span>
                      </div>
                    )}
                    <div className="flex justify-between">
                      <span className="text-gray-600 dark:text-gray-300">Blockiergebühr:</span>
                      <span className="font-semibold">
                        {contact.dcTariff.blockingFee.toFixed(2)}€/min
                      </span>
                    </div>
                    {contact.dcTariff.blockingFeeMax > 0 && (
                      <div className="flex justify-between">
                        <span className="text-gray-600 dark:text-gray-300">Max. Blockier:</span>
                        <span className="font-semibold">
                          {contact.dcTariff.blockingFeeMax.toFixed(2)}€
                        </span>
                      </div>
                    )}
                  </>
                )}
              </div>
            ) : (
              <div className="py-4 text-center text-gray-400">Kein DC-Tarif</div>
            )}
          </div>
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="mb-6">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Roaming Matrix</h1>
          <p className="mt-2 text-gray-600 dark:text-gray-400">
            Übersicht aller Contacts mit ihren gültigen zugeordneten Tarifen
          </p>
        </div>
        <div className="flex h-64 items-center justify-center">
          <div className="border-primary h-12 w-12 animate-spin rounded-full border-b-2"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Roaming Matrix</h1>
        <div className="mt-4 rounded-lg border border-blue-200 bg-blue-50 p-4 dark:border-blue-700 dark:bg-blue-900/20">
          <div className="flex items-start space-x-3">
            <div className="mt-1 flex-shrink-0">
              <div className="flex h-6 w-6 items-center justify-center rounded-full bg-blue-500">
                <span className="text-sm font-bold text-white">i</span>
              </div>
            </div>
            <div>
              <h3 className="mb-2 text-lg font-medium text-blue-900 dark:text-blue-100">
                Was ist die Roaming Matrix?
              </h3>
              <p className="text-sm leading-relaxed text-blue-800 dark:text-blue-200">
                Die Roaming Matrix zeigt, was die einzelnen{" "}
                <strong>Emobility Provider (EMP)</strong> pro Ladevorgang vergüten. Diese Vergütung
                bekommt der <strong>CPO (Charge Point Operator)</strong> auch über die Gutschrift
                ausgeschüttet.
              </p>
              <div className="mt-3 grid grid-cols-1 gap-4 text-xs md:grid-cols-2">
                <div className="flex items-center space-x-2">
                  <span className="h-2 w-2 rounded-full bg-blue-500"></span>
                  <span className="text-blue-700 dark:text-blue-300">
                    <strong>AC/DC Tarife:</strong> Unterschiedliche Preise für Wechsel- und
                    Gleichstrom
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="h-2 w-2 rounded-full bg-green-500"></span>
                  <span className="text-blue-700 dark:text-blue-300">
                    <strong>Startgebühr:</strong> Einmalige Gebühr pro Ladevorgang
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="h-2 w-2 rounded-full bg-orange-500"></span>
                  <span className="text-blue-700 dark:text-blue-300">
                    <strong>Blockiergebühr:</strong> Zusatzkosten bei längerer Standzeit
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="h-2 w-2 rounded-full bg-purple-500"></span>
                  <span className="text-blue-700 dark:text-blue-300">
                    <strong>Provider IDs:</strong> Eindeutige Kennungen der EMPs
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Filter Section */}
        <div className="mt-6 rounded-lg bg-gray-50 p-4 dark:bg-gray-700">
          <h3 className="mb-4 text-lg font-medium text-gray-900 dark:text-white">Filter</h3>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-6">
            {/* Contact Name Filter */}
            <div>
              <label className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
                EMP Name
              </label>
              <input
                type="text"
                value={filters.contactName}
                onChange={(e) => setFilters((prev) => ({ ...prev, contactName: e.target.value }))}
                placeholder="Name oder Firma suchen..."
                className="focus:ring-primary focus:border-primary w-full rounded-md border border-gray-300 px-3 py-2 text-sm shadow-sm focus:outline-none focus:ring-1 dark:border-gray-600 dark:bg-gray-800 dark:text-white"
              />
            </div>

            {/* AC Tariff Filter */}
            <div>
              <label className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
                AC-Tarif
              </label>
              <select
                value={filters.hasAC}
                onChange={(e) => setFilters((prev) => ({ ...prev, hasAC: e.target.value }))}
                className="focus:ring-primary focus:border-primary w-full rounded-md border border-gray-300 px-3 py-2 text-sm shadow-sm focus:outline-none focus:ring-1 dark:border-gray-600 dark:bg-gray-800 dark:text-white"
              >
                <option value="all">Alle</option>
                <option value="true">Mit AC-Tarif</option>
                <option value="false">Ohne AC-Tarif</option>
              </select>
            </div>

            {/* DC Tariff Filter */}
            <div>
              <label className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
                DC-Tarif
              </label>
              <select
                value={filters.hasDC}
                onChange={(e) => setFilters((prev) => ({ ...prev, hasDC: e.target.value }))}
                className="focus:ring-primary focus:border-primary w-full rounded-md border border-gray-300 px-3 py-2 text-sm shadow-sm focus:outline-none focus:ring-1 dark:border-gray-600 dark:bg-gray-800 dark:text-white"
              >
                <option value="all">Alle</option>
                <option value="true">Mit DC-Tarif</option>
                <option value="false">Ohne DC-Tarif</option>
              </select>
            </div>

            {/* Session Fee Filter */}
            <div>
              <label className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
                Startgebühr
              </label>
              <select
                value={filters.hasSessionFee}
                onChange={(e) => setFilters((prev) => ({ ...prev, hasSessionFee: e.target.value }))}
                className="focus:ring-primary focus:border-primary w-full rounded-md border border-gray-300 px-3 py-2 text-sm shadow-sm focus:outline-none focus:ring-1 dark:border-gray-600 dark:bg-gray-800 dark:text-white"
              >
                <option value="all">Alle</option>
                <option value="true">Mit Startgebühr</option>
                <option value="false">Ohne Startgebühr</option>
              </select>
            </div>

            {/* Blocking Fee Filter */}
            <div>
              <label className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
                Blockiergebühr
              </label>
              <select
                value={filters.hasBlockingFee}
                onChange={(e) =>
                  setFilters((prev) => ({ ...prev, hasBlockingFee: e.target.value }))
                }
                className="focus:ring-primary focus:border-primary w-full rounded-md border border-gray-300 px-3 py-2 text-sm shadow-sm focus:outline-none focus:ring-1 dark:border-gray-600 dark:bg-gray-800 dark:text-white"
              >
                <option value="all">Alle</option>
                <option value="true">Mit Blockiergebühr</option>
                <option value="false">Ohne Blockiergebühr</option>
              </select>
            </div>

            {/* Price Range Filter */}
            <div>
              <label className="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
                Energiepreis (€/kWh)
              </label>
              <div className="space-y-3">
                <div className="flex items-center justify-between text-xs text-gray-500 dark:text-gray-400">
                  <span>{filters.priceRange[0].toFixed(3)}€</span>
                  <span>{filters.priceRange[1].toFixed(3)}€</span>
                </div>

                {/* Dual Range Slider */}
                <div className="relative h-6">
                  {/* Track */}
                  <div className="absolute top-1/2 h-2 w-full -translate-y-1/2 transform rounded-lg bg-gray-200 dark:bg-gray-600"></div>

                  {/* Active Range */}
                  <div
                    className="absolute top-1/2 h-2 -translate-y-1/2 transform rounded-lg bg-blue-500"
                    style={{
                      left: `${
                        ((filters.priceRange[0] - priceRange.min) /
                          (priceRange.max - priceRange.min)) *
                        100
                      }%`,
                      width: `${
                        ((filters.priceRange[1] - filters.priceRange[0]) /
                          (priceRange.max - priceRange.min)) *
                        100
                      }%`,
                    }}
                  ></div>

                  {/* Max Slider - placed first so it's behind */}
                  <input
                    type="range"
                    min={priceRange.min}
                    max={priceRange.max}
                    step="0.001"
                    value={filters.priceRange[1]}
                    onChange={(e) => {
                      const newMax = parseFloat(e.target.value);
                      if (newMax >= filters.priceRange[0]) {
                        setFilters((prev) => ({
                          ...prev,
                          priceRange: [prev.priceRange[0], newMax],
                        }));
                      }
                    }}
                    className="absolute z-10 h-6 w-full cursor-pointer appearance-none bg-transparent focus:outline-none
                             [&::-moz-range-thumb]:h-5 [&::-moz-range-thumb]:w-5 [&::-moz-range-thumb]:cursor-pointer
                             [&::-moz-range-thumb]:rounded-full [&::-moz-range-thumb]:border-2 [&::-moz-range-thumb]:border-none
                             [&::-moz-range-thumb]:border-white [&::-moz-range-thumb]:bg-green-500 [&::-moz-range-thumb]:shadow-md
                             [&::-webkit-slider-thumb]:h-5 [&::-webkit-slider-thumb]:w-5 [&::-webkit-slider-thumb]:cursor-pointer
                             [&::-webkit-slider-thumb]:appearance-none [&::-webkit-slider-thumb]:rounded-full [&::-webkit-slider-thumb]:border-2
                             [&::-webkit-slider-thumb]:border-white [&::-webkit-slider-thumb]:bg-green-500 [&::-webkit-slider-thumb]:shadow-md"
                  />

                  {/* Min Slider - placed second so it's on top */}
                  <input
                    type="range"
                    min={priceRange.min}
                    max={priceRange.max}
                    step="0.001"
                    value={filters.priceRange[0]}
                    onChange={(e) => {
                      const newMin = parseFloat(e.target.value);
                      if (newMin <= filters.priceRange[1]) {
                        setFilters((prev) => ({
                          ...prev,
                          priceRange: [newMin, prev.priceRange[1]],
                        }));
                      }
                    }}
                    className="pointer-events-none absolute z-20 h-6 w-full cursor-pointer appearance-none bg-transparent focus:outline-none
                             [&::-moz-range-thumb]:h-5 [&::-moz-range-thumb]:w-5 [&::-moz-range-thumb]:cursor-pointer
                             [&::-moz-range-thumb]:rounded-full [&::-moz-range-thumb]:border-2 [&::-moz-range-thumb]:border-none
                             [&::-moz-range-thumb]:border-white [&::-moz-range-thumb]:bg-blue-500 [&::-moz-range-thumb]:shadow-md
                             [&::-webkit-slider-thumb]:pointer-events-auto
                             [&::-webkit-slider-thumb]:h-5 [&::-webkit-slider-thumb]:w-5 [&::-webkit-slider-thumb]:cursor-pointer
                             [&::-webkit-slider-thumb]:appearance-none [&::-webkit-slider-thumb]:rounded-full [&::-webkit-slider-thumb]:border-2
                             [&::-webkit-slider-thumb]:border-white [&::-webkit-slider-thumb]:bg-blue-500 [&::-webkit-slider-thumb]:shadow-md"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Clear Filters Button */}
          <div className="mt-4">
            <button
              onClick={() =>
                setFilters({
                  contactName: "",
                  hasAC: "all",
                  hasDC: "all",
                  hasSessionFee: "all",
                  hasBlockingFee: "all",
                  priceRange: [priceRange.min, priceRange.max],
                })
              }
              className="focus:ring-primary rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 dark:border-gray-600 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700"
            >
              Filter zurücksetzen
            </button>
          </div>
        </div>

        <div className="mt-4 flex items-center justify-between">
          <span className="text-sm text-gray-500 dark:text-gray-400">
            {filteredData.length} von {data.length} Contacts angezeigt
          </span>
          <button
            onClick={exportToCSV}
            disabled={filteredData.length === 0}
            className="hover:bg-primary-dark focus:ring-primary rounded-md bg-primary px-4 py-2 text-sm font-medium text-white focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:cursor-not-allowed disabled:bg-gray-400"
          >
            CSV Export ({filteredData.length} Einträge)
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 gap-4 lg:grid-cols-2">
        {filteredData.length === 0 ? (
          <div className="col-span-full rounded-lg border border-gray-200 bg-white p-8 text-center shadow-md dark:border-gray-700 dark:bg-gray-800">
            <div className="mb-2 text-lg text-gray-400">🌐</div>
            <h3 className="mb-2 text-lg font-medium text-gray-900 dark:text-white">
              {data.length === 0
                ? "Keine Contacts in der Roaming Matrix"
                : "Keine Contacts entsprechen den Filterkriterien"}
            </h3>
            <p className="text-gray-500 dark:text-gray-400">
              {data.length === 0
                ? "Es wurden noch keine Contacts für die Roaming Matrix markiert. Bearbeiten Sie Contacts und setzen Sie das Flag 'Sichtbar in Roaming Matrix'."
                : "Versuchen Sie, die Filter anzupassen oder zurückzusetzen."}
            </p>
          </div>
        ) : (
          filteredData.map((contact) => <ContactCard key={contact.id} contact={contact} />)
        )}
      </div>
    </div>
  );
};

export default RoamingMatrixPage;
