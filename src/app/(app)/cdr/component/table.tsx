"use client";
import React, { useContext } from "react";
import type { Cdr } from "@prisma/client";
import type { <PERSON><PERSON><PERSON> } from "swr";
import useSWR from "swr";
import { AiOutlineReload } from "react-icons/ai";
import Table, { filterParams } from "~/utils/table/table";
import type { ColDef, ColGroupDef } from "ag-grid-community/dist/lib/entities/colDef";
import {
  dateRenderer,
  dateTimeRenderer,
  monthYearComparator,
  monthYearRenderer,
  twoDecimalPlacesFormatterKwh,
  twoDecimalPlacesFormatterWithCurrency,
} from "~/utils/table/formatter";
import { dayComparator } from "~/utils/comperator/comperators";
import Button from "~/component/button";
import Loading from "~/app/(app)/loading";
import DateContext from "~/component/dateRangePicker/dateContext";

const fetcher: Fetcher<Cdr[], string> = async (url: string) => {
  const response = await fetch(url, { method: "GET" });
  const data = await response.json();
  return data.map((log: any) => ({
    ...log,
    timestamp: new Date(log.timestamp),
  })) as Cdr[];
};

const CdrTableWrapper = () => {
  const { startDate, endDate } = useContext(DateContext);

  const formattedStartDate = startDate.toLocaleString("sv-SE").slice(0, 10);
  const formattedEndDate = endDate.toLocaleString("sv-SE").slice(0, 10); // Format 'YYYY-MM-DD'
  const apiURL = `/api/cdr?startDate=${formattedStartDate}&endDate=${formattedEndDate}`;
  const { data, error, isLoading, mutate } = useSWR<Cdr[], Error>(apiURL, fetcher);

  if (error) return <div>failed to load</div>;
  if (isLoading) return <Loading />;

  const columnDefs: (ColDef | ColGroupDef)[] = [
    {
      field: "CDR_ID",
      headerName: "CDR_ID",
      initialHide: false,
    },
    {
      field: "Start_datetime",
      headerName: "Start_datetime",
      cellRenderer: dateTimeRenderer,
      filter: "agDateColumnFilter",
      filterParams: filterParams,
      initialHide: false,
    },
    {
      field: "End_datetime",
      headerName: "End_datetime",
      cellRenderer: dateTimeRenderer,
      filter: "agDateColumnFilter",
      filterParams: filterParams,
      initialHide: false,
    },
    {
      field: "Duration",
      headerName: "Duration",
      initialHide: false,
    },
    {
      field: "End_datetime",
      headerName: "Tag",
      sort: "asc",
      enableRowGroup: true,
      rowGroup: false,
      hide: true,
      comparator: dayComparator,
      cellRenderer: dateRenderer,
      valueGetter: (params) => {
        const value = new Date(params?.data?.End_datetime);
        value.setHours(0);
        value.setMinutes(0);
        value.setSeconds(0);
        value.setMilliseconds(0);
        return value;
      },
    },
    {
      field: "MonthYear",
      headerName: "Monat/Jahr",
      valueGetter: (params) => {
        const date = new Date(params?.data?.End_datetime);
        const month = date.getMonth() + 1; // Da Monate von 0 bis 11 indexiert sind, fügen wir 1 hinzu
        const year = date.getFullYear();
        return `${month}/${year}`; // Dies gibt z.B. "5/2023" für Mai 2023 zurück
      },
      comparator: monthYearComparator,
      cellRenderer: monthYearRenderer,
    },
    {
      field: "Duration",
      headerName: "Duration",
      initialHide: false,
    },
    {
      field: "DurationInSec",
      headerName: "DurationInSec",
      initialHide: false,
    },
    {
      field: "Volume",
      headerName: "Volume",
      aggFunc: "sum",
      cellRenderer: twoDecimalPlacesFormatterKwh,
    },
    {
      field: "Charge_Point_Address",
      headerName: "Charge_Point_Address",
      enableRowGroup: true,
    },
    {
      field: "Charge_Point_ZIP",
      headerName: "Charge_Point_ZIP",
      initialHide: false,
    },
    {
      field: "Charge_Point_City",
      headerName: "Charge_Point_City",
      initialHide: false,
    },
    {
      field: "Charge_Point_Country",
      headerName: "Charge_Point_Country",
      initialHide: false,
    },
    {
      field: "Charge_Point_Type",
      headerName: "Charge_Point_Type",
      initialHide: false,
    },
    {
      field: "Product_Type",
      headerName: "Product_Type",
      initialHide: false,
    },
    { field: "Tariff_Type", headerName: "Tariff_Type" },
    {
      field: "Authentication_ID",
      headerName: "Authentication_ID",
      initialHide: false,
      enableRowGroup: true,
    },
    {
      field: "Contract_ID",
      headerName: "Contract_ID",
      initialHide: false,
    },
    {
      field: "Meter_ID",
      headerName: "Meter_ID",
      initialHide: false,
    },
    {
      field: "OBIS_Code",
      headerName: "OBIS_Code",
      initialHide: false,
    },
    {
      field: "Charge_Point_ID",
      headerName: "Charge_Point_ID",
      enableRowGroup: true,
      initialHide: false,
    },
    {
      field: "Service_Provider_ID",
      headerName: "Service_Provider_ID",
      enableRowGroup: true,
      initialHide: false,
      rowGroup: false,
    },
    {
      field: "Infra_Provider_ID",
      headerName: "Infra_Provider_ID",
      initialHide: false,
    },
    {
      field: "Timezone",
      headerName: "Timezone",
      initialHide: false,
    },
    {
      field: "LocalStart_datetime",
      headerName: "LocalStart_datetime",
      cellRenderer: dateTimeRenderer,
      filter: "agDateColumnFilter",
      filterParams: filterParams,
      initialHide: false,
    },
    {
      field: "LocalEnd_datetime",
      headerName: "LocalEnd_datetime",
      cellRenderer: dateTimeRenderer,
      filter: "agDateColumnFilter",
      filterParams: filterParams,
      initialHide: false,
    },
    {
      field: "Location_ID",
      headerName: "Location_ID",
      initialHide: false,
    },
    {
      field: "OU_Code",
      headerName: "OU_Code",
      initialHide: false,
    },
    {
      field: "Tariff_Name",
      headerName: "Tariff_Name",
      initialHide: false,
    },
    {
      field: "Start_Tariff",
      headerName: "Start_Tariff",
      initialHide: false,
    },
    {
      field: "Tariff_kWh",
      headerName: "Tariff_kWh",
      initialHide: false,
    },
    {
      field: "Token_OU_Code",
      headerName: "Token_OU_Code",
      initialHide: false,
    },
    {
      field: "Token_OU_Name",
      headerName: "Token_OU_Name",
      initialHide: false,
    },
    {
      field: "OU_Name",
      headerName: "OU_Name",
      initialHide: false,
    },
    {
      field: "Owner",
      headerName: "Owner",
      initialHide: false,
    },
    {
      field: "Operator",
      headerName: "Operator",
      initialHide: false,
    },
    {
      field: "Sub_Operator",
      headerName: "Sub_Operator",
      initialHide: false,
    },
    {
      field: "MeterStart",
      headerName: "MeterStart",
      initialHide: false,
    },
    {
      field: "MeterStop",
      headerName: "MeterStop",
      initialHide: false,
    },
    {
      field: "ExternalReference",
      headerName: "ExternalReference",
      initialHide: false,
    },
    {
      field: "Charging_Time_Cost",
      headerName: "Charging_Time_Cost",
      initialHide: false,
    },
    {
      field: "Parking_Time_Cost",
      headerName: "Parking_Time_Cost",
      initialHide: false,
      aggFunc: "sum",
    },
    {
      field: "ConnectorId",
      headerName: "ConnectorId",
      initialHide: false,
    },
    {
      field: "Calculated_Cost",
      headerName: "Roaming Price",
      aggFunc: "sum",
      cellRenderer: twoDecimalPlacesFormatterWithCurrency,
      initialHide: false,
    },
    {
      field: "cost.cost",
      headerName: "Energy Cost",
      aggFunc: "sum",
      cellRenderer: twoDecimalPlacesFormatterWithCurrency,
      initialHide: false,
    },
    {
      headerName: "Gross Margin",
      aggFunc: "sum",
      cellRenderer: twoDecimalPlacesFormatterWithCurrency,
      initialHide: false,
      valueGetter: (params) => {
        return (params?.data?.Calculated_Cost || 0) - (params?.data?.cost?.cost || 0);
      },
    },
    { field: "billable", headerName: "billable", aggFunc: "count" },
  ];

  const onChangeHandler = () => {
    void mutate();
  };
  return (
    <>
      {data && (
        <div className={"h-[80vh] min-h-[500px]"}>
          <Table
            headerLeft={
              <Button small={true} onClick={() => onChangeHandler()}>
                <AiOutlineReload size={12} className={"mr-1"} />
                Reload
              </Button>
            }
            columnDefs={columnDefs}
            rowData={data}
            gridId={"cdrs"}
            groupIncludeFooter={true}
            groupIncludeTotalFooter={true}
            enableCsvDownload={true}
            csvFileName="cdrs"
          />
        </div>
      )}
    </>
  );
};

export default CdrTableWrapper;
