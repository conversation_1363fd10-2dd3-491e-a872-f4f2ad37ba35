import React from "react";
import prisma from "~/server/db/prisma";
import PageContentWrapper from "~/component/PageContentWrapper";
import Card from "~/component/card";
import OccpMonitorKeyEditForm from "~/app/(app)/occp-monitorKey/[id]/edit/editFormMonitorKey";
import Button from "~/component/button";
import { BsArrowLeft } from "react-icons/bs";

// Option-Typ kompatibel zur Create-Page
type Option = { id: string; label: string; selected: boolean };

type PageProps = {
  params: { id: string };
};

export default async function EditPage({ params }: PageProps) {
  const { id } = params;

  // 1) MonitorKey laden
  const monitorKey = await prisma.occpMonitorKey.findUnique({
    where: { id },
    include: {
      excludedChargePoints: {
        select: { chargePointId: true, chargePoint: { select: { chargePointId: true, displayName: true } } },
      },
    },
  });

  if (!monitorKey) {
    return (
      <PageContentWrapper>
        <Card header_left={<div>OCPP Monitor Key nicht gefunden</div>}>
          <div className="p-6">Kein <PERSON>g mit der ID „{id}“.</div>
        </Card>
      </PageContentWrapper>
    );
  }

  // 2) Vendor/Model Daten (wie in Create-Page)
  const chargePointsVendorModel = await prisma.chargePoint.findMany({
    select: { chargePointVendor: true, chargePointModel: true },
    distinct: ["chargePointVendor", "chargePointModel"],
  });

  const allChargePoints = await prisma.chargePoint.findMany({
    select: { chargePointId: true, displayName: true },
  });

  const vendors: Option[] = [
    ...new Set(chargePointsVendorModel.map(cp => cp.chargePointVendor)),
  ].map(vendor => ({ id: vendor, label: vendor, selected: false }));

  const vendorModelMap: Record<string, Option[]> = {};
  for (const cp of chargePointsVendorModel) {
    vendorModelMap[cp.chargePointVendor] ??= [];
    if (!vendorModelMap[cp.chargePointVendor]?.some(m => m.id === cp.chargePointModel)) {
      vendorModelMap[cp.chargePointVendor]?.push({
        id: cp.chargePointModel,
        label: cp.chargePointModel,
        selected: false,
      });
    }
  }

  // 3) Initial-Objekt für die Edit-Form zusammensetzen
  const initial = {
    id: monitorKey.id,
    chargePointVendor: monitorKey.chargePointVendor ?? "",
    chargePointModel: monitorKey.chargePointModel ?? "",
    configKeyName: monitorKey.configKeyName,
    expectedValues: monitorKey.expectedValues,  // string | null | ggf. CSV
    errorValues: monitorKey.errorValues,        // string | null | ggf. CSV
    excludedChargePoints:
      monitorKey.excludedChargePoints?.map(ecp => ecp.chargePointId) ?? [], // Array von IDs
  };

  return (
    <PageContentWrapper>
      <Card
        header_left={
          <div className="flex items-center gap-2">
            OCPP Monitor Key bearbeiten
          </div>

        }
      >

        <OccpMonitorKeyEditForm
          initial={initial}
          chargePointVendors={vendors}
          vendorModelMap={vendorModelMap}
          allChargePoints={allChargePoints}
        />
        <div className="mt-6 flex justify-start">
          <form action="/occp-monitorKey" method="get">
            <Button type="submit" >
            <span className="inline-flex items-center gap-2">
              <BsArrowLeft aria-hidden />
              <span>Zurück</span>
            </span>
            </Button>
          </form>
        </div>
      </Card>
    </PageContentWrapper >
  );
}
