
import Card from "~/component/card";
import PageContentWrapper from "~/component/PageContentWrapper";
import React from "react";
import prisma from "../../../server/db/prisma";
import OccpMonitorKeyForm from "./OccpMonitorKeyForm";
import MonitorKeyBatchForm from "~/app/(app)/occp-monitorKey/manualCheck/monitorKeyBatchForm";
import MonitorKeyChangeForm from "~/app/(app)/occp-monitorKey/manualCheck/monitorKeyChangeForm";

import OccpMonitorKeyTable from "~/app/(app)/occp-monitorKey/OccpMonitorKeyRow";




// Option-Typ für Dropdowns
type Option = { id: string; label: string; selected: boolean };

export default async function Page() {
  // Alle Vendor-Model-Kombinationen holen
  const chargePointsVendorModel = await prisma.chargePoint.findMany({
    select: {
      chargePointVendor: true,
      chargePointModel: true,
    },
    distinct: ['chargePointVendor', 'chargePointModel'],
  });

  const allChargePoints = await prisma.chargePoint.findMany({
  select: {
      chargePointId: true,
      displayName: true,
  }
  });

  // Vendor-Liste
  const vendors: Option[] = [
    ...new Set(chargePointsVendorModel.map(cp => cp.chargePointVendor))
  ].map(vendor => ({ id: vendor, label: vendor, selected: false }));

  // Mapping: Vendor → passende Models[]
  const vendorModelMap: Record<string, Option[]> = {};
  chargePointsVendorModel.forEach(cp => {
    if (!vendorModelMap[cp.chargePointVendor]) {
      vendorModelMap[cp.chargePointVendor] = [];
    }
    // Keine doppelten Models
    if (!vendorModelMap[cp.chargePointVendor]?.some(m => m.id === cp.chargePointModel)) {
      vendorModelMap[cp.chargePointVendor]?.push({
        id: cp.chargePointModel,
        label: cp.chargePointModel,
        selected: false,
      });
    }
  });

  // Bestehende Monitor-Keys
  const occpMonitorKeys = await prisma.occpMonitorKey.findMany({
    include: {
      excludedChargePoints: {
        include: {
          chargePoint: true, // dann kannst du displayName/chargePointId anzeigen
        },
      },
    },
  });
  const rowDataMapped = occpMonitorKeys.map(key => ({
    ...key,
    excludedChargePoints: key.excludedChargePoints
      .map(ecp =>
        ecp.chargePoint
          ? `${ecp.chargePoint.chargePointId} - ${ecp.chargePoint.displayName}`
          : ecp.chargePointId // Fallback falls deleted
      )
      .join(", "),
  }));







    const allChrargpoints =  await prisma.chargePoint.findMany({
        select: {
            chargePointId: true,
            displayName: true,
            chargePointVendor: true,
            chargePointModel: true,
        },
    });

    const locationsRaw = await prisma.location.findMany({
        select: {
            id: true,
            name: true,
            evses: {
                select: {
                    chargePoint: {
                        select: {
                            chargePointId: true,
                            displayName: true,
                            chargePointVendor: true,
                            chargePointModel: true,
                        },
                    },
                },
            },
        },
    });


    const locationsMinimal = locationsRaw.map(l => {
        const map = new Map<string, {
            chargePointId: string;
            displayName: string;
            chargePointVendor?: string;
            chargePointModel?: string;
        }>();
        for (const e of l.evses) {
            const cp = e.chargePoint;
            if (cp) {
                map.set(cp.chargePointId, {
                    chargePointId: cp.chargePointId,
                    displayName: cp.displayName ?? cp.chargePointId,
                    chargePointVendor: cp.chargePointVendor,
                    chargePointModel: cp.chargePointModel,
                });
            }
        }
        return {
            id: l.id,
            name: l.name,
            chargePoints: Array.from(map.values()),
        };
    });







    return (
    <PageContentWrapper>
      <Card
        header_left={
          <div className="flex items-center gap-2">
            OCPP Monitor Keys ({occpMonitorKeys.length})
          </div>
        }
     >
        <OccpMonitorKeyForm
          chargePointVendors={vendors}
          vendorModelMap={vendorModelMap}
          allChargePoints={allChargePoints}
        />
        <OccpMonitorKeyTable rowData={rowDataMapped} />
      </Card>

        <Card className="mt-6"
              header_left={
                  <div className="flex items-center gap-2">
                      Manuelle Prüfung von Attributen
                  </div>
              }>

            <MonitorKeyBatchForm chargePoints={allChrargpoints} postUrl="/api/occpMonitorKey/manualCheck" locations={locationsMinimal} />

        </Card>


      <Card className="mt-6"
            header_left={
              <div className="flex items-center gap-2">
                Manuelle Setzten von Attributen
              </div>
            }>

        <MonitorKeyChangeForm chargePoints={allChrargpoints} postUrl="/api/occpMonitorKey/manualChange" locations={locationsMinimal} />

      </Card>

    </PageContentWrapper>
  );
}
