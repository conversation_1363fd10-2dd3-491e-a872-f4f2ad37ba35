"use client";

import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import Dropdown, { Option } from "~/app/(app)/util/Dropdown";
import Button from "~/component/button";
import { useRouter } from "next/navigation";
import { BsBuildingGear, BsEvStation } from "react-icons/bs";
import MultiSelect from "~/component/MultiSelect";
import { MultiValue } from "react-select";
import TagsInput from "~/app/(app)/component/TagsInput";
import {OccpMonitorKeyFormData, Props} from "~/types/monitorKey/OccpMonitorKeyTypes";



const DRAFT_GLOW =
  "ring-2 ring-orange-300/60 bg-orange-50/40 rounded-xl shadow-[0_0_1.2rem_rgba(249,115,22,0.25)]";
const SOFT_PULSE = "animate-[pulse_2s_ease-in-out_infinite]";

function onBlurSection(
  e: React.FocusEvent<HTMLDivElement>,
  hasDraft: boolean,
  setTouched: React.Dispatch<React.SetStateAction<boolean>>
) {
  const leavingContainer = !e.currentTarget.contains(e.relatedTarget as Node);
  if (leavingContainer && hasDraft) setTouched(true);
}

const OccpMonitorKeyForm: React.FC<Props> = ({
                                               chargePointVendors,
                                               vendorModelMap,
                                               allChargePoints,
                                             }) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitMessage, setSubmitMessage] = useState<string | null>(null);
  const [submitSuccess, setSubmitSuccess] = useState<boolean | null>(null);

  // Tags & Drafts
  const [expectedTags, setExpectedTags] = useState<string[]>([]);
  const [errorTags, setErrorTags] = useState<string[]>([]);
  const [expectedDraft, setExpectedDraft] = useState("");
  const [errorDraft, setErrorDraft] = useState("");

  const [expectedTouched, setExpectedTouched] = useState(false);
  const [errorTouched, setErrorTouched] = useState(false);

  // Vendor/Model
  const [selectedVendor, setSelectedVendor] = useState<Option | null>(null);
  const [modelOptions, setModelOptions] = useState<Option[]>([]);
  const [selectedModel, setSelectedModel] = useState<Option | null>(null);

  // Multiselect (ChargePoints)
  const [selectedChargePoints, setSelectedChargePoints] =
    useState<MultiValue<{ value: string; label: string }>>([]);
  const [chargePointsError, setChargePointsError] = useState<string | null>(null);

  const router = useRouter();

  const {
    register,
    handleSubmit,
    setValue,
    reset,
    getValues,
    formState: { errors },
    clearErrors,
  } = useForm<OccpMonitorKeyFormData>({
    defaultValues: {
      chargePointVendor: "",
      chargePointModel: "",
      configKeyName: "",
      expectedValues: "",
      errorValues: "",

    },
  });

  const getLabel = (cp: { chargePointId: string; displayName: string }) =>
    cp.chargePointId === cp.displayName
      ? cp.chargePointId
      : `${cp.chargePointId} - ${cp.displayName}`;

  const chargePointOptions = allChargePoints.map((cp) => ({
    value: cp.chargePointId,
    label: getLabel(cp),
  }));

  // Vendor -> Models filtern
  const handleVendorChange = (id: string) => {
    const found = chargePointVendors.find((v) => v.id === id) ?? null;
    setSelectedVendor(found);
    setValue("chargePointVendor", found?.id || "");

    const models = found ? vendorModelMap[found.id] || [] : [];
    setModelOptions(models);
    setSelectedModel(null);
    setValue("chargePointModel", "");
  };

  // Model wählen
  const handleModelChange = (id: string) => {
    const found = modelOptions.find((m) => m.id === id) ?? null;
    setSelectedModel(found);
    setValue("chargePointModel", found?.id || "");
  };

  // Tags in Hidden-Felder spiegeln (ohne sofortige Revalidation)
  useEffect(() => {
    setValue("expectedValues", expectedTags.join(","), {
      shouldValidate: false,   // Meldung soll erst beim Speichern erscheinen
      shouldDirty: true,
    });
  }, [expectedTags, setValue]);

  useEffect(() => {
    setValue("errorValues", errorTags.join(","), {
      shouldValidate: false,   // Meldung soll erst beim Speichern erscheinen
      shouldDirty: true,
    });
  }, [errorTags, setValue]);

  // Submit
  const onSubmit = async (data: OccpMonitorKeyFormData) => {
    // Sanfter Schutz gegen unbestätigte Drafts
    if (expectedDraft.trim() || errorDraft.trim()) {
      setSubmitMessage(
        "Du hast noch unbestätigte Eingaben. Bitte mit „+” übernehmen oder löschen."
      );
      setSubmitSuccess(false);
      return;
    }

    setIsSubmitting(true);
    setSubmitMessage(null);

    const expectedValuesString = expectedTags.join(",");
    const errorValuesString = errorTags.join(",");

    const body = {
      ...data,
      expectedValues: expectedValuesString,
      errorValues: errorValuesString,
      chargePointIds: selectedChargePoints.map((cp) => cp.value),
    };

    try {
      const response = await fetch("/api/occpMonitorKey", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(body),
      });

      if (!response.ok) {
        const errData = await response.json().catch(() => null);
        setSubmitSuccess(false);
        if (response.status === 409) {
          setSubmitMessage("Diese Kombination wurde bereits erstellt.");
        } else if (errData?.error) {
          setSubmitMessage(errData.error);
        } else {
          setSubmitMessage("Fehler beim Speichern!");
        }
        return;
      }

      setSubmitMessage("Eintrag erfolgreich gespeichert!");
      setSubmitSuccess(true);

      // Reset
      router.refresh();
      reset();
      setExpectedTags([]);
      setErrorTags([]);
      setExpectedDraft("");
      setErrorDraft("");
      setExpectedTouched(false);
      setErrorTouched(false);
      setSelectedVendor(null);
      setSelectedModel(null);
      setModelOptions([]);
      setSelectedChargePoints([]);
      setChargePointsError(null);
    } catch (_err) {
      setSubmitMessage("Netzwerkfehler oder Server nicht erreichbar!");
      setSubmitSuccess(false);
    } finally {
      setIsSubmitting(false);
    }
  };

  useEffect(() => {
    // Wenn mind. ein Tag vorhanden ist, entferne die Fehlermeldungen sofort
    if (expectedTags.length > 0 || errorTags.length > 0) {
      clearErrors(["expectedValues", "errorValues"]);
    }
    // Wichtig: hier NICHT aktiv validieren, nur Errors wegräumen.
  }, [expectedTags, errorTags, clearErrors]);

  return (
    <div className="p-6">
      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* Vendor */}
          <div className="flex flex-col">
            <label className="mb-2 text-sm font-bold text-slate-700 dark:text-white/80">
              ChargePoint Vendor
            </label>
            <Dropdown
              options={chargePointVendors.map((o) => ({
                ...o,
                selected: selectedVendor?.id === o.id,
              }))}
              onChange={handleVendorChange}
              onDelete={() => { /* noop */ }}
              canDelete={false}
              placeHolder="Bitte wählen..."
              icon={<BsBuildingGear className="mr-1 items-center" />}
              title="Hersteller"
              searchable={true}
              searchPlaceholder="Vendor suchen..."
            />
            <input
              {...register("chargePointVendor", { required: "Pflichtfeld" })}
              type="hidden"
            />
            {errors.chargePointVendor && (
              <span className="text-xs text-red-600 mt-1">
                {errors.chargePointVendor.message}
              </span>
            )}
          </div>

          {/* Model */}
          <div className="flex flex-col">
            <label className="mb-2 text-sm font-bold text-slate-700 dark:text-white/80">
              ChargePoint Model
            </label>
            <div className={selectedVendor ? "" : "opacity-50 pointer-events-none"}>
              <Dropdown
                options={modelOptions.map((o) => ({
                  ...o,
                  selected: selectedModel?.id === o.id,
                }))}
                onChange={handleModelChange}
                onDelete={() => { /* noop */ }}
                canDelete={false}
                placeHolder={selectedVendor ? "Bitte wählen..." : "Erst Vendor wählen"}
                title="Typ"
                icon={<BsEvStation className="mr-1 items-center" />}
                searchable={true}
                searchPlaceholder="Model suchen..."
              />
            </div>
            <input {...register("chargePointModel")} type="hidden" />
            {errors.chargePointModel && (
              <span className="text-xs text-red-600 mt-1">
                {errors.chargePointModel.message}
              </span>
            )}
          </div>

          {/* Multiselect Excluded ChargePoints (optional) */}
          <div className="flex flex-col">
            <label className="mb-2 text-sm font-bold text-slate-700 dark:text-white/80">
              Excluded ChargePoints auswählen (optional)
            </label>
            <MultiSelect
              options={chargePointOptions}
              value={selectedChargePoints}
              onChange={setSelectedChargePoints}
            />
            {chargePointsError && (
              <span className="text-xs text-red-600 mt-1">{chargePointsError}</span>
            )}
          </div>

          {/* Config Key Name */}
          <div className="flex flex-col">
            <label className="mb-2 text-sm font-bold text-slate-700 dark:text-white/80">
              Config Key Name
            </label>
            <input
              {...register("configKeyName", { required: "Pflichtfeld" })}
              type="text"
              className="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-white dark:bg-slate-800 dark:border-slate-600"
            />
            {errors.configKeyName && (
              <span className="text-xs text-red-600 mt-1">
                {errors.configKeyName.message}
              </span>
            )}
          </div>

          {/* Erwartete Werte (TagsInput) */}
          <div
            className={`flex flex-col ${expectedDraft.trim() ? SOFT_PULSE : ""}`}
            onBlurCapture={(e) =>
              onBlurSection(e, !!expectedDraft.trim(), setExpectedTouched)
            }
          >
            <label className="mb-2 text-sm font-bold text-slate-700 dark:text-white/80">
              Erwartete Werte
            </label>
            <TagsInput
              value={expectedTags}
              onChange={setExpectedTags}
              onDraftChange={setExpectedDraft}
              draftClassName={DRAFT_GLOW}
              placeholder="Wert hinzufügen..."
            />

            {/* Hidden + Cross-Field-Validation */}
            <input
              type="hidden"
              {...register("expectedValues", {
                validate: (val) => {
                  const other = getValues("errorValues");
                  const hasAny =
                    (val?.trim()?.length ?? 0) > 0 ||
                    (other?.trim()?.length ?? 0) > 0;
                  return (
                    hasAny ||
                    "Mindestens ein Feld (Erwartete Werte ODER Fehler Werte) muss befüllt sein."
                  );
                },
              })}
            />

            {expectedDraft.trim() && !expectedTouched && (
              <span className="mt-1 text-xs text-orange-600/80">
                „+” drücken, um den Wert zu übernehmen – sonst wird nicht gespeichert.
              </span>
            )}
            {expectedDraft.trim() && expectedTouched && (
              <span className="mt-2 text-xs text-orange-700">
                Unbestätigter Entwurf erkannt. Bitte mit „+” übernehmen oder Feld
                leeren.
              </span>
            )}
            {errors.expectedValues && (
              <span className="text-xs text-red-600 mt-1">
                {errors.expectedValues.message}
              </span>
            )}
          </div>

          {/* Fehler Werte (TagsInput) */}
          <div
            className={`flex flex-col ${errorDraft.trim() ? SOFT_PULSE : ""}`}
            onBlurCapture={(e) =>
              onBlurSection(e, !!errorDraft.trim(), setErrorTouched)
            }
          >
            <label className="mb-2 text-sm font-bold text-slate-700 dark:text-white/80">
              Fehler Werte
            </label>
            <TagsInput
              value={errorTags}
              onChange={setErrorTags}
              onDraftChange={setErrorDraft}
              draftClassName={DRAFT_GLOW}
              placeholder="Fehlerwert hinzufügen..."
            />

            {/* Hidden + Cross-Field-Validation (spiegelbildlich) */}
            <input
              type="hidden"
              {...register("errorValues", {
                validate: (val) => {
                  const other = getValues("expectedValues");
                  const hasAny =
                    (val?.trim()?.length ?? 0) > 0 ||
                    (other?.trim()?.length ?? 0) > 0;
                  return (
                    hasAny ||
                    "Mindestens ein Feld (Erwartete Werte ODER Fehler Werte) muss befüllt sein."
                  );
                },
              })}
            />

            {errorDraft.trim() && !errorTouched && (
              <span className="mt-1 text-xs text-orange-600/80">
                Unübernommener Text – „+” drücken oder Feld leeren.
              </span>
            )}
            {errorDraft.trim() && errorTouched && (
              <span className="mt-2 text-xs text-orange-700">
                Du hast hier noch einen unbestätigten Eintrag. Mit „+” übernehmen.
              </span>
            )}
            {errors.errorValues && (
              <span className="text-xs text-red-600 mt-1">
                {errors.errorValues.message}
              </span>
            )}
          </div>
        </div>

        {submitMessage && (
          <div
            className={`mt-6 p-4 rounded-lg text-sm ${
              submitSuccess === false
                ? "bg-red-100 text-red-700 border border-red-300"
                : "bg-green-100 text-green-700 border border-green-300"
            }`}
          >
            {submitMessage}
          </div>
        )}

        <div className="mt-8 flex justify-end gap-4 items-center">
          <Button
            type="submit"
            disabled={isSubmitting}
            className={isSubmitting ? "opacity-50 cursor-not-allowed" : ""}
          >
            {isSubmitting ? "Speichert..." : "Speichern"}
          </Button>
        </div>
      </form>
    </div>
  );
};

export default OccpMonitorKeyForm;
