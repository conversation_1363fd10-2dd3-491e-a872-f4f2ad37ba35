"use client";
import { CiLocationOn } from "react-icons/ci";

import React, { useMemo, useState } from "react";
import Button from "~/component/button";
import Dropdown from "~/app/(app)/util/Dropdown";
import type { ChargePoint, LocationForUi, QueueItem, ApiResult } from "~/types/monitorKey/monitorKey";
import { BsEvStation } from "react-icons/bs";
import { IoAddCircleOutline } from "react-icons/io5";

export default function MonitorKeyBatchForm({
                                              chargePoints,
                                              locations = [],
                                              postUrl = "/api/occpMonitorKey/manualCheck",
                                            }: {
  chargePoints: ChargePoint[];
  locations?: LocationForUi[];
  postUrl?: string;
}) {
  // Auswahl & Eingaben
  const [selectedCpId, setSelectedCpId] = useState("");
  const [selectedLocationId, setSelectedLocationId] = useState("");
  const [monitoringKey, setMonitoringKey] = useState("");
  const [expectedValue, setExpectedValue] = useState("");
  const [errorValue, setErrorValue] = useState("");

  // Queue & Ergebnis
  const [queue, setQueue] = useState<QueueItem[]>([]);
  const [submitting, setSubmitting] = useState(false);
  const [results, setResults] = useState<ApiResult[] | null>(null);

  // Hilfen
  const canAddSingle = useMemo(
    () => selectedCpId.trim() !== "" && monitoringKey.trim() !== "",
    [selectedCpId, monitoringKey]
  );
  const canAddLocation = useMemo(
    () => selectedLocationId.trim() !== "" && monitoringKey.trim() !== "",
    [selectedLocationId, monitoringKey]
  );

  const resetInputs = () => {
    setMonitoringKey("");
    setExpectedValue("");
    setErrorValue("");
  };

  const resetSelections = () => {
    setSelectedCpId("");
    setSelectedLocationId("");
  };

  const toQueueItem = (cpId: string): QueueItem => ({
    id: crypto.randomUUID(),
    chargePointId: cpId,
    monitoringKey: monitoringKey.trim(),
    expectedValue: expectedValue.trim() || undefined,
    errorValue: errorValue.trim() || undefined,
  });

  const addOrUpsert = (items: QueueItem[]) => {
    setQueue(prev => {
      const next = [...prev];
      for (const it of items) {
        const idx = next.findIndex(
          x => x.chargePointId === it.chargePointId && x.monitoringKey === it.monitoringKey
        );
        if (idx >= 0) {
          // vorhandenen Eintrag aktualisieren (z.B. neue expected/error Werte)
          const updated = { ...next[idx], ...it };
          next.splice(idx, 1);       // alten entfernen
          next.unshift(updated);     // aktualisiert nach vorne legen
        } else {
          next.unshift(it);          // neuer Eintrag
        }
      }
      return next;
    });
  };

  // Ein einzelner CP wird zur Queue hinzugefügt
  const handleAddSingle = () => {
    if (!canAddSingle) return;
    addOrUpsert([toQueueItem(selectedCpId.trim())]);
    // Anzeige zurücksetzen
    resetSelections();
    resetInputs();
  };

  // Alle CPs einer Location werden zur Queue hinzugefügt
  const handleAddLocation = () => {
    if (!canAddLocation) return;
    const loc = locations.find(l => l.id === selectedLocationId);
    if (!loc) return;
    const items = loc.chargePoints.map(cp => toQueueItem(cp.chargePointId));
    addOrUpsert(items);
    // Anzeige zurücksetzen
    resetSelections();
    resetInputs();
  };

  // Queue prüfen (n viele Requests)
  const handleSubmitQueue = async () => {
    if (queue.length === 0 || submitting) return;
    setSubmitting(true);
    setResults(null);

    try {
      const payloads = queue.map(q => ({
        chargePointId: q.chargePointId,
        monitoringKey: (q as any).monitoringKey ?? (q as any).configKeyName,
        expectedValues: q.expectedValue ?? "",
        errorValues: q.errorValue ?? "",
      }));

      const settled = await Promise.allSettled(
        payloads.map(p =>
          fetch(postUrl, {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify(p),
          }).then(res => res.json())
        )
      );

      const collected: ApiResult[] = [];
      for (const s of settled) {
        if (s.status === "fulfilled" && s.value?.ok && Array.isArray(s.value.results)) {
          collected.push(...(s.value.results as ApiResult[]));
        } else if (s.status === "fulfilled") {
          collected.push({
            status: "fetch-failed",
            key: "unknown",
            chargePointId: "unknown",
            message: JSON.stringify(s.value),
          });
        } else {
          collected.push({
            status: "fetch-failed",
            key: "unknown",
            chargePointId: "unknown",
            message: String(s.reason),
          });
        }
      }

      setResults(collected);
    } catch (e) {
      setResults([
        { status: "fetch-failed", key: "unknown", chargePointId: "unknown", message: String(e) },
      ]);
    } finally {
      setSubmitting(false);
      // Anzeige zurücksetzen (Auswahlen & Inputs), Queue bleibt bestehen
      resetSelections();
      resetInputs();
    }
  };

  const badge = (r: ApiResult) => {
    if (r.status === "ok") return <span className="rounded bg-green-600 px-2 py-0.5 text-xs text-white">OK</span>;
    if (r.status === "error" && r.reason === "listed-error")
      return <span className="rounded bg-red-600 px-2 py-0.5 text-xs text-white">ERROR</span>;
    if (r.status === "error" && r.reason === "unexpected")
      return <span className="rounded bg-yellow-600 px-2 py-0.5 text-xs text-white">UNEXPECTED</span>;
    if (r.status === "not-found")
      return <span className="rounded bg-slate-500 px-2 py-0.5 text-xs text-white">KEY NOT FOUND</span>;
    if (r.status === "no-payload")
      return <span className="rounded bg-slate-500 px-2 py-0.5 text-xs text-white">NO PAYLOAD</span>;
    return <span className="rounded bg-slate-700 px-2 py-0.5 text-xs text-white">FETCH FAILED</span>;
  };

  const cpLabel = (cp: { chargePointId: string; displayName?: string | null }) =>
    cp.displayName && cp.displayName !== cp.chargePointId
      ? `${cp.chargePointId} - ${cp.displayName}`
      : cp.chargePointId;

  // Map für schnellen Lookup in Queue/Ergebnissen
  const cpLabelById = useMemo(() => {
    const m: Record<string, string> = {};
    for (const cp of chargePoints) m[cp.chargePointId] = cpLabel(cp);
    return m;
  }, [chargePoints]);

  // Dropdown-Optionen
  const cpOptions = useMemo(
    () =>
      chargePoints.map(cp => ({
        id: cp.chargePointId,
        label: cpLabel(cp),
        selected: selectedCpId === cp.chargePointId,
      })),
    [chargePoints, selectedCpId]
  );

  const locationOptions = useMemo(
    () =>
      locations.map(loc => ({
        id: loc.id,
        label: `${loc.name} (${loc.chargePoints.length})`,
        selected: selectedLocationId === loc.id,
      })),
    [locations, selectedLocationId]
  );

  return (
    <div className="flex flex-col gap-6">
      {/* Auswahlzeile */}
      <div className="grid grid-cols-1 gap-3 md:grid-cols-12">
        {/* ChargePoint (einzeln) */}
        <div className="col-span-1 md:col-span-6">
          <label className="mb-1 block text-sm font-semibold text-slate-700 dark:text-white/80">
            ChargePoint (Einzeln)
          </label>
          <div className={selectedLocationId.trim() !== "" ? "opacity-50 pointer-events-none" : ""}>
            <Dropdown
              title="ChargePoint wählen"
              options={cpOptions}
              onChange={(id: string) => {
                setSelectedCpId(id);
                if (id) setSelectedLocationId(""); // andere Auswahl leeren
              }}
              onDelete={() => {""}}
              canDelete={false}
              placeHolder="— auswählen —"
              searchable={true}
              searchPlaceholder="ChargePoint suchen…"
              className="w-full text-base"
              icon={<BsEvStation className="mr-1 items-center" />}

            />
          </div>
        </div>

        {/* Location (Batch) */}
        <div className="col-span-1 md:col-span-6">
          <label className="mb-1 block text-sm font-semibold text-slate-700 dark:text-white/80">
            Location (Batch)
          </label>
          <div className={selectedCpId.trim() !== "" ? "opacity-50 pointer-events-none" : ""}>
            <Dropdown
              title="Location wählen"
              options={locationOptions}
              onChange={(id: string) => {
                setSelectedLocationId(id);
                if (id) setSelectedCpId(""); // andere Auswahl leeren
              }}
              onDelete={() => {""}}
              canDelete={false}
              icon={ <CiLocationOn />}

              placeHolder="— Location auswählen —"
              searchable={true}
              searchPlaceholder="Location suchen…"
              className="w-full text-base"

            />
          </div>
        </div>

        {/* Monitoring / Expected / Error */}
        <div className="row-start-2 md:row-start-2 md:col-span-4">
          <label className="mb-1 block text-sm font-semibold text-slate-700 dark:text-white/80">
            Monitoring Key (Pflicht)
          </label>
          <input
            value={monitoringKey}
            onChange={e => setMonitoringKey(e.target.value)}
            className="w-full rounded-lg border border-slate-300 p-2 dark:border-slate-600 dark:bg-slate-800"
            placeholder="z. B. HeartbeatInterval"
          />
        </div>

        <div className="row-start-2 md:row-start-2 md:col-span-4">
          <label className="mb-1 block text-sm font-semibold text-slate-700 dark:text-white/80">
            Expected Value (optional)
          </label>
          <input
            value={expectedValue}
            onChange={e => setExpectedValue(e.target.value)}
            className="w-full rounded-lg border border-slate-300 p-2 dark:border-slate-600 dark:bg-slate-800"
            placeholder="z. B. 30 oder 'A,B,C'"
          />
        </div>

        <div className="row-start-2 md:row-start-2 md:col-span-4">
          <label className="mb-1 block text-sm font-semibold text-slate-700 dark:text-white/80">
            Error Value (optional)
          </label>
          <input
            value={errorValue}
            onChange={e => setErrorValue(e.target.value)}
            className="w-full rounded-lg border border-slate-300 p-2 dark:border-slate-600 dark:bg-slate-800"
            placeholder="z. B. 0 oder 'X,Y'"
          />
        </div>
      </div>

      {/* Aktionen */}
      <div className="flex flex-wrap items-center gap-3">
        <Button
          onClick={handleAddSingle}
          disabled={!canAddSingle}
          className="bg-green-600 hover:brightness-95 disabled:brightness-90"
          title="Einzelnen ChargePoint zur Liste hinzufügen"
        >
          <IoAddCircleOutline /> ChargePoint
        </Button>

        <Button
          onClick={handleAddLocation}
          disabled={!canAddLocation}
          className="bg-green-600 hover:brightness-95 disabled:brightness-90"
          title="Alle ChargePoints der Location zur Liste hinzufügen"
        >
          <IoAddCircleOutline /> Location (alle CPs)
        </Button>

        <Button
          onClick={handleSubmitQueue}
          disabled={queue.length === 0 || submitting}
          className="bg-blue-600 hover:brightness-95 disabled:brightness-90"
        >
          {submitting ? "Prüfe..." : `Queue prüfen (${queue.length})`}
        </Button>
      </div>

      {/* Zwischenablage */}
      {queue.length > 0 && (
        <div className="rounded-lg border border-slate-300 bg-slate-100 p-3 dark:border-slate-700 dark:bg-slate-800/50">
          <div className="mb-2 text-sm font-semibold text-slate-700 dark:text-white/80">
            Zwischenablage ({queue.length})
          </div>
          <ul className="flex flex-col gap-2">
            {queue.map(item => {
              const cp = chargePoints.find(c => c.chargePointId === item.chargePointId);
              return (
                <li
                  key={item.id}
                  className="flex flex-wrap items-center justify-between gap-2 rounded-lg bg-slate-200 p-2 dark:bg-slate-900/60"
                >
                  <div className="flex flex-col">
                    <div className="text-sm font-semibold">{cp?.displayName ?? item.chargePointId}</div>
                    <div className="text-xs text-slate-600 dark:text-slate-400">
                      key: <span className="font-mono">{item.monitoringKey}</span>
                      {item.expectedValue && <> • expected: <span className="font-mono">{item.expectedValue}</span></>}
                      {item.errorValue && <> • error: <span className="font-mono">{item.errorValue}</span></>}
                    </div>
                  </div>
                  <Button
                    small
                    className="bg-red-600 hover:brightness-95"
                    onClick={() => setQueue(prev => prev.filter(x => x.id !== item.id))}
                  >
                    entfernen
                  </Button>
                </li>
              );
            })}
          </ul>
        </div>
      )}

      {/* Ergebnisse */}
      {results && (
        <div className="rounded-lg border border-slate-300 bg-white p-3 dark:border-slate-700 dark:bg-slate-800">
          <div className="mb-2 text-sm font-semibold text-slate-700 dark:text-white/80">
            Prüfergebnisse ({results.length})
          </div>
          <ul className="flex flex-col gap-2">
            {results.map((r, i) => (
              <li
                key={i}
                className="flex flex-wrap items-start justify-between gap-2 rounded-md border border-slate-200 p-2 dark:border-slate-700"
              >
                <div className="flex flex-col">
                  <div className="flex items-center gap-2">
                    {badge(r)}
                    <span className="text-sm font-semibold">
                      {r.chargePointId} — <span className="font-mono">{r.key}</span>
                    </span>
                  </div>
                  <div className="text-xs text-slate-600 dark:text-slate-400">
                    {r.status === "ok" && <>Wert: <span className="font-mono">{(r as any).value}</span></>}
                    {r.status === "error" && <>Wert: <span className="font-mono">{(r as any).value}</span></>}
                    {r.status === "fetch-failed" && <>Fehler: {(r as any).message}</>}
                    {r.status === "not-found" && <>Schlüssel nicht im Payload</>}
                    {r.status === "no-payload" && <>Kein/ungültiger Payload</>}
                  </div>
                </div>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
}
