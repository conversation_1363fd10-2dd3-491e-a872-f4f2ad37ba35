"use client";

import React, { useMemo, useState } from "react";
import Button from "~/component/button";
import Dropdown from "~/app/(app)/util/Dropdown";
import type { ChargePoint, LocationForUi, QueueItem, ApiResult } from "~/types/monitorKey/monitorKey";
import {CiLocationOn} from "react-icons/ci";
import {BsEvStation} from "react-icons/bs";
import {IoAddCircleOutline} from "react-icons/io5";

/** Optional: Passe dein QueueItem/ApiResult-Typ an dein Set-API-Schema an */
type SetQueueItem = QueueItem & {
  setValue: string;
};

export default function MonitorKeyChangeForm({
                                               chargePoints,
                                               locations = [],
                                               postUrl = "/api/occpMonitorKey/manualChange",
                                             }: {
  chargePoints: ChargePoint[];
  locations?: LocationForUi[];
  postUrl?: string;
}) {
  // Auswahl & Eingaben
  const [selectedCpId, setSelectedCpId] = useState("");
  const [selectedLocationId, setSelectedLocationId] = useState("");
  const [monitoringKey, setMonitoringKey] = useState("");
  const [setValueInput, setSetValueInput] = useState("");

  // Queue & Ergebnis
  const [queue, setQueue] = useState<SetQueueItem[]>([]);
  const [submitting, setSubmitting] = useState(false);
  const [results, setResults] = useState<ApiResult[] | null>(null);

  // Hilfen (Pflichtfelder)
  const baseReady = monitoringKey.trim() !== "" && setValueInput.trim() !== "";
  const canAddSingle = useMemo(
    () => selectedCpId.trim() !== "" && baseReady,
    [selectedCpId, baseReady]
  );
  const canAddLocation = useMemo(
    () => selectedLocationId.trim() !== "" && baseReady,
    [selectedLocationId, baseReady]
  );

  const resetInputs = () => {
    setMonitoringKey("");
    setSetValueInput("");
  };

  const resetSelections = () => {
    setSelectedCpId("");
    setSelectedLocationId("");
  };

  const cpLabel = (cp: { chargePointId: string; displayName?: string | null }) =>
    cp.displayName && cp.displayName !== cp.chargePointId
      ? `${cp.chargePointId} - ${cp.displayName}`
      : cp.chargePointId;

  // Dropdown-Optionen
  const cpOptions = useMemo(
    () =>
      chargePoints.map(cp => ({
        id: cp.chargePointId,
        label: cpLabel(cp),
        selected: selectedCpId === cp.chargePointId,
      })),
    [chargePoints, selectedCpId]
  );

  const locationOptions = useMemo(
    () =>
      locations.map(loc => ({
        id: loc.id,
        label: `${loc.name} (${loc.chargePoints.length})`,
        selected: selectedLocationId === loc.id,
      })),
    [locations, selectedLocationId]
  );

  const toQueueItem = (cpId: string): SetQueueItem => ({
    id: crypto.randomUUID(),
    chargePointId: cpId,
    monitoringKey: monitoringKey.trim(),
    setValue: setValueInput.trim(),
  });

  const addOrUpsert = (items: SetQueueItem[]) => {
    setQueue(prev => {
      const next = [...prev];
      for (const it of items) {
        const idx = next.findIndex(
          x => x.chargePointId === it.chargePointId && x.monitoringKey === it.monitoringKey
        );
        if (idx >= 0) {
          const updated = { ...next[idx], ...it };
          next.splice(idx, 1);
          next.unshift(updated);
        } else {
          next.unshift(it);
        }
      }
      return next;
    });
  };

  // Ein einzelner CP wird zur Queue hinzugefügt
  const handleAddSingle = () => {
    if (!canAddSingle) return;
    addOrUpsert([toQueueItem(selectedCpId.trim())]);
    resetSelections();
    resetInputs();
  };

  // Alle CPs einer Location werden zur Queue hinzugefügt
  const handleAddLocation = () => {
    if (!canAddLocation) return;
    const loc = locations.find(l => l.id === selectedLocationId);
    if (!loc) return;
    const items = loc.chargePoints.map(cp => toQueueItem(cp.chargePointId));
    addOrUpsert(items);
    resetSelections();
    resetInputs();
  };

  // Queue ausführen (n Requests)
  const handleSubmitQueue = async () => {
    if (queue.length === 0 || submitting) return;
    setSubmitting(true);
    setResults(null);

    try {
      const payloads = queue.map(q => ({
        chargePointId: q.chargePointId,
        monitoringKey: (q as any).monitoringKey ?? (q as any).configKeyName,
        setValue: q.setValue,
      }));

      const settled = await Promise.allSettled(
        payloads.map(p =>
          fetch(postUrl, {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify(p),
          }).then(res => res.json())
        )
      );

      const collected: ApiResult[] = [];
      for (const s of settled) {
        if (s.status === "fulfilled" && s.value?.ok && Array.isArray(s.value.results)) {
          collected.push(...(s.value.results as ApiResult[]));
        } else {
          collected.push({
            status: "fetch-failed",
            key: "unknown",
            chargePointId: "unknown",
            message: s.status === "fulfilled" ? JSON.stringify(s.value) : String(s.reason),
          } as any);
        }
      }

      setResults(collected);
    } catch (e) {
      setResults([
        { status: "fetch-failed", key: "unknown", chargePointId: "unknown", message: String(e) } as any,
      ]);
    } finally {
      setSubmitting(false);
      resetSelections();
      resetInputs();
    }
  };

  const badge = (r: ApiResult) => {
    if (r.status === "ok") return <span className="rounded bg-green-600 px-2 py-0.5 text-xs text-white">OK</span>;
    if ((r as any).status === "error" && (r as any).reason === "listed-error")
      return <span className="rounded bg-red-600 px-2 py-0.5 text-xs text-white">ERROR</span>;
    if ((r as any).status === "error" && (r as any).reason === "unexpected")
      return <span className="rounded bg-yellow-600 px-2 py-0.5 text-xs text-white">UNEXPECTED</span>;
    return <span className="rounded bg-slate-700 px-2 py-0.5 text-xs text-white">FAILED</span>;
  };

  return (
    <div className="flex flex-col gap-6">
      {/* Auswahlzeile */}
      <div className="grid grid-cols-1 gap-3 md:grid-cols-12">
        {/* ChargePoint (einzeln) */}
        <div className="col-span-1 md:col-span-6">
          <label className="mb-1 block text-sm font-semibold text-slate-700 dark:text-white/80">
            ChargePoint (Einzeln)
          </label>
          {/* Wenn Location gewählt ist, Dropdown visuell deaktivieren */}
          <div className={selectedLocationId.trim() !== "" ? "opacity-50 pointer-events-none" : ""}>
            <Dropdown
              title="ChargePoint wählen"
              options={cpOptions}
              onChange={(id: string) => {
                setSelectedCpId(id);
                if (id) setSelectedLocationId("");
              }}
              onDelete={() => {""}}
              canDelete={false}
              placeHolder="— auswählen —"
              searchable={true}
              searchPlaceholder="ChargePoint suchen…"
              className="w-full text-base"
              icon={<BsEvStation className="mr-1 items-center" />}
            />
          </div>
        </div>

        {/* Location (Batch) */}
        <div className="col-span-1 md:col-span-6">
          <label className="mb-1 block text-sm font-semibold text-slate-700 dark:text-white/80">
            Location (Batch)
          </label>
          {/* Wenn CP gewählt ist, Dropdown visuell deaktivieren */}
          <div className={selectedCpId.trim() !== "" ? "opacity-50 pointer-events-none" : ""}>
            <Dropdown
              title="Location wählen"
              options={locationOptions}
              onChange={(id: string) => {
                setSelectedLocationId(id);
                if (id) setSelectedCpId("");
              }}
              onDelete={() => {""}}
              canDelete={false}
              placeHolder="— Location auswählen —"
              searchable={true}
              icon={ <CiLocationOn />}
              searchPlaceholder="Location suchen…"
              className="w-full text-base"
            />
          </div>
        </div>

        {/* Monitoring Key */}
        <div className="md:col-span-6">
          <label className="mb-1 block text-sm font-semibold text-slate-700 dark:text-white/80">
            Monitoring Key (Pflicht)
          </label>
          <input
            value={monitoringKey}
            onChange={e => setMonitoringKey(e.target.value)}
            className="w-full rounded-lg border border-slate-300 p-2 dark:border-slate-600 dark:bg-slate-800"
            placeholder="z. B. HeartbeatInterval"
          />
        </div>

        {/* Set Value */}
        <div className="md:col-span-6">
          <label className="mb-1 block text-sm font-semibold text-slate-700 dark:text-white/80">
            Set Value (Pflicht)
          </label>
          <input
            value={setValueInput}
            onChange={e => setSetValueInput(e.target.value)}
            className="w-full rounded-lg border border-slate-300 p-2 dark:border-slate-600 dark:bg-slate-800"
            placeholder="z. B. 30 oder 'A,B,C'"
          />
        </div>
      </div>

      {/* Aktionen */}
      <div className="flex flex-wrap items-center gap-3">
        <Button
          onClick={handleAddSingle}
          disabled={!canAddSingle}
          className="bg-green-600 hover:brightness-95 disabled:brightness-90"
          title="Einzelnen ChargePoint zur Liste hinzufügen"
        >
            <IoAddCircleOutline /> ChargePoint
        </Button>

        <Button
          onClick={handleAddLocation}
          disabled={!canAddLocation}
          className="bg-green-600 hover:brightness-95 disabled:brightness-90"
          title="Alle ChargePoints der Location zur Liste hinzufügen"
        >
            <IoAddCircleOutline /> Location (alle CPs)
        </Button>

        <Button
          onClick={handleSubmitQueue}
          disabled={queue.length === 0 || submitting}
          className="bg-blue-600 hover:brightness-95 disabled:brightness-90"
        >
          {submitting ? "Setze Werte..." : `Queue ausführen (${queue.length})`}
        </Button>
      </div>

      {/* Zwischenablage */}
      {queue.length > 0 && (
        <div className="rounded-lg border border-slate-300 bg-slate-100 p-3 dark:border-slate-700 dark:bg-slate-800/50">
          <div className="mb-2 text-sm font-semibold text-slate-700 dark:text-white/80">
            Zwischenablage ({queue.length})
          </div>
          <ul className="flex flex-col gap-2">
            {queue.map(item => {
              const cp = chargePoints.find(c => c.chargePointId === item.chargePointId);
              return (
                <li
                  key={item.id}
                  className="flex flex-wrap items-center justify-between gap-2 rounded-lg bg-slate-200 p-2 dark:bg-slate-900/60"
                >
                  <div className="flex flex-col">
                    <div className="text-sm font-semibold">{cp?.displayName ?? item.chargePointId}</div>
                    <div className="text-xs text-slate-600 dark:text-slate-400">
                      key: <span className="font-mono">{item.monitoringKey}</span>
                      {" • set: "}
                      <span className="font-mono">{item.setValue}</span>
                    </div>
                  </div>
                  <Button
                    small
                    className="bg-red-600 hover:brightness-95"
                    onClick={() => setQueue(prev => prev.filter(x => x.id !== item.id))}
                  >
                    entfernen
                  </Button>
                </li>
              );
            })}
          </ul>
        </div>
      )}

      {/* Ergebnisse */}
      {results && (
        <div className="rounded-lg border border-slate-300 bg-white p-3 dark:border-slate-700 dark:bg-slate-800">
          <div className="mb-2 text-sm font-semibold text-slate-700 dark:text-white/80">
            Ergebnisse ({results.length})
          </div>
          <ul className="flex flex-col gap-2">
            {results.map((r, i) => (
              <li
                key={i}
                className="flex flex-wrap items-start justify-between gap-2 rounded-md border border-slate-200 p-2 dark:border-slate-700"
              >
                <div className="flex flex-col">
                  <div className="flex items-center gap-2">
                    {badge(r)}
                    <span className="text-sm font-semibold">
                      {r.chargePointId} — <span className="font-mono">{r.key}</span>
                    </span>
                  </div>
                  <div className="text-xs text-slate-600 dark:text-slate-400">
                    {(r as any).status === "ok" && <>gesetzt</>}
                    {(r as any).status === "error" && <>Fehler beim Setzen</>}
                    {(r as any).status === "fetch-failed" && <>Fehler: {(r as any).message}</>}
                  </div>
                </div>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
}
