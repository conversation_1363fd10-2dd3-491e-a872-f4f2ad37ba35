"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import Table from "~/utils/table/table";
import { AiOutlineDelete } from "react-icons/ai";
import MessageDialog from "~/app/(app)/util/MessageDialog";
import type { ICellRendererParams } from "ag-grid-community";
import Link from "next/link";
import { BsPencilSquare } from "react-icons/bs";
import { OccpMonitorKeyRow } from "~/types/monitorKey/rowDataoccpMonitorKey";



export default function OccpMonitorKeyTable({ rowData }: { rowData: OccpMonitorKeyRow[] }) {
  const router = useRouter();
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [rowToDelete, setRowToDelete] = useState<OccpMonitorKeyRow | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  const handleDeleteClick = (row: OccpMonitorKeyRow) => {
    setRowToDelete(row);
    setShowDeleteDialog(true);
  };

  const handleDeleteConfirm = async () => {
    if (!rowToDelete) return;
    setIsDeleting(true);

    try {
      const url = `/api/occpMonitorKey/occpMonitorKeyDelete/${encodeURIComponent(rowToDelete.id!)}`;

      const res = await fetch(url, { method: "DELETE" });

      if (!res.ok) {
        const err = await res.json().catch(() => ({}));
        alert(`Fehler beim Löschen: ${err.error ?? res.statusText}`);
        return;
      }

      setShowDeleteDialog(false);
      setRowToDelete(null);
      router.refresh();
    } catch (e) {
      console.error("Fehler beim Löschen:", e);
      alert("Fehler beim Löschen: " + (e as Error).message);
    } finally {
      setIsDeleting(false);
    }

  };

  const handleDeleteCancel = () => {
    setShowDeleteDialog(false);
    setRowToDelete(null);
  };

  const ActionCellRenderer = (params: ICellRendererParams) => {
    return (
      <div className="flex items-center gap-2 justify-center">
        <AiOutlineDelete
          className="cursor-pointer text-red-600 hover:text-red-800"
          size="1.2rem"
          title="Monitor Key löschen"
          onClick={() => handleDeleteClick(params.data as OccpMonitorKeyRow)}
        />
        <div className="flex gap-2">
          <Link href={`/occp-monitorKey/${params?.data?.id}/edit`}>
            <BsPencilSquare className="cursor-pointer text-green-600 hover:text-green-800" />
          </Link>
        </div>
      </div>

    );
  };



  const columnDefs = [
    { field: "chargePointVendor", headerName: "Chargepoint Vendor", width: 160 },
    { field: "chargePointModel", headerName: "Chargepoint Model", width: 160 },
    { field: "configKeyName", headerName: "Config Key Name", width: 160 },
    { field: "expectedValues", headerName: "Erwartete Werte", width: 160 },
    { field: "errorValues", headerName: "Fehler Werte", width: 160 },
    { field: "excludedChargePoints", headerName: "Excluded Chargepoints", width: 200 },
    {
      field: "actions",
      headerName: "Aktionen",
      cellRenderer: ActionCellRenderer,
      width: 90,
      sortable: false,
      filter: false,
      resizable: false,
      pinned: "right" as const,
    },
  ];

  const gridOptions = { suppressAggFuncInHeader: true };

  return (
    <>
      <Table gridOptions={gridOptions} columnDefs={columnDefs} rowData={rowData} />

      {showDeleteDialog && rowToDelete && (
        <MessageDialog
          title="Monitor Key löschen"
          message={`Möchten Sie den Monitor Key wirklich löschen?
Vendor: "${rowToDelete.chargePointVendor}"
Model: "${rowToDelete.chargePointModel}"
Key: "${rowToDelete.configKeyName}"

Diese Aktion kann nicht rückgängig gemacht werden.`}
          onYes={handleDeleteConfirm}
          onNo={handleDeleteCancel}
          yesLabel={isDeleting ? "Wird gelöscht..." : "Löschen"}
          noLabel="Abbrechen"
        />
      )}
    </>
  );
}
