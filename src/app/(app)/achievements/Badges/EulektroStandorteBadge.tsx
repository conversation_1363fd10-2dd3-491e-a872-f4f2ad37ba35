"use client";

import * as React from "react";
import { createPortal } from "react-dom";
import BadgeBase from "~/app/(app)/component/BadgeBase";
import { makeBadge } from "~/utils/achievments/badges/makeBadge";

function ModalPortal({
                       open,
                       onClose,
                       children,
                     }: {
  open: boolean;
  onClose: () => void;
  children: React.ReactNode;
}) {
  const [mounted, setMounted] = React.useState(false);
  React.useEffect(() => setMounted(true), []);
  React.useEffect(() => {
    if (!open) return;
    const prev = document.body.style.overflow;
    document.body.style.overflow = "hidden";
    return () => { document.body.style.overflow = prev; };
  }, [open]);

  if (!open || !mounted) return null;

  return createPortal(
    <div
      className="fixed inset-0 z-[2147483647] flex items-center justify-center p-4"
      role="dialog"
      aria-modal="true"
      onClick={onClose}
    >
      <div className="absolute inset-0 bg-black/50" />
      <div
        className="relative z-[1] w-full max-w-lg overflow-hidden rounded-2xl bg-white p-4 shadow-xl"
        onClick={(e) => e.stopPropagation()}
      >
        {children}
      </div>
    </div>,
    document.body
  );
}

export default function EulektroLocationsBadge({
                                                 count,
                                                 shareUrl,
                                               }: {
  count: number;
  shareUrl?: string;
}) {
  const [open, setOpen] = React.useState(false);

  // Zahl im Kreisbild (das Grid-Icon)
  const imgSrc = React.useMemo(
    () => makeBadge({ color: "#1d4ed8", label: String(count) }),
    [count]
  );

  const MODAL_DIAMETER = 320; // wie bei Sessions groß & rund

  return (
    <>
      {/* Kachel im Grid – ohne Progress-Ring */}
      <BadgeBase
        imgSrc={imgSrc}
        label={`Eulektro-Standorte (${count})`}
        progress={0}
        locked={false}
        onClick={() => setOpen(true)}
        size="xl"
      />

      {/* Modal im Sessions-Look, aber ohne Fortschritts-Balken/Ringe */}
      <ModalPortal open={open} onClose={() => setOpen(false)}>
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">Eulektro-Standorte</h3>
          <button
            type="button"
            onClick={() => setOpen(false)}
            className="rounded-md p-2 hover:bg-gray-100"
            aria-label="Schließen"
          >
            ✕
          </button>
        </div>

        <div className="mt-4 flex items-center gap-6">
          {/* RUNDES Badge-Bild – exakt wie bei Sessions (ohne roten Overlay-Ring) */}
          <div className="relative aspect-square w-[320px] shrink-0 overflow-hidden rounded-full ring-1 ring-gray-200">
            <img src={imgSrc} alt="Eulektro-Standorte" className="absolute inset-0 h-full w-full object-contain" />
          </div>


          {/* rechter Textblock */}
          <div className="min-w-0">
            <div className="text-2xl font-bold">{count}</div>
            <div className="text-sm text-gray-600">
              insgesamt gezählte eulektro-standorte.
            </div>
            <div className="mt-3 rounded-md bg-gray-50 p-3 text-sm text-gray-700 ring-1 ring-gray-200">
              Die Anzahl wächst ohne obere Grenze – deshalb keine Stufen oder
              Progress-Anzeigen.
            </div>
          </div>
        </div>
      </ModalPortal>
    </>
  );
}
