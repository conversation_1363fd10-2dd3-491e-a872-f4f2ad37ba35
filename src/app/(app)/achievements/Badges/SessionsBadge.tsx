"use client";

import * as React from "react";
import BadgeBase from "~/app/(app)/component/BadgeBase";
import { makeBadge } from "~/utils/achievments/badges/makeBadge";
import {Tier} from "~/types/achievment/bagdesTypes";
import {AchievmentBadgeModalPortal} from "~/app/(app)/component/AchievmentBadgeModalPortal";
import {at, computeTierState, pct, clampIndex, clamp01} from "~/utils/achievments/badges/helpers";
import ShareButtonBadge from "~/app/(app)/component/ShareButtonBadge";

const shareUrl = "https://deine-seite.de/trophy/123";
const title = "Ich habe eine neue Trophäe erreicht! 🏆";

const TIERS: readonly Tier[] = [
  { threshold: 10,  key: "starter", label: "Starter", color: "#8b5cf6", bonus: "Willkommens-Perk freigeschaltet." },
  { threshold: 25,  key: "bronze",  label: "Bronze",   imgSrc: "/images/badges/bronze.png", bonus: "Bronze-Badge: kleiner Shop-Bonus." },
  { threshold: 50,  key: "silver",  label: "Silber",  color: "#f59e0b", bonus: "Silber-Badge: -5% auf Zubehör." },
  { threshold: 100, key: "gold",    label: "Gold",    imgSrc: "/images/badges/gold.png", bonus: "Gold-Badge: exklusiver Support-Kanal." },
  { threshold: 250, key: "platin",  label: "Platin",  color: "#64748b", bonus: "Platin-Badge: VIP-Goodies." },
  { threshold: 500, key: "diamond", label: "Diamant", color: "#ef4444", bonus: "Special Event Access." },
] as const;




export default function SessionsBadge({ cdrCount }: { cdrCount: number }) {
  const [open, setOpen] = React.useState(false);

  const { currentTierIndex, nextTierIndex, currentTier, ringProgress, len } =
    computeTierState(cdrCount, TIERS);

  // Grid-Kachel (groß in der Übersicht)
  const displayImg =
    currentTier.imgSrc ??
    makeBadge({
      color: currentTier.color ?? "#8b5cf6",
      label: currentTier.label.charAt(0).toUpperCase() || "S",
    });

  // Modal auf aktueller Stufe starten
  const [index, setIndex] = React.useState<number>(currentTierIndex);

  React.useEffect(() => {
    if (!open) setIndex(currentTierIndex);
  }, [open, currentTierIndex]);

  const goPrev = () => setIndex(i => clampIndex(len, i - 1));
  const goNext = () => setIndex(i => clampIndex(len, i + 1));

  // Touch-Swipe
  const touchStartX = React.useRef<number | null>(null);
  const onTouchStart: React.TouchEventHandler<HTMLDivElement> = (e) => {
    e.preventDefault(); e.stopPropagation();
    touchStartX.current = e.touches[0]?.clientX ?? null;
  };
  const onTouchMove: React.TouchEventHandler<HTMLDivElement> = (e) => {
    e.preventDefault(); e.stopPropagation();
  };
  const onTouchEnd: React.TouchEventHandler<HTMLDivElement> = (e) => {
    e.preventDefault(); e.stopPropagation();
    if (touchStartX.current == null) return;
    const dx = (e.changedTouches[0]?.clientX ?? 0) - touchStartX.current;
    touchStartX.current = null;
    if (Math.abs(dx) < 40) return;
    if (dx > 0) goPrev(); else goNext();
  };

  // Keyboard
  React.useEffect(() => {
    if (!open) return;
    const onKey = (e: KeyboardEvent) => {
      if (e.key === "ArrowLeft") { e.preventDefault(); goPrev(); }
      if (e.key === "ArrowRight") { e.preventDefault(); goNext(); }
      if (e.key === "Escape") { e.preventDefault(); setOpen(false); }
    };
    window.addEventListener("keydown", onKey);
    return () => window.removeEventListener("keydown", onKey);
  }, [open]);

  // Sicherer Zugriff
  const safeIdx = clampIndex(len, index);
  const showShare = safeIdx === currentTierIndex;
  const isLocked = safeIdx > currentTierIndex;
  const t = at(TIERS, safeIdx);
  const toThis = clamp01(cdrCount / t.threshold);
  const missing = Math.max(0, t.threshold - cdrCount);
  const nextForPanel = safeIdx + 1 < len ? at(TIERS, safeIdx + 1) : null;
  const toNext = nextForPanel ? clamp01(cdrCount / nextForPanel.threshold) : 1;

  const tierImg =
    t.imgSrc ??
    makeBadge({
      color: t.color ?? "#8b5cf6",
      label: t.label.charAt(0).toUpperCase() || "S",
    });

  // ⬆️ Badge im Modal noch größer:
  const MODAL_DIAMETER = 256; // px – bei Bedarf anpassen
  const RING_INSET_CLASS = "-inset-2"; // dickerer Ring im Modal

  return (
    <>
      {/* Große Grid-Kachel */}
      <BadgeBase
        imgSrc={displayImg}
        label={`Sessions (${currentTier.label})`}
        locked={false}
        progress={ringProgress}
        size="xl"
        onClick={() => {
          setIndex(currentTierIndex);
          setOpen(true);
        }}
      />

      {/* Modal */}
      <AchievmentBadgeModalPortal open={open} onClose={() => setOpen(false)}>
        <div onTouchStart={onTouchStart} onTouchMove={onTouchMove} onTouchEnd={onTouchEnd}>
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">Sessions-Stufen</h3>
            <button
              type="button"
              onClick={() => setOpen(false)}
              className="rounded-md p-2 hover:bg-gray-100"
              aria-label="Schließen"
            >
              ✕
            </button>
          </div>

          <div className="mt-2 flex items-center justify-between text-sm text-gray-600">
            <button
              type="button"
              onClick={goPrev}
              disabled={safeIdx === 0}
              className="rounded-md px-2 py-1 ring-1 ring-gray-300 disabled:opacity-40"
            >
              ← Zurück
            </button>
            <div>{safeIdx + 1} / {len}</div>
            <button
              type="button"
              onClick={goNext}
              disabled={safeIdx === len - 1}
              className="rounded-md px-2 py-1 ring-1 ring-gray-300 disabled:opacity-40"
            >
              Weiter →
            </button>
          </div>

          <div className="mt-4 space-y-4">
            <div className="flex items-center gap-6">
              {/* >>> Größeres Badge im Modal */}
                <div className="relative" style={{ width: MODAL_DIAMETER, height: MODAL_DIAMETER }}>
                    <div
                        className={`absolute ${RING_INSET_CLASS} rounded-full bg-primary`}
                        style={{
                            WebkitMaskImage: `conic-gradient(#000 ${Math.round(toThis * 360)}deg, transparent ${Math.round(toThis * 360)}deg)`,
                            maskImage: `conic-gradient(#000 ${Math.round(toThis * 360)}deg, transparent ${Math.round(toThis * 360)}deg)`,
                        }}
                        aria-hidden
                    />
                    <div
                        className="relative overflow-hidden rounded-full ring-1 ring-gray-200"
                        style={{ width: MODAL_DIAMETER, height: MODAL_DIAMETER }}
                        title={isLocked ? "Noch nicht erreicht" : undefined}
                    >
                        <img
                            src={tierImg}
                            alt={t.label}
                            className={`h-full w-full object-cover ${isLocked ? "filter grayscale saturate-0 opacity-60" : ""}`}
                        />
                    </div>
                </div>

              <div className="min-w-0">
                <div className="text-lg font-semibold">{t.label}</div>
                <div className="text-sm text-gray-600">Erforderlich: <b>{t.threshold}</b> CDRs</div>
                <div className="text-sm text-gray-600">
                  Dein Fortschritt: <b>{cdrCount}</b> / {t.threshold} ({pct(toThis)}%)
                </div>
                {missing > 0 ? (
                  <div className="text-sm text-amber-700">Es fehlen noch <b>{missing}</b> CDRs.</div>
                ) : (
                  <div className="text-sm text-green-700">Stufe erreicht ✅</div>
                )}
              </div>
            </div>

              <ShareButtonBadge title={title} showShare={showShare} shareUrl={shareUrl} />
            <div className="h-2 w-full overflow-hidden rounded-full bg-gray-200">
              <div className="h-full bg-primary" style={{ width: `${pct(toThis)}%` }} />
            </div>

            {nextForPanel && (
              <div className="space-y-1">
                <div className="text-sm text-gray-700">
                  Fortschritt zur nächsten Stufe (<b>{nextForPanel.label}</b>, {nextForPanel.threshold} CDRs): {pct(toNext)}%
                </div>
                <div className="h-2 w-full overflow-hidden rounded-full bg-gray-200">
                  <div className="h-full bg-gray-700" style={{ width: `${pct(toNext)}%` }} />
                </div>
              </div>
            )}

            {t.bonus && (
              <div className="rounded-lg bg-gray-50 p-3 text-sm text-gray-800 ring-1 ring-gray-200">
                <b>Bonus:</b> {t.bonus}
              </div>
            )}
          </div>
        </div>
      </AchievmentBadgeModalPortal>
    </>
  );
}
