import * as React from "react";
import BadgeBase from "~/app/(app)/component/BadgeBase";
import { makeBadge } from "~/utils/achievments/badges/makeBadge";
import { ModalPortal } from "~/app/(app)/component/ModalBadge";

// gleiche Datei wie EulektroLocationsBadge – ModalPortal ist schon vorhanden

export function PartnerLocationsBadge({
                                        count,
                                        shareUrl,
                                      }: {
  count: number;
  shareUrl?: string;
}) {
  const [open, setOpen] = React.useState(false);

  // Zahl im Kreisbild (Grid-Icon) – eigene Partner-Farbe
  const imgSrc = React.useMemo(
    () => makeBadge({ color: "#0f766e", label: String(count) }), // teal-700
    [count]
  );

  return (
    <>
      {/* Kachel im Grid – ohne Progress-Ring */}
      <BadgeBase
        imgSrc={imgSrc}
        label={`Partner-Standorte (${count})`}
        progress={0}
        locked={false}
        onClick={() => setOpen(true)}
        size="xl"
      />

      {/* Modal wie bei Sessions, aber ohne Fortschritts-UI */}
      <ModalPortal open={open} onClose={() => setOpen(false)}>
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">Partner-Standorte</h3>
          <button
            type="button"
            onClick={() => setOpen(false)}
            className="rounded-md p-2 hover:bg-gray-100"
            aria-label="Schließen"
          >
            ✕
          </button>
        </div>

        <div className="mt-4 flex items-center gap-6">
          {/* RUNDES Badge-Bild (quadratisch erzwingen, nicht schrumpfen) */}
          <div className="relative aspect-square w-[320px] shrink-0 overflow-hidden rounded-full ring-1 ring-gray-200">
            <img
              src={imgSrc}
              alt="Partner-Standorte"
              className="absolute inset-0 h-full w-full object-contain"
              draggable={false}
            />
          </div>

          {/* rechter Textblock */}
          <div className="min-w-0">
            <div className="text-2xl font-bold">{count}</div>
            <div className="text-sm text-gray-600">
              insgesamt gezählte partner-standorte.
            </div>
            <div className="mt-3 rounded-md bg-gray-50 p-3 text-sm text-gray-700 ring-1 ring-gray-200">
              Alle unterschiedlichen Partner-Locations, an denen du geladen hast
              (exkl. Eulektro). Keine Stufen oder Progress-Anzeigen.
            </div>
          </div>
        </div>
      </ModalPortal>
    </>
  );
}
