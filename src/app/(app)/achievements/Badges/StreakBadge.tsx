"use client";

import * as React from "react";
import { createPortal } from "react-dom";
import BadgeBase from "~/app/(app)/component/BadgeBase";
import { makeBadge } from "~/utils/achievments/badges/makeBadge";
import Modal from "~/app/(app)/component/ModalBadge";
import {Tier} from "~/types/achievment/bagdesTypes";
import {AchievmentBadgeModalPortal} from "~/app/(app)/component/AchievmentBadgeModalPortal";
import {at, computeTierState, pct, clampIndex, clamp01} from "~/utils/achievments/badges/helpers";
import ShareButtonBadge from "~/app/(app)/component/ShareButtonBadge";

const shareUrl = "https://deine-seite.de/trophy/123";
const title = "Ich habe eine neue Trophäe erreicht! 🏆";


const TIERS: readonly Tier[] = [
  { threshold: 3,  key: "starter", label: "Starter", color: "#ef4444", bonus: "Willkommens-Perk freigeschaltet." },
  { threshold: 6,  key: "bronze",  label: "Bronze",  color: "#ef4444", bonus: "Kleiner Shop-Bonus." },
  { threshold: 10,  key: "silver",  label: "Silber",  color: "#f59e0b", bonus: "Silber-Badge: -5% auf Zubehör." },
  { threshold: 25, key: "gold",    label: "Gold",    imgSrc: "/images/badges/gold.png", bonus: "Exklusiver Support-Kanal." },
  { threshold: 52, key: "platin",  label: "Platin",  color: "#64748b", bonus: "VIP-Goodies." },
] as const;



export default function NachtEuleBadge({
                                         laengsteLadeStreak,
                                         extraInfo,
                                         infoMode = "button",
                                         infoTitle = "Mehr Infos",
                                       }: {
  laengsteLadeStreak: number;
  extraInfo?: React.ReactNode;        // zusätzlicher Infotext im Modal
  infoMode?: "button" | "inline";     // Info per Button einklappen oder immer sichtbar
  infoTitle?: string;                 // Text auf dem Info-Button
}) {
  const [open, setOpen] = React.useState(false);

  const { currentTierIndex, currentTier, ringProgress, len } =
    computeTierState(laengsteLadeStreak, TIERS);

  // Grid-Kachel
  const displayImg =
    currentTier.imgSrc ??
    makeBadge({
      color: currentTier.color ?? "#8b5cf6",
      label: currentTier.label.charAt(0).toUpperCase() || "N",
    });

  // Modal-Index: auf aktueller Stufe starten
  const [index, setIndex] = React.useState<number>(currentTierIndex);
  React.useEffect(() => {
    if (!open) setIndex(currentTierIndex);
  }, [open, currentTierIndex]);

  const goPrev = () => setIndex(i => clampIndex(len, i - 1));
  const goNext = () => setIndex(i => clampIndex(len, i + 1));

  // Touch (swipe)
  const touchStartX = React.useRef<number | null>(null);
  const onTouchStart: React.TouchEventHandler<HTMLDivElement> = (e) => {
    e.stopPropagation();
    touchStartX.current = e.touches[0]?.clientX ?? null;
  };
  const onTouchEnd: React.TouchEventHandler<HTMLDivElement> = (e) => {
    e.stopPropagation();
    if (touchStartX.current == null) return;
    const dx = (e.changedTouches[0]?.clientX ?? 0) - touchStartX.current;
    touchStartX.current = null;
    if (Math.abs(dx) < 40) return;
    if (dx > 0) goPrev(); else goNext();
  };

  // Keyboard
  React.useEffect(() => {
    if (!open) return;
    const onKey = (e: KeyboardEvent) => {
      if (e.key === "ArrowLeft") { e.preventDefault(); goPrev(); }
      if (e.key === "ArrowRight") { e.preventDefault(); goNext(); }
      if (e.key === "Escape") { e.preventDefault(); setOpen(false); }
    };
    window.addEventListener("keydown", onKey);
    return () => window.removeEventListener("keydown", onKey);
  }, [open]);

  // aktuell gewählte Stufe im Modal
  const safeIdx = clampIndex(len, index);
  const showShare = safeIdx === currentTierIndex;
    const isLocked = safeIdx > currentTierIndex;


    const t = at(TIERS, safeIdx);
  const toThis = clamp01(laengsteLadeStreak / t.threshold);
  const missing = Math.max(0, t.threshold - laengsteLadeStreak);
  const nextForPanel = safeIdx + 1 < len ? at(TIERS, safeIdx + 1) : null;
  const toNext = nextForPanel ? clamp01(laengsteLadeStreak / nextForPanel.threshold) : 1;

  const tierImg =
    t.imgSrc ??
    makeBadge({
      color: t.color ?? "#8b5cf6",
      label: t.label.charAt(0).toUpperCase() || "N",
    });

  const MODAL_DIAMETER = 256;
  const RING_INSET_CLASS = "-inset-2";

  const [showInfo, setShowInfo] = React.useState(false);

  return (
    <>
      <BadgeBase
        imgSrc={displayImg}
        label={`Längste Wochen Ladestreak  (${currentTier.label})`}
        locked={false}
        progress={ringProgress}
        size="xl"
        onClick={() => {
          setIndex(currentTierIndex);
          setOpen(true);
        }}
      />

      <AchievmentBadgeModalPortal open={open} onClose={() => setOpen(false)}>
        <div onTouchStart={onTouchStart} onTouchEnd={onTouchEnd} style={{ touchAction: "pan-y" }}>
          <div className="flex items-center justify-between">
            <h3 className="text-lg font-semibold">Längste Wochen Ladestreak – Stufen</h3>
            <button
              type="button"
              onClick={() => setOpen(false)}
              className="rounded-md p-2 hover:bg-gray-100"
              aria-label="Schließen"
            >
              ✕
            </button>
          </div>

          <div className="mt-2 flex items-center justify-between text-sm text-gray-600">
            <button
              type="button"
              onClick={goPrev}
              disabled={safeIdx === 0}
              className="rounded-md px-2 py-1 ring-1 ring-gray-300 disabled:opacity-40"
            >
              ← Zurück
            </button>
            <div>{safeIdx + 1} / {len}</div>
            <button
              type="button"
              onClick={goNext}
              disabled={safeIdx === len - 1}
              className="rounded-md px-2 py-1 ring-1 ring-gray-300 disabled:opacity-40"
            >
              Weiter →
            </button>
          </div>

          <div className="mt-4 space-y-4">
            <div className="flex items-center gap-6">
                {/* großes Badge mit primärem Ring */}
                <div className="relative" style={{ width: MODAL_DIAMETER, height: MODAL_DIAMETER }}>
                    <div
                        className={`absolute ${RING_INSET_CLASS} rounded-full bg-primary`}
                        style={{
                            WebkitMaskImage: `conic-gradient(#000 ${Math.round(toThis * 360)}deg, transparent ${Math.round(toThis * 360)}deg)`,
                            maskImage: `conic-gradient(#000 ${Math.round(toThis * 360)}deg, transparent ${Math.round(toThis * 360)}deg)`,
                        }}
                        aria-hidden
                    />
                    <div
                        className="relative overflow-hidden rounded-full ring-1 ring-gray-200"
                        style={{ width: MODAL_DIAMETER, height: MODAL_DIAMETER }}
                        title={isLocked ? "Noch nicht erreicht" : undefined}
                    >
                        <img
                            src={tierImg}
                            alt={t.label}
                            className={`h-full w-full object-cover ${isLocked ? "filter grayscale saturate-0 opacity-60" : ""}`}
                        />
                    </div>
                </div>


                <div className="min-w-0">
                <div className="text-lg font-semibold">{t.label}</div>
                <div className="text-sm text-gray-600">
                  Dein Fortschritt (nachts): <b>{laengsteLadeStreak}</b> / {t.threshold} ({pct(toThis)}%)
                </div>
                {missing > 0 ? (
                  <div className="text-sm text-amber-700">Es fehlen noch <b>{missing}</b> Nacht-CDRs.</div>
                ) : (
                  <div className="text-sm text-green-700">Stufe erreicht ✅</div>
                )}
              </div>
            </div>
              <ShareButtonBadge title={title} showShare={showShare} shareUrl={shareUrl} />
            {/* Primäre Fortschrittsleiste */}
            <div className="h-2 w-full overflow-hidden rounded-full bg-gray-200">
              <div className="h-full bg-primary" style={{ width: `${pct(toThis)}%` }} />
            </div>

            {/* Zusatzinfos wie im CO2-Beispiel */}
            {extraInfo &&
              (infoMode === "inline" ? (
                <div className="rounded-md bg-gray-50 p-3 text-sm text-gray-700 ring-1 ring-gray-200">{extraInfo}</div>
              ) : (
                <div className="space-y-2">
                  <button
                    type="button"
                    onClick={() => setShowInfo(v => !v)}
                    className="inline-flex items-center gap-2 rounded-md px-3 py-1.5 text-sm ring-1 ring-gray-300 hover:bg-gray-50"
                    aria-expanded={showInfo}
                  >
                    ⓘ {infoTitle}
                  </button>
                  {showInfo && (
                    <div className="rounded-md bg-gray-50 p-3 text-sm text-gray-700 ring-1 ring-gray-200">
                      {extraInfo}
                    </div>
                  )}
                </div>
              ))}

            {/* Nächste Stufe */}
            {nextForPanel && (
              <div className="space-y-1">
                <div className="text-sm text-gray-700">
                  Fortschritt zur nächsten Stufe (<b>{nextForPanel.label}</b>): {pct(toNext)}%
                </div>
                <div className="h-2 w-full overflow-hidden rounded-full bg-gray-200">
                  <div className="h-full bg-gray-700" style={{ width: `${pct(toNext)}%` }} />
                </div>
              </div>
            )}

            {t.bonus && (
              <div className="rounded-lg bg-gray-50 p-3 text-sm text-gray-800 ring-1 ring-gray-200">
                <b>Bonus:</b> {t.bonus}
              </div>
            )}
          </div>
        </div>
      </AchievmentBadgeModalPortal>
    </>
  );
}
