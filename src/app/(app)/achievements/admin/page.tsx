"use client";

import * as React from "react";
import Button from "~/component/button";
import Card from "~/component/card";
import {MetricDraft, RewardDraft} from "~/types/achievment/admin";



const PARAM_PRESETS = [
  { value: "energy_kwh_month", label: "Geladene Energie (kWh) pro Monat", window: "month" as const, unit: "kWh" },
  { value: "cdr_count_month",  label: "Sessions pro Monat",                window: "month" as const, unit: "Sessions" },
  { value: "cdr_count_event",  label: "Sessions während Event",            window: "event" as const, unit: "Sessions" },
  { value: "co2_kg_total",     label: "CO₂-Ersparnis gesamt (kg)",         window: "event" as const, unit: "kg" },
  { value: "referrals",        label: "Einladungen/Freunde",               window: "event" as const, unit: "" },
  { value: "custom",           label: "Custom (eigenes Tracking)",         window: "event" as const, unit: "" },
];

export default function AdminEventsPage() {
  const [code, setCode] = React.useState("");
  const [title, setTitle] = React.useState("");
  const [description, setDescription] = React.useState("");
  const [badgeImageUrl, setBadgeImageUrl] = React.useState("");
  const [startsAt, setStartsAt] = React.useState("");
  const [endsAt, setEndsAt] = React.useState("");

  const [metrics, setMetrics] = React.useState<MetricDraft[]>([]);
  const [reward, setReward] = React.useState<RewardDraft | null>(null);

  const [saving, setSaving] = React.useState(false);

  function addMetric() {
    setMetrics((m) => [
      ...m,
      { label: "", param: "energy_kwh_month", target: 0, window: "month", start: 0, color: "" },
    ]);
  }
  function updateMetric(i: number, patch: Partial<MetricDraft>) {
    setMetrics((m) => m.map((row, idx) => (idx === i ? { ...row, ...patch } : row)));
  }
  function removeMetric(i: number) {
    setMetrics((m) => m.filter((_, idx) => idx !== i));
  }

  async function createEvent() {
    try {
      setSaving(true);

      const body: any = {
        code: code.trim(),
        title: title.trim(),
        description: description || undefined,
        badgeImageUrl: badgeImageUrl || undefined,
        startsAt: startsAt || undefined,
        endsAt: endsAt || undefined,
        metrics: metrics
          .map(m => ({
            ...m,
            target: Number(m.target || 0),
            start: m.start != null ? Number(m.start) : undefined,
            window: (m.window ?? "event"),
          }))
          .filter(m => m.label && m.param && m.target > 0),
        reward: reward ?? undefined,
      };

      const res = await fetch("/api/achievments/admin/events", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(body),
      });
      if (!res.ok) throw new Error(await res.text());
      alert("Event erstellt.");
      // reset
      setCode(""); setTitle(""); setDescription(""); setBadgeImageUrl("");
      setStartsAt(""); setEndsAt("");
      setMetrics([]); setReward(null);
    } catch (e: any) {
      alert(e?.message || "Fehler beim Anlegen.");
    } finally {
      setSaving(false);
    }
  }

  return (
   <Card>
    <div className="w-full min-h-dvh p-4">
      <div className="mx-auto w-full max-w-6xl space-y-6">
        <h1 className="text-xl font-semibold">Events – Admin</h1>

        {/* Event Stammdaten */}
        <div className="rounded-xl border p-4">
          <div className="grid grid-cols-1 gap-3 sm:grid-cols-2">
            <label className="text-sm">
              <div className="mb-1 text-gray-700">Code *</div>
              <input className="w-full rounded-md border px-2 py-1" value={code} onChange={(e)=>setCode(e.target.value)}/>
            </label>
            <label className="text-sm">
              <div className="mb-1 text-gray-700">Titel *</div>
              <input className="w-full rounded-md border px-2 py-1" value={title} onChange={(e)=>setTitle(e.target.value)}/>
            </label>
            <label className="sm:col-span-2 text-sm">
              <div className="mb-1 text-gray-700">Beschreibung</div>
              <textarea className="w-full rounded-md border px-2 py-1" rows={3} value={description} onChange={(e)=>setDescription(e.target.value)}/>
            </label>
            <label className="text-sm">
              <div className="mb-1 text-gray-700">Badge Image URL</div>
              <input className="w-full rounded-md border px-2 py-1" placeholder="/images/events/summer.png" value={badgeImageUrl} onChange={(e)=>setBadgeImageUrl(e.target.value)}/>
            </label>
            <label className="text-sm">
              <div className="mb-1 text-gray-700">Start</div>
              <input type="datetime-local" className="w-full rounded-md border px-2 py-1" value={startsAt} onChange={(e)=>setStartsAt(e.target.value)}/>
            </label>
            <label className="text-sm">
              <div className="mb-1 text-gray-700">Ende</div>
              <input type="datetime-local" className="w-full rounded-md border px-2 py-1" value={endsAt} onChange={(e)=>setEndsAt(e.target.value)}/>
            </label>
          </div>
        </div>

        {/* Metrics Builder */}
        <div className="rounded-xl border p-4">
          <div className="mb-3 flex items-center justify-between">
            <h2 className="text-base font-semibold">Metriken (Progressbars)</h2>
            <Button type="button" onClick={addMetric} className="px-3 py-1.5" small>+ Metrik</Button>
          </div>

          {metrics.length === 0 ? (
            <div className="rounded-md border border-dashed p-4 text-sm text-gray-600">
              Noch keine Metriken. Füge z. B. „Geladene Energie (kWh) pro Monat ≥ 250“ hinzu.
            </div>
          ) : (
            <div className="space-y-3">
              {metrics.map((m, i) => {
                const preset = PARAM_PRESETS.find(p => p.value === m.param);
                return (
                  <div key={i} className="grid grid-cols-1 gap-2 sm:grid-cols-12 rounded-lg border p-3">
                    <label className="sm:col-span-3 text-sm">
                      <div className="mb-1 text-gray-700">Parameter</div>
                      <select
                        className="w-full rounded-md border px-2 py-1"
                        value={m.param}
                        onChange={(e) => {
                          const pv = e.target.value;
                          const p = PARAM_PRESETS.find(x => x.value === pv);
                          updateMetric(i, { param: pv, window: p?.window ?? "event" });
                        }}
                      >
                        {PARAM_PRESETS.map(p => (
                          <option key={p.value} value={p.value}>{p.label}</option>
                        ))}
                      </select>
                    </label>
                    <label className="sm:col-span-3 text-sm">
                      <div className="mb-1 text-gray-700">Label</div>
                      <input className="w-full rounded-md border px-2 py-1" value={m.label} onChange={(e)=>updateMetric(i,{label:e.target.value})}/>
                    </label>
                    <label className="sm:col-span-2 text-sm">
                      <div className="mb-1 text-gray-700">Zielwert</div>
                      <input type="number" className="w-full rounded-md border px-2 py-1" value={m.target} onChange={(e)=>updateMetric(i,{target:Number(e.target.value)})}/>
                    </label>
                    <label className="sm:col-span-2 text-sm">
                      <div className="mb-1 text-gray-700">Startwert</div>
                      <input type="number" className="w-full rounded-md border px-2 py-1" value={m.start ?? 0} onChange={(e)=>updateMetric(i,{start:Number(e.target.value)})}/>
                    </label>
                    <label className="sm:col-span-2 text-sm">
                      <div className="mb-1 text-gray-700">Fenster</div>
                      <select className="w-full rounded-md border px-2 py-1" value={m.window ?? "event"} onChange={(e)=>updateMetric(i,{window:e.target.value as any})}>
                        <option value="event">Event</option>
                        <option value="month">Monat</option>
                        <option value="week">Woche</option>
                        <option value="rolling30d">Rolling 30 Tage</option>
                      </select>
                    </label>
                    <label className="sm:col-span-11 text-sm">
                      <div className="mb-1 text-gray-700">Farbe (optional, z. B. #06b6d4)</div>
                      <input className="w-full rounded-md border px-2 py-1" value={m.color ?? ""} onChange={(e)=>updateMetric(i,{color:e.target.value})}/>
                    </label>
                    <div className="sm:col-span-1 flex items-end justify-end">
                      <Button type="button" className="bg-gray-800 px-3 py-1.5" small onClick={()=>removeMetric(i)}>Entfernen</Button>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>

        {/* Reward */}
        <div className="rounded-xl border p-4">
          <div className="mb-3 flex items-center justify-between">
            <h2 className="text-base font-semibold">Reward (optional)</h2>
            {!reward ? (
              <Button type="button" className="px-3 py-1.5" small onClick={()=>setReward({type:"discount_percent", value:5, description:"5% Rabatt ab Erreichen"})}>
                + Reward
              </Button>
            ) : (
              <Button type="button" className="bg-gray-800 px-3 py-1.5" small onClick={()=>setReward(null)}>
                Entfernen
              </Button>
            )}
          </div>

          {reward && (
            <div className="grid grid-cols-1 gap-3 sm:grid-cols-12">
              <label className="sm:col-span-3 text-sm">
                <div className="mb-1 text-gray-700">Typ</div>
                <select className="w-full rounded-md border px-2 py-1" value={reward.type} onChange={(e)=>setReward(r=>r?{...r, type: e.target.value as any}:r)}>
                  <option value="discount_percent">Rabatt (%)</option>
                  <option value="badge">Badge</option>
                  <option value="text">Nur Text</option>
                </select>
              </label>
              <label className="sm:col-span-2 text-sm">
                <div className="mb-1 text-gray-700">Wert</div>
                <input type="number" className="w-full rounded-md border px-2 py-1" value={reward.value ?? 0} onChange={(e)=>setReward(r=>r?{...r, value:Number(e.target.value)}:r)}/>
              </label>
              <label className="sm:col-span-7 text-sm">
                <div className="mb-1 text-gray-700">Beschreibung</div>
                <input className="w-full rounded-md border px-2 py-1" value={reward.description ?? ""} onChange={(e)=>setReward(r=>r?{...r, description:e.target.value}:r)}/>
              </label>
            </div>
          )}
        </div>

        <div className="flex justify-end">
          <Button type="button" onClick={createEvent} disabled={saving || !code || !title}>
            {saving ? "Speichere…" : "Event anlegen"}
          </Button>
        </div>
      </div>
    </div>
   </Card>
  );
}
