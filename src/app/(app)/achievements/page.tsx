// app/(app)/achievements/page.tsx
import AchievementsForm from "~/app/(app)/achievements/achievmentsForm";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { getServerSession } from "next-auth";
import prisma from "~/server/db/prisma";
import { Prisma } from "@prisma/client";
import { Session } from "next-auth";

export default async function Page() {
  const session = await getServerSession(authOptions);

  // wenn keine Session -> ggf. redirect/login
  const userId = session?.user?.id ?? "";

  // holt einmal die Token-UIDs des aktuellen Users
  const getTokenUidsForCurrentUser = async (
    session: Session | null
  ): Promise<string[]> => {
    const userId = session?.user?.id;
    if (!userId) return [];

    const cards = await prisma.eMPCard.findMany({
      where: { userId },
      select: { physicalCard: { select: { uid: true } } },
    });

    return cards
      .map(c => c.physicalCard?.uid)
      .filter((uid): uid is string => !!uid);
  };

  const user = await prisma.user.findUnique({
    where: { id: userId },
    select: {
      achievementProfile: {
        select: { trophiesOptIn: true },
      },
    },
  });

  const isCollector = !!user?.achievementProfile?.trophiesOptIn;

  const getCdrCountForCardHolder = async (tokenUids: string[]): Promise<number> => {
    if (tokenUids.length === 0) return 0;
    return prisma.cdr.count({
      where: { Authentication_ID: { in: tokenUids }, billable: true },
    });
  };

  const getCdrCo2Savings = async (tokenUids: string[]): Promise<number> => {
    if (tokenUids.length === 0) return 0;
    const agg = await prisma.cdr.aggregate({
      where: { Authentication_ID: { in: tokenUids }, billable: true },
      _sum: { Volume: true },
    });
    const totalKWh = Number(agg._sum.Volume ?? 0);
    const CO2_PER_KWH_SAVED = 0.57;
    return totalKWh * CO2_PER_KWH_SAVED;
  };

  const getNachtsLaden = async (tokenUids: string[]): Promise<number> => {
    if (tokenUids.length === 0) return 0;

    const [late, early] = await prisma.$transaction([
      prisma.$queryRaw<{ cnt: bigint }[]>(Prisma.sql`
        SELECT COUNT(*) AS cnt
        FROM \`Cdr\`
        WHERE billable = TRUE
          AND Authentication_ID IN (${Prisma.join(tokenUids)})
          AND TIME(Start_datetime) >= '22:00:00'
      `),
      prisma.$queryRaw<{ cnt: bigint }[]>(Prisma.sql`
        SELECT COUNT(*) AS cnt
        FROM \`Cdr\`
        WHERE billable = TRUE
          AND Authentication_ID IN (${Prisma.join(tokenUids)})
          AND TIME(Start_datetime) < '06:00:00'
      `),
    ]);

    return Number(late[0]?.cnt ?? 0) + Number(early[0]?.cnt ?? 0);
  };

  const getLaengsteLadeStreakWeekly = async (tokenUids: string[]): Promise<number> => {
    if (tokenUids.length === 0) return 0;

    // längste Serie von Wochen mit ≥1 Ladevorgang (billable)
    const rows = await prisma.$queryRaw<{ longest: bigint | number }[]>(Prisma.sql`
      WITH weeks AS (
        -- Wochenanfang (Montag) je CDR
        SELECT DISTINCT DATE_SUB(DATE(Start_datetime), INTERVAL WEEKDAY(Start_datetime) DAY) AS week_start
        FROM \`Cdr\`
        WHERE billable = TRUE
          AND Authentication_ID IN (${Prisma.join(tokenUids)})
      ),
           ordered AS (
             SELECT week_start,
                    ROW_NUMBER() OVER (ORDER BY week_start) AS rn
             FROM weeks
           ),
           grouped AS (
             -- Konstante Gruppe für aufeinanderfolgende Wochen
             SELECT (TO_DAYS(week_start) DIV 7) - rn AS grp, COUNT(*) AS len
             FROM ordered
             GROUP BY grp
           )
      SELECT COALESCE(MAX(len), 0) AS longest
      FROM grouped
    `);

    return Number(rows[0]?.longest ?? 0);
  };

  // Zählt distinct Location_IDs: Partner vs. Eulektro
  const getBesuchteStandorteCounts = async (tokenUids: string[]): Promise<{
    partnerCount: number;
    eulektroCount: number;
  }> => {
    if (tokenUids.length === 0) return { partnerCount: 0, eulektroCount: 0 };

    // WICHTIG: wir holen ALLE relevanten CDRs (ohne distinct),
    // und deduplizieren später per Set über Location_ID
    const rows = await prisma.cdr.findMany({
      where: {
        Authentication_ID: { in: tokenUids },
        billable: true,
      },
      select: {
        Location_ID: true,
        OU_Code: true, // <-- HIER ggf. an dein wirkliches Feld anpassen!
      },
    });

    // '0000' und '0000-0002' sind Eulektro
    const EUL_CODES = new Set(["0000", "0000-0002"]);

    // Codes aus CDR sind teils mit Quotes: z.B. "'0000-0002'"
    const normalizeCode = (raw: unknown): string => {
      return String(raw ?? "")
        .trim()
        // führende/abschließende einfache ODER doppelte Anführungszeichen entfernen
        .replace(/^['"]+|['"]+$/g, "");
    };

    const eulektroSet = new Set<string>(); // distinct Location_ID
    const partnerSet = new Set<string>();   // distinct Location_ID

    for (const r of rows) {
      const loc = r.Location_ID;
      if (!loc) continue;

      const codeNorm = normalizeCode(r.OU_Code);

      if (EUL_CODES.has(codeNorm)) {
        eulektroSet.add(loc);
        // Falls Location_ID zuvor fälschlich als Partner erfasst: entfernen
        if (partnerSet.has(loc)) partnerSet.delete(loc);
      } else {
        // Nur als Partner zählen, wenn nicht bereits als Eulektro klassifiziert
        if (!eulektroSet.has(loc)) partnerSet.add(loc);
      }
    }

    return {
      partnerCount: partnerSet.size,
      eulektroCount: eulektroSet.size,
    };
  };
  const getFinishedBadgeImageUrls = async (userId: string): Promise<string[]> => {
    if (!userId) return [];

    const events = await prisma.achievementEvent.findMany({
      where: {
        // nur Events, bei denen es mindestens eine Completed-Enrollment
        // für dieses Profil (userId) gibt
        enrollments: {
          some: {
            status: "COMPLETED",
            profile: { userId },
          },
        },
        // nur Events mit Badge-Bild
        badgeImageUrl: { not: null },
      },
      select: { badgeImageUrl: true },
      orderBy: { createdAt: "desc" }, // optional
    });

    // Nur string (filtert nulls raus)
    return events
      .map(e => e.badgeImageUrl)
      .filter((u): u is string => !!u);
  };




  const tokenUids = await getTokenUidsForCurrentUser(session);

  const [
    co2Kg,
    cdrCount,
    nachtsLaden,
    laengsteLadeStreak,
    { partnerCount: besuchteStandorte, eulektroCount: besuchteEulektroStandorte },
    finishedBadgeImageUrls,
  ] = await Promise.all([
    getCdrCo2Savings(tokenUids),
    getCdrCountForCardHolder(tokenUids),
    getNachtsLaden(tokenUids),
    getLaengsteLadeStreakWeekly(tokenUids),
    getBesuchteStandorteCounts(tokenUids),
    getFinishedBadgeImageUrls(userId),
  ]);

  return (
    <AchievementsForm userData={{ isCollector, userId, cdrCount, co2Kg, nachtsLaden, laengsteLadeStreak, finishedBadgeImageUrls, besuchteStandorte, besuchteEulektroStandorte }} />
  );
}

//standort Level.
