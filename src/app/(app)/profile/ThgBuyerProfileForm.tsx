"use client";

import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import Button from "~/component/button";
import { FiSave, FiEdit2, FiX, FiLoader } from "react-icons/fi";

interface ThgBuyerProfileFormProps {
  user: {
    id: string;
    name: string | null;
    lastName: string | null;
    email: string;
    companyName: string | null;
    phone: string | null;
    address: Array<{
      id: string;
      street: string | null;
      streetNr: string | null;
      city: string | null;
      zip: string | null;
      country: string | null;
    }>;
  };
}

interface FormInputs {
  companyName: string;
  contactPerson: string;
  phone: string;
  website: string;
  notes: string;
}

export const ThgBuyerProfileForm = ({ user }: ThgBuyerProfileFormProps) => {
  const address = user?.address.find(Boolean); // get first element
  const [successMessage, setSuccessMessage] = useState<string>("");
  const [errorMessage, setErrorMessage] = useState<string>("");
  const [isEditing, setIsEditing] = useState(false);
  const [isFetching, setIsFetching] = useState(true);
  const [thgContactData, setThgContactData] = useState({
    contactPerson: "",
    phone: "",
    website: "",
    notes: "",
  });

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
  } = useForm<FormInputs>({
    defaultValues: {
      companyName: user.companyName || "",
      contactPerson: `${user.name} ${user.lastName}`,
      phone: user.phone || "",
      website: "",
      notes: "",
    },
  });

  // THG Company Contact Daten beim Laden abrufen
  useEffect(() => {
    const fetchThgContactData = async () => {
      try {
        const response = await fetch("/api/thg/company-contact");
        if (response.ok) {
          const data = await response.json();
          const contactData = {
            contactPerson: data.contactPerson || `${user.name} ${user.lastName}`,
            phone: data.phone || user.phone || "",
            website: data.website || "",
            notes: data.notes || "",
          };
          setThgContactData(contactData);
          reset({
            companyName: user.companyName || "",
            ...contactData,
          });
        } else if (response.status !== 404) {
          console.error("Failed to fetch THG company contact data");
        }
      } catch (error) {
        console.error("Error fetching THG company contact data:", error);
      } finally {
        setIsFetching(false);
      }
    };

    fetchThgContactData();
  }, [user.name, user.lastName, user.phone, user.companyName, reset]);

  const onSubmit = async (data: FormInputs) => {
    setSuccessMessage("");
    setErrorMessage("");

    try {
      // Update company name in user profile
      const userResponse = await fetch("/api/user/updateProfile", {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          companyName: data.companyName,
        }),
      });

      // Update THG company contact data
      const contactResponse = await fetch("/api/thg/company-contact", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          contactPerson: data.contactPerson,
          phone: data.phone,
          website: data.website,
          notes: data.notes,
        }),
      });

      if (userResponse.ok && contactResponse.ok) {
        setSuccessMessage("Profil erfolgreich aktualisiert!");
        setIsEditing(false);
        // Update local state
        setThgContactData({
          contactPerson: data.contactPerson,
          phone: data.phone,
          website: data.website,
          notes: data.notes,
        });
        // Reset success message after 3 seconds
        setTimeout(() => setSuccessMessage(""), 3000);
      } else {
        const userError = userResponse.ok ? null : await userResponse.text();
        const contactError = contactResponse.ok ? null : await contactResponse.json();
        setErrorMessage(
          userError || contactError?.error || "Fehler beim Aktualisieren des Profils",
        );
      }
    } catch (error: unknown) {
      if (error instanceof Error) {
        setErrorMessage(error.message);
      } else {
        setErrorMessage("Fehler beim Speichern");
      }
      setSuccessMessage("");
    }
  };

  const handleCancel = () => {
    reset({
      companyName: user.companyName || "",
      ...thgContactData,
    });
    setIsEditing(false);
    setErrorMessage("");
    setSuccessMessage("");
  };

  if (isFetching) {
    return (
      <div className="flex items-center justify-center p-8">
        <FiLoader className="animate-spin text-2xl text-primary" />
        <h3 className="ml-2">Lade Firmendaten...</h3>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium text-gray-800 dark:text-gray-200">Firmendaten</h3>
        {!isEditing ? (
          <Button onClick={() => setIsEditing(true)} className="bg-primary text-white" small={true}>
            <FiEdit2 className="mr-2 h-4 w-4" />
            Bearbeiten
          </Button>
        ) : (
          <div className="flex gap-2">
            <Button small={true} onClick={handleCancel} className="bg-gray-500 text-white">
              <FiX className="mr-2 h-4 w-4" />
              Abbrechen
            </Button>
          </div>
        )}
      </div>

      {successMessage && (
        <div className="rounded border border-green-200 bg-green-50 px-4 py-3 text-green-700">
          {successMessage}
        </div>
      )}

      {errorMessage && (
        <div className="rounded border border-red-200 bg-red-50 px-4 py-3 text-red-700">
          {errorMessage}
        </div>
      )}

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        {/* Firmenname */}
        <div>
          <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
            Firmenname *
          </label>
          <input
            {...register("companyName", {
              required: "Firmenname ist erforderlich",
            })}
            type="text"
            disabled={!isEditing}
            className={`input ${
              isEditing ? "bg-white dark:bg-gray-800" : "bg-gray-100 dark:bg-gray-700"
            }`}
          />
          {errors.companyName && (
            <p className="mt-1 text-sm text-red-500">{errors.companyName.message}</p>
          )}
        </div>

        {/* Ansprechpartner */}
        <div>
          <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
            Ansprechpartner
          </label>
          <input
            {...register("contactPerson")}
            type="text"
            disabled={!isEditing}
            className={`input ${
              isEditing ? "bg-white dark:bg-gray-800" : "bg-gray-100 dark:bg-gray-700"
            }`}
          />
        </div>

        {/* Telefon */}
        <div>
          <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
            Telefon
          </label>
          <input
            {...register("phone")}
            type="tel"
            disabled={!isEditing}
            placeholder="+49 **********"
            className={`input ${
              isEditing ? "bg-white dark:bg-gray-800" : "bg-gray-100 dark:bg-gray-700"
            }`}
          />
        </div>

        {/* Website */}
        <div>
          <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
            Website
          </label>
          <input
            {...register("website")}
            type="url"
            disabled={!isEditing}
            placeholder="https://www.ihre-firma.de"
            className={`input ${
              isEditing ? "bg-white dark:bg-gray-800" : "bg-gray-100 dark:bg-gray-700"
            }`}
          />
        </div>

        {/* Notizen */}
        <div>
          <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
            Notizen
          </label>
          <textarea
            {...register("notes")}
            disabled={!isEditing}
            rows={3}
            placeholder="Zusätzliche Informationen..."
            className={`input ${
              isEditing ? "bg-white dark:bg-gray-800" : "bg-gray-100 dark:bg-gray-700"
            }`}
          />
        </div>

        {/* Anzeige der nicht editierbaren Felder */}
        <div className="space-y-4 border-t border-gray-200 pt-4 dark:border-gray-700">
          <h4 className="text-md font-medium text-gray-800 dark:text-gray-200">
            Kontaktdaten (nicht änderbar)
          </h4>

          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div>
              <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
                Vorname
              </label>
              <input
                type="text"
                value={user.name || ""}
                disabled
                className="input bg-gray-100 dark:bg-gray-700"
              />
            </div>

            <div>
              <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
                Nachname
              </label>
              <input
                type="text"
                value={user.lastName || ""}
                disabled
                className="input bg-gray-100 dark:bg-gray-700"
              />
            </div>
          </div>

          <div>
            <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
              E-Mail
            </label>
            <input
              type="email"
              value={user.email}
              disabled
              className="input bg-gray-100 dark:bg-gray-700"
            />
          </div>

          <div>
            <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
              Telefon
            </label>
            <input
              type="tel"
              value={user.phone || "Nicht angegeben"}
              disabled
              className="input bg-gray-100 dark:bg-gray-700"
            />
          </div>
        </div>

        {isEditing && (
          <div className="flex justify-end">
            <Button type="submit" disabled={isSubmitting} className="bg-green-500 text-white">
              <FiSave className="mr-2 h-4 w-4" />
              {isSubmitting ? "Speichern..." : "Speichern"}
            </Button>
          </div>
        )}
      </form>
    </div>
  );
};
