import React from "react";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role } from "@prisma/client";
import InvoiceOverviewTable from "./components/InvoiceOverviewTable";
import NotFound from "~/app/(app)/not-found";
import Headline from "~/component/Headline";

const Page = async () => {
  const session = await getServerSession(authOptions);

  if (!session || session?.user?.role !== Role.ADMIN) {
    return <NotFound />;
  }

  return (
    <>
      <Headline title="Rechnungsübersicht" />
      <div className="mb-4">
        <p className="text-gray-600 dark:text-gray-400">
          Übersicht über alle Contacts und deren Rechnungsstellung der letzten 12 Monate. Aufgeteilt
          nach CPO und nicht-CPO Contacts mit Unterscheidung zwischen Rechnungen (INVOICE) und
          Gutschriften (CREDIT).
          <br />
          <span className="text-red-600">Rot markiert</span>: Ke<PERSON> Eintrag für den aktuellen Monat
        </p>
      </div>
      <InvoiceOverviewTable />
    </>
  );
};

export default Page;
