import {
  FacebookShareButton,
  FacebookIcon,
  LinkedinShareButton,
  LinkedinIcon,

  XIcon,
  TwitterShareButton,

} from "react-share";
import * as React from "react";


export default function ShareButtonBadge({
                                           shareUrl,
                                           title,
                                           showShare,
                                         }: {
  shareUrl: string;
  title: string;
  showShare: boolean;
}) {
  return (
    <>
      {showShare && (
        <div className="flex justify-end space-x-3">
          <FacebookShareButton url={shareUrl} hashtag="#EV #Achievement">
            <FacebookIcon size={40} round />
          </FacebookShareButton>

          <LinkedinShareButton url={shareUrl} title={title}>
            <LinkedinIcon size={40} round />
          </LinkedinShareButton>

          <TwitterShareButton
            url={shareUrl}
            title={title}
            hashtags={["Achievement", "EV"]}
          >
            <XIcon size={40} round />
          </TwitterShareButton>
        </div>
      )}
    </>
  );
}
