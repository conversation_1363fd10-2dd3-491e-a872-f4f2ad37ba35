// AvatarUploadButton.tsx
"use client";
import React, { useRef, useState } from "react";
import { useRouter } from "next/navigation";
import Button from "~/component/button";
import { FiUpload } from "react-icons/fi";

type Props = { className?: string };

export default function AvatarUploadButton({ className }: Props) {
  const inputRef = useRef<HTMLInputElement>(null);
  const [busy, setBusy] = useState(false);
  const router = useRouter();

  const openPicker = () => inputRef.current?.click();

  const onChange: React.ChangeEventHandler<HTMLInputElement> = async (e) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Client-Prechecks
    if (!/^image\/(png|jpe?g|webp|avif)$/.test(file.type)) { alert("Nur PNG/JPG/WebP/AVIF"); e.target.value=""; return; }
    if (file.size > 5 * 1024 * 1024) { alert("Max 5 MB"); e.target.value=""; return; }

    try {
      setBusy(true);
      const fd = new FormData();
      fd.append("file", file);

      const res = await fetch("/api/user/avatar", { method: "POST", body: fd });
      if (!res.ok) throw new Error("upload failed");

      // Server gibt z. B. { imageUrl } zurück
      await res.json();
      router.refresh();
    } catch (err) {
      console.error(err);
      alert("Upload fehlgeschlagen");
    } finally {
      setBusy(false);
      e.target.value = "";
    }
  };

  return (
    <div className={["inline-flex items-center", className].filter(Boolean).join(" ")}>
      <input
        ref={inputRef}
        type="file"
        accept="image/png,image/jpeg,image/webp,image/avif"
        className="hidden"
        onChange={onChange}
      />
      <Button
        type="button"
        onClick={openPicker}
        disabled={busy}
        className="flex items-center justify-center gap-3 h-16 px-10 text-xl rounded-2xl w-full md:w-[600px]"
      >
        <FiUpload className="h-6 w-6" />
        {busy ? "Lädt..." : "Profilbild auswählen"}
      </Button>

    </div>
  );
}
