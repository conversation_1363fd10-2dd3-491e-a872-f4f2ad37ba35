'use client';
import React, { useId, useState } from 'react';
import {PropsAccordion} from "~/types/componentTypes/accordionTypes";



const BadgeAccordion: React.FC<PropsAccordion> = ({
                                           summary,
                                           children,
                                           defaultExpanded = false,
                                           disabled = false,
                                           id,
                                           className = '',
                                         }) => {
  const autoId = useId();
  const baseId = id ?? `badge-accordion-${autoId}`;
  const headerId = `${baseId}-header`;
  const panelId = `${baseId}-panel`;

  const [open, setOpen] = useState(defaultExpanded);

  return (
    <div className={`rounded-lg border border-gray-200 ${className}`}>
      <button
        id={headerId}
        type="button"
        className="flex w-full items-center justify-between gap-3 rounded-lg px-4 py-3 text-left hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary disabled:opacity-60"
        aria-controls={panelId}
        aria-expanded={open}
        onClick={() => !disabled && setOpen((v) => !v)}
        disabled={disabled}
      >
        <span className="font-medium">
          {typeof summary === 'string' ? <span>{summary}</span> : summary}
        </span>
        <svg
          className={`h-5 w-5 transition-transform ${open ? 'rotate-180' : ''}`}
          xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
          strokeWidth={1.5} stroke="currentColor" aria-hidden="true"
        >
          <path strokeLinecap="round" strokeLinejoin="round" d="M19.5 8.25l-7.5 7.5-7.5-7.5"/>
        </svg>
      </button>

      <div
        id={panelId}
        role="region"
        aria-labelledby={headerId}
        className={`overflow-hidden transition-[max-height,opacity] duration-200 ease-out ${open ? 'opacity-100' : 'max-h-0 opacity-0'}`}
        style={{ maxHeight: open ? 1000 : 0 }}
      >
        <div className="px-4 pb-4 pt-1">
          {children}
        </div>
      </div>
    </div>
  );
};

export default BadgeAccordion;
