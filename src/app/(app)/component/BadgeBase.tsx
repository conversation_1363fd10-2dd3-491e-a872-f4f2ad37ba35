"use client";

import * as React from "react";

export interface BadgeBaseProps {
  imgSrc: string;            // Bild-URL oder data:URI
  label: string;
  locked?: boolean;
  progress?: number;         // 0..1 Ring
  onClick?: () => void;

  // Größe
  size?: "md" | "lg" | "xl";

  // NEU: Zusatzinfos
  info?: React.ReactNode;                 // Freitext/JSX
  infoMode?: "button" | "inline";         // Default: "button"
  infoButtonLabel?: string;               // a11y-Label für den Button
}

export default function BadgeBase({
                                    imgSrc,
                                    label,
                                    locked,
                                    progress = 0,
                                    onClick,
                                    size = "xl",

                                    info,
                                    infoMode = "button",
                                    infoButtonLabel = "Weitere Informationen",
                                  }: BadgeBaseProps) {
  const p = Math.max(0, Math.min(1, progress));
  const deg = Math.round(p * 360);

  const diameter =
    size === "xl" ? 176 : size === "lg" ? 144 : 112;

  const [openInfo, setOpenInfo] = React.useState(false);
  const showInlineInfo = info && infoMode === "inline";
  const showButtonInfo = info && infoMode === "button";

  return (
    <div
      role={onClick ? "button" : undefined}
      tabIndex={onClick ? 0 : -1}
      onClick={onClick}
      onKeyDown={(e) => {
        if (!onClick) return;
        if (e.key === "Enter" || e.key === " ") {
          e.preventDefault();
          onClick();
        }
      }}
      className={[
        "flex flex-col items-center gap-2 outline-none",
        onClick ? "cursor-pointer focus-visible:ring-2 focus-visible:ring-purple-500/60 rounded-xl p-1" : "",
      ].join(" ")}
    >
      <div className="relative" style={{ width: diameter, height: diameter }}>
        {/* Fortschrittsring: OU-Primary per bg-primary + Mask */}
        {p > 0 && (
          <div
            className="absolute -inset-1 rounded-full bg-primary"
            style={{
              WebkitMaskImage: `conic-gradient(#000 ${deg}deg, transparent ${deg}deg)`,
              maskImage: `conic-gradient(#000 ${deg}deg, transparent ${deg}deg)`,
            }}
            aria-hidden
          />
        )}

        <div
          className={[
            "relative overflow-hidden rounded-full ring-1 ring-gray-200",
            locked ? "grayscale opacity-70" : "",
          ].join(" ")}
          style={{ width: diameter, height: diameter }}
          title={label}
        >
          <img
            src={imgSrc}
            alt={label}
            className="h-full w-full object-cover"
            draggable={false}
          />
        </div>

        {locked && (
          <span
            className="absolute -right-1 -top-1 rounded-full bg-white/90 p-1 ring-1 ring-gray-200"
            title="Gesperrt"
            aria-label="Gesperrt"
          >
            <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <rect x="4" y="10" width="16" height="10" rx="2" strokeWidth="2" />
              <path d="M8 10V7a4 4 0 1 1 8 0v3" strokeWidth="2" />
            </svg>
          </span>
        )}

        {p > 0 && p < 1 && (
          <span className="absolute bottom-2 left-1/2 -translate-x-1/2 rounded-full bg-white/90 px-2 py-0.5 text-xs ring-1 ring-gray-200">
            {Math.round(p * 100)}%
          </span>
        )}
      </div>

      {/* Label + optionaler Info-Button */}
      <div className="flex items-center gap-1">
        <div className="text-sm text-gray-600">{label}</div>

        {showButtonInfo && (
          <button
            type="button"
            aria-label={infoButtonLabel}
            aria-expanded={openInfo}
            onClick={(e) => {
              e.stopPropagation(); // Badge-Click nicht triggern
              setOpenInfo((v) => !v);
            }}
            className="ml-1 inline-flex h-5 w-5 items-center justify-center rounded-full ring-1 ring-gray-300 text-gray-600 hover:bg-gray-100"
            title={infoButtonLabel}
          >
            i
          </button>
        )}
      </div>

      {/* Inline-Info (immer sichtbar) */}
      {showInlineInfo && (
        <div
          className="max-w-[20rem] text-center text-xs text-gray-700"
          onClick={(e) => e.stopPropagation()}
        >
          {info}
        </div>
      )}

      {/* Button-Info (ein-/ausklappbar) */}
      {showButtonInfo && openInfo && (
        <div
          className="max-w-[20rem] rounded-md bg-gray-50 px-3 py-2 text-center text-xs text-gray-700 ring-1 ring-gray-200"
          onClick={(e) => e.stopPropagation()}
        >
          {info}
        </div>
      )}
    </div>
  );
}
