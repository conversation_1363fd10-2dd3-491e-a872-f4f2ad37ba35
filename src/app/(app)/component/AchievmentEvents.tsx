"use client";

import * as React from "react";
import But<PERSON> from "~/component/button";
import { createPortal } from "react-dom";
import {EventDTO, EventStatus} from "~/types/achievment/achievmentEventTypes";


export default function AchievementEvents() {
  const [events, setEvents] = React.useState<EventDTO[] | null>(null);
  const [loading, setLoading] = React.useState(true);
  const [err, setErr] = React.useState<string | null>(null);
  const [enrolling, setEnrolling] = React.useState<string | null>(null);

  const [previewUrl, setPreviewUrl] = React.useState<string | null>(null);
  const [previewTitle, setPreviewTitle] = React.useState<string>("");
  const [mounted, setMounted] = React.useState(false);
  React.useEffect(() => setMounted(true), []);

  React.useEffect(() => {
    let alive = true;
    (async () => {
      try {
        setLoading(true);
        const res = await fetch("/api/achievments/events/list", { cache: "no-store" });
        if (!res.ok) throw new Error(await res.text());
        const data = (await res.json()) as { events: EventDTO[] };
        if (alive) setEvents(data.events);
      } catch (e: any) {
        if (alive) setErr(e?.message || "Konnte Events nicht laden.");
      } finally {
        if (alive) setLoading(false);
      }
    })();
    return () => { alive = false; };
  }, []);

  async function handleEnroll(eventId: string) {
    try {
      setEnrolling(eventId);
      const res = await fetch("/api/achievments/events/enroll", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ eventId }),
      });
      if (!res.ok) throw new Error(await res.text());
      setEvents((prev) => prev ? prev.map(e => e.id === eventId ? { ...e, enrollmentStatus: "PARTICIPATING" as const } : e) : prev);
    } catch (e: any) {
      alert(e?.message || "Opt-in fehlgeschlagen.");
    } finally {
      setEnrolling(null);
    }
  }

  if (loading) return <div className="rounded-xl border border-dashed border-gray-300 p-6 text-center text-sm text-gray-600">Events werden geladen…</div>;
  if (err) return <div className="rounded-xl border border-red-200 bg-red-50 p-4 text-sm text-red-700">{err}</div>;
  if (!events?.length) return <div className="rounded-xl border border-dashed border-gray-300 p-6 text-center text-sm text-gray-600">Keine Events gerade am Laufen</div>;

  return (
    <>
      {/* Längliche Karten – identischer Look wie deine Dummy-Events */}
      <div className="space-y-3">
        {events.map((ev) => {
          const pill =
            ev.enrollmentStatus === "COMPLETED" ? "bg-green-50 text-green-700 ring-green-200" :
              ev.enrollmentStatus === "FAILED"    ? "bg-red-50 text-red-700 ring-red-200"       :
                ev.phase === "ONGOING"               ? "bg-blue-50 text-blue-700 ring-blue-200"   :
                  ev.phase === "UPCOMING"              ? "bg-amber-50 text-amber-700 ring-amber-200":
                    "bg-gray-50 text-gray-700 ring-gray-200";

          const pillText =
            ev.enrollmentStatus === "COMPLETED" ? "success" :
              ev.enrollmentStatus === "FAILED"    ? "failed"  :
                ev.phase === "ONGOING"              ? "läuft"   :
                  ev.phase === "UPCOMING"             ? "info"    : "vorbei";

          const canEnroll = ev.enrollmentStatus === null && ev.phase !== "ENDED";

          return (
            <article key={ev.id} className="relative rounded-xl border p-4 transition-shadow hover:shadow-sm">
              {/* Kopfzeile analog Dummy-Karte */}
              <div className="flex items-start justify-between gap-3">
                <div className="min-w-0">
                  <h4 className="text-sm font-medium">{ev.title}</h4>
                  <p className="text-xs text-gray-500">{formatMeta(ev.startsAt, ev.endsAt, ev.phase)}</p>
                  {ev.description && <p className="mt-1 text-xs text-gray-600">{ev.description}</p>}
                </div>

                <div className="flex items-center gap-2">
                  {canEnroll && (
                    <Button
                      type="button"
                      onClick={() => handleEnroll(ev.id)}
                      disabled={enrolling === ev.id}
                      className="px-3 py-1.5"
                      small
                      title="Mitmachen"
                    >
                      {enrolling === ev.id ? "Beitreten…" : "Mitmachen"}
                    </Button>
                  )}
                  <span className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs ring-1 ${pill}`}>{pillText}</span>
                </div>
              </div>

              {/* Progressbars */}
              {!!ev.stats?.length && (
                <div className="mt-3 space-y-2">
                  {ev.stats.map((s, idx) => {
                    const current = Math.max(0, Number(s.value ?? 0));
                    const target  = Math.max(0, Number(s.target ?? 0));
                    const p = target > 0 ? Math.max(0, Math.min(1, current / target)) : 0;
                    return (
                      <div key={idx}>
                        <div className="mb-1 flex items-center justify-between text-[11px] text-gray-600">
                          <span className="truncate">{s.label}</span>
                          <span>{Math.round(p * 100)}% ({current}/{target})</span>
                        </div>
                        <div className="h-2 w-full overflow-hidden rounded-full bg-gray-200">
                          <div
                            className="h-full bg-primary"
                            style={{
                              width: `${Math.round(p * 100)}%`,
                              ...(s.color ? { backgroundColor: s.color } : {}),
                            }}
                          />
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}

              {/* Reward-Box */}
              {ev.reward && (
                <div className="mt-3 rounded-md bg-gray-50 p-3 text-sm text-gray-700 ring-1 ring-gray-200">
                  <b>Reward:</b>{" "}
                  {ev.reward.type === "discount_percent" && ev.reward.value != null
                    ? `${ev.reward.value}% Rabatt – `
                    : ""}
                  {ev.reward.description ?? "Belohnung verfügbar."}
                </div>
              )}

              {/* Teilnahme-Hinweis wie bei Dummy-Zeilen */}
              <div className="mt-3 text-xs text-gray-500">
                {ev.enrollmentStatus
                  ? ev.enrollmentStatus === "PARTICIPATING" ? "Du nimmst teil." :
                    ev.enrollmentStatus === "COMPLETED"     ? "Abgeschlossen."  :
                      "Beendet."
                  : "Noch nicht beigetreten."}
              </div>

              {/* Badge unten rechts – klick zum Preview */}

              {(() => {
                const badgeSrc = ensureExtension(resolveBadgeUrl(ev.badgeImageUrl));
                if (!badgeSrc) return null;

                return (
                  <button
                    type="button"
                    title="Event-Badge ansehen"
                    onClick={() => { setPreviewUrl(badgeSrc); setPreviewTitle(ev.title); }}
                    className="absolute bottom-3 right-3 h-12 w-12 overflow-hidden rounded-full ring-1 ring-gray-200 hover:brightness-95"
                  >
                    <img
                      src={badgeSrc}
                      alt={`${ev.title} Badge`}
                      className="h-full w-full object-cover"
                      draggable={false}
                      onError={(e) => {
                        // Optionaler Fallback, wenn der Pfad doch nicht stimmt:
                        e.currentTarget.src = "/images/badges/fallback.png";
                      }}
                    />
                  </button>
                );
              })()}
            </article>
          );
        })}
      </div>

      {/* Badge Preview Modal */}
      {previewUrl && mounted && createPortal(
        <div
          className="fixed inset-0 flex items-center justify-center p-4"
          style={{ zIndex: 2147483647 }}           // 🔝 über alles
          onClick={() => setPreviewUrl(null)}
          role="dialog" aria-modal="true"
        >
          <div className="absolute inset-0 bg-black/50 z-0" />
          <div
            className="relative z-[1] w-full max-w-sm rounded-2xl bg-white p-4 shadow-xl"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="mb-3 flex items-center justify-between">
              <div className="font-semibold">{previewTitle}</div>
              <button
                type="button"
                className="rounded-md p-2 hover:bg-gray-100"
                aria-label="Schließen"
                onClick={() => setPreviewUrl(null)}
              >
                ✕
              </button>
            </div>
            <div className="mx-auto h-56 w-56 overflow-hidden rounded-full ring-1 ring-gray-200">
              <img src={previewUrl} alt={previewTitle} className="h-full w-full object-cover" />
            </div>
          </div>
        </div>,
        document.body
      )}

    </>
  );
}

// ---- Helper: Badge-URL normalisieren ----
function resolveBadgeUrl(u?: string | null): string | null {
  if (!u) return null;
  const s = u.trim();
  if (!s) return null;

  // data-URLs oder http(s) durchlassen
  if (s.startsWith("data:")) return s;
  if (/^https?:\/\//i.test(s)) return s;

  // "~/" -> "/" (manchmal aus Imports kopiert)
  if (s.startsWith("~/")) return "/" + s.slice(2);

  // "public/..."  -> ohne "public"
  if (s.startsWith("public/")) return "/" + s.slice("public/".length);

  // "./images/..." -> "/images/..."
  if (s.startsWith("./")) return "/" + s.slice(2);

  // "images/..." -> "/images/..."
  if (s.startsWith("images/")) return "/" + s;

  // bereits absolute Site-Root?
  if (s.startsWith("/")) return s;

  // letzte Option: als site-root behandeln
  return "/" + s;
}

// Optional: Prüfen, ob eine Dateiendung vorhanden ist (png/jpg/svg)
// – Wenn du oft nur Basisnamen speicherst, kannst du hier z.B. ".png" anhängen.
function ensureExtension(src: string | null): string | null {
  if (!src) return null;
  if (/\.(png|jpe?g|webp|gif|svg)$/i.test(src)) return src;
  // Wenn du IMMER PNG nutzt, kannst du notfalls ".png" ergänzen:
  return src + ".png";
}


function formatMeta(startsAt?: string | null, endsAt?: string | null, phase?: EventStatus): string {
  const s = startsAt ? new Date(startsAt) : null;
  const e = endsAt ? new Date(endsAt) : null;
  const fmt = (d: Date) => d.toLocaleDateString("de-DE", { day: "2-digit", month: "2-digit", year: "numeric" });
  if (phase === "UPCOMING") return s ? `Start: ${fmt(s)}` : "Bald";
  if (phase === "ONGOING")  return e ? `Ende: ${fmt(e)}` : "Läuft";
  if (phase === "ENDED")    return e ? `Ende: ${fmt(e)}` : "Vorbei";
  return s || e ? [s && `Start: ${fmt(s)}`, e && `Ende: ${fmt(e)}`].filter(Boolean).join(" • ") : "";
}
