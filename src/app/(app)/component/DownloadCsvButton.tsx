'use client';
import React from 'react';
import Button from '~/component/button';
import { mkConfig, generateCsv, download } from 'export-to-csv';
import {CsvRowAdhocTarif} from '~/types/csv/csvDownloadButtonAdhocTarif';


export default function DownloadCsvButton({ rows, csvFileName }: { rows: CsvRowAdhocTarif[], csvFileName?: string }) {
  const onDownload = () => {
    const csvConfig = mkConfig({
      filename: `${csvFileName}_${new Date().toISOString().slice(0,10)}`,
      useKeysAsHeaders: true,     // Spalten aus Keys ableiten
      fieldSeparator: ';',        // ; ist oft Excel-freundlicher in DE
      decimalSeparator: ',',      // für Kommazahlen in DE
      useBom: true,                  // UTF-8 BOM -> Umlaute in Excel
    });
    const csv = generateCsv(csvConfig)(rows);
    download(csvConfig)(csv);
  };

  return <Button onClick={onDownload}>Alle Preise herunterladen (CSV)</Button>;
}
