import * as React from "react";
import { createPortal } from "react-dom";

export function AchievmentBadgeModalPortal({ open, onClose, children }: {
  open: boolean; onClose: () => void; children: React.ReactNode;
}) {
  React.useEffect(() => {
    if (!open) return;
    const prev = document.body.style.overflow;
    document.body.style.overflow = "hidden";
    return () => { document.body.style.overflow = prev; };
  }, [open]);

  if (!open) return null;

  return createPortal(
    <div
      className="fixed inset-0 flex items-center justify-center p-4 overscroll-contain"
      style={{ zIndex: 2147483647 }} // 🚀 höher als alles andere
      onClick={onClose}
      role="dialog"
      aria-modal="true"
    >
      {/* Overlay über gesamtem Viewport */}
      <div className="absolute inset-0 bg-black/50 z-0" />

      {/* Panel über Overlay */}
      <div
        className="
          relative z-[1] shadow-xl
          w-full h-dvh max-h-dvh overflow-y-auto bg-white p-0 rounded-none
          sm:h-auto sm:max-w-lg sm:max-h-[90vh] sm:rounded-2xl sm:p-4
        "
        onClick={(e) => e.stopPropagation()}
      >
        {children}
      </div>
    </div>,
    document.body
  );
}
