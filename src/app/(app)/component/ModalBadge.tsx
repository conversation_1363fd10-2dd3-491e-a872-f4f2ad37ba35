'use client';

import * as React from 'react';
import { createPortal } from 'react-dom';
import { ModalBadgeProps } from "~/types/componentTypes/modalBadgeTypes";



export function ModalPortal({ open, onClose, children }: ModalBadgeProps) {
  // Body-Scroll sperren
  React.useEffect(() => {
    if (!open) return;
    const prev = document.body.style.overflow;
    document.body.style.overflow = 'hidden';
    return () => { document.body.style.overflow = prev; };
  }, [open]);

  // ESC schließt
  React.useEffect(() => {
    if (!open) return;
    const onKey = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        e.preventDefault();
        onClose();
      }
    };
    window.addEventListener('keydown', onKey);
    return () => window.removeEventListener('keydown', onKey);
  }, [open, onClose]);

  if (!open) return null;

  return createPortal(
    <div
      className="fixed inset-0 flex items-center justify-center p-4 overscroll-contain"
      style={{ zIndex: 2147483647 }}
      onClick={onClose}
      role="dialog"
      aria-modal="true"
    >
      {/* Overlay */}
      <div className="absolute inset-0 bg-black/50 z-0" />

      {/* Panel */}
      <div
        className="
          relative z-[1] shadow-xl
          w-full h-dvh max-h-dvh overflow-y-auto bg-white p-0 rounded-none
          sm:h-auto sm:max-w-lg sm:max-h-[90vh] sm:rounded-2xl sm:p-4
        "
        onClick={(e) => e.stopPropagation()}
      >
        {children}
      </div>
    </div>,
    document.body
  );
}

/** Bequemer Wrapper mit Kopfzeile + Close-Button */
const Modal: React.FC<ModalBadgeProps & { title?: React.ReactNode }> = ({
                                                                     open, onClose, children, title, titleId = 'modal-title',
                                                                   }) => {
  return (
    <ModalPortal open={open} onClose={onClose} titleId={titleId}>
      <div className="flex items-center justify-between p-3 sm:p-0">
        {title ? <h3 id={titleId} className="text-lg font-semibold">{title}</h3> : <span />}
        <button
          type="button"
          onClick={onClose}
          className="rounded-md p-2 hover:bg-gray-100"
          aria-label="Schließen"
        >
          ✕
        </button>
      </div>
      <div className="p-3 sm:p-0">
        {children}
      </div>
    </ModalPortal>
  );
};

export default Modal;
