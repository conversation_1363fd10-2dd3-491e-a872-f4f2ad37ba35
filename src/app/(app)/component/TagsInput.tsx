import React, { useState } from "react";

interface TagsInputProps {
    value: string[];
    onChange: (tags: string[]) => void;
    placeholder?: string;
    disabled?: boolean;
    className?: string;

    /** NEU: meldet den unübernommenen Eingabetext an den Parent */
    onDraftChange?: (s: string) => void;

    /** NEU: zusätzliche Klassen, die angewendet werden, wenn Draft vorhanden ist (z. B. roter Glow) */
    draftClassName?: string;
}

const TagsInput: React.FC<TagsInputProps> = ({
                                                 value,
                                                 onChange,
                                                 placeholder = "Wert hinzufügen...",
                                                 disabled = false,
                                                 className = "",
                                                 onDraftChange,
                                                 draftClassName,
                                             }) => {
    const [inputValue, setInputValue] = useState("");

    const addTag = () => {
        const tag = inputValue.trim();
        if (tag && !value.includes(tag)) {
            onChange([...value, tag]);
            setInputValue("");
            onDraftChange?.(""); // Draft geleert
        }
    };

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const v = e.target.value;
        setInputValue(v);
        onDraftChange?.(v); // Draft an Parent melden
    };

    const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
        if ((e.key === "Enter" || e.key === ",") && inputValue.trim() !== "") {
            e.preventDefault();
            addTag();
        }
    };

    const removeTag = (tag: string) => {
        onChange(value.filter(t => t !== tag));
    };

    const hasDraft = inputValue.trim().length > 0;

    return (
        <div
            className={`
        flex flex-wrap items-center gap-1 min-h-[48px]
        bg-white border border-gray-300 rounded-xl px-3 py-2
        shadow-sm focus-within:ring-2 focus-within:ring-primary/50
        ${hasDraft && draftClassName ? draftClassName : ""}
        ${className}
      `}
        >
            {value.map(tag => (
                <span
                    key={tag}
                    className="
            flex items-center gap-1
            bg-gray-200 text-slate-700 rounded-md px-2 py-1 my-0.5
            font-normal text-base
          "
                >
          {tag}
                    {!disabled && (
                        <button
                            type="button"
                            onClick={() => removeTag(tag)}
                            className="focus:outline-none ml-1 text-slate-500 hover:text-red-500"
                            tabIndex={-1}
                        >
                            <span>×</span>
                        </button>
                    )}
        </span>
            ))}

            <input
                type="text"
                value={inputValue}
                onChange={handleInputChange}
                onKeyDown={handleKeyDown}
                disabled={disabled}
                placeholder={placeholder}
                className="
          flex-1 min-w-[120px] py-1 px-1
          bg-transparent border-none outline-none text-base
          text-slate-700 placeholder:text-gray-400
        "
            />

            {!disabled && (
                <button
                    type="button"
                    onClick={addTag}
                    className="
            w-8 h-8 flex items-center justify-center
            bg-gray-200 text-slate-700 hover:bg-primary/80 hover:text-white
            rounded-md ml-1 transition-all duration-150
            focus:outline-none
          "
                    tabIndex={-1}
                >
                    +
                </button>
            )}
        </div>
    );
};

export default TagsInput;
