import React from "react";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role } from "@prisma/client";
import prisma from "~/server/db/prisma";
import { PowerContractForm } from "../components/PowerContractForm";
import { notFound } from "next/navigation";
import { getOusBelowOu } from "~/server/model/ou/func";

export const revalidate = 0;

interface Props {
  params: {
    slug: string;
  };
}

async function getPowerContract(id: string) {
  const session = await getServerSession(authOptions);
  if (!session || ![Role.ADMIN, Role.CPO, Role.CARD_MANAGER].includes(session?.user?.role)) {
    return null;
  }

  const contract = await prisma.powerContract.findUnique({
    where: {
      id: id,
    },
    include: {
      location: {
        select: {
          id: true,
          name: true,
          ouId: true,
        },
      },
    },
  });

  // <PERSON><PERSON><PERSON><PERSON>, ob der Benutzer Zugriff auf diesen Vertrag hat (alle Rollen verwenden selectedOu)
  if (contract) {
    const currentOu = await prisma.ou.findUnique({
      where: { id: session.user.selectedOu.id }
    });

    if (currentOu) {
      const ousBelow = await getOusBelowOu(currentOu);
      const ouIds = ousBelow.map(ou => ou.id);

      if (!ouIds.includes(contract.location.ouId)) {
        return null; // Kein Zugriff
      }
    }
  }

  return contract ? JSON.parse(JSON.stringify(contract)) : null;
}

async function getLocations() {
  const session = await getServerSession(authOptions);
  if (!session || ![Role.ADMIN, Role.CPO, Role.CARD_MANAGER].includes(session?.user?.role)) {
    return [];
  }

  // Alle Rollen verwenden selectedOu für konsistente Filterung
  const currentOu = await prisma.ou.findUnique({
    where: { id: session.user.selectedOu.id }
  });

  let whereClause = {};
  if (currentOu) {
    const ousBelow = await getOusBelowOu(currentOu);
    const ouIds = ousBelow.map(ou => ou.id);

    whereClause = {
      ouId: { in: ouIds },
    };
  }

  const locations = await prisma.location.findMany({
    where: whereClause,
    select: {
      id: true,
      name: true,
      ouId: true,
    },
    orderBy: {
      name: 'asc',
    },
  });

  return JSON.parse(JSON.stringify(locations));
}

const EditPowerContractPage = async ({ params }: Props) => {
  const session = await getServerSession(authOptions);

  if (!session || ![Role.ADMIN, Role.CPO, Role.CARD_MANAGER].includes(session?.user?.role)) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-red-500 text-lg">Keine Berechtigung für diese Seite</p>
      </div>
    );
  }

  const contract = await getPowerContract(params.slug);
  
  if (!contract) {
    notFound();
  }

  const locations = await getLocations();

  return (
    <PowerContractForm 
      contract={contract} 
      locations={locations} 
    />
  );
};

export default EditPowerContractPage;
