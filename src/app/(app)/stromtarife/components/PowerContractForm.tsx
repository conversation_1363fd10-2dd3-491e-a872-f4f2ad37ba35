"use client";
import React, { useState, useTransition } from "react";
import { useForm, type SubmitHandler } from "react-hook-form";
import { useRouter } from "next/navigation";
import { type PowerContract, type Location } from "@prisma/client";
import Button from "~/component/button";
import Card from "~/component/card";
import { FaSave, FaArrowLeft, FaInfoCircle } from "react-icons/fa";
import { BsLightningChargeFill, BsCalendar } from "react-icons/bs";
import { MdEuro, MdBusiness } from "react-icons/md";

type PowerContractWithLocation = PowerContract & {
  location?: {
    id: string;
    name: string;
    ouId: string;
  };
};

interface Props {
  contract?: PowerContractWithLocation;
  locations: Location[];
}

export const PowerContractForm = ({ contract, locations }: Props) => {
  const router = useRouter();
  const [isPending, startTransition] = useTransition();
  const [contractType, setContractType] = useState(
    contract?.typeOfContract || "fixed"
  );

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors, isDirty },
  } = useForm<PowerContract>({
    defaultValues: contract
      ? {
          ...contract,
          start: contract.start ? new Date(contract.start).toISOString().split('T')[0] : '',
          end: contract.end ? new Date(contract.end).toISOString().split('T')[0] : '',
        }
      : {
          typeOfContract: "fixed",
          start: "",
          end: "",
          monthlyFixCost: 0,
          baseKWhPrice: 0,
          kwhPrice: 0,
          contractWith: "",
          contractNumber: "",
          customerNumber: "",
          supplyNetworkOperator: "",
          locationId: "",
        },
  });

  const watchedContractType = watch("typeOfContract");

  const onSubmit: SubmitHandler<PowerContract> = async (data) => {
    try {
      const response = await fetch("/api/powerContract/upsert", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          data: {
            ...data,
            id: contract?.id,
          }
        }),
      });

      if (response.ok) {
        startTransition(() => {
          router.push("/stromtarife");
          router.refresh();
        });
      } else {
        console.error("Fehler beim Speichern des Stromtarifs");
      }
    } catch (error) {
      console.error("Fehler beim Speichern:", error);
    }
  };

  const isFixed = watchedContractType === "fixed" || watchedContractType === "Contract";

  return (
    <div className="max-w-4xl mx-auto">
      <div className="mb-6 flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold dark:text-white">
            {contract ? "Stromtarif bearbeiten" : "Neuer Stromtarif"}
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            {contract 
              ? `Bearbeitung des Stromvertrags für ${contract.location?.name || 'Standort'}`
              : "Erstellen Sie einen neuen Stromvertrag für einen Standort"
            }
          </p>
        </div>
        <Button
          type="button"
          onClick={() => router.back()}
          className="bg-gray-500 hover:bg-gray-600"
        >
          <FaArrowLeft className="mr-2" />
          Zurück
        </Button>
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* Grunddaten */}
        <Card>
          <div className="p-6">
            <h2 className="text-xl font-semibold mb-4 text-primary flex items-center">
              <MdBusiness className="mr-2" />
              Grunddaten
            </h2>
            
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  <span className="flex items-center">
                    <FaInfoCircle className="mr-1 text-blue-500" size={12} />
                    Standort *
                  </span>
                </label>
                <p className="text-xs text-gray-500 mb-2">
                  Wählen Sie den Standort aus, für den dieser Stromvertrag gelten soll.
                </p>
                <select
                  {...register("locationId", { required: "Standort ist erforderlich" })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white dark:border-gray-600"
                  disabled={!!contract}
                >
                  <option value="">Standort auswählen...</option>
                  {locations.map((location) => (
                    <option key={location.id} value={location.id}>
                      {location.name}
                    </option>
                  ))}
                </select>
                {errors.locationId && (
                  <p className="text-red-500 text-xs mt-1">{errors.locationId.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  <span className="flex items-center">
                    <FaInfoCircle className="mr-1 text-blue-500" size={12} />
                    Vertragstyp *
                  </span>
                </label>
                <p className="text-xs text-gray-500 mb-2">
                  Fix = Fester kWh-Preis | Dynamisch = Variable Preise basierend auf EPEX-Markt
                </p>
                <select
                  {...register("typeOfContract", { required: "Vertragstyp ist erforderlich" })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white dark:border-gray-600"
                  onChange={(e) => setContractType(e.target.value)}
                >
                  <option value="fixed">Fix - Fester kWh-Preis</option>
                  <option value="stock">Dynamisch - Variable Preise (EPEX)</option>
                </select>
                {errors.typeOfContract && (
                  <p className="text-red-500 text-xs mt-1">{errors.typeOfContract.message}</p>
                )}
              </div>
            </div>
          </div>
        </Card>

        {/* Vertragslaufzeit */}
        <Card>
          <div className="p-6">
            <h2 className="text-xl font-semibold mb-4 text-primary flex items-center">
              <BsCalendar className="mr-2" />
              Vertragslaufzeit
            </h2>
            
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  <span className="flex items-center">
                    <FaInfoCircle className="mr-1 text-blue-500" size={12} />
                    Vertragsbeginn *
                  </span>
                </label>
                <p className="text-xs text-gray-500 mb-2">
                  Das Datum, ab dem dieser Stromvertrag gültig ist.
                </p>
                <input
                  type="date"
                  {...register("start", { required: "Vertragsbeginn ist erforderlich" })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white dark:border-gray-600"
                />
                {errors.start && (
                  <p className="text-red-500 text-xs mt-1">{errors.start.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  <span className="flex items-center">
                    <FaInfoCircle className="mr-1 text-blue-500" size={12} />
                    Vertragsende *
                  </span>
                </label>
                <p className="text-xs text-gray-500 mb-2">
                  Das Datum, bis zu dem dieser Stromvertrag gültig ist.
                </p>
                <input
                  type="date"
                  {...register("end", { required: "Vertragsende ist erforderlich" })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white dark:border-gray-600"
                />
                {errors.end && (
                  <p className="text-red-500 text-xs mt-1">{errors.end.message}</p>
                )}
              </div>
            </div>
          </div>
        </Card>

        {/* Preisgestaltung */}
        <Card>
          <div className="p-6">
            <h2 className="text-xl font-semibold mb-4 text-primary flex items-center">
              <MdEuro className="mr-2" />
              Preisgestaltung
            </h2>
            
            <div className="grid md:grid-cols-2 gap-6">
              {isFixed ? (
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    <span className="flex items-center">
                      <BsLightningChargeFill className="mr-1 text-orange-500" size={12} />
                      Fester kWh-Preis (€/kWh) *
                    </span>
                  </label>
                  <p className="text-xs text-gray-500 mb-2">
                    Der feste Preis pro Kilowattstunde über die gesamte Vertragslaufzeit. Netto-Preis ohne MwSt.
                  </p>
                  <input
                    type="number"
                    step="0.0001"
                    {...register("kwhPrice", { 
                      required: isFixed ? "kWh-Preis ist erforderlich" : false,
                      min: { value: 0, message: "Preis muss positiv sein" }
                    })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white dark:border-gray-600"
                    placeholder="z.B. 0.2500"
                  />
                  {errors.kwhPrice && (
                    <p className="text-red-500 text-xs mt-1">{errors.kwhPrice.message}</p>
                  )}
                </div>
              ) : (
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    <span className="flex items-center">
                      <BsLightningChargeFill className="mr-1 text-green-500" size={12} />
                      Basis kWh-Preis (€/kWh) *
                    </span>
                  </label>
                  <p className="text-xs text-gray-500 mb-2">
                    Der Aufschlag zum EPEX-Spotmarktpreis. Der finale Preis = EPEX-Preis + Basis-Preis.
                  </p>
                  <input
                    type="number"
                    step="0.0001"
                    {...register("baseKWhPrice", { 
                      required: !isFixed ? "Basis kWh-Preis ist erforderlich" : false,
                      min: { value: 0, message: "Preis muss positiv sein" }
                    })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white dark:border-gray-600"
                    placeholder="z.B. 0.0500"
                  />
                  {errors.baseKWhPrice && (
                    <p className="text-red-500 text-xs mt-1">{errors.baseKWhPrice.message}</p>
                  )}
                </div>
              )}

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  <span className="flex items-center">
                    <MdEuro className="mr-1 text-blue-500" size={12} />
                    Monatliche Grundkosten (€/Monat)
                  </span>
                </label>
                <p className="text-xs text-gray-500 mb-2">
                  Optionale monatliche Fixkosten unabhängig vom Verbrauch. Lassen Sie das Feld leer oder setzen Sie 0 für keine Grundkosten.
                </p>
                <input
                  type="number"
                  step="0.01"
                  {...register("monthlyFixCost", {
                    min: { value: 0, message: "Grundkosten müssen positiv sein" }
                  })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white dark:border-gray-600"
                  placeholder="z.B. 50.00"
                />
                {errors.monthlyFixCost && (
                  <p className="text-red-500 text-xs mt-1">{errors.monthlyFixCost.message}</p>
                )}
              </div>
            </div>
          </div>
        </Card>

        {/* Vertragsdetails */}
        <Card>
          <div className="p-6">
            <h2 className="text-xl font-semibold mb-4 text-primary flex items-center">
              <FaInfoCircle className="mr-2" />
              Vertragsdetails
            </h2>
            
            <div className="grid md:grid-cols-2 gap-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Vertragspartner
                </label>
                <p className="text-xs text-gray-500 mb-2">
                  Der Name des Stromlieferanten oder Energieversorgers.
                </p>
                <input
                  type="text"
                  {...register("contractWith")}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white dark:border-gray-600"
                  placeholder="z.B. Stadtwerke Musterstadt"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Vertragsnummer
                </label>
                <p className="text-xs text-gray-500 mb-2">
                  Die eindeutige Vertragsnummer des Stromlieferanten.
                </p>
                <input
                  type="text"
                  {...register("contractNumber")}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white dark:border-gray-600"
                  placeholder="z.B. SV-2024-001234"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Kundennummer *
                </label>
                <p className="text-xs text-gray-500 mb-2">
                  Ihre Kundennummer beim Stromlieferanten.
                </p>
                <input
                  type="text"
                  {...register("customerNumber", { required: "Kundennummer ist erforderlich" })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white dark:border-gray-600"
                  placeholder="z.B. KD-123456789"
                />
                {errors.customerNumber && (
                  <p className="text-red-500 text-xs mt-1">{errors.customerNumber.message}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Netzbetreiber *
                </label>
                <p className="text-xs text-gray-500 mb-2">
                  Der örtliche Netzbetreiber, der das Stromnetz betreibt.
                </p>
                <input
                  type="text"
                  {...register("supplyNetworkOperator", { required: "Netzbetreiber ist erforderlich" })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white dark:border-gray-600"
                  placeholder="z.B. Wesernetz, Avacon"
                />
                {errors.supplyNetworkOperator && (
                  <p className="text-red-500 text-xs mt-1">{errors.supplyNetworkOperator.message}</p>
                )}
              </div>
            </div>
          </div>
        </Card>

        {/* Aktionen */}
        <div className="flex justify-end gap-4">
          <Button
            type="button"
            onClick={() => router.back()}
            className="bg-gray-500 hover:bg-gray-600"
          >
            Abbrechen
          </Button>
          <Button
            type="submit"
            disabled={isPending || !isDirty}
            className="bg-green-500 hover:bg-green-600"
          >
            <FaSave className="mr-2" />
            {isPending ? "Speichern..." : "Speichern"}
          </Button>
        </div>
      </form>
    </div>
  );
};
