import React from "react";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role } from "@prisma/client";
import prisma from "~/server/db/prisma";
import Card from "~/component/card";
import Button from "~/component/button";
import Link from "next/link";
import { AiOutlinePlusCircle } from "react-icons/ai";
import { PowerContractTable } from "./components/PowerContractTable";
import { getOusBelowOu } from "~/server/model/ou/func";

export const revalidate = 0;

async function getPowerContracts() {
  const session = await getServerSession(authOptions);
  if (!session || ![Role.ADMIN, Role.CPO, Role.CARD_MANAGER].includes(session?.user?.role)) {
    return { fixedContracts: [], dynamicContracts: [] };
  }

  // Alle Rollen verwenden selectedOu für konsistente Filterung
  const currentOu = await prisma.ou.findUnique({
    where: { id: session.user.selectedOu.id }
  });

  let whereClause = {};
  if (currentOu) {
    const ousBelow = await getOusBelowOu(currentOu);
    const ouIds = ousBelow.map(ou => ou.id);

    whereClause = {
      location: {
        ouId: { in: ouIds },
      },
    };
  }

  const currentDate = new Date();

  const contracts = await prisma.powerContract.findMany({
    where: {
      ...whereClause,
      // Nur aktive Verträge anzeigen
      start: {
        lte: currentDate,
      },
      end: {
        gte: currentDate,
      },
    },
    include: {
      location: {
        select: {
          id: true,
          name: true,
          ouId: true,
        },
      },
    },
    orderBy: [
      { typeOfContract: 'asc' },
      { start: 'desc' }
    ],
  });

  // Trennung zwischen fixen und dynamischen Verträgen
  const fixedContracts = contracts.filter(contract => 
    contract.typeOfContract === 'fixed' || contract.typeOfContract === 'Contract'
  );
  
  const dynamicContracts = contracts.filter(contract => 
    contract.typeOfContract === 'stock' || contract.typeOfContract === 'Stock'
  );

  return {
    fixedContracts: JSON.parse(JSON.stringify(fixedContracts)),
    dynamicContracts: JSON.parse(JSON.stringify(dynamicContracts)),
  };
}

const StromtarifePage = async () => {
  const session = await getServerSession(authOptions);

  if (!session || ![Role.ADMIN, Role.CPO, Role.CARD_MANAGER].includes(session?.user?.role)) {
    return (
      <div className="flex items-center justify-center h-64">
        <p className="text-red-500 text-lg">Keine Berechtigung für diese Seite</p>
      </div>
    );
  }

  const { fixedContracts, dynamicContracts } = await getPowerContracts();

  return (
    <>
      <h1 className="mb-0 font-bold dark:text-white">
        Stromtarife
      </h1>
      <div className="mb-3 flex justify-end">
        <Link href="/stromtarife/new">
          <Button type="button">
            <AiOutlinePlusCircle className="mr-2" size="1.5rem" />
            Neuer Stromtarif
          </Button>
        </Link>
      </div>

      <Card header_left="">
        <PowerContractTable data={[...fixedContracts, ...dynamicContracts]} />
      </Card>
    </>
  );
};

export default StromtarifePage;
