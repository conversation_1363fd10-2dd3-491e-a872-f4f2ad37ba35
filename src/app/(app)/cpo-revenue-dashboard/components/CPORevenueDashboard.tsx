"use client";

import React, { useState, useEffect } from "react";
import Card from "~/component/card";
import Button from "~/component/button";
import { FaSearch, FaDownload, FaDollarSign, FaFileAlt, FaCreditCard, FaRocket, FaSync } from "react-icons/fa";
import { MdOutlineEuroSymbol, MdTrendingUp } from "react-icons/md";
// Local types to match the enhanced CPO Revenue Dashboard data
type CPOInvoiceDetailNet = {
  id: string;
  invoiceNumber: string | null;
  invoiceDate: Date | null;
  sumGross: number;
  sumNet: number;
  onboardingAmount: number;
  recurringAmount: number;
  otherAmount: number;
  kindOfInvoice: string;
  isOnboarding: boolean;
  positions: {
    title: string;
    description: string | null;
    amount: number;
    unitPrice: number;
    sumGross: number;
    sumNet: number;
    isOnboarding?: boolean;
    isRecurring?: boolean;
  }[];
};

type CPORevenueDataNet = {
  cpoId: string;
  cpoName: string;
  totalCreditNotes: number;
  totalInvoiced: number;
  onboardingRevenue: number;
  recurringRevenue: number;
  yearForecast: number;
  monthlyBreakdown: {
    month: string;
    revenue: number;
    creditNotes: number;
    invoiced: number;
    recurringRevenue: number;
    invoiceToCreditsRatio: number;
  }[];
  invoiceDetails: CPOInvoiceDetailNet[];
  creditNoteDetails: CPOInvoiceDetailNet[];
};

type CPORevenueDashboardDataNet = {
  cpos: CPORevenueDataNet[];
  totalCreditNotes: number;
  totalInvoiced: number;
  totalOnboarding: number;
  totalRecurring: number;
  globalMonthlyBreakdown: {
    month: string;
    totalRevenue: number;
    totalCreditNotes: number;
    totalInvoiced: number;
    totalRecurringRevenue: number;
    totalOnboardingRevenue: number;
    globalInvoiceToCreditsRatio: number;
  }[];
  globalYearForecast: number;
};
import CPORevenueTable from "./CPORevenueTable";
import CPOGlobalTrendChart from "./CPOGlobalTrendChart";
import RealtimeWidget from "~/app/(app)/component/RealtimeWidget";

const CPORevenueDashboard = () => {
  const [data, setData] = useState<CPORevenueDashboardDataNet | null>(null);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredCpos, setFilteredCpos] = useState<CPORevenueDataNet[]>([]);

  useEffect(() => {
    fetchData();
  }, []);

  useEffect(() => {
    if (data) {
      const filtered = data.cpos.filter(cpo =>
        cpo.cpoName.toLowerCase().includes(searchTerm.toLowerCase())
      );
      setFilteredCpos(filtered);
    }
  }, [data, searchTerm]);

  const fetchData = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/cpo-revenue-dashboard");
      if (response.ok) {
        const result = await response.json();
        console.log("CPO Revenue Data:", result);
        setData(result);
      } else {
        console.error("Failed to fetch CPO revenue data");
      }
    } catch (error) {
      console.error("Error fetching CPO revenue data:", error);
    } finally {
      setLoading(false);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("de-DE", {
      style: "currency",
      currency: "EUR",
    }).format(amount);
  };

  const exportToCSV = () => {
    if (!data) return;

    const headers = [
      "CPO Name",
      "Rechnungen",
      "Gutschriften",
      "Onboarding",
      "Wiederkehrend",
      "Jahresprognose"
    ];

    const csvData = filteredCpos.map(cpo => [
      cpo.cpoName,
      cpo.totalInvoiced.toFixed(2),
      cpo.totalCreditNotes.toFixed(2),
      cpo.onboardingRevenue.toFixed(2),
      cpo.recurringRevenue.toFixed(2),
      cpo.yearForecast.toFixed(2)
    ]);

    const csvContent = [headers, ...csvData]
      .map(row => row.map(cell => `"${cell}"`).join(","))
      .join("\n");

    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const link = document.createElement("a");
    const url = URL.createObjectURL(blob);
    link.setAttribute("href", url);
    link.setAttribute("download", `cpo-revenue-${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = "hidden";
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="text-center text-gray-500 dark:text-gray-400">
        Fehler beim Laden der CPO-Umsatzdaten
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Summary Overview with RealtimeWidgets */}
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
        <RealtimeWidget
          caption="Gesamte Rechnungen"
          primaryValue={formatCurrency(data?.totalInvoiced || 0)}
          loading={loading}
          icon={<MdOutlineEuroSymbol size={21} />}
        />

        <RealtimeWidget
          caption="Onboarding"
          primaryValue={formatCurrency(data?.totalOnboarding || 0)}
          secondaryValue={data && data.totalInvoiced > 0
            ? `${((data.totalOnboarding / data.totalInvoiced) * 100).toFixed(1)}%`
            : "0%"
          }
          loading={loading}
          icon={<FaRocket size={21} />}
        />

        <RealtimeWidget
          caption="Wiederkehrend"
          primaryValue={formatCurrency(data?.totalRecurring || 0)}
          secondaryValue={data && data.totalInvoiced > 0
            ? `${((data.totalRecurring / data.totalInvoiced) * 100).toFixed(1)}%`
            : "0%"
          }
          loading={loading}
          icon={<FaSync size={21} />}
        />

        <RealtimeWidget
          caption={`Jahresprognose ${new Date().getFullYear()}`}
          primaryValue={formatCurrency(data?.globalYearForecast || data?.cpos.reduce((sum, cpo) => sum + cpo.yearForecast, 0) || 0)}
          secondaryValue="Global"
          loading={loading}
          icon={<MdTrendingUp size={21} />}
        />
      </div>

      {/* Global Trend Chart */}
      {!loading && data && data.globalMonthlyBreakdown.length > 0 && (
        <Card>
          <div className="p-6">
            <CPOGlobalTrendChart
              monthlyData={data.globalMonthlyBreakdown.map(month => ({
                month: month.month,
                totalRecurringRevenue: month.totalRecurringRevenue,
              }))}
              yearForecast={data.globalYearForecast}
              totalOnboardingRevenue={data.totalOnboarding}
            />
          </div>
        </Card>
      )}

      {/* Filters and Actions */}
      <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
        <div className="relative flex-1 max-w-sm">
          <FaSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <input
            type="text"
            placeholder="CPO suchen..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="input pl-10 w-full"
          />
        </div>

        <div className="flex gap-2 items-center">
          <span className="text-sm text-gray-600 bg-gray-100 px-2 py-1 rounded">
            {filteredCpos.length} von {data.cpos.length} CPOs
          </span>
          <Button onClick={exportToCSV} className="flex items-center gap-2">
            <FaDownload className="h-4 w-4" />
            CSV Export
          </Button>
        </div>
      </div>

      {/* CPO Revenue Table */}
      <CPORevenueTable cpos={filteredCpos} />
    </div>
  );
};

export default CPORevenueDashboard;
