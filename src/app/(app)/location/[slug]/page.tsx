import { IoIosRocket } from "react-icons/io";
import { FaAddressCard } from "react-icons/fa";
import React from "react";
import prisma from "../../../../server/db/prisma";
import Title from "./compontent/title";
import FormContextWrapper from "./compontent/formContextWrapper";
import BasicInfo from "./compontent/basicInfo";

import type { Prisma } from "@prisma/client";
import Contract from "~/app/(app)/location/[slug]/compontent/contracts";

export const revalidate = 0; // revalidate every 30sec

export type LocationWithIncludes = Prisma.LocationGetPayload<{
  include: {
    powerContract: {
      select: {
        id: true,
        typeOfContract: true,
        start: true,
        end: true,
        monthlyFixCost: true,
        baseKWhPrice: true,
        kwhPrice: true,
        contractWith: true,
        contractNumber: true,
        customerNumber: true,
        supplyNetworkOperator: true,
        locationId: true,
      },
    };
  };
}>;

const getLocations = async (slug: string) => {
  return prisma.location.findUniqueOrThrow({
    where: {
      id: slug,
    },
    include: {
      powerContract: {
        select: {
          id: true,
          typeOfContract: true,
          start: true,
          end: true,
          monthlyFixCost: true,
          baseKWhPrice: true,
          kwhPrice: true,
          contractWith: true,
          contractNumber: true,
          customerNumber: true,
          supplyNetworkOperator: true,
          locationId: true,
        },
      },
    },
  });
};

const Page = async ({ params }: { params: { slug: string } }) => {
  const { slug } = params;

  let location = await getLocations(slug);

  if (location) {
    location = JSON.parse(JSON.stringify(location));
  }

  return (
    <div className={"-mx-3 mb-12 flex flex-wrap"}>
      <div className={"flex w-full max-w-full flex-col gap-3"}>
        <FormContextWrapper>
          <BasicInfo location={location} />
          <Contract location={location} />
        </FormContextWrapper>
      </div>
    </div>
  );
};

export default Page;
