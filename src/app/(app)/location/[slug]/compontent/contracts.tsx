"use client";
import React, { useState, useTransition } from "react";
import type { SubmitHand<PERSON> } from "react-hook-form";
import { useForm } from "react-hook-form";
import { useRouter } from "next/navigation";

import type { GridOptions, ICellRendererParams } from "ag-grid-community";
import type { ColDef, ColGroupDef } from "ag-grid-community/dist/lib/entities/colDef";
import { LicenseManager } from "ag-grid-enterprise";

import "ag-grid-community/dist/styles/ag-grid.css";
import "ag-grid-community/dist/styles/ag-theme-alpine.css";
import { AiOutlinePlusCircle } from "react-icons/ai";

import Table from "../../../../../utils/table/table";
import type { LocationWithIncludes } from "../page";
import { TfiPencil } from "react-icons/tfi";
import { PowerContract } from "@prisma/client";
import { TypeOfContract } from "~/server/types/types";
import Card from "~/component/card";

LicenseManager.setLicenseKey(
  "CompanyName=Zeitgleich GmbH,LicensedApplication=BEAST,LicenseType=SingleApplication,LicensedConcurrentDeveloperCount=1,LicensedProductionInstancesCount=0,AssetReference=AG-016007,ExpiryDate=2_July_2022_[v2]_MTY1NjcxNjQwMDAwMA==579f3e57c0b0b4db77e0428d0cac15be",
);

interface Props {
  location: LocationWithIncludes;
}

const Contract = ({ location }: Props) => {
  const router = useRouter();
  const [isPending, startTransition] = useTransition();

  const [newContract, setNewContract] = useState(false);

  const {
    register,
    handleSubmit,
    watch,
    reset,
    formState: { isDirty, dirtyFields, errors },
  } = useForm<PowerContract>();

  const onSubmit: SubmitHandler<PowerContract> = async (data) => {
    if (data.id === undefined) {
      // Mutate external data source
      await fetch(`/api/location/${location.id}/contract`, {
        method: "PUT",
        body: JSON.stringify({ location: location, contract: data }),
      });
    } else {
      // Mutate external data source
      await fetch(`/api/powerContract/${data.id}`, {
        method: "PUT",
        body: JSON.stringify({ contract: data }),
      });
    }
    reset(data);
    startTransition(() => {
      // Refresh the current route and fetch new data from the server without
      // losing client-side browser or React state.
      router.refresh();
    });
    setNewContract(false);
  };

  const ContractActionCellRenderer = (params: ICellRendererParams) => {
    return (
      <>
        <span
          className={"cursor-pointer"}
          onClick={() => {
            setNewContract(true);
            reset({
              ...params.node.data,

              start: new Date(params.node.data.start).toISOString().split("T")[0],
              end: new Date(params.node.data.end).toISOString().split("T")[0],
            });
            setTimeout(() => {
              document?.getElementById("contract-form")?.scrollIntoView({ behavior: "smooth" });
            }, 100);
          }}
        >
          <TfiPencil />
        </span>
      </>
    );
  };

  const [columnDefs, setColumnDefs] = useState<(ColDef | ColGroupDef)[] | null>([
    { field: "typeOfContract", editable: false },
    { field: "start", editable: false },
    { field: "end", editable: false },
    { field: "monthlyFixCost", editable: false },
    { field: "baseKWhPrice", editable: false },
    { field: "kwhPrice", editable: false },
    { field: "contractNumber", editable: false },
    { field: "customerNumber", editable: false },
    { field: "supplyNetworkOperator", editable: false },
    {
      field: "action",
      headerName: "Aktion",
      cellRenderer: ContractActionCellRenderer,
      editable: false,
    },
  ]);

  const defaultBackendGridOptions: GridOptions = {
    rowModelType: "clientSide",
    defaultColDef: {
      sortable: true,
      floatingFilter: true,
      filter: true,
      resizable: true,
      editable: true,
      filterParams: {
        buttons: ["reset", "apply"],
      },
      menuTabs: ["columnsMenuTab"],
    },
    rowHeight: 30,
  };

  return (
    <Card>
      <div id="power-contract" className="mb-0 flex w-full justify-between rounded-t-2xl p-6">
        <h5 className="dark:text-white">Power Contracts</h5>
        <button
          onClick={() => {
            reset({
              id: undefined,
              typeOfContract: "",
              start: undefined,
              end: undefined,
              monthlyFixCost: null,
              baseKWhPrice: null,
              kwhPrice: null,
              contractWith: undefined,
              contractNumber: null,
              customerNumber: "",
              supplyNetworkOperator: "",
            });
            setNewContract(true);
            setTimeout(() => {
              document?.getElementById("contract-form")?.scrollIntoView({ behavior: "smooth" });
            }, 100);
          }}
          type={"button"}
          className="mb-0  cursor-pointer justify-self-end rounded-lg border-0 bg-primary bg-150 bg-x-25 px-8 py-2 text-right align-middle text-xs font-bold uppercase leading-pro tracking-tight-soft text-white shadow-soft-md transition-all ease-soft-in hover:scale-102 hover:shadow-soft-xs active:opacity-85 dark:bg-gradient-to-tl dark:from-slate-850 dark:to-gray-850"
        >
          <AiOutlinePlusCircle className={"mr-2"} size={"1rem"} />
          New Power Contract
        </button>
      </div>
      <div className="relative mt-3" style={{ width: "100%", height: 600 }}>
        <Table
          rowData={Object.values(location.powerContract)}
          gridOptions={defaultBackendGridOptions}
          columnDefs={columnDefs}
        />
      </div>

      <div id={"contract-form"}>
        {newContract && (
          <form onSubmit={handleSubmit(onSubmit)}>
            <>
              <input
                disabled={!watch("id")}
                type={"text"}
                {...register("id")}
                className={"hidden"}
              />
              <div className="mb-0 rounded-t-2xl py-6">
                <h5 className="dark:text-white">
                  {watch("id") ? "Edit Power Contract" : "New Power Contract"}
                </h5>
              </div>
              <div className="-mx-3 flex flex-wrap">
                <div className="w-6/12 max-w-full flex-0 px-3">
                  <label
                    className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80"
                    htmlFor="Name"
                  >
                    Gültig ab
                  </label>
                  <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                    <input
                      {...register("start", { valueAsDate: true })}
                      type="date"
                      className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
                    />
                  </div>
                </div>
                <div className="w-6/12 max-w-full flex-0 px-3">
                  <label
                    className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80"
                    htmlFor="Firmen Name"
                  >
                    Gültig bis
                  </label>
                  <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                    <input
                      {...register("end", { valueAsDate: true })}
                      type="date"
                      className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
                    />
                  </div>
                </div>
              </div>
              <div className="-mx-3 mt-6 flex flex-wrap">
                <div className="w-3/12 max-w-full flex-0 px-3">
                  <label
                    className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80"
                    htmlFor="typeOfContract"
                  >
                    Vertragstype
                  </label>
                  <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                    <select
                      {...register("typeOfContract")}
                      className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
                    >
                      <option value={TypeOfContract.stock}>EPEX Spotmarkt</option>
                      <option value={TypeOfContract.fixed}>Standard</option>
                    </select>
                  </div>
                </div>

                <div className="w-2/12 max-w-full flex-0 px-3">
                  <label
                    className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80"
                    htmlFor="kwhPrice"
                  >
                    Preis pro kWh in Euro
                  </label>
                  <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                    <input
                      {...register("kwhPrice")}
                      type="number"
                      step={"0.0001"}
                      max={1}
                      className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
                    />
                  </div>
                </div>
                <div className="w-2/12 max-w-full flex-0 px-3">
                  <label
                    className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80"
                    htmlFor="monthlyFixCost"
                  >
                    Monatliche Grundgebühr
                  </label>
                  <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                    <input
                      {...register("monthlyFixCost")}
                      type="number"
                      step={"0.01"}
                      className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
                    />
                  </div>
                </div>
                <div className="w-2/12 max-w-full flex-0 px-3">
                  <label
                    className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80"
                    htmlFor="baseKWhPrice"
                  >
                    Basis kWh-Preis (Offset)
                  </label>
                  <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                    <input
                      {...register("baseKWhPrice")}
                      type="number"
                      step={"0.0001"}
                      className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
                    />
                  </div>
                </div>
              </div>
              <div className="-mx-3 mt-6 flex flex-wrap">
                <div className="w-3/12 max-w-full flex-0 px-3">
                  <label
                    className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80"
                    htmlFor="contractWith"
                  >
                    Stromanbieter
                  </label>
                  <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                    <input
                      {...register("contractWith")}
                      type="text"
                      className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
                    />
                  </div>
                </div>
                <div className="w-3/12 max-w-full flex-0 px-3">
                  <label
                    className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80"
                    htmlFor="contractNumber"
                  >
                    Vertragsnummer
                  </label>
                  <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                    <input
                      {...register("contractNumber")}
                      type="text"
                      className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
                    />
                  </div>
                </div>
                <div className="w-3/12 max-w-full flex-0 px-3">
                  <label
                    className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80"
                    htmlFor="customerNumber"
                  >
                    Kundennummer
                  </label>
                  <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                    <input
                      {...register("customerNumber")}
                      type="text"
                      className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
                    />
                  </div>
                </div>
                <div className="w-3/12 max-w-full flex-0 px-3">
                  <label
                    className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80"
                    htmlFor="supplyNetworkOperator"
                  >
                    Netzbetreiber
                  </label>
                  <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
                    <input
                      {...register("supplyNetworkOperator")}
                      type="text"
                      className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
                    />
                  </div>
                </div>
              </div>
              <div className="-mx-3 mt-6 flex flex-wrap">
                <div className="w-8/12 w-full max-w-full flex-0 px-3">
                  <button
                    type={"submit"}
                    className="float-right mb-0 mt-6 cursor-pointer justify-self-end rounded-lg border-0 bg-primary bg-150 bg-x-25 px-8 py-2 text-right align-middle text-xs font-bold uppercase leading-pro tracking-tight-soft text-white shadow-soft-md transition-all ease-soft-in hover:scale-102 hover:shadow-soft-xs active:opacity-85 dark:bg-gradient-to-tl dark:from-slate-850 dark:to-gray-850"
                  >
                    {watch("id") ? "" : "Neue"} Vertrag Speichern
                  </button>
                </div>
              </div>
            </>
          </form>
        )}
      </div>
    </Card>
  );
};

export default Contract;
