import Link from "next/link";
import type { UserWithOu } from "../page";
import React from "react";
import { Role, UserGroup } from "@prisma/client";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { getRoleLabel } from "~/utils/user/roleLabels";

interface UserTableProps {
  users: UserWithOu[];
}
export const revalidate = 0;
const UserTable = async ({ users }: UserTableProps) => {
  const session = await getServerSession(authOptions);
  const isAdmin = session?.user?.role == Role.ADMIN;

  const cols = [
    "Name",
    "Nachname",
    "Kennung",
    "E-Mail",
    "Rolle",
    "Nutzergruppe",
    "Organisationseinheit",
  ];

  return (
    <table className="w-full table-auto divide-y rounded-xl">
      <thead className="rounded-xl  bg-primary font-bold text-white">
        <tr>
          {cols.map((col, index) => (
            <th
              scope="col"
              key={index}
              className="px-6 py-2 text-left text-xs uppercase tracking-wider"
            >
              {col.toUpperCase()}
            </th>
          ))}
        </tr>
      </thead>
      <tbody className="divide-y divide-gray-500">
        {users.map((user) => (
          <tr key={user.id} className={"border-b border-gray-200 "}>
            <td className="whitespace-nowrap border-b border-gray-200 bg-white px-5 py-2 text-sm">
              {isAdmin ? (
                <Link href={`/users/${user.id}`}>{user.name}</Link>
              ) : (
                <span>{user.name}</span>
              )}
            </td>
            <td className="whitespace-nowrap border-b border-gray-200 bg-white px-5 py-2 text-sm">
              {isAdmin ? (
                <Link href={`/users/${user.id}`}>{user.lastName}</Link>
              ) : (
                <span>{user.lastName}</span>
              )}
            </td>
            <td className="whitespace-nowrap border-b border-gray-200 bg-white px-5 py-2 text-sm">
              {isAdmin ? (
                <Link href={`/users/${user.id}`}>{user.identifier}</Link>
              ) : (
                <span>{user.identifier}</span>
              )}
            </td>
            <td className="whitespace-nowrap border-b border-gray-200 bg-white px-5 py-2 text-sm">
              {isAdmin ? (
                <Link href={`/users/${user.id}`}>{user.email}</Link>
              ) : (
                <span>{user.email}</span>
              )}
            </td>
            <td className="whitespace-nowrap border-b border-gray-200 bg-white px-5 py-2 text-sm">
              {isAdmin ? (
                <Link href={`/users/${user.id}`}>{getRoleLabel(user.role)}</Link>
              ) : (
                <span>{getRoleLabel(user.role)}</span>
              )}
            </td>
            <td className="whitespace-nowrap border-b border-gray-200 bg-white px-5 py-2 text-sm">
              {isAdmin ? (
                <Link href={`/users/${user.id}`}>{user.userGroup ? user.userGroup.name : "-"}</Link>
              ) : (
                <span>{user.userGroup ? user.userGroup.name : "-"}</span>
              )}
            </td>
            <td className="whitespace-nowrap border-b border-gray-200 bg-white px-5 py-2 text-sm">
              {isAdmin ? (
                <Link href={`/users/${user.id}`}>{user.ou.name}</Link>
              ) : (
                <span>{user.ou.name}</span>
              )}
            </td>
          </tr>
        ))}
      </tbody>
    </table>
  );
};

export default UserTable;
