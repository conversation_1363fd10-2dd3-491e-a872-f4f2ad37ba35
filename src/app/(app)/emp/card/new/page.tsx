"use client";
import Button from "~/component/button";
import Card from "~/component/card";
import SweatAlert from "~/component/sweatAlert";
import TarifCard from "~/app/(app)/emp/card/component/TarifCard";
import React, { useEffect, useState } from "react";
import Modal from "~/component/modal/modal";
import Loading from "~/app/(app)/loading";
import { z } from "zod";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import Headline from "~/component/Headline";
import PageContentWrapper from "~/component/PageContentWrapper";
import { CompanyTarif } from "@prisma/client";
import StyledLink from "~/app/(app)/util/StyledLink";
import { FiLoader } from "react-icons/fi";

const userDetailsSchema = z.object({
  name: z.string(),
  lastName: z.string(),
  email: z.string(),
  street: z.string(),
  streetNr: z.string(),
  city: z.string(),
  zip: z.string(),
  country: z.string(),
});

interface UserDetails {
  name: string;
  lastName: string;
  email: string;
  street?: string | null;
  streetNr?: string | null;
  city?: string | null;
  zip?: string | null;
  country?: string | null;
}

const NewEMPCardPage = () => {
  const [hasPaymentMethod, setHasPaymentMethod] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(true);
  const [paymentsLoading, setPaymentsLoading] = useState<boolean>(true);
  const [tariffsLoading, setTariffsLoading] = useState<boolean>(true);
  const [orderLoading, setOrderLoading] = useState<boolean>(false);
  const [error, setError] = useState<string>("");

  const [userDetails, setUserDetails] = useState<UserDetails | undefined>(undefined);
  const [showOrderForm, setShowOrderForm] = useState<boolean>(false);
  const router = useRouter();
  const [selectedCompanyTariffIds, setSelectedCompanyTariffIds] = useState<string[]>([]);
  const [companyTariffs, setCompanyTariffs] = useState<CompanyTarif[]>([]);
  const [errorMessage, setErrorMessage] = useState<string>("");

  const fetchCompanyTarrifs = async () => {
    try {
      const result = await fetch("/api/tarif/userGroupTarifs", {
        method: "GET",
      });
      if (result.ok) {
        const tarifs: CompanyTarif[] = await result.json();
        setCompanyTariffs(tarifs);
        setSelectedCompanyTariffIds(
          tarifs.filter((tarif) => !tarif.optional).map((tarif) => tarif.id),
        );
      }
    } catch (e) {
      setError("Fehler beim Laden der Tarife");
    }
    setTariffsLoading(false);
  };

  // AC/DC validation function
  const validateTariffSelection = (newSelectedTariffs: string[]) => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const selectedTariffObjects = companyTariffs.filter((tarif) =>
      newSelectedTariffs.includes(tarif.id),
    );
    const validSelectedTariffs = selectedTariffObjects.filter((tarif) => {
      const validFrom = new Date(tarif.validFrom);
      const validTo = new Date(tarif.validTo);
      return validFrom <= today && validTo >= today;
    });

    const acTariffs = validSelectedTariffs.filter((tarif) => tarif.currentType === "AC");
    const dcTariffs = validSelectedTariffs.filter((tarif) => tarif.currentType === "DC");

    if (acTariffs.length > 1) {
      return { valid: false, error: "Sie können nur einen AC-Tarif auswählen" };
    }

    if (dcTariffs.length > 1) {
      return { valid: false, error: "Sie können nur einen DC-Tarif auswählen" };
    }

    return { valid: true, error: "" };
  };

  // Handle tariff selection
  const handleTariffToggle = (tariffId: string, checked: boolean) => {
    let newSelection: string[];

    if (checked) {
      newSelection = [...selectedCompanyTariffIds, tariffId];
    } else {
      // Don't allow deselecting non-optional tariffs
      const tariff = companyTariffs.find((t) => t.id === tariffId);
      if (tariff && !tariff.optional) {
        setErrorMessage("Pflicht-Tarife können nicht abgewählt werden");
        return;
      }
      newSelection = selectedCompanyTariffIds.filter((id) => id !== tariffId);
    }

    // Validate AC/DC constraints
    const validation = validateTariffSelection(newSelection);
    if (!validation.valid) {
      setErrorMessage(validation.error);
      return;
    }

    setSelectedCompanyTariffIds(newSelection);
    setErrorMessage("");
  };

  const sendOrderToServer = async () => {
    try {
      setOrderLoading(true);
      const result = await fetch("/api/emp/orderCard", {
        method: "POST",
        body: JSON.stringify({ selectedTarifIds: selectedCompanyTariffIds }),
      });
      if (result.ok) {
        setTimeout(() => {
          setOrderLoading(false);
          router.push("/emp/card");
          router.refresh();
        }, 3000);
      }
    } catch (e) {
      setOrderLoading(false);
      setLoading(false);
      setError("Fehler bei der Bestellung");
    }
  };

  const fetchPaymentMethods = async () => {
    try {
      const result = await fetch("/api/stripe/getPaymentMethodsFromUser", {
        method: "GET",
      });
      if (result.ok) {
        setHasPaymentMethod((await result.json()).length > 0);
      }
    } catch (e) {
      setError("Fehler beim Prüfen der Zahlungsmittel");
    }
    setPaymentsLoading(false);
  };

  const fetchUserDetails = async () => {
    try {
      const result = await fetch("/api/user/details", {
        method: "GET",
      });
      if (result.ok) {
        setUserDetails(await result.json());
      }
    } catch (e) {
      setError("Fehler beim laden der Benutzerdaten");
    }
    setLoading(false);
  };
  useEffect(() => {
    void fetchUserDetails();
    void fetchPaymentMethods();
    void fetchCompanyTarrifs();
  }, []);

  return loading ? (
    <Loading />
  ) : (
    <PageContentWrapper>
      <Card>
        <Headline title={"Ladekarte bestellen"} />
        {companyTariffs && companyTariffs.length > 0 && (
          <div className="my-4 rounded-lg bg-gray-50  p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                  <path
                    fillRule="evenodd"
                    d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-primary dark:text-blue-200">
                  Ladekarten bestellen
                </h3>
                <div className="mt-2 text-sm text-primary dark:text-blue-300">
                  <p>
                    Hier können Sie eine Ladekarte bestellen. Eine Ladekarte ist immer an einen oder
                    mehreren Tarifen gebunden und wird nach der Bestellung zu den aufgeführten
                    Tarifen abgerechnet. Ein Tarif beinhaltet unterschiedliche Preiskomponenten.
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}
        {tariffsLoading && <Loading />}
        {!companyTariffs ||
          (companyTariffs.length == 0 && (
            <div className={"sm:max-w-120"}>
              <SweatAlert
                text={
                  "Es wurde noch kein Tarif für Sie hinterlegt zum Wählen. Bitte wenden Sie sich an Ihren Ansprechpartner."
                }
              />
            </div>
          ))}

        {/* Error Message */}
        {errorMessage && (
          <div className="mb-4 rounded-lg bg-red-50 p-4 dark:bg-red-900/20">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path
                    fillRule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
              <div className="ml-3">
                <p className="text-sm text-red-800 dark:text-red-200">{errorMessage}</p>
              </div>
            </div>
          </div>
        )}

        <div className={"flex flex-col gap-4 sm:flex-row"}>
          {companyTariffs
            .filter((tarif) => !tarif.internal)
            .map((tarif: CompanyTarif) => (
              <TarifCard
                internal={tarif.internal}
                blockingFeeBeginAtMin={tarif.blockingFeeBeginAtMin}
                blockingFeeMax={tarif.blockingFeeMax}
                oneTimeFeePayer={tarif.oneTimeFeePayer}
                vat={19}
                blockingFee={tarif.blockingFee}
                size={"normal"}
                oneTimeFee={tarif.oneTimeFee}
                basicFee={tarif.basicFee}
                currentType={tarif.currentType ?? ""}
                onCheck={(event) => handleTariffToggle(tarif.id, event.target.checked)}
                key={tarif.id}
                tarifId={tarif.id}
                interactive={true}
                optional={tarif.optional}
                tarifName={tarif.name ?? ""}
                pricekWh={tarif.energyPrice}
                priceSession={tarif.sessionPrice}
                title={tarif.name ?? ""}
                description={tarif.description ?? "Keine weitere Beschreibung zum Tarif vorhanden"}
                checked={selectedCompanyTariffIds.includes(tarif.id)}
              />
            ))}
        </div>

        {!hasPaymentMethod && !paymentsLoading && companyTariffs && companyTariffs.length > 0 && (
          <div className={"max-w-100"}>
            <SweatAlert
              text={
                "Vor der Bestellung einer Ladekarte, muss zunächst ein Zahlungsmittel hinzugefügt werden!"
              }
            >
              <StyledLink href={"/emp/payment/new"}>Zahlungsmittel hinzufügen</StyledLink>
            </SweatAlert>
          </div>
        )}

        {hasPaymentMethod && !showOrderForm && (
          <>
            <div className={"mt-3 w-full sm:max-w-52"}>
              <div className={"mt-3 w-full sm:max-w-52"}>
                <Button
                  onClick={() => setShowOrderForm(true)}
                  className="block w-full rounded-full  px-4  py-2 text-center font-bold"
                >
                  Zum Bestellformular
                </Button>
              </div>
            </div>
          </>
        )}
        {showOrderForm && (
          <Card header_left={"Bitte prüfen Sie ihre hinterlegten Kontaktedaten:"}>
            {userDetails && (
              <div className="w-full space-y-4 rounded-lg bg-white">
                <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <label htmlFor="name" className="block text-sm font-medium text-black">
                      Name:
                    </label>
                    <input
                      type="text"
                      id="name"
                      value={userDetails.name}
                      readOnly
                      className="w-full rounded-md border border-gray-300 p-2 "
                    />
                  </div>
                  <div className="space-y-2">
                    <label htmlFor="lastName" className="block text-sm font-medium text-black">
                      Nachname:
                    </label>
                    <input
                      type="text"
                      id="lastName"
                      value={userDetails.lastName}
                      readOnly
                      className="w-full rounded-md border border-gray-300 p-2 focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
                    />
                  </div>
                  <div className="space-y-2">
                    <label htmlFor="email" className="block text-sm font-medium text-black">
                      E-Mail:
                    </label>
                    <input
                      type="email"
                      id="email"
                      value={userDetails.email}
                      readOnly
                      className="w-full rounded-md border border-gray-300 p-2 focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
                    />
                  </div>
                  <div className="space-y-2">
                    <label htmlFor="street" className="block text-sm font-medium text-black">
                      Straße:
                    </label>
                    <input
                      type="text"
                      id="street"
                      value={userDetails.street ?? ""}
                      readOnly
                      className="w-full rounded-md border border-gray-300 p-2 focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
                    />
                  </div>
                  <div className="space-y-2">
                    <label htmlFor="streetNr" className="block text-sm font-medium text-black">
                      Hausnummer:
                    </label>
                    <input
                      type="text"
                      id="streetNr"
                      value={userDetails.streetNr ?? ""}
                      readOnly
                      className="w-full rounded-md border border-gray-300 p-2 focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
                    />
                  </div>
                  <div className="space-y-2">
                    <label htmlFor="city" className="block text-sm font-medium text-black">
                      Stadt:
                    </label>
                    <input
                      type="text"
                      id="city"
                      value={userDetails.city ?? ""}
                      readOnly
                      className="w-full rounded-md border border-gray-300 p-2 focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
                    />
                  </div>
                  <div className="space-y-2">
                    <label htmlFor="zip" className="block text-sm font-medium text-black">
                      PLZ:
                    </label>
                    <input
                      type="text"
                      id="zip"
                      value={userDetails.zip ?? ""}
                      readOnly
                      className="w-full rounded-md border border-gray-300 p-2 focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
                    />
                  </div>
                  <div className="space-y-2">
                    <label htmlFor="country" className="block text-sm font-medium text-black">
                      Land:
                    </label>
                    <input
                      type="text"
                      id="country"
                      value={userDetails.country ?? ""}
                      readOnly
                      className="w-full rounded-md border border-gray-300 p-2 focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
                    />
                  </div>
                </div>
              </div>
            )}
            <div className="relative mb-4 mt-3 w-full rounded-lg border border-solid border-slate-100 bg-gradient-to-tl from-gray-400 to-gray-100 p-4">
              {"Mit dem Klick auf 'Zahlungspflichtig bestellen' erwerben Sie eine Ladekarte zu den oben ausgewählten Tarifen. " +
                "Alle Kosten werden von Ihrem hinterlegten Standard-Zahlungsmittel abgebucht. " +
                "Die Karte wird an die angegebene Adresse gesendet. Sobald Sie ihre Karte erhalten haben, können Sie diese über dieses Portal aktivieren und auch wieder deaktivieren"}
            </div>
            <div className={"mt-3 w-full sm:max-w-56"}>
              <Button
                onClick={() => sendOrderToServer()}
                className="block w-full rounded-full px-3 px-4  py-2 text-center font-bold"
              >
                Zahlungspflichtig bestellen
                {orderLoading && <FiLoader className="ml-1 animate-spin text-blue-500" />}
              </Button>
            </div>
          </Card>
        )}

        <Modal title={"Fehler"} isOpen={!!error} onClose={() => setError("")}>
          <span>{error}</span>
        </Modal>
      </Card>
    </PageContentWrapper>
  );
};

export default NewEMPCardPage;
