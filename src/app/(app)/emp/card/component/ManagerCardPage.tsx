"use server";

import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import prisma from "~/server/db/prisma";

import React from "react";

import PageContentWrapper from "~/component/PageContentWrapper";

import { EMPCardTable } from "~/app/(app)/emp/card/component/EMPCardTable";
import Card from "~/component/card";
import Headline from "~/component/Headline";
import Link from "next/link";
import StyledLink from "~/app/(app)/util/StyledLink";
import EMPCard from "~/app/(app)/emp/card/component/EMPCard";

const getEMPCardsForManager = async () => {
  const session = await getServerSession(authOptions);

  if (!session) {
    return [];
  }

  const ouId = session?.user?.ou?.id;

  const empcards = await prisma.eMPCard.findMany({
    where: {
      OR: [{ user: { ouId: ouId } }, { contact: { ouId: ouId } }],
    },
    include: {
      user: true,
      contact: true,
      physicalCard: true,
      tarifs: {
        include: {
          tarif: true,
        },
      },
    },
  });
  return empcards;
};
export const ManagerCardPage = async () => {
  const empCards = (await getEMPCardsForManager()) ?? [];
  return (
    <>
      <Headline title={"Ladekarten"} />
      <div className="rounded-lg bg-gray-50 p-4 dark:bg-blue-900/20">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
              <path
                fillRule="evenodd"
                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                clipRule="evenodd"
              />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-primary dark:text-blue-200">
              Übersicht der Ladekarten
            </h3>
            <div className="mt-2 text-sm text-primary dark:text-blue-300">
              <p>
                Hier können Sie alle alle internen Ladekarten einsehen. In der unteren Tabelle ist
                eine Übersicht über alle anderen Ladekarten die Ihre Ladepunkte freischalten und
                werden zum gebuchten Tarif abgerechnet werden. Desweiteren können Sie eine interne
                Ladekarte bestellen, die einem internen Tarif zugeordnet wird. Interne Tarife werden
                nur zur Berechnung der Ladevorgänge verwendet werden aber nicht in Rechnung
                gestellt. Intere Karten sind ideal für Firmenfahrzeuge, die Laden sollen, jedoch
                keiner Partei in Rechnung gestellt werden.
              </p>
            </div>
          </div>
        </div>
      </div>

      <div className={"mt-3"}>
        <div className={"gap-2 md:grid md:grid-cols-2 md:gap-4 lg:max-w-240"}>
          {empCards
            .filter((ecard) => ecard.contact)
            .sort((a, b) => (a.deactivatedAt ? 1 : -1))
            .map((card, index) => (
              <EMPCard card={card} key={index} />
            ))}
        </div>
      </div>
      <EMPCardTable empcards={empCards} />
    </>
  );
};
