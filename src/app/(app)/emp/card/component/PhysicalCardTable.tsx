"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";

import { PhysicalCard, User } from "@prisma/client";
import Table from "~/utils/table/table";
import { ValueGetterParams, ICellRendererParams } from "ag-grid-community/dist/lib/entities/colDef";
import { AiOutlineDelete } from "react-icons/ai";
import MessageDialog from "~/app/(app)/util/MessageDialog";

interface UserGroup {
  id: string;
  name: string;
  description?: string;
  ou: {
    id: string;
    name: string;
  };
}

export const PhysicalCardTable = ({ physicalCards }: { physicalCards: PhysicalCard[] }) => {
  const [userGroups, setUserGroups] = useState<UserGroup[]>([]);
  const [loadingUserGroups, setLoadingUserGroups] = useState(true);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [cardToDelete, setCardToDelete] = useState<PhysicalCard | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  const router = useRouter();

  useEffect(() => {
    const fetchUserGroups = async () => {
      try {
        // Fetch UserGroups for current user's OU only
        const response = await fetch("/api/userGroup");
        if (response.ok) {
          const data = await response.json();
          setUserGroups(data);
        }
      } catch (error) {
        console.error("Error fetching user groups:", error);
      } finally {
        setLoadingUserGroups(false);
      }
    };

    fetchUserGroups();
  }, []);

  const UserGroupCellRenderer = (params: ICellRendererParams) => {
    const [selectedUserGroupId, setSelectedUserGroupId] = useState(params.data?.userGroupId || "");
    const [updating, setUpdating] = useState(false);

    const handleUserGroupChange = async (newUserGroupId: string) => {
      setUpdating(true);
      try {
        const response = await fetch(`/api/physicalCard/${params.data.uid}`, {
          method: "PUT",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            userGroupId: newUserGroupId || null,
          }),
        });

        if (response.ok) {
          setSelectedUserGroupId(newUserGroupId);
          // Update the data in the grid
          params.data.userGroupId = newUserGroupId || null;
          params.api.refreshCells({ rowNodes: [params.node] });
        } else {
          console.error("Failed to update user group");
        }
      } catch (error) {
        console.error("Error updating user group:", error);
      } finally {
        setUpdating(false);
      }
    };

    return (
      <select
        value={selectedUserGroupId}
        onChange={(e) => handleUserGroupChange(e.target.value)}
        disabled={updating || loadingUserGroups}
        className="w-full rounded border border-gray-300 px-2 py-1 text-sm"
      >
        <option value="">Keine Nutzergruppe</option>
        {userGroups.map((group) => (
          <option key={group.id} value={group.id}>
            {group.name} ({group.ou.name})
          </option>
        ))}
      </select>
    );
  };

  const handleDeleteClick = (card: PhysicalCard) => {
    setCardToDelete(card);
    setShowDeleteDialog(true);
  };

  const handleDeleteConfirm = async () => {
    if (!cardToDelete) return;

    setIsDeleting(true);
    try {
      const response = await fetch(`/api/physicalCard/${cardToDelete.uid}`, {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (response.ok) {
        setShowDeleteDialog(false);
        setCardToDelete(null);
        // Refresh the page to update the table
        router.refresh();
      } else {
        const error = await response.json();
        alert(`Fehler beim Löschen der Karte: ${error.error || "Unbekannter Fehler"}`);
      }
    } catch (error) {
      console.error("Error deleting physical card:", error);
      alert("Fehler beim Löschen der Karte");
    } finally {
      setIsDeleting(false);
    }
  };

  const handleDeleteCancel = () => {
    setShowDeleteDialog(false);
    setCardToDelete(null);
  };

 const ActionCellRenderer = (params: ICellRendererParams) => {
    return (
      <div className="flex items-center justify-center">
        <AiOutlineDelete
          className="cursor-pointer text-red-600 hover:text-red-800"
          size="1.2rem"
          title="Karte löschen"
          onClick={() => handleDeleteClick(params.data)}
        />
      </div>
    );
  };

  const gridOptions = { suppressAggFuncInHeader: true };
  const columnDefs = [
    {
      field: "uid",
      headerName: "UID",
    },
    {
      field: "visualNumber",
      headerName: "Kartennummer",
      editable: false,
      enableRowGroup: true,
    },

    {
      field: "empcard.user.email",
      headerName: "Verwendet von",
      editable: false,
      enableRowGroup: true,
      valueGetter: (params: ValueGetterParams) => {
        {
          if (params?.data?.EMPCard?.user) {
            return params.data.EMPCard.user.email;
          } else {
            return "<nicht verwendet zugewiesen bisher>";
          }
        }
      },
    },
    {
      field: "ou.name",
      headerName: "CPO",
      editable: false,
      enableRowGroup: true,
      valueGetter: (params: ValueGetterParams) => {
        {
          if (params?.data?.cpo?.name) {
            return params?.data?.cpo?.name;
          } else {
            return "Keinem CPO zugeordnet";
          }
        }
      },
    },
    {
      field: "userGroup",
      headerName: "Nutzergruppe",
      editable: false,
      enableRowGroup: true,
      cellRenderer: UserGroupCellRenderer,
      width: 200,
    },
    {
      field: "actions",
      headerName: "Aktionen",
      cellRenderer: ActionCellRenderer,
      width: 80,
      sortable: false,
      filter: false,
      resizable: false,
      pinned: "right",
    },
  ];

  return (
    <>
      <Table gridOptions={gridOptions} columnDefs={columnDefs} rowData={physicalCards} />

      {showDeleteDialog && cardToDelete && (
        <MessageDialog
          message={`Möchten Sie die physische Karte "${cardToDelete.visualNumber}" (UID: ${cardToDelete.uid}) wirklich löschen?
                   Diese Aktion kann nicht rückgängig gemacht werden. Alle Referenzen zu dieser Karte werden ebenfalls entfernt.`}
          title="Physische Karte löschen"
          onYes={handleDeleteConfirm}
          onNo={handleDeleteCancel}
          yesLabel={isDeleting ? "Wird gelöscht..." : "Löschen"}
          noLabel="Abbrechen"
        />
      )}
    </>
  );
};
