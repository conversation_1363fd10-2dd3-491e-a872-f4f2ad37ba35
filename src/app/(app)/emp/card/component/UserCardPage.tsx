"use server";

import EMPCard from "~/app/(app)/emp/card/component/EMPCard";
import React from "react";
import SettingsSVG from "~/app/(app)/util/SettingsSVG";
import { getPaymentMethods } from "~/server/stripe/paymentUtils";
import Headline from "~/component/Headline";
import StyledLink from "~/app/(app)/util/StyledLink";
import { getEMPCardForUser } from "~/utils/user/empcard";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import PredeliverdCardForm from "~/app/(app)/emp/card/component/PreDeliveredCardForm";

export const UserCardPage = async () => {
  const empCards = await getEMPCardForUser();
  const paymentMethods = await getPaymentMethods();
  const session = await getServerSession(authOptions);
  const allowOrderCards = session?.user?.selectedOu.allowOrderEMPCards;
  const canRegisterCard = session?.user?.selectedOu.registrationSlug;
  return (
    <>
      {!empCards ||
        (empCards?.length === 0 && (
          <>
            <Headline title={"Keine Ladekarte vorhanden"} />
            <div className="flex w-full items-center justify-center text-secondary">
              <SettingsSVG />{" "}
            </div>
          </>
        ))}

      {empCards && empCards?.length > 0 && (
        <>
          <Headline title={"Ladekarten"} />
          {empCards.some((card) => !card.activatedAt) && (
            <div className="my-4 rounded-lg bg-gray-50  p-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-blue-400" viewBox="0 0 20 20" fill="currentColor">
                    <path
                      fillRule="evenodd"
                      d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-primary dark:text-blue-200">
                    Ladekarte(n) unterwegs
                  </h3>
                  <div className="mt-2 text-sm text-primary dark:text-blue-300">
                    <p>Ihre bestelle Ladekarte ist unterwegs zu Ihnen. </p>
                  </div>
                </div>
              </div>
            </div>
          )}
          <div className={"gap-2 md:grid md:grid-cols-2 md:gap-4 lg:max-w-240"}>
            {empCards
              //.filter((card) => card?.physicalCard !== null)
              .sort((a, b) => (a.deactivatedAt ? 1 : -1))
              .map((card, index) => (
                <EMPCard card={card} key={index} />
              ))}
          </div>
        </>
      )}
      {canRegisterCard && (
        <>
          <hr className={"my-2 mb-3 h-px w-full border-solid bg-gray-400 dark:bg-gray-700"} />
          <PredeliverdCardForm />{" "}
        </>
      )}
      {allowOrderCards && (
        <>
          <hr className={"my-2 mb-3 h-px w-full border-solid bg-gray-400 dark:bg-gray-700"} />

          <div className={"w-full sm:max-w-100"}>
            {paymentMethods && paymentMethods.length > 0 ? (
              <>
                <span>Hier können Sie eine Ladekarte bestellen</span>
                <StyledLink href={"/emp/card/new"}>
                  {`${empCards && empCards?.length > 0 ? "Weitere" : ""}`} Ladekarte bestellen
                </StyledLink>
              </>
            ) : (
              <>
                {empCards.find((card) => card.active) ? (
                  <span className={"my-3 block text-lg text-eul-gray"}>
                    Um Ladekarten nutzen zu können, bitte Zahlungsdaten hinterlegen. Danach können
                    Karten genutzt werden, um Ladepunkte freizuschalten. Tarife können unter{" "}
                    <a className={"font-bold"} href={"/emp/tarif/userview"}>
                      Mein Tarif
                    </a>{" "}
                    eingesehen werden.
                  </span>
                ) : (
                  <span className={"my-3 block text-lg text-eul-gray"}>
                    Um eine Ladekarte zu bestellen oder eine vorhandene zu aktivieren, bitte
                    Zahlungsdaten hinterlegen. Die Kosten für die Bestellung einer Ladekarte, können
                    vorab im Punkt{" "}
                    <a className={"font-bold"} href={"/emp/tarif/userview"}>
                      Mein Tarif
                    </a>{" "}
                    eingesehen werden.
                  </span>
                )}

                <StyledLink href={"/emp/payment/new"}>Zahlungsdaten hinterlegen</StyledLink>
              </>
            )}
          </div>
        </>
      )}
    </>
  );
};
