"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";

import { EMPCard, PhysicalCard, User } from "@prisma/client";
import Table, { filterParams } from "~/utils/table/table";
import { dateTimeRenderer } from "~/utils/table/formatter";
import { ICellRendererParams } from "ag-grid-community";
import { IoMdCheckmarkCircleOutline, IoMdRemoveCircleOutline } from "react-icons/io";
import { FaGear } from "react-icons/fa6";
import { TbMailbox, TbMailDown } from "react-icons/tb";
import MessageDialog from "~/app/(app)/util/MessageDialog";
import { ValueFormatterParams } from "ag-grid-community/dist/lib/entities/colDef";
import { env } from "~/env";

export const EMPCardAdminTable = ({
  empcards,
  physicalCards,
}: {
  empcards: EMPCard[];
  physicalCards: PhysicalCard[];
}) => {
  //const [data, setData] = useState<ExtractedDataType>([]);
  const router = useRouter();
  const [empCardId, setEmpCardId] = useState<string>("");
  const setCardSent = async (cardId: string, kartennummer: string) => {
    if (cardId && kartennummer && cardId != "" && kartennummer != "") {
      const url = `/api/emp/setCardSent?cardId=${cardId}&visualNumber=${kartennummer}`;
      const response = await fetch(url);
      if (!(response.status == 200)) {
        //todo error
      } else {
        setTimeout(() => {
          router.refresh();
        }, 3000);
      }
    } else {
      alert("Keine Karte ausgewählt - Karte wird nicht als versendet markiert");
    }
  };
  const [showCardSelectInput, setShowCardSelectInput] = useState<boolean>(false);

  const downloadWelcomeLetter = async (user: User) => {
    // Finden Sie die erste PDF-Datei

    // URL zu Ihrem API-Endpunkt
    const url = `/api/user/generateWelcomeLetter?userId=${user?.id}`;

    // Laden Sie die Datei herunter
    const response = await fetch(url);
    if (response.status == 200) {
      const blob = await response.blob();

      // Erstellen Sie einen Link und klicken Sie darauf, um den Download zu starten
      const downloadUrl = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.href = downloadUrl;
      link.download = `${env.NEXT_PUBLIC_DEBUG == "true" ? "_DEV_" : ""}${user.name}_${
        user.lastName
      }_welcome_letter.pdf`;
      link.click();

      // URL freigeben
      URL.revokeObjectURL(downloadUrl);
    } else {
      console.log("Error");
    }
  };

  const gridOptions = { suppressAggFuncInHeader: true };
  const columnDefs = [
    {
      field: "physicalCard.visualNumber",
      headerName: "Kartennummer",
    },
    {
      field: "user.email",
      headerName: "Eigentümer",
      editable: false,
      enableRowGroup: true,
      valueFormatter: (params: ValueFormatterParams) => {
        if (!params.data?.user) {
          return params.data?.contact?.name;
        } else {
          return params.data?.user?.email;
        }
      },
    },

    {
      field: "orderedAt",
      filter: "agDateColumnFilter",
      headerName: "Bestellt am",
      editable: false,
      cellRenderer: dateTimeRenderer,
      filterParams: filterParams,
      enableRowGroup: false,
    },
    {
      field: "deactivatedAt",
      filter: "agDateColumnFilter",
      headerName: "Deaktiviert am",
      editable: false,
      cellRenderer: dateTimeRenderer,
      filterParams: filterParams,
      enableRowGroup: false,
    },
    {
      field: "active",
      headerName: "Aktiv",
      editable: false,
      enableRowGroup: true,
      cellRenderer: (params: ICellRendererParams) => {
        {
          if (params.data.active == 1) {
            return (
              <>
                <IoMdCheckmarkCircleOutline size={20} className={"mr-1 text-green-400"} />
                <span
                  title={
                    "Karte ist aktiv und kann zum Freischalten von Ladepunkten verwendet werden"
                  }
                >
                  Karte ist aktiviert
                </span>
              </>
            );
          }
          if (params.data.active == 0) {
            if (params.data.deactivatedAt) {
              return (
                <>
                  <IoMdRemoveCircleOutline size={20} className={"mr-1 text-red-400"} />
                  <span
                    title={
                      "Ladekarte wurde deaktiviert und kann nicht mehr zum Freischalten verwendet werden."
                    }
                  >
                    Karte ist deaktiviert
                  </span>
                </>
              );
            } else {
              return (
                <>
                  <FaGear size={20} className={"mr-1 text-orange-400"} />
                  <span title={"Karte wurde versand, jedoch noch nicht im Portal aktiviert"}>
                    noch nicht aktiviert
                  </span>
                </>
              );
            }
          }
        }
      },
    },
    { field: "activatedAt", headerName: "Aktiviert am:", cellRenderer: dateTimeRenderer },
    {
      field: "user.id",
      headerName: "Anschreiben",
      editable: false,
      cellRenderer: (params: ICellRendererParams) => {
        return (
          <>
            {!params?.data?.sentOut ? (
              <div className={"flex flex-row"}>
                <TbMailDown
                  title={"Personalisiertes Anschreiben herunterladen"}
                  size={20}
                  className={"ml-5 cursor-pointer"}
                  onClick={() => downloadWelcomeLetter(params?.data?.user)}
                />
                <TbMailbox
                  title={"Heutiges Datum wird als 'versendet' Datum hinterlegt"}
                  size={20}
                  className={"ml-5 cursor-pointer"}
                  //onClick={() => setCardSent(params?.data?.id)}
                  onClick={() => {
                    setEmpCardId(params.data.id);
                    setShowCardSelectInput(true);
                  }}
                />
              </div>
            ) : (
              <span>(Post {params?.data?.sentOut.toLocaleDateString()})</span>
            )}
          </>
        );
      },
    },
    {
      headerName: "Notiz",
      field: "note",
    },
  ];
  return (
    <>
      <Table
        gridOptions={gridOptions}
        gridId={"empCardAdmin"}
        columnDefs={columnDefs}
        rowData={empcards}
      />
      {showCardSelectInput && (
        <MessageDialog
          onYes={(uid) => {
            setShowCardSelectInput(false);
            setCardSent(
              empCardId,
              physicalCards.find((card) => card.uid.toUpperCase() == uid?.toUpperCase())
                ?.visualNumber ?? "",
            );
          }}
          onNo={() => setShowCardSelectInput(false)}
          withSelect={true}
          title={"Karte auswählen"}
          message={"Welche Karte wird in die Post gegeben?"}
          yesLabel={"Ab in die Post!"}
          noLabel={"Abbrechen"}
          selectOptions={physicalCards.map((physicalCard) => {
            return { id: physicalCard.uid, label: physicalCard.visualNumber, selected: false };
          })}
        />
      )}
    </>
  );
};
