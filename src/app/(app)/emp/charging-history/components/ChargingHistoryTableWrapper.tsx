"use client";
import React, { useContext } from "react";
import type { Cdr } from "@prisma/client";
import type { Fe<PERSON><PERSON> } from "swr";
import useSWR from "swr";

import Loading from "~/app/(app)/loading";
import DateContext from "~/component/dateRangePicker/dateContext";
import { ChargingHistoryTable } from "./ChargingHistoryTable";

const fetcher: Fetcher<Cdr[], string> = async (url: string) => {
  const response = await fetch(url, { method: "GET" });
  
  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }
  
  const data = await response.json();
  
  // Handle error responses from the API
  if (data.error) {
    throw new Error(data.error);
  }
  
  return data.map((cdr: any) => ({
    ...cdr,
    Start_datetime: new Date(cdr.Start_datetime),
    End_datetime: new Date(cdr.End_datetime),
  })) as Cdr[];
};

const ChargingHistoryTableWrapper = () => {
  const { startDate, endDate } = useContext(DateContext);

  const formattedStartDate = startDate.toLocaleString("sv-SE").slice(0, 10);
  const formattedEndDate = endDate.toLocaleString("sv-SE").slice(0, 10); // Format 'YYYY-MM-DD'
  const apiURL = `/api/emp/charging-history/managerview?startDate=${formattedStartDate}&endDate=${formattedEndDate}`;
  
  const { data, error, isLoading, mutate } = useSWR<Cdr[], Error>(apiURL, fetcher);

  if (error) {
    return (
      <div className="rounded-lg bg-red-50 p-4 dark:bg-red-900/20">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
              <path
                fillRule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                clipRule="evenodd"
              />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800 dark:text-red-200">
              Fehler beim Laden der Daten
            </h3>
            <div className="mt-2 text-sm text-red-700 dark:text-red-300">
              <p>{error.message}</p>
            </div>
          </div>
        </div>
      </div>
    );
  }
  
  if (isLoading) return <Loading />;

  return (
    <>
      {data && <ChargingHistoryTable cdrs={data} />}
    </>
  );
};

export default ChargingHistoryTableWrapper;
