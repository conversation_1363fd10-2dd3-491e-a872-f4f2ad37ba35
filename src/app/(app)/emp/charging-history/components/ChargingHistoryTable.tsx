"use client";

import Table, { filterParams } from "~/utils/table/table";
import React, { useState } from "react";
import {
  dateTimeRenderer,
  monthYearComparator,
  monthYearRenderer,
  twoDecimalPlacesFormatterKwh,
  twoDecimalPlacesFormatterWithCurrency,
} from "~/utils/table/formatter";
import { Cdr, Role } from "@prisma/client";
import { IAggFuncParams } from "ag-grid-community";
import type {
  ValueFormatterParams,
  ValueGetterParams,
} from "ag-grid-community/dist/lib/entities/colDef";
import { BiWorld } from "react-icons/bi";
import { GrUserExpert } from "react-icons/gr";
import { BsQrCode } from "react-icons/bs";
import { useSession } from "next-auth/react";

export const ChargingHistoryTable = ({ cdrs }: { cdrs: Cdr[] }) => {
  //const [data, setData] = useState<ExtractedDataType>([]);
  const { data: session } = useSession();

  const gridOptions = {
    suppressAggFuncInHeader: true,
    // Enable multiple group columns to better handle nested grouping
    groupMultiAutoColumn: true,
    // Allow multiple columns to be grouped
    rowGroupPanelShow: "always",
  };
  const columnDefs = [
    {
      field: "MonthYear",
      headerName: "Monat/Jahr",
      valueGetter: (params: { data: Cdr }) => {
        const date = new Date(params?.data?.End_datetime);
        const month = date.getMonth() + 1; // Da Monate von 0 bis 11 indiziert sind, fügen wir 1 hinzu
        const year = date.getFullYear();
        return `${month}/${year}`; // Dies gibt z.B. "5/2023" für Mai 2023 zurück
      },
      comparator: monthYearComparator,
      cellRenderer: monthYearRenderer,
      aggFunc: (params: IAggFuncParams) => {
        return "Summe";
      },
    },
    {
      field: "Start_datetime",
      filter: "agDateColumnFilter",
      headerName: "Start",
      editable: false,
      cellRenderer: dateTimeRenderer,
      filterParams: filterParams,
      enableRowGroup: false,
    },
    {
      field: "End_datetime",
      headerName: "Ende",
      filter: "agDateColumnFilter",
      editable: false,
      cellRenderer: dateTimeRenderer,
      filterParams: filterParams,
      enableRowGroup: false,
    },
    { field: "Duration", headerName: "Dauer (h:m:s)", editable: false, filter: false },
    {
      field: "Volume",
      headerName: "Menge",
      cellRenderer: twoDecimalPlacesFormatterKwh,
      filter: "agNumberColumnFilter",
      editable: false,
      aggFunc: "sum",
    },
    {
      field: "Calculated_Cost",
      headerName: "Gesamtkosten (Brutto)",
      filter: "agNumberColumnFilter",
      editable: false,
      aggFunc: "sum",
      cellRenderer: twoDecimalPlacesFormatterWithCurrency,
      valueGetter: (params: { data: Cdr }) => {
        if (params?.data?.Calculated_Cost) {
          return params?.data?.Calculated_Cost * 1.19;
        }
        return 0;
      },
    },
    {
      field: "Parking_Time_Cost",
      headerName: "Blockiergebühr (Brutto)",
      filter: "agNumberColumnFilter",
      editable: false,
      aggFunc: "sum",
      cellRenderer: twoDecimalPlacesFormatterWithCurrency,
      valueGetter: (params: { data: Cdr }) => {
        if (params?.data?.Parking_Time_Cost) {
          return params?.data?.Parking_Time_Cost * 1.19;
        }
        return 0;
      },
    },
    {
      field: "EnergyCosts",
      headerName: "Energiekosten (Brutto)",
      filter: "agNumberColumnFilter",
      editable: false,
      aggFunc: "sum",
      cellRenderer: twoDecimalPlacesFormatterWithCurrency,
      valueGetter: (params: { data: Cdr }) => {
        if (params?.data?.EnergyCosts) {
          return params?.data?.EnergyCosts * 1.19;
        }
        return 0;
      },
    },
    {
      field: "Start_Tariff",
      headerName: "Startgebühr (Brutto)",
      filter: "agNumberColumnFilter",
      editable: false,
      aggFunc: "sum",
      cellRenderer: twoDecimalPlacesFormatterWithCurrency,
      valueGetter: (params: { data: Cdr }) => {
        if (params?.data?.Start_Tariff) {
          return params?.data?.Start_Tariff * 1.19;
        }
        return 0;
      },
    },

    {
      field: "tariff",
      headerName: "Tarif",
      filter: "agSetColumnFilter",
      editable: false,
      enableRowGroup: true,

      valueGetter: (params: ValueGetterParams) => {
        // For regular data rows
        if (params.data) {
          if (params.data.tarif) {
            return `Roaming ${params.data.Service_Provider_ID}`;
          }
          if (params.data.companyTarif) {
            return `Tarif ${params.data.companyTarif.name}`;
          }
          if (!params.data.tarif && params.data.Service_Provider_ID == "DEEUL") {
            return "Adhoc Zahlung per QR-Code";
          }
          return "Keinem Tarif zugeordnet";
        }

        // For group nodes
        if (params.node?.group) {
          // Only show the group key if we're grouped by tariff
          if (params.node.field === "tariff") {
            return params.node.key;
          }
        }

        return "";
      },
      cellRenderer: (params: ValueFormatterParams) => {
        // For group rows
        if (params.node?.group) {
          // If we have a valid group value, display it
          if (params.value && params.value !== "[object Object]") {
            // Determine which icon to show based on the group value
            if (params.value.includes("Roaming")) {
              return (
                <>
                  <BiWorld className={"mr-1"} size={20} />
                  <span>{params.value}</span>
                </>
              );
            } else if (params.value.includes("Tarif")) {
              return (
                <>
                  <GrUserExpert size={20} className={"mr-1"} />
                  <span>{params.value}</span>
                </>
              );
            } else if (params.value.includes("Adhoc")) {
              return (
                <>
                  <BsQrCode size={20} className={"mr-1"} />
                  <span>{params.value}</span>
                </>
              );
            }
            return params.value;
          }
          return "";
        }

        // For data rows
        const data = params.data;
        if (!data) return "Keinem Tarif zugeordnet";

        if (data.tarif) {
          return (
            <>
              <BiWorld className={"mr-1"} size={20} />
              <span>{`Roaming ${data.Service_Provider_ID ?? ""}`}</span>
            </>
          );
        }
        if (data.companyTarif) {
          return (
            <>
              <GrUserExpert size={20} className={"mr-1"} />
              <span>{`Tarif ${data.companyTarif.name}`}</span>
            </>
          );
        }
        if (!data.tarif && data.Service_Provider_ID == "DEEUL") {
          return (
            <>
              <BsQrCode size={20} className={"mr-1"} />
              <span>Adhoc Zahlung per QR-Code</span>
            </>
          );
        }
        return "Keinem Tarif zugeordnet";
      },
    },
    {
      field: "billable",
      headerName: "Abrechenbar",
      editable: false,
      valueGetter: (params: ValueGetterParams) => (params?.data?.billable ? "Ja" : "Nein"),
      aggFunc: (params: IAggFuncParams) =>
        params.values.reduce((count, value) => {
          // Falls der Wert "ja" ist, erhöhe den Zähler
          return count + (value === "Ja" ? 1 : 0);
        }, 0),
    },

    { field: "Charge_Point_Address", headerName: "Straße", editable: false },
    { field: "Charge_Point_ZIP", headerName: "PLZ", editable: false, width: 100 },
    { field: "Charge_Point_City", headerName: "Stadt", editable: false },
    { field: "Charge_Point_ID", headerName: "EVSE ID", editable: false },
  ];

  if (session?.user?.role == Role.ADMIN || session?.user?.role == Role.CPO) {
    columnDefs.push({ field: "Contract_ID", headerName: "Contract", editable: false });
    columnDefs.push({
      field: "marge",
      headerName: "Marge (abzgl. Gebühren)",
      filter: "agNumberColumnFilter",
      editable: false,
      aggFunc: "sum",
      cellRenderer: twoDecimalPlacesFormatterWithCurrency,
      valueGetter: (params: { data: Cdr }) => {
        return (params?.data?.Calculated_Cost || 0) - (params?.data?.cost?.cost || 0);
      },
    });

    columnDefs.push({
      field: "cost.cost",
      headerName: "Energie Einkauf",
      filter: "agNumberColumnFilter",
      editable: false,
      aggFunc: "sum",
      cellRenderer: twoDecimalPlacesFormatterWithCurrency,
    });
  }

  return (
    <>
      <Table
        gridId={"emp_charging_history_manager"}
        gridOptions={gridOptions}
        columnDefs={columnDefs}
        rowData={cdrs}
        groupIncludeTotalFooter={true}
        overlayNoRowsTemplate={"Keine Ladevorgänge vorhanden"}
        enableCsvDownload={true}
        csvFileName="ladevorgaenge"
      />
    </>
  );
};
