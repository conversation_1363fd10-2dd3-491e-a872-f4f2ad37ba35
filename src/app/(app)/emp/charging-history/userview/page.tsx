import Card from "~/component/card";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import prisma from "~/server/db/prisma";
import React from "react";
import { ChargingHistoryTable } from "~/app/(app)/emp/charging-history/components/ChargingHistoryTable";
import { Role } from "@prisma/client";
import Headline from "~/component/Headline";
import NotFound from "~/app/(app)/not-found";

 const getCdrsForCardHolder = async () => {
  const session = await getServerSession(authOptions);
  const userId = session?.user?.id;
  if (userId) {
    const userCards = await prisma?.eMPCard.findMany({
      where: { userId: userId },
      include: { physicalCard: true },
    });
    const tokenUids = userCards
      .map((card) => card?.physicalCard?.uid)
      .filter((item): item is string => item !== undefined);
    return prisma.cdr.findMany({
      where: { Authentication_ID: { in: tokenUids } },
      include: { companyTarif: true },
    });
  }
};

const Page = async () => {
  const session = await getServerSession(authOptions);

  if (!session || session?.user?.role !== Role.CARD_HOLDER) {
    return <NotFound />;
  }

  return (
    <>
      <Card className={"mb-4"}>
        <Headline title={"Ladevorgänge"} />
        <ChargingHistoryTable cdrs={(await getCdrsForCardHolder()) ?? []} />
      </Card>
    </>
  );
};

export default Page;
