"use client";

import React, { useState, useEffect, useCallback, useRef } from "react";
import { PlugAndChargeEventType } from "@prisma/client";
import { PlugAndChargeEventEntity } from "~/types/plugAndCharge";
import {
  FaPlay,
  FaPause,
  FaSync,
  FaList,
  FaTh,
  FaChevronDown,
  FaChevronUp,
  FaKey,
  FaCheckCircle,
} from "react-icons/fa";
import { MdOutgoingMail, MdCallReceived } from "react-icons/md";
import EventCard from "./components/EventCard";
import LiveStats from "./components/LiveStats";
import { userStore } from "~/server/zustand/store";
import Button from "~/component/button";

interface EventPair {
  incoming: PlugAndChargeEventEntity | null;
  outgoing: PlugAndChargeEventEntity[];
  sessionId: string | null;
  evseid: string;
  linkedCount: number;
}

export default function PlugAndChargeEventsPage() {
  const [events, setEvents] = useState<PlugAndChargeEventEntity[]>([]);
  const [linkedPairs, setLinkedPairs] = useState<EventPair[]>([]);
  const [loading, setLoading] = useState(true);
  const [liveMode, setLiveMode] = useState(false);
  const [viewMode, setViewMode] = useState<"pairs" | "all">("pairs");
  const [showBearerSection, setShowBearerSection] = useState(false);
  const [bearerRefreshing, setBearerRefreshing] = useState(false);
  const [tokenTesting, setTokenTesting] = useState(false);

  const [filter, setFilter] = useState<{
    evseid: string;
    type: string;
    sessionId: string;
  }>({
    evseid: "",
    type: "",
    sessionId: "",
  });

  // Fetch all events
  const fetchEvents = useCallback(async () => {
    try {
      const params = new URLSearchParams();
      if (filter.evseid) params.append("evseid", filter.evseid);
      if (filter.type) params.append("type", filter.type);
      if (filter.sessionId) params.append("sessionId", filter.sessionId);

      params.append("limit", "50");

      const response = await fetch(`/api/plug-and-charge-events?${params}`);
      const result = await response.json();

      if (result.success) {
        setEvents(result.data);
      }
    } catch (error) {
      console.error("Error fetching events:", error);
    }
  }, [filter]);

  // Fetch linked event pairs
  const fetchLinkedPairs = useCallback(async () => {
    try {
      const params = new URLSearchParams();
      if (filter.evseid) params.append("evseid", filter.evseid);
      if (filter.sessionId) params.append("sessionId", filter.sessionId);

      params.append("limit", "20");

      const response = await fetch(`/api/plug-and-charge-events/linked?${params}`);
      const result = await response.json();

      if (result.success) {
        setLinkedPairs(result.data);
      }
    } catch (error) {
      console.error("Error fetching linked pairs:", error);
    }
  }, [filter]);

  // Remove the fetchAvailableOus function as we don't need it anymore

  // Refresh bearer token function
  const refreshBearerToken = async () => {
    setBearerRefreshing(true);
    try {
      const response = await fetch("/api/plug-and-charge/refresh-bearer", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      });

      const data = await response.json();

      if (response.ok) {
        alert(
          `Bearer token erfolgreich aktualisiert!\nToken Preview: ${data.token_preview}\nExpires in: ${data.expires_in} seconds`,
        );
      } else {
        alert(`Fehler beim Aktualisieren des Bearer Tokens: ${data.error}\n${data.details || ""}`);
      }
    } catch (error) {
      alert(
        `Fehler beim Aktualisieren des Bearer Tokens: ${error instanceof Error ? error.message : "Unbekannter Fehler"}`,
      );
    } finally {
      setBearerRefreshing(false);
    }
  };

  // Test bearer token function
  const testBearerToken = async () => {
    setTokenTesting(true);
    try {
      const response = await fetch("/api/plug-and-charge/test-token", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      });

      const data = await response.json();

      if (response.ok) {
        if (data.success) {
          alert(`✅ ${data.message}\n${data.uuid ? `UUID: ${data.uuid}` : "Kein UUID erhalten"}\nStatus: ${data.status}`);
        } else {
          alert(`❌ ${data.message}\nStatus: ${data.status}\nDetails: ${JSON.stringify(data.response_data, null, 2)}`);
        }
      } else {
        alert(`Fehler beim Testen des Bearer Tokens: ${data.error}\n${data.details || ""}`);
      }
    } catch (error) {
      alert(`Fehler beim Testen des Bearer Tokens: ${error instanceof Error ? error.message : "Unbekannter Fehler"}`);
    } finally {
      setTokenTesting(false);
    }
  };

  // Load data
  const loadData = useCallback(async () => {
    setLoading(true);
    await Promise.all([fetchEvents(), fetchLinkedPairs()]);
    setLoading(false);
  }, [fetchEvents, fetchLinkedPairs]);

  // Initial load
  useEffect(() => {
    loadData();
  }, [loadData]);

  // Live mode interval
  useEffect(() => {
    if (liveMode) {
      const interval = setInterval(loadData, 5000); // Update every 5 seconds
      return () => clearInterval(interval);
    }
  }, [liveMode, loadData]);

  // Reload on OU change (watch zustand store)
  const selectedOuId = userStore((s) => s.selectedOuId);
  const previousOuIdRef = useRef<string | null>(null);
  const reloadTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    // Skip initial mount
    if (previousOuIdRef.current === null) {
      previousOuIdRef.current = selectedOuId;
      return;
    }
    if (previousOuIdRef.current === selectedOuId) return;

    // Clear pending
    if (reloadTimeoutRef.current) clearTimeout(reloadTimeoutRef.current);

    // Set loading and delay reload slightly so server session catches up
    setLoading(true);
    reloadTimeoutRef.current = setTimeout(() => {
      loadData().finally(() => {
        previousOuIdRef.current = selectedOuId;
      });
    }, 800);

    return () => {
      if (reloadTimeoutRef.current) clearTimeout(reloadTimeoutRef.current);
    };
  }, [selectedOuId, loadData]);

  // Remove frontend filtering as we now handle it via API

  const formatTimestamp = (timestamp: string | Date) => {
    return new Date(timestamp).toLocaleString("de-DE", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
    });
  };

  const incomingEvents = events.filter((e) => e.type === PlugAndChargeEventType.INCOMING);
  const outgoingEvents = events.filter((e) => e.type === PlugAndChargeEventType.OUTGOING);

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Plug & Charge Events</h1>

        <div className="flex items-center space-x-4">
          {/* View Mode Toggle */}
          <div className="flex rounded-lg bg-gray-200 p-1">
            <button
              onClick={() => setViewMode("pairs")}
              className={`flex items-center space-x-2 rounded-md px-3 py-1 text-sm font-medium transition-colors ${
                viewMode === "pairs"
                  ? "bg-white text-gray-900 shadow-sm"
                  : "text-gray-600 hover:text-gray-900"
              }`}
            >
              <FaTh size={14} />
              <span>Pairs</span>
            </button>
            <button
              onClick={() => setViewMode("all")}
              className={`flex items-center space-x-2 rounded-md px-3 py-1 text-sm font-medium transition-colors ${
                viewMode === "all"
                  ? "bg-white text-gray-900 shadow-sm"
                  : "text-gray-600 hover:text-gray-900"
              }`}
            >
              <FaList size={14} />
              <span>All</span>
            </button>
          </div>

          {/* Live Mode Toggle */}
          <button
            onClick={() => setLiveMode(!liveMode)}
            className={`flex items-center space-x-2 rounded-lg px-4 py-2 font-medium transition-colors ${
              liveMode
                ? "bg-green-500 text-white hover:bg-green-600"
                : "bg-gray-200 text-gray-700 hover:bg-gray-300"
            }`}
          >
            {liveMode ? <FaPause /> : <FaPlay />}
            <span>{liveMode ? "Live" : "Paused"}</span>
          </button>

          {/* Manual Refresh */}
          <button
            onClick={loadData}
            disabled={loading}
            className="hover:bg-primary/90 flex items-center space-x-2 rounded-lg bg-primary px-4 py-2 text-white disabled:opacity-50"
          >
            <FaSync className={loading ? "animate-spin" : ""} />
            <span>Refresh</span>
          </button>
        </div>
      </div>

      {/* Live Statistics */}
      <LiveStats autoRefresh={liveMode} refreshInterval={5000} />

      {/* Bearer Token Management */}
      <div className="rounded-lg bg-white p-4 shadow dark:bg-gray-800">
        <div
          className="flex cursor-pointer items-center justify-between"
          onClick={() => setShowBearerSection(!showBearerSection)}
        >
          <h3 className="flex items-center space-x-2 text-lg font-semibold text-gray-900 dark:text-white">
            <FaKey className="text-blue-600" />
            <span>Bearer Token Management</span>
          </h3>
          {showBearerSection ? (
            <FaChevronUp className="text-gray-500" />
          ) : (
            <FaChevronDown className="text-gray-500" />
          )}
        </div>

        {showBearerSection && (
          <div className="mt-4 border-t border-gray-200 pt-4 dark:border-gray-600">
            <p className="mb-4 text-sm text-gray-600 dark:text-gray-400">
              Hier können Sie einen neuen Bearer Token vom Auth-Endpoint abrufen und die
              Umgebungsvariable aktualisieren.
            </p>
            <div className="flex space-x-3">
              <Button
                onClick={refreshBearerToken}
                disabled={bearerRefreshing}
                className="flex items-center space-x-2"
              >
                <FaSync className={bearerRefreshing ? "animate-spin" : ""} />
                <span>{bearerRefreshing ? "Aktualisiere..." : "Refresh Bearer"}</span>
              </Button>

              <Button
                onClick={testBearerToken}
                disabled={tokenTesting}
                className="flex items-center space-x-2"
              >
                <FaCheckCircle className={tokenTesting ? "animate-spin" : ""} />
                <span>{tokenTesting ? "Teste..." : "Test Token"}</span>
              </Button>
            </div>
          </div>
        )}
      </div>

      {/* Filters */}
      <div className="rounded-lg bg-white p-4 shadow dark:bg-gray-800">
        <h3 className="mb-4 text-lg font-semibold text-gray-900 dark:text-white">Filter</h3>

        <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
          <div>
            <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
              EVSE ID
            </label>
            <input
              type="text"
              value={filter.evseid}
              onChange={(e) => setFilter({ ...filter, evseid: e.target.value })}
              placeholder="z.B. DEABDEDE100002600000010000001"
              className="focus:ring-primary w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2"
            />
          </div>
          <div>
            <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
              Event Type
            </label>
            <select
              value={filter.type}
              onChange={(e) => setFilter({ ...filter, type: e.target.value })}
              className="focus:ring-primary w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2"
            >
              <option value="">Alle</option>
              <option value="INCOMING">Incoming</option>
              <option value="OUTGOING">Outgoing</option>
            </select>
          </div>
          <div>
            <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
              Session ID
            </label>
            <input
              type="text"
              value={filter.sessionId}
              onChange={(e) => setFilter({ ...filter, sessionId: e.target.value })}
              placeholder="z.B. C4972C98-D0E3-4168-86C9-8801B8EE7D61"
              className="focus:ring-primary w-full rounded-md border border-gray-300 px-3 py-2 focus:outline-none focus:ring-2"
            />
          </div>
        </div>
      </div>

      {/* Content based on view mode */}
      {viewMode === "pairs" ? (
        /* Linked Event Pairs */
        <div className="rounded-lg bg-white p-6 shadow dark:bg-gray-800">
          <h3 className="mb-4 text-lg font-semibold text-gray-900 dark:text-white">
            Verknüpfte Event-Paare
          </h3>

          {loading ? (
            <div className="animate-pulse space-y-4">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="h-20 rounded bg-gray-200"></div>
              ))}
            </div>
          ) : linkedPairs.length === 0 ? (
            <p className="text-gray-500 dark:text-gray-400">Keine verknüpften Events gefunden.</p>
          ) : (
            <div className="space-y-4">
              {linkedPairs.map((pair, index) => (
                <div
                  key={index}
                  className="rounded-lg border border-gray-200 p-4 dark:border-gray-600"
                >
                  <div className="grid grid-cols-1 gap-4 lg:grid-cols-2">
                    {/* Outgoing Events (Left) */}
                    <div className="space-y-2">
                      <h4 className="flex items-center space-x-2 font-medium text-blue-600">
                        <MdOutgoingMail />
                        <span>Outgoing Events ({pair.outgoing.length})</span>
                      </h4>
                      {pair.outgoing.length === 0 ? (
                        <p className="text-sm text-gray-500">Keine outgoing events</p>
                      ) : (
                        pair.outgoing.map((event) => (
                          <div
                            key={event.id}
                            className="rounded border-l-4 border-blue-500 bg-blue-50 p-3 dark:bg-blue-900/20"
                          >
                            <div className="text-sm">
                              <div className="font-medium">EVSE: {event.evseid}</div>
                              <div className="text-gray-600 dark:text-gray-400">
                                {formatTimestamp(event.plugInEventTimestamp)}
                              </div>
                              {event.latitude && event.longitude && (
                                <div className="text-gray-600 dark:text-gray-400">
                                  📍 {event.latitude.toFixed(4)}, {event.longitude.toFixed(4)}
                                </div>
                              )}
                            </div>
                          </div>
                        ))
                      )}
                    </div>

                    {/* Incoming Event (Right) */}
                    <div className="space-y-2">
                      <h4 className="flex items-center space-x-2 font-medium text-green-600">
                        <MdCallReceived />
                        <span>Incoming Event</span>
                      </h4>
                      {pair.incoming ? (
                        <div className="rounded border-l-4 border-green-500 bg-green-50 p-3 dark:bg-green-900/20">
                          <div className="text-sm">
                            <div className="font-medium">EVSE: {pair.incoming.evseid}</div>
                            <div className="text-gray-600 dark:text-gray-400">
                              {formatTimestamp(pair.incoming.plugInEventTimestamp)}
                            </div>
                            <div className="text-gray-600 dark:text-gray-400">
                              Session: {pair.incoming.sessionId}
                            </div>
                            <div className="text-gray-600 dark:text-gray-400">
                              EMAID: {pair.incoming.emaid}
                            </div>
                            <div className="text-gray-600 dark:text-gray-400">
                              Confidence: {pair.incoming.matchingConfidence?.toFixed(2)}
                            </div>
                          </div>
                        </div>
                      ) : (
                        <p className="text-sm text-gray-500">Kein incoming event verknüpft</p>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      ) : (
        /* All Events View */
        <div className="space-y-6">
          {/* Outgoing Events */}
          <div className="rounded-lg bg-white p-6 shadow dark:bg-gray-800">
            <h3 className="mb-4 flex items-center space-x-2 text-lg font-semibold text-gray-900 dark:text-white">
              <MdOutgoingMail className="text-blue-600" />
              <span>Outgoing Events ({outgoingEvents.length})</span>
            </h3>

            {loading ? (
              <div className="animate-pulse space-y-4">
                {[...Array(3)].map((_, i) => (
                  <div key={i} className="h-32 rounded bg-gray-200"></div>
                ))}
              </div>
            ) : outgoingEvents.length === 0 ? (
              <p className="text-gray-500 dark:text-gray-400">Keine outgoing events gefunden.</p>
            ) : (
              <div className="grid grid-cols-1 gap-4 lg:grid-cols-2">
                {outgoingEvents.map((event) => (
                  <EventCard key={event.id} event={event} showRawData={true} />
                ))}
              </div>
            )}
          </div>

          {/* Incoming Events */}
          <div className="rounded-lg bg-white p-6 shadow dark:bg-gray-800">
            <h3 className="mb-4 flex items-center space-x-2 text-lg font-semibold text-gray-900 dark:text-white">
              <MdCallReceived className="text-green-600" />
              <span>Incoming Events ({incomingEvents.length})</span>
            </h3>

            {loading ? (
              <div className="animate-pulse space-y-4">
                {[...Array(3)].map((_, i) => (
                  <div key={i} className="h-32 rounded bg-gray-200"></div>
                ))}
              </div>
            ) : incomingEvents.length === 0 ? (
              <p className="text-gray-500 dark:text-gray-400">Keine incoming events gefunden.</p>
            ) : (
              <div className="grid grid-cols-1 gap-4 lg:grid-cols-2">
                {incomingEvents.map((event) => (
                  <EventCard key={event.id} event={event} showRawData={true} />
                ))}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
