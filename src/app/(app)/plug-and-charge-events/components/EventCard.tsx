"use client";

import React, { useState } from "react";
import { PlugAndChargeEventType } from "@prisma/client";
import { PlugAndChargeEventEntity } from "~/types/plugAndCharge";
import { MdOutgoingMail, MdCallReceived, MdExpandMore, MdExpandLess } from "react-icons/md";

interface EventCardProps {
  event: PlugAndChargeEventEntity;
  showRawData?: boolean;
}

export default function EventCard({ event, showRawData = false }: EventCardProps) {
  const [expanded, setExpanded] = useState(false);

  const formatTimestamp = (timestamp: string | Date) => {
    return new Date(timestamp).toLocaleString("de-DE", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
    });
  };

  const getEventTypeColor = (type: PlugAndChargeEventType) => {
    return type === PlugAndChargeEventType.INCOMING 
      ? "border-green-500 bg-green-50 dark:bg-green-900/20" 
      : "border-blue-500 bg-blue-50 dark:bg-blue-900/20";
  };

  const getEventTypeIcon = (type: PlugAndChargeEventType) => {
    return type === PlugAndChargeEventType.INCOMING 
      ? <MdCallReceived className="text-green-600" size={20} />
      : <MdOutgoingMail className="text-blue-600" size={20} />;
  };

  const getEventTypeLabel = (type: PlugAndChargeEventType) => {
    return type === PlugAndChargeEventType.INCOMING ? "Incoming" : "Outgoing";
  };

  return (
    <div className={`border-l-4 rounded-lg p-4 ${getEventTypeColor(event.type)}`}>
      {/* Header */}
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center space-x-2">
          {getEventTypeIcon(event.type)}
          <span className="font-medium text-gray-900 dark:text-white">
            {getEventTypeLabel(event.type)} Event
          </span>
        </div>
        <button
          onClick={() => setExpanded(!expanded)}
          className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
        >
          {expanded ? <MdExpandLess size={20} /> : <MdExpandMore size={20} />}
        </button>
      </div>

      {/* Basic Info */}
      <div className="space-y-2 text-sm">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
          <div>
            <span className="font-medium text-gray-700 dark:text-gray-300">EVSE ID:</span>
            <span className="ml-2 text-gray-900 dark:text-white font-mono text-xs">
              {event.evseid}
            </span>
          </div>
          <div>
            <span className="font-medium text-gray-700 dark:text-gray-300">Timestamp:</span>
            <span className="ml-2 text-gray-900 dark:text-white">
              {formatTimestamp(event.plugInEventTimestamp)}
            </span>
          </div>
        </div>

        {/* Incoming-specific fields */}
        {event.type === PlugAndChargeEventType.INCOMING && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
            {event.emaid && (
              <div>
                <span className="font-medium text-gray-700 dark:text-gray-300">EMA ID:</span>
                <span className="ml-2 text-gray-900 dark:text-white font-mono text-xs">
                  {event.emaid}
                </span>
              </div>
            )}
            {event.pncSessionId && (
              <div>
                <span className="font-medium text-gray-700 dark:text-gray-300">Session ID:</span>
                <span className="ml-2 text-gray-900 dark:text-white font-mono text-xs">
                  {event.pncSessionId}
                </span>
              </div>
            )}
            {event.pcid && (
              <div>
                <span className="font-medium text-gray-700 dark:text-gray-300">PC ID:</span>
                <span className="ml-2 text-gray-900 dark:text-white font-mono text-xs">
                  {event.pcid}
                </span>
              </div>
            )}
            {event.matchingConfidence !== null && event.matchingConfidence !== undefined && (
              <div>
                <span className="font-medium text-gray-700 dark:text-gray-300">Confidence:</span>
                <span className="ml-2 text-gray-900 dark:text-white">
                  {(event.matchingConfidence * 100).toFixed(1)}%
                </span>
              </div>
            )}
          </div>
        )}

        {/* Location info */}
        {(event.latitude || event.longitude || event.locationId) && (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
            {event.locationId && (
              <div>
                <span className="font-medium text-gray-700 dark:text-gray-300">Location ID:</span>
                <span className="ml-2 text-gray-900 dark:text-white font-mono text-xs">
                  {event.locationId}
                </span>
              </div>
            )}
            {event.latitude && event.longitude && (
              <div>
                <span className="font-medium text-gray-700 dark:text-gray-300">Coordinates:</span>
                <span className="ml-2 text-gray-900 dark:text-white">
                  📍 {event.latitude.toFixed(4)}, {event.longitude.toFixed(4)}
                </span>
              </div>
            )}
          </div>
        )}

        {/* Linking info */}
        {event.type === PlugAndChargeEventType.OUTGOING && event.pncSessionId && (
          <div>
            <span className="font-medium text-gray-700 dark:text-gray-300">Linked Session:</span>
            <span className="ml-2 text-gray-900 dark:text-white font-mono text-xs">
              {event.pncSessionId}
            </span>
          </div>
        )}
      </div>

      {/* Expanded Details */}
      {expanded && (
        <div className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-600">
          <div className="space-y-3">
            {/* Metadata */}
            <div>
              <h4 className="font-medium text-gray-700 dark:text-gray-300 mb-2">Metadata</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm">
                <div>
                  <span className="font-medium text-gray-600 dark:text-gray-400">Event ID:</span>
                  <span className="ml-2 text-gray-900 dark:text-white font-mono text-xs">
                    {event.id}
                  </span>
                </div>
                <div>
                  <span className="font-medium text-gray-600 dark:text-gray-400">Created:</span>
                  <span className="ml-2 text-gray-900 dark:text-white">
                    {formatTimestamp(event.createdAt)}
                  </span>
                </div>
              </div>
            </div>

            {/* Raw Data */}
            {showRawData && event.raw && (
              <div>
                <h4 className="font-medium text-gray-700 dark:text-gray-300 mb-2">Raw Data</h4>
                <pre className="bg-gray-100 dark:bg-gray-800 p-3 rounded text-xs overflow-x-auto">
                  {JSON.stringify(event.raw, null, 2)}
                </pre>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
