"use client";

import React, { useState, useEffect, useRef } from "react";
import RealtimeWidget from "~/app/(app)/component/RealtimeWidget";
import {
  MdOutgoingMail,
  MdCallReceived,
  MdLink,
  MdTrendingUp,
  MdExpandMore,
  MdExpandLess,
} from "react-icons/md";
import { FaChartLine, FaMapMarkerAlt } from "react-icons/fa";
import { Chip } from "~/app/(app)/component/chip";
import { userStore } from "~/server/zustand/store";

interface StatsData {
  total: {
    events: number;
    incoming: number;
    outgoing: number;
    linkedOutgoing: number;
    uniqueEvses: number;
  };
  recent: {
    events: number;
    incoming: number;
    outgoing: number;
    hours: number;
  };
  quality: {
    avgMatchingConfidence: number;
    linkingRate: number;
  };
  latest: Array<{
    id: string;
    type: string;
    evseid: string;
    createdAt: string;
    emaid?: string;
    pncSessionId?: string;
    raw: any;
  }>;
  hourly: Array<{
    hour: string;
    type: string;
    count: number;
  }>;
}

interface LiveStatsProps {
  refreshInterval?: number;
  autoRefresh?: boolean;
}

export default function LiveStats({ refreshInterval = 30000, autoRefresh = true }: LiveStatsProps) {
  const [stats, setStats] = useState<StatsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);
  const [expandedEvents, setExpandedEvents] = useState<Set<string>>(new Set());
  const [simulatingIds, setSimulatingIds] = useState<Set<string>>(new Set());
  const [incomingRawByEventId, setIncomingRawByEventId] = useState<Record<string, any>>({});
  const [incomingLoadingIds, setIncomingLoadingIds] = useState<Set<string>>(new Set());

  const fetchStats = async () => {
    try {
      const response = await fetch("/api/plug-and-charge-events/stats");
      const result = await response.json();

      if (result.success) {
        setStats(result.data);
        setLastUpdate(new Date());
      }
    } catch (error) {
      console.error("Error fetching stats:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchStats();
  }, []);

  useEffect(() => {
    if (autoRefresh) {
      const interval = setInterval(fetchStats, refreshInterval);
      return () => clearInterval(interval);
    }
  }, [autoRefresh, refreshInterval]);

  // Reload on OU change (zustand store)
  const selectedOuId = userStore((s) => s.selectedOuId);
  const previousOuIdRef = useRef<string | null>(null);
  const reloadTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    if (previousOuIdRef.current === null) {
      previousOuIdRef.current = selectedOuId;
      return;
    }
    if (previousOuIdRef.current === selectedOuId) return;

    if (reloadTimeoutRef.current) clearTimeout(reloadTimeoutRef.current);
    setLoading(true);
    reloadTimeoutRef.current = setTimeout(() => {
      fetchStats().finally(() => {
        previousOuIdRef.current = selectedOuId;
      });
    }, 800);

    return () => {
      if (reloadTimeoutRef.current) clearTimeout(reloadTimeoutRef.current);
    };
  }, [selectedOuId]);

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString("de-DE", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
    });
  };

  const fetchIncomingRawForOutgoing = async (ev: StatsData["latest"][number]) => {
    if (!ev.pncSessionId) return;
    if (incomingRawByEventId[ev.id]) return; // already fetched
    setIncomingLoadingIds((prev) => new Set(prev).add(ev.id));
    try {
      const res = await fetch(
        `/api/plug-and-charge-events?sessionId=${encodeURIComponent(ev.pncSessionId)}&limit=5`,
      );
      const json = await res.json();
      if (json?.success && Array.isArray(json.data)) {
        const incoming = json.data.find(
          (e: any) =>
            e.type === "INCOMING" &&
            (e.pncSessionId === ev.pncSessionId || e.raw?.sessionId === ev.pncSessionId),
        );
        if (incoming) {
          setIncomingRawByEventId((prev) => ({ ...prev, [ev.id]: incoming.raw }));
        }
      }
    } catch (e) {
      console.error("Failed to fetch incoming raw:", e);
    } finally {
      setIncomingLoadingIds((prev) => {
        const n = new Set(prev);
        n.delete(ev.id);
        return n;
      });
    }
  };

  const toggleEventExpansion = (eventId: string, ev?: StatsData["latest"][number]) => {
    setExpandedEvents((prev) => {
      const newSet = new Set(prev);
      const willExpand = !newSet.has(eventId);
      if (willExpand) {
        newSet.add(eventId);
      } else {
        newSet.delete(eventId);
      }
      return newSet;
    });
    if (ev && ev.type === "OUTGOING" && ev.pncSessionId) {
      void fetchIncomingRawForOutgoing(ev);
    }
  };

  const simulateIncomingForOutgoing = async (ev: StatsData["latest"][number]) => {
    // nur für OUTGOING sinnvoll
    if (ev.type !== "OUTGOING") return;
    setSimulatingIds((prev) => new Set(prev).add(ev.id));
    try {
      const ts = (ev as any)?.raw?.plugInEventTimestamp || ev.createdAt;
      const payload = {
        emaid: `EMP99CPnCRemot1`,
        evseid: ev.evseid,
        pcid: `HUBTESTPROVCERT${Math.floor(Math.random() * 999)
          .toString()
          .padStart(3, "0")}`,
        sessionId:
          ev.pncSessionId ||
          `${Math.random().toString(36).slice(2, 10).toUpperCase()}-${Math.random().toString(36).slice(2, 6).toUpperCase()}`,
        matchingConfidence: 0.9,
        plugInEventTimestamp: new Date(ts).toISOString(),
      };
      const res = await fetch("/api/webhook/pnc", {
        method: "POST",
        headers: { "content-type": "application/json" },
        body: JSON.stringify(payload),
      });
      if (!res.ok) {
        const t = await res.text();
        console.error("Simulate Match failed:", res.status, res.statusText, t);
      }
      // Nach Erfolg neu laden
      await fetchStats();
    } catch (e) {
      console.error(e);
    } finally {
      setSimulatingIds((prev) => {
        const n = new Set(prev);
        n.delete(ev.id);
        return n;
      });
    }
  };

  return (
    <div className="space-y-6">
      {/* Main Statistics */}
      <div className="grid grid-cols-2 gap-4 md:grid-cols-3 lg:grid-cols-6">
        <RealtimeWidget
          caption="Total Events"
          primaryValue={stats?.total.events || 0}
          loading={loading}
          icon={<FaChartLine size={21} />}
        />
        <RealtimeWidget
          caption="Incoming"
          primaryValue={stats?.total.incoming || 0}
          loading={loading}
          icon={<MdCallReceived size={21} />}
        />
        <RealtimeWidget
          caption="Outgoing"
          primaryValue={stats?.total.outgoing || 0}
          loading={loading}
          icon={<MdOutgoingMail size={21} />}
        />
        <RealtimeWidget
          caption="Matches"
          primaryValue={stats?.total.linkedOutgoing || 0}
          secondaryValue={`${stats ? stats?.quality.linkingRate.toFixed(1) : 0}%`}
          loading={loading}
          icon={<MdLink size={21} />}
        />
        <RealtimeWidget
          caption="Avg Confidence"
          primaryValue={`${stats ? (stats?.quality?.avgMatchingConfidence * 100).toFixed(1) : 0}% `}
          loading={loading}
          icon={<MdTrendingUp size={21} />}
        />
        <RealtimeWidget
          caption="Unique EVSEs"
          primaryValue={stats?.total.uniqueEvses || 0}
          loading={loading}
          icon={<FaMapMarkerAlt size={21} />}
        />
      </div>

      {/* Recent Activity */}
      <div className="rounded-lg bg-white p-4 shadow dark:bg-gray-800">
        <h3 className="mb-4 text-lg font-semibold text-gray-900 dark:text-white">
          Recent Activity ({stats?.recent.hours || 0}h)
        </h3>
        <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
          <div className="flex items-center">
            <span className="text-gray-600 dark:text-gray-400">Total Events: </span>
            <span className="font-semibold text-gray-900 dark:text-white">
              {stats?.recent.events || 0}
            </span>
          </div>
          <div className="flex items-center space-x-1">
            <MdCallReceived size={16} className="text-green-600" />
            <span className="text-green-600">Incoming: </span>
            <span className="font-semibold text-gray-900 dark:text-white">
              {stats?.recent.incoming || 0}
            </span>
          </div>
          <div className="flex items-center space-x-1">
            <MdOutgoingMail size={16} className="text-blue-600" />
            <span className="text-blue-600">Outgoing: </span>
            <span className="font-semibold text-gray-900 dark:text-white">
              {stats?.recent.outgoing || 0}
            </span>
          </div>
        </div>
      </div>

      {/* Latest Events */}
      <div className="rounded-lg bg-white p-4 shadow dark:bg-gray-800">
        <h3 className="mb-4 text-lg font-semibold text-gray-900 dark:text-white">Latest Events</h3>
        <div className="relative">
          {loading ? (
            <div className="animate-pulse rounded bg-gray-100 p-2 dark:bg-gray-700">
              <div className="flex items-center space-x-2">
                <div className="h-4 w-4 rounded-full bg-gray-300 dark:bg-gray-600" />
                <div className="h-5 w-40 rounded bg-gray-300 dark:bg-gray-600" />
                <div className="h-5 w-28 rounded bg-gray-300 dark:bg-gray-600" />
                <div className="h-5 w-24 rounded bg-gray-300 dark:bg-gray-600" />
                <div className="h-5 w-20 rounded bg-gray-300 dark:bg-gray-600" />
              </div>
            </div>
          ) : (
            <div className="space-y-2">
              {stats?.latest.length === 0 ? (
                <p className="text-sm text-gray-500 dark:text-gray-400">No recent events</p>
              ) : (
                stats?.latest.map((event) => {
                  const isExpanded = expandedEvents.has(event.id);
                  return (
                    <div key={event.id} className="rounded bg-gray-50 p-2 text-sm dark:bg-gray-700">
                      <div className="flex items-center justify-between">
                        <div className="flex min-w-0 flex-1 items-center space-x-2">
                          {/* Event Type Icon */}
                          <div className="flex items-center space-x-1">
                            {event.type === "INCOMING" ? (
                              <MdCallReceived className="text-green-600" size={16} />
                            ) : (
                              <MdOutgoingMail className="text-blue-600" size={16} />
                            )}
                            {event.type === "OUTGOING" && event.pncSessionId && (
                              <MdLink className="text-orange-500" size={12} title="Linked" />
                            )}
                          </div>

                          {/* All Data as Chips */}
                          <div className="flex flex-wrap items-center space-x-1">
                            {/* EVSE ID Chip */}
                            <Chip
                              label={event.evseid}
                              className="bg-gray-100 text-gray-800 dark:bg-gray-600 dark:text-gray-200"
                              title={`EVSE ID: ${event.evseid}`}
                            />

                            {/* Timestamp Chip */}
                            <Chip
                              label={formatTimestamp(event.createdAt)}
                              className="bg-slate-100 text-slate-800 dark:bg-slate-600 dark:text-slate-200"
                              title={`Created: ${formatTimestamp(event.createdAt)}`}
                            />

                            {event.emaid && (
                              <Chip
                                label={`EMAID: ${event.emaid}`}
                                className="bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-200"
                                title={`EMAID: ${event.emaid}`}
                              />
                            )}

                            {event.sessionId && (
                              <Chip
                                label={`Session: ${event.sessionId.slice(-8)}`}
                                className="bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-200"
                                title={`Session ID: ${event.sessionId}`}
                              />
                            )}

                            {event.type === "OUTGOING" && event.sessionId && (
                              <Chip
                                label={`Linked: ${event.sessionId.slice(-8)}`}
                                className="bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-200"
                                title={`Linked to: ${event.sessionId}`}
                              />
                            )}
                          </div>
                        </div>

                        {/* Actions */}
                        <div className="ml-2 flex items-center gap-2">
                          {/* Simulate Match nur für OUTGOING */}
                          {event.type === "OUTGOING" && (
                            <button
                              onClick={() => simulateIncomingForOutgoing(event)}
                              disabled={simulatingIds.has(event.id)}
                              className={`rounded px-2 py-1 text-xs font-medium ${
                                simulatingIds.has(event.id)
                                  ? "bg-gray-300 text-gray-600 dark:bg-gray-600 dark:text-gray-300"
                                  : "bg-green-500 text-white hover:bg-green-600"
                              }`}
                              title="Simulate matching INCOMING event"
                            >
                              {simulatingIds.has(event.id) ? "Simulating…" : "Simulate Match"}
                            </button>
                          )}
                          {/* Expand/Collapse Button */}
                          <button
                            onClick={() => toggleEventExpansion(event.id, event)}
                            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                            title={isExpanded ? "Collapse" : "Show raw data"}
                          >
                            {isExpanded ? <MdExpandLess size={20} /> : <MdExpandMore size={20} />}
                          </button>
                        </div>
                      </div>

                      {/* Expanded Raw Data */}
                      {isExpanded && (
                        <div className="mt-3 border-t border-gray-200 pt-3 dark:border-gray-600">
                          <h4 className="mb-2 text-xs font-medium text-gray-700 dark:text-gray-300">
                            Raw Event Data:
                          </h4>
                          <pre className="overflow-x-auto rounded bg-gray-100 p-3 text-xs dark:bg-gray-800">
                            <code className="text-gray-800 dark:text-gray-200">
                              {JSON.stringify(event.raw, null, 2)}
                            </code>
                          </pre>

                          {/* If outgoing is linked (has pncSessionId), show matching incoming raw as well */}
                          {event.type === "OUTGOING" && event.pncSessionId && (
                            <div className="mt-3 border-t border-gray-200 pt-3 dark:border-gray-600">
                              <h4 className="mb-2 text-xs font-medium text-gray-700 dark:text-gray-300">
                                Incoming Raw Data (matched session)
                                {incomingLoadingIds.has(event.id) && (
                                  <span className="ml-2 text-gray-400">Loading…</span>
                                )}
                              </h4>
                              {incomingRawByEventId[event.id] ? (
                                <pre className="overflow-x-auto rounded bg-gray-100 p-3 text-xs dark:bg-gray-800">
                                  <code className="text-gray-800 dark:text-gray-200">
                                    {JSON.stringify(incomingRawByEventId[event.id], null, 2)}
                                  </code>
                                </pre>
                              ) : (
                                !incomingLoadingIds.has(event.id) && (
                                  <div className="text-xs text-gray-500">
                                    Keine Incoming-Daten gefunden.
                                  </div>
                                )
                              )}
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  );
                })
              )}
            </div>
          )}
        </div>
      </div>

      {/* Last Update Info */}
      {lastUpdate && (
        <div className="text-center text-sm text-gray-500 dark:text-gray-400">
          Last updated: {lastUpdate.toLocaleTimeString("de-DE")}
          {autoRefresh && (
            <span className="ml-2">(Auto-refresh every {Math.round(refreshInterval / 1000)}s)</span>
          )}
        </div>
      )}
    </div>
  );

  {
    /* If outgoing has a matching incoming, show its raw when expanded */
  }
  {
    isExpanded && event.type === "OUTGOING" && event.pncSessionId && (
      <div className="mt-3 border-t border-gray-200 pt-3 dark:border-gray-600">
        <h4 className="mb-2 text-xs font-medium text-gray-700 dark:text-gray-300">
          Incoming Raw Data (matched session)
          {incomingLoadingIds.has(event.id) && <span className="ml-2 text-gray-400">Loading…</span>}
        </h4>
        {incomingRawByEventId[event.id] ? (
          <pre className="overflow-x-auto rounded bg-gray-100 p-3 text-xs dark:bg-gray-800">
            <code className="text-gray-800 dark:text-gray-200">
              {JSON.stringify(incomingRawByEventId[event.id], null, 2)}
            </code>
          </pre>
        ) : (
          !incomingLoadingIds.has(event.id) && (
            <div className="text-xs text-gray-500">Keine Incoming-Daten gefunden.</div>
          )
        )}
      </div>
    );
  }
}
