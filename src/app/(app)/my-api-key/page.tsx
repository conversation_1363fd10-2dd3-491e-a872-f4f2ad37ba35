import React from "react";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role } from "@prisma/client";
import { redirect } from "next/navigation";
import Card from "~/component/card";
import Headline from "~/component/Headline";
import { MyApiKeyManager } from "./components/MyApiKeyManager";
import prisma from "~/server/db/prisma";

export const revalidate = 0;

const getMyContact = async () => {
  const session = await getServerSession(authOptions);

  if (!session || !session?.user?.role || ![Role.CPO, Role.CARD_MANAGER, Role.ADMIN].includes(session.user.role)) {
    return null;
  }

  const userOuId = session.user.selectedOu?.id || session.user.ou?.id;
  
  if (!userOuId) {
    return null;
  }

  try {
    // Find CPO contact for this OU
    const contact = await prisma.contact.findFirst({
      where: {
        ouId: userOuId,
        cpo: true,
      },
      select: {
        id: true,
        name: true,
        companyName: true,
        apiKey: true,
        ou: {
          select: {
            id: true,
            name: true,
            code: true,
          },
        },
      },
    });

    return contact;
  } catch (error) {
    console.error("Error fetching contact:", error);
    return null;
  }
};

export default async function MyApiKeyPage() {
  const session = await getServerSession(authOptions);

  // Check if user is authenticated and has correct role
  if (!session || !session?.user?.role || ![Role.CPO, Role.CARD_MANAGER, Role.ADMIN].includes(session.user.role)) {
    redirect("/login");
  }

  const contact = await getMyContact();

  return (
    <>
      <Headline title={"Mein API Key"} />
      <div className={"flex flex-col gap-5"}>
        <Card header_left={"API Key für Ihre Organisation"}>
          <div className="mb-4">
            <p className="text-sm text-gray-600">
              Hier können Sie den API Key für Ihre Organisation einsehen und verwalten.
              Der API Key ermöglicht es Ihnen, auf bestimmte API-Endpunkte zuzugreifen.
            </p>
            {session.user.selectedOu && (
              <div className="mt-3 p-3 bg-blue-50 border border-blue-200 rounded">
                <p className="text-sm text-blue-800 font-semibold">
                  📍 Aktuelle Organisation: {session.user.selectedOu.name} ({session.user.selectedOu.code})
                </p>
                {session.user.role === Role.ADMIN && (
                  <p className="text-xs text-blue-600 mt-1">
                    Als Administrator sehen Sie hier den API Key für die aktuell ausgewählte OU
                  </p>
                )}
              </div>
            )}
          </div>
          
          <MyApiKeyManager contact={contact} userRole={session.user.role} />
        </Card>
        
        <Card header_left={"API Key Verwendung"}>
          <div className="space-y-4">
            <div>
              <h4 className="font-semibold text-primary mb-2">Test-Endpunkt</h4>
              <p className="text-sm text-gray-600 mb-2">
                Verwenden Sie den folgenden Endpunkt zum Testen Ihres API Keys:
              </p>
              <code className="bg-gray-100 px-2 py-1 rounded text-sm">
                GET /api/v1/test/apikey
              </code>
            </div>
            
            <div>
              <h4 className="font-semibold text-primary mb-2">Authorization Header</h4>
              <p className="text-sm text-gray-600 mb-2">
                Ihr API Key muss im Authorization Header gesendet werden:
              </p>
              <div className="bg-gray-100 p-3 rounded text-sm font-mono">
                <div>Authorization: Bearer IHR_API_KEY</div>
                <div className="text-gray-500">oder</div>
                <div>Authorization: ApiKey IHR_API_KEY</div>
              </div>
            </div>
            
            <div>
              <h4 className="font-semibold text-primary mb-2">Beispiel cURL</h4>
              <div className="bg-gray-100 p-3 rounded text-sm font-mono">
                curl -H "Authorization: Bearer IHR_API_KEY" {typeof window !== 'undefined' ? window.location.origin : ''}/api/test/apikey
              </div>
            </div>

            <div className="bg-yellow-50 border border-yellow-200 rounded p-4">
              <h4 className="font-semibold text-yellow-800 mb-2">⚠️ Wichtige Sicherheitshinweise</h4>
              <ul className="text-sm text-yellow-700 space-y-1">
                <li>• Teilen Sie Ihren API Key niemals mit Dritten</li>
                <li>• Speichern Sie den Key sicher und verwenden Sie ihn nur in vertrauenswürdigen Anwendungen</li>
                <li>• Bei Verdacht auf Kompromittierung generieren Sie sofort einen neuen Key</li>
                <li>• Der alte Key wird ungültig, sobald ein neuer generiert wird</li>
              </ul>
            </div>
          </div>
        </Card>
      </div>
    </>
  );
}
