"use client";

import React, { useState, useTransition } from "react";
import { useRouter } from "next/navigation";
import Button from "~/component/button";
import { AiOutlineKey, AiOutlineCopy, AiOutlineReload } from "react-icons/ai";
import { Role } from "@prisma/client";

interface Contact {
  id: string;
  name: string | null;
  companyName: string | null;
  apiKey: string | null;
  ou: {
    id: string;
    name: string;
    code: string;
  } | null;
}

interface MyApiKeyManagerProps {
  contact: Contact | null;
  userRole: Role;
}

export const MyApiKeyManager: React.FC<MyApiKeyManagerProps> = ({ contact, userRole }) => {
  const router = useRouter();
  const [isPending, startTransition] = useTransition();
  const [isLoading, setIsLoading] = useState(false);
  const [showFullKey, setShowFullKey] = useState(false);

  const generateApiKey = async () => {
    setIsLoading(true);
    try {
      const response = await fetch("/api/my-api-key", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (response.ok) {
        const result = await response.json();
        alert(contact?.apiKey ? "API Key erfolgreich regeneriert!" : "API Key erfolgreich generiert!");
        startTransition(() => {
          router.refresh();
        });
      } else {
        const error = await response.json();
        alert(`Fehler: ${error.error}`);
      }
    } catch (error) {
      alert("Fehler beim Generieren des API Keys");
      console.error("Error generating API key:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      alert("API Key in die Zwischenablage kopiert!");
    } catch (error) {
      console.error("Error copying to clipboard:", error);
      alert("Fehler beim Kopieren in die Zwischenablage");
    }
  };

  const toggleShowFullKey = () => {
    setShowFullKey(!showFullKey);
  };

  if (!contact) {
    return (
      <div className="bg-red-50 border border-red-200 rounded p-4">
        <h4 className="font-semibold text-red-800 mb-2">❌ Kein CPO-Contact gefunden</h4>
        <p className="text-sm text-red-700">
          {userRole === Role.ADMIN
            ? "Für die ausgewählte OU wurde kein CPO-Contact gefunden. Bitte erstellen Sie einen CPO-Contact für diese OU oder wählen Sie eine andere OU aus."
            : "Für Ihre Organisation wurde kein CPO-Contact gefunden. Bitte wenden Sie sich an einen Administrator, um einen CPO-Contact für Ihre OU zu erstellen."
          }
        </p>
      </div>
    );
  }

  const displayName = contact.name || contact.companyName || "Unbekannt";
  const maskedKey = contact.apiKey 
    ? `${contact.apiKey.substring(0, 8)}...${contact.apiKey.substring(contact.apiKey.length - 4)}`
    : null;

  return (
    <div className="space-y-6">
      {/* Contact Information */}
      <div className="bg-gray-50 rounded p-4">
        <h4 className="font-semibold text-gray-800 mb-2">CPO-Contact Information</h4>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <span className="font-medium text-gray-600">Name:</span>
            <span className="ml-2">{displayName}</span>
          </div>
          <div>
            <span className="font-medium text-gray-600">Organisation:</span>
            <span className="ml-2">{contact.ou?.name} ({contact.ou?.code})</span>
          </div>
        </div>
      </div>

      {/* API Key Status */}
      <div className="border rounded p-4">
        <div className="flex items-center justify-between mb-4">
          <h4 className="font-semibold text-gray-800">API Key Status</h4>
          <span className={`px-3 py-1 rounded text-sm font-semibold ${
            contact.apiKey 
              ? "bg-green-100 text-green-800" 
              : "bg-gray-100 text-gray-800"
          }`}>
            {contact.apiKey ? "Aktiv" : "Nicht vorhanden"}
          </span>
        </div>

        {contact.apiKey ? (
          <div className="space-y-4">
            {/* API Key Display */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Ihr API Key:
              </label>
              <div className="flex items-center space-x-2">
                <code className="flex-1 bg-gray-100 px-3 py-2 rounded font-mono text-sm">
                  {showFullKey ? contact.apiKey : maskedKey}
                </code>
                <Button
                  type="button"
                  onClick={toggleShowFullKey}
                  className="px-3 py-2 text-xs bg-gray-500 hover:bg-gray-600"
                >
                  {showFullKey ? "Verbergen" : "Anzeigen"}
                </Button>
                <Button
                  type="button"
                  onClick={() => copyToClipboard(contact.apiKey!)}
                  className="px-3 py-2 text-xs bg-blue-500 hover:bg-blue-600"
                >
                  <AiOutlineCopy className="mr-1" size="0.8rem" />
                  Kopieren
                </Button>
              </div>
            </div>

            {/* Regenerate Button */}
            <div>
              <Button
                type="button"
                onClick={generateApiKey}
                disabled={isLoading}
                className="px-4 py-2 bg-orange-500 hover:bg-orange-600"
              >
                <AiOutlineReload className="mr-2" size="1rem" />
                {isLoading ? "Regeneriere..." : "API Key regenerieren"}
              </Button>
              <p className="text-xs text-gray-500 mt-2">
                ⚠️ Das Regenerieren macht den aktuellen API Key ungültig
              </p>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            <p className="text-gray-600">
              Sie haben noch keinen API Key. Generieren Sie einen, um auf die API zugreifen zu können.
            </p>
            <Button
              type="button"
              onClick={generateApiKey}
              disabled={isLoading}
              className="px-4 py-2 bg-green-500 hover:bg-green-600"
            >
              <AiOutlineKey className="mr-2" size="1rem" />
              {isLoading ? "Generiere..." : "API Key generieren"}
            </Button>
          </div>
        )}
      </div>
    </div>
  );
};
