"use client";

import React, { useState, useTransition } from "react";
import { useRouter } from "next/navigation";
import Table from "~/utils/table/table";
import { GridOptions, ICellRendererParams } from "ag-grid-community";
import { ColDef } from "ag-grid-community/dist/lib/entities/colDef";
import Button from "~/component/button";
import { AiOutlineKey, AiOutlineDelete, AiOutlineCopy } from "react-icons/ai";
import { GiPerspectiveDiceSixFacesRandom } from "react-icons/gi";

interface Contact {
  id: string;
  name: string | null;
  companyName: string | null;
  apiKey: string | null;
  cpo: boolean;
  ou: {
    id: string;
    name: string;
    code: string;
  } | null;
}

interface ApiKeyTableProps {
  contacts: Contact[];
}

export const ApiKeyTable: React.FC<ApiKeyTableProps> = ({ contacts }) => {
  const router = useRouter();
  const [isPending, startTransition] = useTransition();
  const [loadingStates, setLoadingStates] = useState<Record<string, boolean>>({});

  // Nur CPO-Contacts anzeigen
  const cpoContacts = contacts.filter(contact => contact.cpo);

  const setLoading = (contactId: string, loading: boolean) => {
    setLoadingStates((prev) => ({ ...prev, [contactId]: loading }));
  };

  const generateApiKey = async (contactId: string) => {
    setLoading(contactId, true);
    try {
      const response = await fetch("/api/contact/apikey", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ contactId }),
      });

      if (response.ok) {
        startTransition(() => {
          router.refresh();
        });
      } else {
        const error = await response.json();
        alert(`Fehler beim Generieren des API Keys: ${error.error}`);
      }
    } catch (error) {
      alert("Fehler beim Generieren des API Keys");
      console.error("Error generating API key:", error);
    } finally {
      setLoading(contactId, false);
    }
  };

  const deleteApiKey = async (contactId: string) => {
    if (!confirm("Sind Sie sicher, dass Sie den API Key löschen möchten?")) {
      return;
    }

    setLoading(contactId, true);
    try {
      const response = await fetch("/api/contact/apikey", {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ contactId }),
      });

      if (response.ok) {
        startTransition(() => {
          router.refresh();
        });
      } else {
        const error = await response.json();
        alert(`Fehler beim Löschen des API Keys: ${error.error}`);
      }
    } catch (error) {
      alert("Fehler beim Löschen des API Keys");
      console.error("Error deleting API key:", error);
    } finally {
      setLoading(contactId, false);
    }
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      alert("API Key in die Zwischenablage kopiert!");
    } catch (error) {
      console.error("Error copying to clipboard:", error);
      alert("Fehler beim Kopieren in die Zwischenablage");
    }
  };

  const ActionsCellRenderer = (params: ICellRendererParams) => {
    const contact = params.data as Contact;
    const isLoading = loadingStates[contact.id] || false;

    return (
      <div className="flex items-center gap-2">
        {contact.apiKey ? (
          <>
            <Button
              type="button"
              onClick={() => copyToClipboard(contact.apiKey!)}
              className="bg-blue-500 px-2 py-1 text-xs hover:bg-blue-600"
              disabled={isLoading}
            >
              <AiOutlineCopy className="mr-1" size="0.8rem" />
              Kopieren
            </Button>
            <Button
              type="button"
              onClick={() => deleteApiKey(contact.id)}
              className="bg-red-500 px-2 py-1 text-xs hover:bg-red-600"
              disabled={isLoading}
            >
              <AiOutlineDelete className="mr-1" size="0.8rem" />
              {isLoading ? "..." : "Löschen"}
            </Button>
          </>
        ) : (
          <Button
            onClick={() => generateApiKey(contact.id)}
            className="mt-1 bg-green-500 px-2 py-1 text-xs hover:bg-green-600"
            disabled={isLoading}
          >
            <AiOutlineKey className="mr-1" size="0.8rem" />
            {isLoading ? "Generiere..." : "Generieren"}
          </Button>
        )}
      </div>
    );
  };

  const ApiKeyRenderer = (params: ICellRendererParams) => {
    const apiKey = params.value as string | null;

    if (!apiKey) {
      return <span className="italic text-gray-400">Kein API Key</span>;
    }

    // Show only first 8 and last 4 characters for security
    const maskedKey = `${apiKey.substring(0, 8)}...${apiKey.substring(apiKey.length - 4)}`;

    return <span className="font-mono rounded bg-gray-100 px-2 py-1 text-sm">{maskedKey}</span>;
  };

  const StatusRenderer = (params: ICellRendererParams) => {
    const hasApiKey = !!params.data.apiKey;

    return (
      <span
        className={`rounded px-2 py-1 text-xs font-semibold ${
          hasApiKey ? "bg-green-100 text-green-800" : "bg-gray-100 text-gray-800"
        }`}
      >
        {hasApiKey ? "Aktiv" : "Inaktiv"}
      </span>
    );
  };

  const columnDefs: ColDef[] = [
    {
      field: "name",
      headerName: "Name",
      pinned: "left",
      width: 200,
    },
    {
      field: "companyName",
      headerName: "Firma",
      width: 250,
    },
    {
      field: "ou.name",
      headerName: "OU",
      width: 150,
      valueGetter: (params) => params.data.ou?.name || "Keine OU",
    },
    {
      field: "apiKey",
      headerName: "API Key",
      width: 200,
      cellRenderer: ApiKeyRenderer,
    },
    {
      field: "status",
      headerName: "Status",
      width: 100,
      cellRenderer: StatusRenderer,
    },
    {
      field: "actions",
      headerName: "Aktionen",
      pinned: "right",
      width: 200,
      cellRenderer: ActionsCellRenderer,
      sortable: false,
      filter: false,
    },
  ];

  const gridOptions: GridOptions = {
    rowModelType: "clientSide",
    defaultColDef: {
      sortable: true,
      filter: true,
      resizable: true,
      filterParams: {
        buttons: ["reset", "apply"],
      },
    },
    rowHeight: 50,
  };

  return (
    <div className="w-full">
      <div className="mb-4 flex items-center justify-between">
        <div className="text-sm text-gray-600">
          {cpoContacts.length} CPO-Contact{cpoContacts.length !== 1 ? 's' : ''} gefunden
        </div>
      </div>
      <div className="h-[600px] w-full">
        <Table rowData={cpoContacts} columnDefs={columnDefs} gridOptions={gridOptions} />
      </div>
    </div>
  );
};
