"use client";

import React, { useState, useEffect } from "react";
import { useForm, SubmitHandler } from "react-hook-form";
import { useRouter } from "next/navigation";
import Button from "~/component/button";
import Card from "~/component/card";
import { FiUpload, FiFile, FiTrash2, FiDownload } from "react-icons/fi";
import { ContactWithIncludes } from "../page";

interface EnergyResellerFormData {
  file: FileList;
  validFrom: string;
  validTo: string;
}

interface EnergyResellerDocument {
  id: string;
  start: string;
  end: string;
  valid: boolean;
  fileRef: {
    id: string;
    name: string;
    contentType: string;
  };
}

interface Props {
  contact: ContactWithIncludes;
}

const EnergyResellerUpload = ({ contact }: Props) => {
  const router = useRouter();
  const [isUploading, setIsUploading] = useState(false);
  const [documents, setDocuments] = useState<EnergyResellerDocument[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<EnergyResellerFormData>();

  // Fetch existing documents
  useEffect(() => {
    fetchDocuments();
  }, [contact.id]);

  const fetchDocuments = async () => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/contact/energyReseller?contactId=${contact.id}`);
      if (response.ok) {
        const data = await response.json();
        setDocuments(data);
      } else {
        setError("Fehler beim Laden der Dokumente");
      }
    } catch (err) {
      setError("Fehler beim Laden der Dokumente");
    } finally {
      setIsLoading(false);
    }
  };

  const onSubmit: SubmitHandler<EnergyResellerFormData> = async (data) => {
    if (!data.file || data.file.length === 0) {
      setError("Bitte wählen Sie eine Datei aus");
      return;
    }

    setIsUploading(true);
    setError(null);

    try {
      const formData = new FormData();
      formData.append("file", data.file[0]);
      formData.append("contactId", contact.id);
      formData.append("validFrom", data.validFrom);
      formData.append("validTo", data.validTo);

      const response = await fetch("/api/contact/energyReseller", {
        method: "POST",
        body: formData,
      });

      if (response.ok) {
        reset();
        await fetchDocuments();
        router.refresh();
      } else {
        const errorData = await response.json();
        setError(errorData.error || "Fehler beim Hochladen der Datei");
      }
    } catch (err) {
      setError("Fehler beim Hochladen der Datei");
    } finally {
      setIsUploading(false);
    }
  };

  const handleDelete = async (documentId: string) => {
    if (!confirm("Möchten Sie dieses Dokument wirklich löschen?")) {
      return;
    }

    try {
      const response = await fetch("/api/contact/energyReseller", {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ energyResellerId: documentId }),
      });

      if (response.ok) {
        await fetchDocuments();
        router.refresh();
      } else {
        setError("Fehler beim Löschen des Dokuments");
      }
    } catch (err) {
      setError("Fehler beim Löschen des Dokuments");
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("de-DE");
  };

  const isDocumentValid = (doc: EnergyResellerDocument) => {
    const now = new Date();
    const endDate = new Date(doc.end);
    return doc.valid && endDate >= now;
  };

  return (
    <Card header_left="Wiederverkäufernachweis">
      <div className="space-y-6">
        {/* Upload Form */}
        <div className="rounded-lg border border-gray-200 p-4">
          <h3 className="mb-4 text-lg font-medium text-gray-900 dark:text-white">
            Neuen Wiederverkäufernachweis hochladen
          </h3>

          {error && (
            <div className="mb-4 rounded-md bg-red-50 p-4 text-sm text-red-700">{error}</div>
          )}

          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <div>
                <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Gültig von *
                </label>
                <input
                  {...register("validFrom", { required: "Gültigkeitsbeginn ist erforderlich" })}
                  type="date"
                  className="w-full rounded-lg border border-gray-300 px-3 py-2 text-gray-900 focus:shadow-soft-primary-outline focus:outline-none dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                />
                {errors.validFrom && (
                  <p className="mt-1 text-sm text-red-600">{errors.validFrom.message}</p>
                )}
              </div>

              <div>
                <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Gültig bis *
                </label>
                <input
                  {...register("validTo", { required: "Gültigkeitsende ist erforderlich" })}
                  type="date"
                  className="w-full rounded-lg border border-gray-300 px-3 py-2 text-gray-900 focus:shadow-soft-primary-outline focus:outline-none dark:border-gray-600 dark:bg-gray-700 dark:text-white"
                />
                {errors.validTo && (
                  <p className="mt-1 text-sm text-red-600">{errors.validTo.message}</p>
                )}
              </div>
            </div>

            <div>
              <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
                PDF-Datei *
              </label>
              <input
                {...register("file", { required: "Datei ist erforderlich" })}
                type="file"
                accept=".pdf"
                className="w-full rounded-lg border border-gray-300 px-3 py-2 text-gray-900 focus:shadow-soft-primary-outline focus:outline-none dark:border-gray-600 dark:bg-gray-700 dark:text-white"
              />
              {errors.file && <p className="mt-1 text-sm text-red-600">{errors.file.message}</p>}
              <p className="mt-1 text-sm text-gray-500">Nur PDF-Dateien bis 10MB sind erlaubt</p>
            </div>

            <Button type="submit" disabled={isUploading} className="flex items-center gap-2">
              <FiUpload className="h-4 w-4" />
              {isUploading ? "Wird hochgeladen..." : "Hochladen"}
            </Button>
          </form>
        </div>

        {/* Existing Documents */}
        <div className="rounded-lg border border-gray-200 p-4">
          <h3 className="mb-4 text-lg font-medium text-gray-900 dark:text-white">
            Vorhandene Dokumente
          </h3>

          {isLoading ? (
            <p className="text-gray-500">Lade Dokumente...</p>
          ) : documents.length === 0 ? (
            <p className="text-gray-500">Keine Dokumente vorhanden</p>
          ) : (
            <div className="space-y-3">
              {documents.map((doc) => (
                <div
                  key={doc.id}
                  className={`flex items-center justify-between rounded-lg border p-3 ${
                    isDocumentValid(doc)
                      ? "border-green-200 bg-green-50"
                      : "border-red-200 bg-red-50"
                  }`}
                >
                  <div className="flex items-center gap-3">
                    <FiFile className="h-5 w-5 text-gray-500" />
                    <div>
                      <p className="font-medium text-gray-900">{doc?.fileRef?.name}</p>
                      <p className="text-sm text-gray-500">
                        Gültig: {formatDate(doc.start)} - {formatDate(doc.end)}
                      </p>
                      <span
                        className={`inline-flex rounded-full px-2 py-1 text-xs font-medium ${
                          isDocumentValid(doc)
                            ? "bg-green-100 text-green-800"
                            : "bg-red-100 text-red-800"
                        }`}
                      >
                        {isDocumentValid(doc) ? "Gültig" : "Abgelaufen"}
                      </span>
                    </div>
                  </div>

                  <div className="flex items-center gap-2">
                    <a
                      href={`/api/download/${doc?.fileRef?.id}`}
                      className="rounded-md bg-blue-100 p-2 text-blue-600 hover:bg-blue-200"
                      title="Herunterladen"
                    >
                      <FiDownload className="h-4 w-4" />
                    </a>
                    <button
                      onClick={() => handleDelete(doc.id)}
                      className="rounded-md bg-red-100 p-2 text-red-600 hover:bg-red-200"
                      title="Löschen"
                    >
                      <FiTrash2 className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </Card>
  );
};

export default EnergyResellerUpload;
