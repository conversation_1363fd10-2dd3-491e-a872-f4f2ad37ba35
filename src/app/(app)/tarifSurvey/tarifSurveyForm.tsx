// tarifSurveyForm.tsx (Client)
"use client";

import React from "react";
import { useForm, type SubmitHandler } from "react-hook-form";
import { useRouter } from "next/navigation";
import Button from "~/component/button";
import {FormValuesTarifSurvey, ExistingTarif} from "~/types/tarifSurvey/tarifSurveyTypes";



const DEFAULTS: Partial<FormValuesTarifSurvey> = {
  sessionFee: 0.0,
  kwh: 0.0,
  blockingFee: 0.0,
  blockingFeeBeginAtMin: 0,
  blockingFeeMax: 0.0,
  currentType: "AC",
};

function buildDefaults(
  type: "AC" | "DC",
  existing?: { AC: ExistingTarif | null; DC: ExistingTarif | null }
): FormValuesTarifSurvey {
  const e = type === "DC" ? existing?.DC : existing?.AC;
  return {
    currentType: type,
    kwh: (e?.kwh ?? DEFAULTS.kwh) as number,
    sessionFee: e?.sessionFee ?? DEFAULTS.sessionFee,
    blockingFee: e?.blockingFee ?? DEFAULTS.blockingFee,
    blockingFeeBeginAtMin: e?.blockingFeeBeginAtMin ?? DEFAULTS.blockingFeeBeginAtMin,
    blockingFeeMax: e?.blockingFeeMax ?? DEFAULTS.blockingFeeMax,
    description: e?.description ?? "",
  };
}

export default function TarifVorschlagForm({
                                             name,
                                             existing,
                                           }: {
  name?: string;
  existing?: { AC: ExistingTarif | null; DC: ExistingTarif | null };
}) {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = React.useState(false);
  const [submitMessage, setSubmitMessage] = React.useState<string | null>(null);
  const [submitStatus, setSubmitStatus] = React.useState<"idle" | "success" | "error">("idle");

  const initial = React.useMemo(() => buildDefaults("AC", existing), [existing]);

  const {
    register,
    handleSubmit,
    reset,
    watch,
    formState: { errors },
  } = useForm<FormValuesTarifSurvey>({ defaultValues: initial });

  const typeVal = (watch("currentType") ?? "AC") as "AC" | "DC";
  React.useEffect(() => {
    reset(buildDefaults(typeVal, existing), { keepDirty: false, keepTouched: false });
  }, [typeVal, existing, reset]);

  const onSubmit: SubmitHandler<FormValuesTarifSurvey> = async (data) => {
    setIsSubmitting(true);
    setSubmitMessage(null);
    setSubmitStatus("idle");
    try {
      const res = await fetch("/api/tarif/tarifSurvey", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ name: name ?? "", ...data }), // CPO-Name bleibt mit drin
      });
      if (!res.ok) {
        const msg = await res.text().catch(() => "Fehler beim Speichern!");
        throw new Error(msg);
      }
      setSubmitStatus("success");
      setSubmitMessage("Erfolgreich gespeichert ✅");
      router.refresh();
    } catch (e) {
      setSubmitStatus("error");
      setSubmitMessage((e as Error).message || "Fehler beim Speichern oder Netzwerkfehler!");
    } finally {
      setIsSubmitting(false);
    }
  };

  const kwhVal = watch("kwh");
  const canSubmit = !isSubmitting && Number.isFinite(kwhVal as number);

  return (
    <div className="p-6">
      <form onSubmit={handleSubmit(onSubmit)}>
        {/* Hidden CPO-Name (optional, rein für Debug/HTML-Form-Kompatibilität) */}
        <input type="hidden" name="cpoName" value={name ?? ""} readOnly />

        {/* Eingaben */}
        <div className="flex justify-end mb-4">
        <Button
          type="submit"
          disabled={!canSubmit}
          className={!canSubmit ? "opacity-50 cursor-not-allowed" : ""}
          onClick={() => router.refresh()}
        >
          {isSubmitting ? "Speichert..." : "Speichern"}
        </Button>
          </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="flex flex-col">
            <label className="mb-2 text-sm font-bold text-slate-700 dark:text-white/80">Preis pro kWh (€)</label>
            <input
              type="number"
              step={0.01}
              {...register("kwh", { required: true, valueAsNumber: true })}
              className="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary bg-white dark:bg-slate-800"
            />
            {errors.kwh && <span className="text-red-600 text-sm mt-1">Pflichtfeld</span>}
          </div>

          <div className="flex flex-col">
            <label className="mb-2 text-sm font-bold text-slate-700 dark:text-white/80">Session Fee (€)</label>
            <input
              type="number"
              step={0.01}
              {...register("sessionFee", { valueAsNumber: true })}
              className="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary bg-white dark:bg-slate-800"
            />
          </div>

          <div className="flex flex-col">
            <label className="mb-2 text-sm font-bold text-slate-700 dark:text-white/80">Blockiergebühr (€/min)</label>
            <input
              type="number"
              step={0.01}
              {...register("blockingFee", { valueAsNumber: true })}
              className="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary bg-white dark:bg-slate-800"
            />
          </div>

          <div className="flex flex-col">
            <label className="mb-2 text-sm font-bold text-slate-700 dark:text-white/80">Blockiergebühr ab (min)</label>
            <input
              type="number"
              step={1}
              {...register("blockingFeeBeginAtMin", { valueAsNumber: true })}
              className="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary bg-white dark:bg-slate-800"
            />
          </div>

          <div className="flex flex-col">
            <label className="mb-2 text-sm font-bold text-slate-700 dark:text-white/80">Max. Blockiergebühr (€)</label>
            <input
              type="number"
              step={0.01}
              {...register("blockingFeeMax", { valueAsNumber: true })}
              className="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary bg-white dark:bg-slate-800"
            />
          </div>

          <div className="mb-4">
            <label className="mb-2 text-sm font-bold text-slate-700 dark:text-white/80">Stromtyp</label>
            <select
              {...register("currentType")}
              className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary bg-white dark:bg-slate-800"
            >
              <option value="AC">AC</option>
              <option value="DC">DC</option>
            </select>
          </div>

          <div className="md:col-span-2 lg:col-span-3">
            <label className="mb-2 text-sm font-bold text-slate-700 dark:text-white/80">Beschreibung</label>
            <textarea
              rows={3}
              {...register("description")}
              className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary bg-white dark:bg-slate-800"
            />
          </div>
        </div>

        {submitMessage && (
          <div
            className={`mt-6 p-4 rounded-lg text-sm ${
              submitStatus === "success"
                ? "bg-green-100 text-green-700 border border-green-300"
                : "bg-red-100 text-red-700 border border-red-300"
            }`}
          >
            {submitMessage}
          </div>
        )}

        <div className="mt-8 flex justify-end gap-4">


        </div>
      </form>
    </div>
  );
}
