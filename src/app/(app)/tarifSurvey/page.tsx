// page.tsx (Server Component)
import React from "react";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import Card from "~/component/card";
import TarifVorschlagForm from "./tarifSurveyForm";
import prisma from "~/server/db/prisma";
import TarifCard from "~/app/(app)/emp/card/component/TarifCard";
import { Role } from "@prisma/client";

export default async function Page() {
  const session = await getServerSession(authOptions);
  const cpoName = session?.user?.selectedOu?.name ?? "";

  const names = [`${cpoName}_AC_survey`, `${cpoName}_DC_survey`];

  const existing = await prisma.tarif.findMany({
    where: { name: { in: names } },
    select: {
      name: true,
      currentType: true,
      kwh: true,
      sessionFee: true,
      blockingFee: true,
      blockingFeeBeginAtMin: true,
      blockingFeeMax: true,
      description: true,
    },
  });

  const existingByType = {
    AC: existing.find(t => t.name === `${cpoName}_AC_survey`) ?? null,
    DC: existing.find(t => t.name === `${cpoName}_DC_survey`) ?? null,
  };


  return (
    <Card header_left={"Tarif-Vorschlag"}>
      <TarifVorschlagForm name={cpoName} existing={existingByType} />
      {(existingByType.AC || existingByType.DC) && (
        <div className="mt-4 ml-6 grid grid-cols-[auto_auto] justify-start gap-6">
          {existingByType.AC && (
            <TarifCard
              title={"Vorschlag AC"}
              description={"Ihr AC Tarif Vorschlag" }
              pricekWh={existingByType.AC.kwh}
              priceSession={0}
              tarifName={"AC Tarif Vorschlag"}
              optional={false}
              interactive={false}
              tarifId={`survey-${existingByType.AC.name}`}
              currentType={"AC"}
              basicFee={0}
              oneTimeFee={0}
              oneTimeFeePayer={Role.CARD_MANAGER}
              vat={19}
              blockingFee={existingByType.AC.blockingFee }
              blockingFeeBeginAtMin={existingByType.AC.blockingFeeBeginAtMin }
              blockingFeeMax={existingByType.AC.blockingFeeMax }
              size={"normal"}
              internal={false}
            />
          )}


          {existingByType.DC && (
            <TarifCard
              title={`Vorschlag DC`}
              description={"Ihr DC Tarif Vorschlag" ?? ""}
              pricekWh={existingByType.DC.kwh}
              priceSession={0}
              tarifName={"DC Tarif Vorschlag"}
              optional={true}
              interactive={false}
              tarifId={`survey-${existingByType.DC.name}`}
              currentType={"DC"}
              basicFee={0}
              oneTimeFee={0}
              oneTimeFeePayer={Role.CARD_MANAGER}
              vat={19}
              blockingFee={existingByType.DC.blockingFee ?? 0}
              blockingFeeBeginAtMin={existingByType.DC.blockingFeeBeginAtMin ?? 0}
              blockingFeeMax={existingByType.DC.blockingFeeMax ?? 0}
              size={"normal"}
              internal={false}
            />
          )}
        </div>
      )}
    </Card>
  );
}
