import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role } from "@prisma/client";
import { notFound } from "next/navigation";
import Card from "~/component/card";
import ThgInviteForm from "./components/ThgInviteForm";
import Headline from "~/component/Headline";

const ThgInvitesPage = async () => {
  const session = await getServerSession(authOptions);

  if (!session?.user || session.user.role !== Role.ADMIN) {
    return notFound();
  }

  return (
    <Card>
      <Headline title="THG-Quotenkäufer einladen" />
      <div className="mb-6">
        <p className="text-gray-600">
          Hier können Sie Unternehmen einladen, die THG-Quoten kaufen möchten.
        </p>
      </div>
      <ThgInviteForm />
    </Card>
  );
};

export default ThgInvitesPage;
