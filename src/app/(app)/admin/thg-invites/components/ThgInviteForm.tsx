"use client";

import React, { useState } from "react";
import { useForm } from "react-hook-form";
import Button from "~/component/button";
import { <PERSON>Loader, FiCheckCircle, FiAlertCircle } from "react-icons/fi";

interface InviteFormData {
  firstName: string;
  lastName: string;
  email: string;
  companyName: string;
}

const ThgInviteForm = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);
  const [submitError, setSubmitError] = useState<string | null>(null);
  
  const {
    register,
    handleSubmit,
    reset,
    formState: { errors },
  } = useForm<InviteFormData>();

  const onSubmit = async (data: InviteFormData) => {
    setIsSubmitting(true);
    setSubmitError(null);
    
    try {
      const response = await fetch("/api/user/invite-thg", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (response.ok) {
        setSubmitSuccess(true);
        reset();
        
        // Reset success message after 5 seconds
        setTimeout(() => setSubmitSuccess(false), 5000);
      } else {
        const errorData = await response.json();
        setSubmitError(errorData.error || "Fehler beim Senden der Einladung");
      }
    } catch (error) {
      console.error("Error sending invitation:", error);
      setSubmitError("Netzwerkfehler beim Senden der Einladung");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="max-w-2xl">
      {submitSuccess && (
        <div className="relative border-l-4 border-green-500 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg shadow-sm mb-6">
          <div className="flex items-center p-4">
            <FiCheckCircle className="w-5 h-5 mr-3 text-green-500" />
            <span className="text-sm font-medium text-green-800 dark:text-green-200">
              Einladung erfolgreich versendet! Der THG-Quotenkäufer erhält eine E-Mail mit dem Registrierungslink.
            </span>
          </div>
        </div>
      )}

      {submitError && (
        <div className="relative border-l-4 border-red-500 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg shadow-sm mb-6">
          <div className="flex items-center p-4">
            <FiAlertCircle className="w-5 h-5 mr-3 text-red-500" />
            <span className="text-sm font-medium text-red-800 dark:text-red-200">
              {submitError}
            </span>
          </div>
        </div>
      )}

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Vorname *
            </label>
            <input
              {...register("firstName", { required: "Vorname ist erforderlich" })}
              type="text"
              placeholder="Max"
              className="input"
            />
            {errors.firstName && (
              <p className="text-red-500 text-sm mt-1">{errors.firstName.message}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Nachname *
            </label>
            <input
              {...register("lastName", { required: "Nachname ist erforderlich" })}
              type="text"
              placeholder="Mustermann"
              className="input"
            />
            {errors.lastName && (
              <p className="text-red-500 text-sm mt-1">{errors.lastName.message}</p>
            )}
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            E-Mail Adresse *
          </label>
          <input
            {...register("email", {
              required: "E-Mail ist erforderlich",
              pattern: {
                value: /\S+@\S+\.\S+/,
                message: "Bitte geben Sie eine gültige E-Mail Adresse ein"
              }
            })}
            type="email"
            placeholder="<EMAIL>"
            className="input"
          />
          {errors.email && (
            <p className="text-red-500 text-sm mt-1">{errors.email.message}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Firmenname *
          </label>
          <input
            {...register("companyName", { required: "Firmenname ist erforderlich" })}
            type="text"
            placeholder="Mustermann Energie GmbH"
            className="input"
          />
          {errors.companyName && (
            <p className="text-red-500 text-sm mt-1">{errors.companyName.message}</p>
          )}
        </div>

        <div className="bg-blue-50 p-4 rounded-lg">
          <h4 className="font-medium text-blue-900 mb-2">Was passiert nach dem Absenden?</h4>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• Der Kontakt erhält eine E-Mail mit einem Registrierungslink</li>
            <li>• Nach der Registrierung kann er sich über die THG-Login-Seite anmelden</li>
            <li>• Er erhält Zugang zum THG-Dashboard und kann Angebote abgeben</li>
          </ul>
        </div>

        <div className="flex justify-end">
          <Button
            type="submit"
            disabled={isSubmitting}
            className="px-6 py-2"
          >
            Einladung senden
            {isSubmitting && <FiLoader className="ml-2 animate-spin" />}
          </Button>
        </div>
      </form>
    </div>
  );
};

export default ThgInviteForm;
