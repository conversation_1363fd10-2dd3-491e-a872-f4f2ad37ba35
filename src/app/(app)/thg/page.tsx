import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role } from "@prisma/client";
import { notFound } from "next/navigation";
import Card from "~/component/card";
import Headline from "~/component/Headline";

const ThgDashboard = async () => {
  const session = await getServerSession(authOptions);

  if (!session?.user || session.user.role !== Role.THG_BUYER) {
    return notFound();
  }

  return (
    <div className="space-y-6">
      <Card>
        <Headline title="THG-Quoten Dashboard" />
        <div className="mb-4">
          <p className="text-gray-600">Willkommen im THG-Quoten Portal, {session.user.name}!</p>
          <p className="text-sm text-gray-500">Firma: {session.user.companyName}</p>
        </div>
      </Card>

      {/* Portal-Erläuterung */}
      <Card>
        <Headline title="Wie funktioniert das THG-Quoten Portal?" />
        <div className="space-y-4">
          <div className="rounded-lg border border-blue-200 bg-blue-50 p-4 dark:border-blue-800 dark:bg-blue-900/20">
            <div className="flex items-start">
              <div className="mr-3 mt-1 flex h-8 w-8 items-center justify-center rounded-lg bg-blue-100 dark:bg-blue-900/20">
                <span className="text-lg text-blue-600 dark:text-blue-400">ℹ️</span>
              </div>
              <div className="flex-1">
                <h3 className="mb-2 font-medium text-blue-900 dark:text-blue-200">
                  Portal-Funktionsweise
                </h3>
                <div className="space-y-2 text-sm text-blue-800 dark:text-blue-300">
                  <p>
                    Als <strong>THG-Quotenkäufer (Angebotsersteller)</strong> können Sie hier
                    unverbindliche Angebote für THG-Quoten unterbreiten, die Eulektro begutachten
                    und bei Interesse entsprechend reagieren kann.
                  </p>
                  <p>
                    Bei Rückfragen wird der von Ihnen angegebene Ansprechpartner direkt kontaktiert.
                    Alle Angebote werden als <strong>nicht verbindlich</strong> betrachtet und
                    dienen lediglich zur einfacheren Kommunikation zwischen Eulektro und
                    THG-Quotenkäufern.
                  </p>
                  <p>
                    Aufgrund der hohen Anzahl an Anfragen hat Eulektro dieses Portal geschaffen,
                    damit die Kommunikation schneller und effizienter ablaufen kann.
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div className="rounded-lg border border-green-200 bg-green-50 p-4 dark:border-green-800 dark:bg-green-900/20">
            <div className="flex items-start">
              <div className="mr-3 mt-1 flex h-8 w-8 items-center justify-center rounded-lg bg-green-100 dark:bg-green-900/20">
                <span className="text-lg text-green-600 dark:text-green-400">📋</span>
              </div>
              <div className="flex-1">
                <h3 className="mb-2 font-medium text-green-900 dark:text-green-200">
                  Angebotsverwaltung
                </h3>
                <div className="space-y-2 text-sm text-green-800 dark:text-green-300">
                  <p>
                    Unter <strong>"Meine Angebote"</strong> finden Sie alle von Ihnen abgegebenen
                    Angebote. Diese können Sie jederzeit zurückziehen oder sie laufen automatisch
                    nach dem angegebenen Gültigkeitsdatum ab.
                  </p>
                  <p>
                    Sie haben die volle Kontrolle über Ihre Angebote und können diese nach Bedarf
                    verwalten und aktualisieren.
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div className="rounded-lg border border-yellow-200 bg-yellow-50 p-4 dark:border-yellow-800 dark:bg-yellow-900/20">
            <div className="flex items-start">
              <div className="mr-3 mt-1 flex h-8 w-8 items-center justify-center rounded-lg bg-yellow-100 dark:bg-yellow-900/20">
                <span className="text-lg text-yellow-600 dark:text-yellow-400">⚠️</span>
              </div>
              <div className="flex-1">
                <h3 className="mb-2 font-medium text-yellow-900 dark:text-yellow-200">
                  Wichtiger Hinweis
                </h3>
                <p className="text-sm text-yellow-800 dark:text-yellow-300">
                  Alle über dieses Portal abgegebenen Angebote sind <strong>unverbindlich</strong>{" "}
                  und stellen keine rechtlich bindenden Verträge dar. Sie dienen ausschließlich der
                  ersten Kontaktaufnahme und Interessensbekundung.
                </p>
              </div>
            </div>
          </div>
        </div>
      </Card>


    </div>
  );
};

export default ThgDashboard;
