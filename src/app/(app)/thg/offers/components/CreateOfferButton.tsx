"use client";

import React, { useState } from "react";
import <PERSON>ton from "~/component/button";
import { FiPlus } from "react-icons/fi";
import CreateOfferModal from "./CreateOfferModal";

const CreateOfferButton = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);

  return (
    <>
      <Button
        onClick={() => setIsModalOpen(true)}
        className="bg-primary text-white"
      >
        <FiPlus className="w-4 h-4 mr-2" />
        Neues Angebot
      </Button>

      <CreateOfferModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
      />
    </>
  );
};

export default CreateOfferButton;
