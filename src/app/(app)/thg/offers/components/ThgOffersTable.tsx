"use client";

import React, { useEffect, useState } from "react";
import { <PERSON><PERSON><PERSON>, FiXCircle, FiPower } from "react-icons/fi";
import Button from "~/component/button";

interface ThgOffer {
  id: string;
  status: "ACTIVE" | "EXPIRED";
  title?: string;
  pricePerKwh: number;
  priceUnit: "CENT_KWH" | "EURO_MWH";
  quantityMin: number;
  quantityMax: number;
  validUntil: string;
  createdAt: string;
  contactPerson?: string;
}

interface ThgOffersTableProps {
  userId: string;
}

const ThgOffersTable = ({ userId }: ThgOffersTableProps) => {
  const [offers, setOffers] = useState<ThgOffer[]>([]);
  const [loading, setLoading] = useState(true);

  const handleDeactivateOffer = async (offerId: string) => {
    if (confirm("Möchten Sie dieses Angebot wirklich deaktivieren?")) {
      try {
        const response = await fetch(`/api/thg/offers/${offerId}/deactivate`, {
          method: 'POST'
        });

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.error || "Fehler beim Deaktivieren");
        }

        // Update local state
        setOffers((prevOffers) =>
          prevOffers.map((offer) =>
            offer.id === offerId ? { ...offer, status: "EXPIRED" as const } : offer,
          ),
        );

        console.log("Offer deactivated:", offerId);
      } catch (error) {
        console.error("Error deactivating offer:", error);
        alert(error instanceof Error ? error.message : "Fehler beim Deaktivieren des Angebots");
      }
    }
  };

  useEffect(() => {
    const fetchOffers = async () => {
      try {
        const response = await fetch("/api/thg/offers");
        if (response.ok) {
          const data = await response.json();
          // Transform API data to match component interface
          const transformedOffers = data.map((offer: any) => ({
            id: offer.id,
            status: offer.status,
            title: offer.title,
            pricePerKwh: parseFloat(offer.pricePerKwh),
            priceUnit: offer.priceUnit || "CENT_KWH",
            quantityMin: parseInt(offer.quantityMin) || parseInt(offer.quantity) || 0,
            quantityMax: parseInt(offer.quantityMax) || parseInt(offer.quantity) || 0,
            validUntil: offer.validUntil,
            createdAt: offer.createdAt,
            contactPerson: offer.contactPerson,
          }));
          setOffers(transformedOffers);
        } else {
          console.error("Failed to fetch offers");
        }
      } catch (error) {
        console.error("Error fetching offers:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchOffers();
  }, [userId]);

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      ACTIVE: { color: "green", icon: FiClock, text: "Aktiv" },
      EXPIRED: { color: "red", icon: FiXCircle, text: "Abgelaufen" },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.ACTIVE;
    const Icon = config.icon;

    const colorClasses = {
      green: "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-200",
      red: "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-200",
    };

    return (
      <span
        className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${
          colorClasses[config.color]
        }`}
      >
        <Icon className="mr-1 h-3 w-3" />
        {config.text}
      </span>
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("de-DE");
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("de-DE", {
      style: "currency",
      currency: "EUR",
    }).format(amount);
  };

  if (loading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <div key={i} className="animate-pulse rounded-lg bg-gray-100 p-4 dark:bg-gray-800">
            <div className="mb-2 h-4 rounded bg-gray-300 dark:bg-gray-600"></div>
            <div className="h-4 w-3/4 rounded bg-gray-300 dark:bg-gray-600"></div>
          </div>
        ))}
      </div>
    );
  }

  if (offers.length === 0) {
    return (
      <div className="py-12 text-center">
        <div className="mb-4 text-gray-400">
          <FiClock className="mx-auto h-12 w-12" />
        </div>
        <h3 className="mb-2 text-lg font-medium text-gray-900 dark:text-white">
          Noch keine Angebote
        </h3>
        <p className="text-gray-500 dark:text-gray-400">
          Erstellen Sie Ihr erstes THG-Angebot, um loszulegen.
        </p>
      </div>
    );
  }

  return (
    <div className="overflow-x-auto">
      <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
        <thead className="bg-gray-50 dark:bg-gray-800">
          <tr>
            <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
              Angebot
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
              Preis
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
              Menge
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
              Ansprechpartner
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
              Status
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
              Gültig bis
            </th>
            <th className="px-6 py-3 text-right text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
              Aktionen
            </th>
          </tr>
        </thead>
        <tbody className="divide-y divide-gray-200 bg-white dark:divide-gray-700 dark:bg-gray-900">
          {offers.map((offer) => (
            <tr key={offer.id} className="hover:bg-gray-50 dark:hover:bg-gray-800">
              <td className="whitespace-nowrap px-6 py-4">
                <div>
                  <div className="text-sm font-medium text-gray-900 dark:text-white">
                    {offer.title || `Angebot #${offer.id}`}
                  </div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    Erstellt: {formatDate(offer.createdAt)}
                  </div>
                </div>
              </td>

              <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-900 dark:text-white">
                {offer.pricePerKwh.toFixed(2)} {offer.priceUnit === "CENT_KWH" ? "ct/kWh" : "€/MWh"}
              </td>
              <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-900 dark:text-white">
                {offer.quantityMin === offer.quantityMax
                  ? `${offer.quantityMin.toLocaleString("de-DE")} kWh`
                  : `${offer.quantityMin.toLocaleString(
                      "de-DE",
                    )} - ${offer.quantityMax.toLocaleString("de-DE")} kWh`}
              </td>
              <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-900 dark:text-white">
                {offer.contactPerson || "-"}
              </td>
              <td className="whitespace-nowrap px-6 py-4">{getStatusBadge(offer.status)}</td>
              <td className="whitespace-nowrap px-6 py-4 text-sm text-gray-900 dark:text-white">
                {formatDate(offer.validUntil)}
              </td>

              <td className="whitespace-nowrap px-6 py-4 text-right text-sm font-medium">
                <div className="flex justify-end space-x-2">
                  {offer.status === "ACTIVE" && (
                    <Button
                      onClick={() => handleDeactivateOffer(offer.id)}
                      className="bg-red-500 text-white"
                      small
                      title="Angebot deaktivieren"
                    >
                      <FiPower className="h-4 w-4" />
                    </Button>
                  )}
                  {offer.status === "EXPIRED" && (
                    <span className="text-sm text-gray-400">Abgelaufen</span>
                  )}
                </div>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default ThgOffersTable;
