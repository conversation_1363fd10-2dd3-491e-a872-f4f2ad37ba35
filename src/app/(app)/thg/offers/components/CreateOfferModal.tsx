"use client";

import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import But<PERSON> from "~/component/button";
import { FiX, <PERSON>Loader, FiCheckCircle } from "react-icons/fi";
import { useSession } from "next-auth/react";

interface CreateOfferModalProps {
  isOpen: boolean;
  onClose: () => void;
}

interface OfferFormData {
  pricePerKwh: number;
  priceUnit: "CENT_KWH" | "EURO_MWH";
  quantityMin: number;
  quantityMax: number;
  validUntil: string;
  title?: string;
  description?: string;
  contactPerson?: string;
  paymentTerms?: string;
  deliveryTerms?: string;
}

const CreateOfferModal = ({ isOpen, onClose }: CreateOfferModalProps) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);
  const { data: session } = useSession();

  const {
    register,
    handleSubmit,
    reset,
    watch,
    setValue,
    formState: { errors },
  } = useForm<OfferFormData>({
    defaultValues: {
      priceUnit: "CENT_KWH",
      contactPerson: session?.user?.name
        ? `${session.user.name} ${session.user.lastName || ""}`.trim()
        : "",
    },
  });

  // Update contact person when session changes
  useEffect(() => {
    if (session?.user?.name) {
      setValue("contactPerson", `${session.user.name} ${session.user.lastName || ""}`.trim());
    }
  }, [session, setValue]);

  const onSubmit = async (data: OfferFormData) => {
    setIsSubmitting(true);

    try {
      const response = await fetch("/api/thg/offers", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        const errorData = await response.json();
        const errorMessage = typeof errorData.error === 'string'
          ? errorData.error
          : "Fehler beim Erstellen des Angebots";
        throw new Error(errorMessage);
      }

      setSubmitSuccess(true);
      reset();

      // Close modal after success
      setTimeout(() => {
        setSubmitSuccess(false);
        onClose();
        // Reload page to show new offer
        window.location.reload();
      }, 2000);
    } catch (error) {
      console.error("Error creating offer:", error);
      // Sicherstellen, dass nur String-Nachrichten angezeigt werden
      let errorMessage = "Fehler beim Erstellen des Angebots";
      if (error instanceof Error) {
        errorMessage = error.message;
      } else if (typeof error === 'string') {
        errorMessage = error;
      }
      alert(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      reset();
      setSubmitSuccess(false);
      onClose();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed left-0 top-0 z-50 flex h-full w-full items-center justify-center bg-opacity-50 backdrop-blur-sm">
      <div className="max-h-[90vh] w-full max-w-2xl overflow-y-auto rounded-2xl bg-white shadow-xl dark:bg-gray-950">
        {/* Header */}
        <div className="flex items-center justify-between border-b border-gray-200 p-6 dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            Neues Angebot erstellen
          </h2>
          <button
            onClick={handleClose}
            disabled={isSubmitting}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <FiX className="h-6 w-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {submitSuccess ? (
            <div className="py-8 text-center">
              <FiCheckCircle className="mx-auto mb-4 h-16 w-16 text-green-500" />
              <h3 className="mb-2 text-lg font-semibold text-green-800 dark:text-green-200">
                Angebot erfolgreich erstellt!
              </h3>
              <p className="text-green-600 dark:text-green-400">
                Ihr Angebot ist jetzt aktiv und für Eulektro sichtbar
              </p>
            </div>
          ) : (
            <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
              {/* Info Header */}
              <div className="rounded-lg border border-purple-200 bg-purple-50 p-4 dark:border-purple-800 dark:bg-purple-900/20">
                <div className="flex items-center">
                  <div className="mr-3 flex h-8 w-8 items-center justify-center rounded-lg bg-purple-100 dark:bg-purple-900/20">
                    <svg
                      className="h-4 w-4 text-purple-600 dark:text-purple-400"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"
                      />
                    </svg>
                  </div>
                  <div>
                    <h3 className="font-medium text-purple-900 dark:text-purple-200">
                      THG-Quoten Angebot
                    </h3>
                    <p className="text-sm text-purple-700 dark:text-purple-300">
                      Sie erstellen ein Angebot zum Kauf von THG-Quoten
                    </p>
                  </div>
                </div>
              </div>

              {/* Preis und Einheit */}
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <div>
                  <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Preis *
                  </label>
                  <input
                    {...register("pricePerKwh", {
                      required: "Preis ist erforderlich",
                      min: { value: 0.01, message: "Preis muss größer als 0 sein" },
                    })}
                    type="number"
                    step="0.01"
                    placeholder="0.00"
                    className="input"
                  />
                  {errors.pricePerKwh && (
                    <p className="mt-1 text-sm text-red-500">{errors.pricePerKwh.message}</p>
                  )}
                </div>

                <div>
                  <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Preiseinheit *
                  </label>
                  <select
                    {...register("priceUnit", {
                      required: "Preiseinheit ist erforderlich",
                    })}
                    className="input"
                  >
                    <option value="CENT_KWH">Cent pro kWh</option>
                    <option value="EURO_MWH">Euro pro MWh</option>
                  </select>
                  {errors.priceUnit && (
                    <p className="mt-1 text-sm text-red-500">{errors.priceUnit.message}</p>
                  )}
                </div>
              </div>

              {/* Mengenbereich */}
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <div>
                  <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Mindestmenge (kWh) *
                  </label>
                  <input
                    {...register("quantityMin", {
                      required: "Mindestmenge ist erforderlich",
                      min: { value: 1, message: "Mindestmenge muss mindestens 1 kWh sein" },
                    })}
                    type="number"
                    placeholder="5000"
                    className="input"
                  />
                  {errors.quantityMin && (
                    <p className="mt-1 text-sm text-red-500">{errors.quantityMin.message}</p>
                  )}
                </div>

                <div>
                  <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Höchstmenge (kWh) *
                  </label>
                  <input
                    {...register("quantityMax", {
                      required: "Höchstmenge ist erforderlich",
                      min: { value: 1, message: "Höchstmenge muss mindestens 1 kWh sein" },
                    })}
                    type="number"
                    placeholder="15000"
                    className="input"
                  />
                  {errors.quantityMax && (
                    <p className="mt-1 text-sm text-red-500">{errors.quantityMax.message}</p>
                  )}
                </div>
              </div>

              {/* Gültigkeit und Titel */}
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <div>
                  <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Gültig bis *
                  </label>
                  <input
                    {...register("validUntil", {
                      required: "Gültigkeitsdatum ist erforderlich",
                    })}
                    type="date"
                    min={new Date().toISOString().split("T")[0]}
                    className="input"
                  />
                  {errors.validUntil && (
                    <p className="mt-1 text-sm text-red-500">{errors.validUntil.message}</p>
                  )}
                </div>

                <div>
                  <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Titel (optional)
                  </label>
                  <input
                    {...register("title")}
                    type="text"
                    placeholder="z.B. Großeinkauf Q1 2024"
                    className="input"
                  />
                </div>
              </div>

              {/* Ansprechpartner */}
              <div>
                <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Ansprechpartner *
                </label>
                <input
                  {...register("contactPerson", {
                    required: "Ansprechpartner ist erforderlich",
                  })}
                  type="text"
                  placeholder="Max Mustermann"
                  className="input"
                />
                {errors.contactPerson && (
                  <p className="mt-1 text-sm text-red-500">{errors.contactPerson.message}</p>
                )}
                <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                  Diese Person wird bei Rückfragen kontaktiert
                </p>
              </div>

              {/* Beschreibung */}
              <div>
                <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
                  Beschreibung (optional)
                </label>
                <textarea
                  {...register("description")}
                  rows={3}
                  placeholder="Zusätzliche Informationen zu Ihrem Angebot..."
                  className="input"
                />
              </div>

              {/* Bedingungen */}
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                <div>
                  <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Zahlungsbedingungen
                  </label>
                  <input
                    {...register("paymentTerms")}
                    type="text"
                    placeholder="z.B. 30 Tage netto"
                    className="input"
                  />
                </div>

                <div>
                  <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Lieferbedingungen
                  </label>
                  <input
                    {...register("deliveryTerms")}
                    type="text"
                    placeholder="z.B. Sofortige Lieferung"
                    className="input"
                  />
                </div>
              </div>

              {/* Submit Button */}
              <div className="flex justify-end space-x-3 pt-4">
                <Button
                  type="button"
                  onClick={handleClose}
                  disabled={isSubmitting}
                  className="bg-gray-500 text-white"
                >
                  Abbrechen
                </Button>
                <Button type="submit" disabled={isSubmitting} className="bg-primary text-white">
                  {isSubmitting ? (
                    <>
                      <FiLoader className="mr-2 h-4 w-4 animate-spin" />
                      Erstelle Angebot...
                    </>
                  ) : (
                    "Angebot erstellen"
                  )}
                </Button>
              </div>
            </form>
          )}
        </div>
      </div>
    </div>
  );
};

export default CreateOfferModal;
