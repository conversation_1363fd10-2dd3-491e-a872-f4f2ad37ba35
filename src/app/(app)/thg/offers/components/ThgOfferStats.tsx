"use client";

import React, { useEffect, useState } from "react";
import { FiTrendingUp, FiDollarSign, FiClock, FiCheckCircle } from "react-icons/fi";

interface OfferStats {
  totalOffers: number;
  activeOffers: number;
  expiredOffers: number;
}

interface ThgOfferStatsProps {
  userId: string;
}

const ThgOfferStats = ({ userId }: ThgOfferStatsProps) => {
  const [stats, setStats] = useState<OfferStats>({
    totalOffers: 0,
    activeOffers: 0,
    expiredOffers: 0,
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        const response = await fetch("/api/thg/offers/stats");
        if (response.ok) {
          const data = await response.json();
          setStats(data);
        } else {
          console.error("Failed to fetch stats");
        }
      } catch (error) {
        console.error("Error fetching stats:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, [userId]);

  const statCards = [
    {
      title: "Gesamt",
      value: stats.totalOffers,
      icon: FiTrendingUp,
      color: "blue",
      suffix: "",
    },
    {
      title: "Aktiv",
      value: stats.activeOffers,
      icon: FiClock,
      color: "green",
      suffix: "",
    },
    {
      title: "Abgelaufen",
      value: stats.expiredOffers,
      icon: FiCheckCircle,
      color: "red",
      suffix: "",
    },
  ];

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {[...Array(3)].map((_, i) => (
          <div key={i} className="bg-gray-100 dark:bg-gray-800 rounded-lg p-4 animate-pulse">
            <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded mb-2"></div>
            <div className="h-8 bg-gray-300 dark:bg-gray-600 rounded"></div>
          </div>
        ))}
      </div>
    );
  }

  const formatValue = (value: number, format?: string, suffix?: string) => {
    if (format === "currency") {
      return new Intl.NumberFormat("de-DE", {
        style: "currency",
        currency: "EUR",
        minimumFractionDigits: 0,
        maximumFractionDigits: 0,
      }).format(value);
    }
    return `${value.toLocaleString("de-DE")}${suffix || ""}`;
  };

  const getColorClasses = (color: string) => {
    const colors = {
      blue: "bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800 text-blue-600 dark:text-blue-400",
      green: "bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800 text-green-600 dark:text-green-400",
      red: "bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800 text-red-600 dark:text-red-400",
    };
    return colors[color as keyof typeof colors] || colors.blue;
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
      {statCards.map((stat, index) => {
        const Icon = stat.icon;
        return (
          <div
            key={index}
            className={`rounded-lg border p-4 ${getColorClasses(stat.color)}`}
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium opacity-75">{stat.title}</p>
                <p className="text-2xl font-bold">
                  {formatValue(stat.value, stat.format, stat.suffix)}
                </p>
              </div>
              <Icon className="h-8 w-8 opacity-75" />
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default ThgOfferStats;
