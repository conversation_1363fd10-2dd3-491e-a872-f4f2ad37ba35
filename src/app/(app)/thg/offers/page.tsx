import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role } from "@prisma/client";
import { notFound } from "next/navigation";
import Card from "~/component/card";
import Headline from "~/component/Headline";
import ThgOffersTable from "./components/ThgOffersTable";
import ThgOfferStats from "./components/ThgOfferStats";
import CreateOfferButton from "./components/CreateOfferButton";

const ThgOffersPage = async () => {
  const session = await getServerSession(authOptions);

  if (!session?.user || session.user.role !== Role.THG_BUYER) {
    return notFound();
  }

  return (
    <div className="space-y-6">
      {/* Header mit Statistiken */}
      <Card>
        <div className="mb-6 flex items-center justify-between">
          <Headline title="<PERSON>ne <PERSON>" />
          <CreateOfferButton />
        </div>
        <ThgOfferStats userId={session.user.id} />
      </Card>

      {/* Angebote-Tabelle */}
      <Card>
        <Headline title="Alle Angebote" />
        <ThgOffersTable userId={session.user.id} />
      </Card>
    </div>
  );
};

export default ThgOffersPage;
