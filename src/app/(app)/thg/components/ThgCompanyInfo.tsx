"use client";

import React, { useState, useEffect } from "react";
import Button from "~/component/button";
import { FiEdit2, FiSave, FiX, FiLoader } from "react-icons/fi";
import { FaCheckCircle, FaExclamationTriangle } from "react-icons/fa";

interface ThgCompanyInfoProps {
  user: {
    name: string;
    lastName: string;
    email: string;
    companyName?: string;
    phone?: string;
  };
}

const ThgCompanyInfo = ({ user }: ThgCompanyInfoProps) => {
  const [isEditing, setIsEditing] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [isFetching, setIsFetching] = useState(true);
  const [saveSuccess, setSaveSuccess] = useState(false);
  const [saveError, setSaveError] = useState<string | null>(null);
  const [originalData, setOriginalData] = useState({
    contactPerson: `${user.name} ${user.lastName}`,
    phone: user.phone || "",
    website: "",
    notes: "",
  });
  const [formData, setFormData] = useState({
    contactPerson: `${user.name} ${user.lastName}`,
    phone: user.phone || "",
    website: "",
    notes: "",
  });

  // Daten beim Laden der Komponente abrufen
  useEffect(() => {
    const fetchData = async () => {
      try {
        const response = await fetch("/api/thg/company-contact");
        if (response.ok) {
          const data = await response.json();
          const loadedData = {
            contactPerson: data.contactPerson || `${user.name} ${user.lastName}`,
            phone: data.phone || user.phone || "",
            website: data.website || "",
            notes: data.notes || "",
          };
          setFormData(loadedData);
          setOriginalData(loadedData);
        } else if (response.status !== 404) {
          console.error("Failed to fetch company contact data");
        }
      } catch (error) {
        console.error("Error fetching company contact data:", error);
      } finally {
        setIsFetching(false);
      }
    };

    fetchData();
  }, [user.name, user.lastName, user.phone]);

  const handleSave = async () => {
    setIsLoading(true);
    setSaveError(null);
    setSaveSuccess(false);

    try {
      const response = await fetch("/api/thg/company-contact", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        setSaveSuccess(true);
        setIsEditing(false);
        setTimeout(() => setSaveSuccess(false), 3000);
      } else {
        const errorData = await response.json();
        setSaveError(errorData.error || "Fehler beim Speichern der Daten");
      }
    } catch (error) {
      setSaveError("Netzwerkfehler. Bitte versuchen Sie es erneut.");
      console.error("Save error:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    // Reset to original data
    setFormData(originalData);
    setSaveError(null);
    setIsEditing(false);
  };

  if (isFetching) {
    return (
      <div className="flex items-center justify-center p-8">
        <FiLoader className="animate-spin text-2xl text-primary" />
        <span className="ml-2">Lade Firmendaten...</span>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {saveSuccess && (
        <div className="mb-4 p-4 bg-green-100 border border-green-400 text-green-700 rounded flex items-center">
          <FaCheckCircle className="mr-2" />
          Firmendaten erfolgreich gespeichert!
        </div>
      )}

      {saveError && (
        <div className="mb-4 p-4 bg-red-100 border border-red-400 text-red-700 rounded flex items-center">
          <FaExclamationTriangle className="mr-2" />
          {saveError}
        </div>
      )}

      <div className="flex justify-between items-center">
        <h3 className="text-lg font-medium text-gray-800">Kontaktdaten</h3>
        {!isEditing ? (
          <Button
            onClick={() => setIsEditing(true)}
            small
          >
            <FiEdit2 className="w-4 h-4 mr-1" />
            Bearbeiten
          </Button>
        ) : (
          <div className="flex gap-2">
            <Button
              onClick={handleSave}
              className="bg-green-500"
              small
              disabled={isLoading}
            >
              {isLoading ? (
                <FiLoader className="w-4 h-4 mr-1 animate-spin" />
              ) : (
                <FiSave className="w-4 h-4 mr-1" />
              )}
              {isLoading ? "Speichere..." : "Speichern"}
            </Button>
            <Button
              onClick={handleCancel}
              className="bg-gray-500"
              small
            >
              <FiX className="w-4 h-4 mr-1" />
              Abbrechen
            </Button>
          </div>
        )}
      </div>

      <div className="grid grid-cols-1 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Firmenname
          </label>
          <input
            type="text"
            value={user.companyName || ""}
            disabled
            className="input bg-gray-100"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            E-Mail
          </label>
          <input
            type="email"
            value={user.email}
            disabled
            className="input bg-gray-100"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Ansprechpartner
          </label>
          <input
            type="text"
            value={formData.contactPerson}
            onChange={(e) => setFormData({ ...formData, contactPerson: e.target.value })}
            disabled={!isEditing}
            className={`input ${isEditing ? "bg-white" : "bg-gray-100"}`}
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Telefon
          </label>
          <input
            type="tel"
            value={formData.phone}
            onChange={(e) => setFormData({ ...formData, phone: e.target.value })}
            disabled={!isEditing}
            placeholder="+49 123 456789"
            className={`input ${isEditing ? "bg-white" : "bg-gray-100"}`}
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Website
          </label>
          <input
            type="url"
            value={formData.website}
            onChange={(e) => setFormData({ ...formData, website: e.target.value })}
            disabled={!isEditing}
            placeholder="https://www.ihre-firma.de"
            className={`input ${isEditing ? "bg-white" : "bg-gray-100"}`}
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Notizen
          </label>
          <textarea
            value={formData.notes}
            onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
            disabled={!isEditing}
            rows={3}
            placeholder="Zusätzliche Informationen..."
            className={`input ${isEditing ? "bg-white" : "bg-gray-100"}`}
          />
        </div>
      </div>
    </div>
  );
};

export default ThgCompanyInfo;
