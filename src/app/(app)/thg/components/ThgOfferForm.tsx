"use client";

import React, { useState } from "react";
import { useForm } from "react-hook-form";
import Button from "~/component/button";
import { <PERSON>Loa<PERSON>, FiCheckCircle, FiTrendingUp } from "react-icons/fi";

interface OfferFormData {
  pricePerKwh: number;
  priceUnit: "CENT_KWH" | "EURO_MWH";
  quantityMin: number;
  quantityMax: number;
  validUntil: string;
  title?: string;
  description?: string;
  contactPerson?: string;
}

const ThgOfferForm = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitSuccess, setSubmitSuccess] = useState(false);

  const {
    register,
    handleSubmit,
    reset,
    watch,
    formState: { errors },
  } = useForm<OfferFormData>();



  const onSubmit = async (data: OfferFormData) => {
    setIsSubmitting(true);

    try {
      // TODO: Implement API call to save offer
      console.log("Submitting offer:", data);

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));

      setSubmitSuccess(true);
      reset();

      // Reset success message after 3 seconds
      setTimeout(() => setSubmitSuccess(false), 3000);
    } catch (error) {
      console.error("Error submitting offer:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-4">
      {submitSuccess && (
        <div className="relative border-l-4 border-green-500 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg shadow-sm">
          <div className="flex items-center p-4">
            <FiCheckCircle className="w-5 h-5 mr-3 text-green-500" />
            <span className="text-sm font-medium text-green-800 dark:text-green-200">
              Angebot erfolgreich übermittelt!
            </span>
          </div>
        </div>
      )}

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* Info Header */}
        <div className="bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-lg p-4">
          <div className="flex items-center">
            <div className="w-10 h-10 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex items-center justify-center mr-3">
              <FiTrendingUp className="w-5 h-5 text-purple-600 dark:text-purple-400" />
            </div>
            <div>
              <h3 className="font-medium text-purple-900 dark:text-purple-200">THG-Quoten Angebot erstellen</h3>
              <p className="text-sm text-purple-700 dark:text-purple-300">Erstellen Sie ein Angebot zum Kauf von THG-Quoten</p>
            </div>
          </div>
        </div>

        {/* Titel */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Titel (optional)
          </label>
          <input
            {...register("title")}
            type="text"
            placeholder="z.B. Großeinkauf Q1 2024"
            className="input"
          />
        </div>

        {/* Preis und Menge */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Preis pro kWh (in Cent) *
            </label>
            <input
              {...register("pricePerKwh", {
                required: "Preis ist erforderlich",
                min: { value: 0.01, message: "Preis muss größer als 0 sein" },
                max: { value: 100, message: "Preis scheint unrealistisch hoch" }
              })}
              type="number"
              step="0.01"
              placeholder="25.50"
              className="input"
            />
            {errors.pricePerKwh && (
              <p className="text-red-500 text-sm mt-1">{errors.pricePerKwh.message}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Menge (kWh) *
            </label>
            <input
              {...register("quantity", {
                required: "Menge ist erforderlich",
                min: { value: 1, message: "Menge muss mindestens 1 kWh sein" }
              })}
              type="number"
              placeholder="10000"
              className="input"
            />
            {errors.quantity && (
              <p className="text-red-500 text-sm mt-1">{errors.quantity.message}</p>
            )}
          </div>
        </div>

        {/* Gesamtbetrag Anzeige */}
        {totalAmount !== "0.00" && (
          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
            <div className="flex justify-between items-center">
              <span className="text-sm font-medium text-blue-800 dark:text-blue-200">
                Geschätzter Gesamtbetrag:
              </span>
              <span className="text-xl font-bold text-blue-900 dark:text-blue-100">
                {totalAmount} €
              </span>
            </div>
          </div>
        )}

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Gültig bis *
          </label>
          <input
            {...register("validUntil", {
              required: "Gültigkeitsdatum ist erforderlich"
            })}
            type="date"
            min={new Date().toISOString().split('T')[0]}
            className="input"
          />
          {errors.validUntil && (
            <p className="text-red-500 text-sm mt-1">{errors.validUntil.message}</p>
          )}
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            Beschreibung (optional)
          </label>
          <textarea
            {...register("description")}
            rows={3}
            placeholder="Zusätzliche Informationen zu Ihrem Angebot..."
            className="input"
          />
        </div>

        <div className="relative border-l-4 border-blue-500 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg shadow-sm">
          <div className="p-4">
            <p className="text-sm text-blue-800 dark:text-blue-200">
              <strong>Hinweis:</strong> Dies ist eine Dummy-Implementation.
              Ihr Angebot wird noch nicht gespeichert oder übertragen.
            </p>
          </div>
        </div>

        <Button
          type="submit"
          disabled={isSubmitting}
          className="w-full"
        >
          Angebot abgeben
          {isSubmitting && <FiLoader className="ml-2 animate-spin" />}
        </Button>
      </form>
    </div>
  );
};

export default ThgOfferForm;
