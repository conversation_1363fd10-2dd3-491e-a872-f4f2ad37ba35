import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role } from "@prisma/client";
import { notFound } from "next/navigation";
import Card from "~/component/card";
import Headline from "~/component/Headline";
import ThgMarketChart from "./components/ThgMarketChart";
import ThgMarketStats from "./components/ThgMarketStats";
import ThgActiveOffers from "./components/ThgActiveOffers";

const ThgMarketPage = async () => {
  const session = await getServerSession(authOptions);

  if (!session?.user || session.user.role !== Role.ADMIN) {
    return notFound();
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <Card>
        <Headline title="THG-Quoten Marktübersicht" />
        <p className="text-gray-600 dark:text-gray-400">
          Aktuelle Marktpreise und verfügbare Angebote für THG-Quoten
        </p>
      </Card>

      {/* Marktstatistiken */}
      <Card>
        <Headline title="Marktstatistiken" />
        <ThgMarketStats />
      </Card>

      {/* Preisentwicklung Chart */}
      <Card>
        <Headline title="Preisentwicklung" />
        <ThgMarketChart />
      </Card>

      {/* Aktive Angebote */}
      <Card>
        <Headline title="Aktuelle Marktangebote" />
        <ThgActiveOffers />
      </Card>
    </div>
  );
};

export default ThgMarketPage;
