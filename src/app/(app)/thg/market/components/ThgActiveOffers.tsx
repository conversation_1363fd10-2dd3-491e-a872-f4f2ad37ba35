"use client";

import React, { useEffect, useState } from "react";
import { FiClock } from "react-icons/fi";

interface MarketOffer {
  id: string;
  type: "PURCHASE" | "SALE";
  title?: string;
  companyName: string;
  pricePerKwh: number;
  priceUnit?: string;
  quantity: number;
  quantityMin?: number;
  quantityMax?: number;
  validUntil: string;
  createdAt: string;
  description?: string;
  contactPerson?: string;
}

const ThgActiveOffers = () => {
  const [offers, setOffers] = useState<MarketOffer[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState<"ALL" | "PURCHASE" | "SALE">("ALL");

  useEffect(() => {
    const fetchActiveOffers = async () => {
      try {
        const response = await fetch('/api/admin/thg/market/offers');

        if (response.ok) {
          const data = await response.json();
          setOffers(data);
        } else {
          console.error("Failed to fetch offers:", response.status, response.statusText);
        }
      } catch (error) {
        console.error("Error fetching active offers:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchActiveOffers();
  }, []);

  const filteredOffers = offers.filter((offer) => filter === "ALL" || offer.type === filter);

  const getTypeBadge = (type: string) => {
    const typeConfig = {
      PURCHASE: { color: "purple", text: "Kaufangebot" },
      SALE: { color: "orange", text: "Verkaufsangebot" },
    };

    const config = typeConfig[type as keyof typeof typeConfig] || typeConfig.PURCHASE;

    const colorClasses = {
      purple: "bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-200",
      orange: "bg-orange-100 text-orange-800 dark:bg-orange-900/20 dark:text-orange-200",
    };

    return (
      <span
        className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${colorClasses[config.color]}`}
      >
        {config.text}
      </span>
    );
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("de-DE");
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("de-DE", {
      style: "currency",
      currency: "EUR",
    }).format(amount);
  };

  const calculateTotal = (price: number, quantity: number) => {
    return (price * quantity) / 100; // Convert from cent to euro
  };

  const getDaysUntilExpiry = (validUntil: string) => {
    const today = new Date();
    const expiryDate = new Date(validUntil);
    const diffTime = expiryDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  if (loading) {
    return (
      <div className="space-y-4">
        {[...Array(4)].map((_, i) => (
          <div key={i} className="animate-pulse rounded-lg bg-gray-100 p-4 dark:bg-gray-800">
            <div className="mb-2 h-4 rounded bg-gray-300 dark:bg-gray-600"></div>
            <div className="mb-2 h-4 w-3/4 rounded bg-gray-300 dark:bg-gray-600"></div>
            <div className="h-4 w-1/2 rounded bg-gray-300 dark:bg-gray-600"></div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Filter Buttons */}
      <div className="flex space-x-2">
        {[
          { key: "ALL", label: "Alle Angebote" },
          { key: "PURCHASE", label: "Kaufangebote" },
          { key: "SALE", label: "Verkaufsangebote" },
        ].map((option) => (
          <button
            key={option.key}
            onClick={() => setFilter(option.key as "ALL" | "PURCHASE" | "SALE")}
            className={`rounded-lg px-4 py-2 text-sm font-medium transition-colors ${
              filter === option.key
                ? "bg-primary text-white"
                : "bg-gray-100 text-gray-700 hover:bg-gray-200 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700"
            }`}
          >
            {option.label}
          </button>
        ))}
      </div>

      {/* Offers List */}
      <div className="space-y-4">
        {filteredOffers.map((offer) => {
          const daysLeft = getDaysUntilExpiry(offer.validUntil);
          const isExpiringSoon = daysLeft <= 7;

          return (
            <div
              key={offer.id}
              className="rounded-lg border border-gray-200 bg-white p-6 transition-shadow hover:shadow-md dark:border-gray-700 dark:bg-gray-900"
            >
              <div className="mb-4 flex items-start justify-between">
                <div className="flex-1">
                  <div className="mb-2 flex items-center space-x-3">
                    {getTypeBadge(offer.type)}
                    <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                      {offer.title ||
                        `${offer.type === "PURCHASE" ? "Kaufangebot" : "Verkaufsangebot"} #${offer.id.slice(-8)}`}
                    </h3>
                  </div>
                  <p className="font-medium text-gray-600 dark:text-gray-400">
                    {offer.companyName}
                  </p>
                  {offer.description && (
                    <p className="mt-2 text-sm text-gray-500 dark:text-gray-500">
                      {offer.description}
                    </p>
                  )}
                </div>

                <div className="text-right">
                  <div className="text-2xl font-bold text-primary">
                    {offer.pricePerKwh.toFixed(2)} {offer.priceUnit === "EURO_MWH" ? "€/MWh" : "ct/kWh"}
                  </div>
                  <div className="text-sm text-gray-500 dark:text-gray-400">
                    {offer.quantityMin && offer.quantityMax && offer.quantityMin !== offer.quantityMax
                      ? `${offer.quantityMin.toLocaleString("de-DE")} - ${offer.quantityMax.toLocaleString("de-DE")} kWh`
                      : `${offer.quantity.toLocaleString("de-DE")} kWh`
                    }
                  </div>
                  <div className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    ≈ {formatCurrency(calculateTotal(offer.pricePerKwh, offer.quantity))}
                  </div>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4 text-sm text-gray-500 dark:text-gray-400">
                  <div className="flex items-center">
                    <FiClock className="mr-1 h-4 w-4" />
                    <span className={isExpiringSoon ? "font-medium text-red-600" : ""}>
                      {daysLeft > 0 ? `${daysLeft} Tage` : "Abgelaufen"}
                    </span>
                  </div>
                  <div>Erstellt: {formatDate(offer.createdAt)}</div>
                </div>


              </div>
            </div>
          );
        })}
      </div>

      {filteredOffers.length === 0 && (
        <div className="py-12 text-center">
          <div className="mb-4 text-gray-400">
            <FiClock className="mx-auto h-12 w-12" />
          </div>
          <h3 className="mb-2 text-lg font-medium text-gray-900 dark:text-white">
            Keine Angebote gefunden
          </h3>
          <p className="text-gray-500 dark:text-gray-400">
            {filter === "ALL"
              ? "Aktuell sind keine aktiven Angebote verfügbar."
              : `Keine ${filter === "PURCHASE" ? "Kaufangebote" : "Verkaufsangebote"} verfügbar.`}
          </p>
        </div>
      )}
    </div>
  );
};

export default ThgActiveOffers;
