"use client";

import React, { useEffect, useState } from "react";
import { FiTrendingUp, FiTrendingDown, FiActivity, FiUsers } from "react-icons/fi";

interface MarketStats {
  averagePrice: number;
  priceChange: number;
  totalVolume: number;
  activeOffers: number;
  minPrice: number;
  maxPrice: number;
  offersWithPrices: number;
}

const ThgMarketStats = () => {
  const [stats, setStats] = useState<MarketStats>({
    averagePrice: 0,
    priceChange: 0,
    totalVolume: 0,
    activeOffers: 0,
    minPrice: 0,
    maxPrice: 0,
    offersWithPrices: 0,
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchMarketStats = async () => {
      try {
        const response = await fetch('/api/admin/thg/market/stats');
        if (response.ok) {
          const data = await response.json();
          setStats(data);
        } else {
          console.error("Failed to fetch market stats:", response.statusText);
          // Fallback zu leeren Daten
          setStats({
            averagePrice: 0,
            priceChange: 0,
            totalVolume: 0,
            activeOffers: 0,
            minPrice: 0,
            maxPrice: 0,
            offersWithPrices: 0,
          });
        }
      } catch (error) {
        console.error("Error fetching market stats:", error);
        // Fallback zu leeren Daten
        setStats({
          averagePrice: 0,
          priceChange: 0,
          totalVolume: 0,
          activeOffers: 0,
          minPrice: 0,
          maxPrice: 0,
          offersWithPrices: 0,
        });
      } finally {
        setLoading(false);
      }
    };

    fetchMarketStats();
  }, []);

  const statCards = [
    {
      title: "Durchschnittspreis",
      value: stats.averagePrice > 0 ? `${stats.averagePrice.toFixed(2)} ct/kWh` : "Keine Daten",
      change: stats.priceChange,
      icon: FiActivity,
      color: "blue",
    },
    {
      title: "Preisspanne",
      value: stats.minPrice > 0 && stats.maxPrice > 0
        ? `${stats.minPrice.toFixed(2)} - ${stats.maxPrice.toFixed(2)} ct`
        : "Keine Daten",
      icon: FiTrendingUp,
      color: "purple",
    },
    {
      title: "Gesamtvolumen",
      value: stats.totalVolume > 0
        ? `${(stats.totalVolume / 1000).toFixed(0)}k kWh`
        : "0 kWh",
      icon: FiActivity,
      color: "green",
    },
    {
      title: "Aktive Angebote",
      value: stats.activeOffers.toString(),
      icon: FiUsers,
      color: "orange",
    },
  ];

  const getColorClasses = (color: string) => {
    const colors = {
      blue: "bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800 text-blue-600 dark:text-blue-400",
      purple: "bg-purple-50 dark:bg-purple-900/20 border-purple-200 dark:border-purple-800 text-purple-600 dark:text-purple-400",
      green: "bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800 text-green-600 dark:text-green-400",
      orange: "bg-orange-50 dark:bg-orange-900/20 border-orange-200 dark:border-orange-800 text-orange-600 dark:text-orange-400",
    };
    return colors[color as keyof typeof colors] || colors.blue;
  };

  const renderChangeIndicator = (change: number) => {
    if (change === 0) return null;
    
    const isPositive = change > 0;
    const Icon = isPositive ? FiTrendingUp : FiTrendingDown;
    const colorClass = isPositive ? "text-green-600" : "text-red-600";
    
    return (
      <div className={`flex items-center text-sm ${colorClass}`}>
        <Icon className="w-4 h-4 mr-1" />
        {Math.abs(change).toFixed(1)}%
      </div>
    );
  };

  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {[...Array(4)].map((_, i) => (
          <div key={i} className="bg-gray-100 dark:bg-gray-800 rounded-lg p-4 animate-pulse">
            <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded mb-2"></div>
            <div className="h-8 bg-gray-300 dark:bg-gray-600 rounded mb-2"></div>
            <div className="h-4 bg-gray-300 dark:bg-gray-600 rounded w-1/2"></div>
          </div>
        ))}
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      {statCards.map((stat, index) => {
        const Icon = stat.icon;
        return (
          <div
            key={index}
            className={`rounded-lg border p-4 ${getColorClasses(stat.color)}`}
          >
            <div className="flex items-center justify-between mb-2">
              <p className="text-sm font-medium opacity-75">{stat.title}</p>
              <Icon className="h-5 w-5 opacity-75" />
            </div>
            <div className="flex items-center justify-between">
              <p className="text-xl font-bold">{stat.value}</p>
              {stat.change !== undefined && renderChangeIndicator(stat.change)}
            </div>
          </div>
        );
      })}
    </div>
  );
};

export default ThgMarketStats;
