"use client";

import React, { useEffect, useState } from "react";

interface ChartDataPoint {
  date: string;
  averagePrice: number;
  minPrice: number;
  maxPrice: number;
  volume: number;
  offerCount: number;
}

const ThgMarketChart = () => {
  const [chartData, setChartData] = useState<ChartDataPoint[]>([]);
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState<"7d" | "30d" | "90d">("30d");

  useEffect(() => {
    const fetchChartData = async () => {
      try {
        const response = await fetch(`/api/admin/thg/market/chart?range=${timeRange}`);
        if (response.ok) {
          const data = await response.json();
          setChartData(data);
        } else {
          console.error("Failed to fetch chart data:", response.statusText);
          setChartData([]);
        }
      } catch (error) {
        console.error("Error fetching chart data:", error);
        setChartData([]);
      } finally {
        setLoading(false);
      }
    };

    fetchChartData();
  }, [timeRange]);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString("de-DE", { 
      month: "short", 
      day: "numeric" 
    });
  };

  if (loading) {
    return (
      <div className="h-64 bg-gray-100 dark:bg-gray-800 rounded-lg animate-pulse flex items-center justify-center">
        <div className="text-gray-500 dark:text-gray-400">Lade Chart-Daten...</div>
      </div>
    );
  }

  if (chartData.length === 0) {
    return (
      <div className="h-64 bg-gray-50 dark:bg-gray-800 rounded-lg flex items-center justify-center">
        <div className="text-center">
          <div className="text-gray-400 mb-2">📊</div>
          <div className="text-gray-500 dark:text-gray-400">Keine Chart-Daten verfügbar</div>
          <div className="text-sm text-gray-400 dark:text-gray-500">
            Erstellen Sie Angebote, um Preisentwicklungen zu sehen
          </div>
        </div>
      </div>
    );
  }

  const validPrices = chartData.filter(d => d.averagePrice > 0);

  // Berechne schöne Y-Achsen-Werte (runde auf 5er oder 10er)
  const getYAxisValues = (min: number, max: number, steps: number = 5) => {
    const range = max - min;
    const stepSize = range / (steps - 1);

    // Runde Step-Size auf schöne Werte
    let niceStepSize;
    if (stepSize <= 1) niceStepSize = 0.5;
    else if (stepSize <= 2) niceStepSize = 1;
    else if (stepSize <= 5) niceStepSize = 2;
    else if (stepSize <= 10) niceStepSize = 5;
    else if (stepSize <= 20) niceStepSize = 10;
    else niceStepSize = Math.ceil(stepSize / 10) * 10;

    // Berechne schönen Min-Wert (abgerundet)
    const niceMin = Math.floor(min / niceStepSize) * niceStepSize;

    // Generiere Y-Achsen-Werte
    const values = [];
    for (let i = 0; i < steps; i++) {
      values.push(niceMin + (i * niceStepSize));
    }

    return values;
  };

  let yAxisValues: number[] = [];
  let chartMinPrice = 0;
  let chartMaxPrice = 0;
  let chartPriceRange = 0;

  if (validPrices.length > 0) {
    // Berechne Min/Max mit Padding für bessere Sichtbarkeit
    const dataMaxPrice = Math.max(...validPrices.map(d => d.maxPrice));
    const dataMinPrice = Math.min(...validPrices.map(d => d.minPrice));
    const dataRange = dataMaxPrice - dataMinPrice;

    // Füge 10% Padding oben und unten hinzu
    const padding = Math.max(dataRange * 0.1, 1); // Mindestens 1 ct Padding
    const maxPrice = dataMaxPrice + padding;
    const minPrice = Math.max(0, dataMinPrice - padding); // Nicht unter 0

    yAxisValues = getYAxisValues(minPrice, maxPrice);
    chartMinPrice = yAxisValues[0];
    chartMaxPrice = yAxisValues[yAxisValues.length - 1];
    chartPriceRange = chartMaxPrice - chartMinPrice;
  }

  return (
    <div className="space-y-4">
      {/* Time Range Selector */}
      <div className="flex justify-between items-center">
        <div className="flex space-x-2">
          {[
            { key: "7d", label: "7 Tage" },
            { key: "30d", label: "30 Tage" },
            { key: "90d", label: "90 Tage" },
          ].map((option) => (
            <button
              key={option.key}
              onClick={() => setTimeRange(option.key as "7d" | "30d" | "90d")}
              className={`px-3 py-1 rounded-lg text-sm font-medium transition-colors ${
                timeRange === option.key
                  ? "bg-primary text-white"
                  : "bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-gray-200 dark:hover:bg-gray-700"
              }`}
            >
              {option.label}
            </button>
          ))}
        </div>
        
        <div className="flex items-center space-x-4 text-sm">
          <div className="flex items-center">
            <div className="w-3 h-3 bg-blue-500 rounded-full mr-2"></div>
            <span className="text-gray-600 dark:text-gray-400">Durchschnittspreis</span>
          </div>
          <div className="flex items-center">
            <div className="w-3 h-3 bg-gray-300 dark:bg-gray-600 rounded-full mr-2"></div>
            <span className="text-gray-600 dark:text-gray-400">Preisspanne</span>
          </div>
        </div>
      </div>

      {/* Chart Container */}
      <div className="relative h-64 bg-gray-50 dark:bg-gray-800 rounded-lg p-4 overflow-hidden">
        <div className="w-full h-full relative">
          <svg width="100%" height="100%" className="absolute inset-0" style={{ clipPath: 'inset(0)' }}>
          {/* Grid Lines */}
          {validPrices.length > 0 && yAxisValues.map((value, index) => {
            const y = 5 + ((chartMaxPrice - value) / chartPriceRange) * 85; // Adjust for 5% margin
            return (
              <g key={value}>
                <line
                  x1="5%"
                  y1={`${y}%`}
                  x2="95%"
                  y2={`${y}%`}
                  stroke="currentColor"
                  strokeWidth="1"
                  className="text-gray-200 dark:text-gray-700"
                  opacity="0.5"
                />
                <text
                  x="2%"
                  y={`${y}%`}
                  dy="4"
                  className="text-xs fill-gray-500 dark:fill-gray-400"
                >
                  {value.toFixed(1)} ct
                </text>
              </g>
            );
          })}

          {/* Price Range Areas */}
          <defs>
            <linearGradient id="priceGradient" x1="0%" y1="0%" x2="0%" y2="100%">
              <stop offset="0%" stopColor="rgb(59, 130, 246)" stopOpacity="0.1" />
              <stop offset="100%" stopColor="rgb(59, 130, 246)" stopOpacity="0.05" />
            </linearGradient>
          </defs>

          {/* Price Range Polygon */}
          {validPrices.length > 0 && (
            <polygon
              points={chartData.map((point, index) => {
                const x = 5 + (index / (chartData.length - 1)) * 90; // 5% margin on each side
                const yMax = 5 + ((chartMaxPrice - point.maxPrice) / chartPriceRange) * 85; // 5% margin top/bottom
                return `${x},${yMax}`;
              }).join(' ') + ' ' + chartData.map((point, index) => {
                const x = 5 + ((chartData.length - 1 - index) / (chartData.length - 1)) * 90;
                const yMin = 5 + ((chartMaxPrice - point.minPrice) / chartPriceRange) * 85;
                return `${x},${yMin}`;
              }).join(' ')}
              fill="url(#priceGradient)"
              className="opacity-50"
            />
          )}

          {/* Average Price Line */}
          {validPrices.length > 0 && (
            <polyline
              points={chartData.map((point, index) => {
                const x = 5 + (index / (chartData.length - 1)) * 90; // 5% margin on each side
                const y = 5 + ((chartMaxPrice - point.averagePrice) / chartPriceRange) * 85; // 5% margin top/bottom
                return `${x},${y}`;
              }).join(' ')}
              fill="none"
              stroke="rgb(59, 130, 246)"
              strokeWidth="2"
              className="drop-shadow-sm"
            />
          )}

          {/* Data Points */}
          {validPrices.length > 0 && chartData.map((point, index) => {
            const x = 5 + (index / (chartData.length - 1)) * 90; // 5% margin on each side
            const y = 5 + ((chartMaxPrice - point.averagePrice) / chartPriceRange) * 85; // 5% margin top/bottom
            return (
              <circle
                key={index}
                cx={`${x}%`}
                cy={`${y}%`}
                r="3"
                fill="rgb(59, 130, 246)"
                className="hover:r-4 transition-all cursor-pointer"
              >
                <title>
                  {formatDate(point.date)}: {point.averagePrice.toFixed(2)} ct/kWh
                </title>
              </circle>
            );
          })}
          </svg>

          {/* X-Axis Labels */}
          <div className="absolute bottom-0 left-4 right-4 flex justify-between text-xs text-gray-500 dark:text-gray-400">
            {chartData.filter((_, index) => index % Math.ceil(chartData.length / 6) === 0).map((point, index) => (
              <span key={index}>{formatDate(point.date)}</span>
            ))}
          </div>
        </div>
      </div>

      {/* Chart Summary */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
        <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-3">
          <div className="font-medium text-blue-900 dark:text-blue-200">Aktueller Preis</div>
          <div className="text-lg font-bold text-blue-600 dark:text-blue-400">
            {validPrices.length > 0
              ? chartData[chartData.length - 1]?.averagePrice.toFixed(2)
              : "0.00"} ct/kWh
          </div>
        </div>
        <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-3">
          <div className="font-medium text-green-900 dark:text-green-200">Höchstpreis</div>
          <div className="text-lg font-bold text-green-600 dark:text-green-400">
            {validPrices.length > 0
              ? Math.max(...validPrices.map(d => d.maxPrice)).toFixed(2)
              : "0.00"} ct/kWh
          </div>
        </div>
        <div className="bg-red-50 dark:bg-red-900/20 rounded-lg p-3">
          <div className="font-medium text-red-900 dark:text-red-200">Niedrigstpreis</div>
          <div className="text-lg font-bold text-red-600 dark:text-red-400">
            {validPrices.length > 0
              ? Math.min(...validPrices.map(d => d.minPrice)).toFixed(2)
              : "0.00"} ct/kWh
          </div>
        </div>
      </div>
    </div>
  );
};

export default ThgMarketChart;
