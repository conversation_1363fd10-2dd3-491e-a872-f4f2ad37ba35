"use client";

import React, { useEffect, useState, useRef } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { FaKey, FaIdCard, FaSave, FaTimes, FaUpload, FaTrash, FaImage } from 'react-icons/fa';

interface DashboardConfig {
  id: string;
  name: string;
  description?: string;
  solarEdgeApiKey?: string;
  solarEdgeSiteId?: string;
  logoUrl?: string;
  location: {
    id: string;
    name: string;
    city: string;
  };
}

interface UpdateForm {
  name: string;
  description: string;
  solarEdgeApiKey: string;
  solarEdgeSiteId: string;
  settings: {
    fuelSavingsFormula: number;
    co2SavingsFormula: {
      electricityGridFactor: number;
      gasolineEmissionFactor: number;
      efficiencyFactor: number;
    };
    hourlyChartMaxKWh: number;
    refreshInterval?: number;
    showWeather?: boolean;
    showCharging?: boolean;
    theme?: string;
    units?: string;
    language?: string;
  };
}

export default function EditSolarDashboardPage() {
  const params = useParams();
  const router = useRouter();
  const dashboardId = params.id as string;

  const [dashboard, setDashboard] = useState<DashboardConfig | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [logoUploading, setLogoUploading] = useState(false);
  const [logoError, setLogoError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [formData, setFormData] = useState<UpdateForm>({
    name: '',
    description: '',
    solarEdgeApiKey: '',
    solarEdgeSiteId: '',
    settings: {
      fuelSavingsFormula: 0.25,
      co2SavingsFormula: {
        electricityGridFactor: 0.4,
        gasolineEmissionFactor: 2.3,
        efficiencyFactor: 0.25
      },
      hourlyChartMaxKWh: 50,
      refreshInterval: 300,
      showWeather: true,
      showCharging: true,
      theme: 'light',
      units: 'metric',
      language: 'de'
    }
  });

  useEffect(() => {
    fetchDashboard();
  }, [dashboardId]);

  const fetchDashboard = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch(`/api/solar-dashboard/${dashboardId}`);

      if (!response.ok) {
        if (response.status === 404) {
          setError('Dashboard nicht gefunden');
        } else if (response.status === 403) {
          setError('Zugriff verweigert');
        } else {
          setError('Fehler beim Laden des Dashboards');
        }
        return;
      }

      const data = await response.json();
      setDashboard(data);
      setFormData({
        name: data.name || '',
        description: data.description || '',
        solarEdgeApiKey: data.solarEdgeApiKey || '',
        solarEdgeSiteId: data.solarEdgeSiteId || '',
        settings: {
          fuelSavingsFormula: data.settings?.fuelSavingsFormula || 0.25,
          co2SavingsFormula: data.settings?.co2SavingsFormula || {
            electricityGridFactor: 0.4,
            gasolineEmissionFactor: 2.3,
            efficiencyFactor: 0.25
          },
          hourlyChartMaxKWh: data.settings?.hourlyChartMaxKWh || 50,
          refreshInterval: data.settings?.refreshInterval || 300,
          showWeather: data.settings?.showWeather ?? true,
          showCharging: data.settings?.showCharging ?? true,
          theme: data.settings?.theme || 'light',
          units: data.settings?.units || 'metric',
          language: data.settings?.language || 'de'
        }
      });
    } catch (err) {
      console.error('Error fetching dashboard:', err);
      setError('Netzwerkfehler beim Laden des Dashboards');
    } finally {
      setLoading(false);
    }
  };

  const handleInputChange = (field: keyof UpdateForm, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleLogoUpload = async (file: File) => {
    setLogoUploading(true);
    setLogoError(null);

    try {
      const formData = new FormData();
      formData.append('logo', file);

      const response = await fetch(`/api/solar-dashboard/${dashboardId}/logo`, {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Fehler beim Hochladen');
      }

      const result = await response.json();

      // Update dashboard state with new logo URL
      setDashboard(prev => prev ? { ...prev, logoUrl: result.logoUrl } : null);

      // Clear file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }

    } catch (error) {
      console.error('Logo upload error:', error);
      setLogoError(error instanceof Error ? error.message : 'Fehler beim Hochladen');
    } finally {
      setLogoUploading(false);
    }
  };

  const handleLogoDelete = async () => {
    setLogoUploading(true);
    setLogoError(null);

    try {
      const response = await fetch(`/api/solar-dashboard/${dashboardId}/logo`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Fehler beim Löschen');
      }

      // Update dashboard state to remove logo URL
      setDashboard(prev => prev ? { ...prev, logoUrl: undefined } : null);

    } catch (error) {
      console.error('Logo delete error:', error);
      setLogoError(error instanceof Error ? error.message : 'Fehler beim Löschen');
    } finally {
      setLogoUploading(false);
    }
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      // Validate file type
      if (!['image/svg+xml', 'image/png'].includes(file.type)) {
        setLogoError('Nur SVG und PNG Dateien sind erlaubt');
        return;
      }

      // Validate file size (5MB)
      if (file.size > 5 * 1024 * 1024) {
        setLogoError('Datei zu groß. Maximum 5MB erlaubt.');
        return;
      }

      handleLogoUpload(file);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name) {
      setError('Name ist erforderlich');
      return;
    }

    setSaving(true);
    setError(null);

    try {
      const response = await fetch(`/api/solar-dashboard/${dashboardId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: formData.name,
          description: formData.description || undefined,
          solarEdgeApiKey: formData.solarEdgeApiKey || undefined,
          solarEdgeSiteId: formData.solarEdgeSiteId || undefined,
          settings: formData.settings,
        }),
      });

      if (response.ok) {
        router.push('/solar-dashboard');
      } else {
        const errorData = await response.json();
        setError(errorData.error || 'Fehler beim Aktualisieren des Dashboards');
      }
    } catch (err) {
      console.error('Error updating dashboard:', err);
      setError('Netzwerkfehler beim Aktualisieren des Dashboards');
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Dashboard wird geladen...</p>
        </div>
      </div>
    );
  }

  if (error && !dashboard) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center bg-white rounded-lg p-8 shadow-lg max-w-md mx-auto">
          <div className="text-red-500 text-4xl mb-4">⚠️</div>
          <h2 className="text-xl font-bold text-gray-800 mb-4">Fehler</h2>
          <p className="text-gray-600 mb-6">{error}</p>
          <button
            onClick={() => router.back()}
            className="bg-gray-600 text-white px-4 py-2 rounded hover:bg-gray-700 transition-colors"
          >
            Zurück
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-2xl mx-auto">
        <div className="flex items-center gap-4 mb-8">
          <div className="w-12 h-12 bg-green-600 rounded-lg flex items-center justify-center text-white text-2xl">
            🌞
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-800">Dashboard bearbeiten</h1>
            <p className="text-gray-600">Bearbeiten Sie die Einstellungen für "{dashboard?.name}"</p>
          </div>
        </div>

        {dashboard && (
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
            <h3 className="font-semibold text-blue-800 mb-2">📍 Standort-Information</h3>
            <p className="text-blue-700">
              <strong>Standort:</strong> {dashboard.location.name}, {dashboard.location.city}
            </p>
            <p className="text-sm text-blue-600 mt-1">
              Der Standort kann nach der Erstellung nicht mehr geändert werden.
            </p>
          </div>
        )}

        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <div className="flex items-center gap-2">
              <span className="text-red-500 text-xl">⚠️</span>
              <p className="text-red-700">{error}</p>
            </div>
          </div>
        )}

        <form onSubmit={handleSubmit} className="bg-white rounded-lg shadow-lg p-8">
          {/* Logo Upload Section */}
          <div className="mb-8 pb-6 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
              <FaImage className="text-green-600" />
              Dashboard Logo
            </h3>

            <div className="flex items-start gap-6">
              {/* Logo Preview */}
              <div className="flex-shrink-0">
                {dashboard?.logoUrl ? (
                  <div className="relative">
                    <img
                      src={dashboard.logoUrl}
                      alt="Dashboard Logo"
                      className="w-24 h-24 object-contain border border-gray-200 rounded-lg bg-white p-2"
                    />
                    <button
                      type="button"
                      onClick={handleLogoDelete}
                      disabled={logoUploading}
                      className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600 disabled:opacity-50"
                      title="Logo löschen"
                    >
                      <FaTimes />
                    </button>
                  </div>
                ) : (
                  <div className="w-24 h-24 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center bg-gray-50">
                    <FaImage className="text-gray-400 text-2xl" />
                  </div>
                )}
              </div>

              {/* Upload Controls */}
              <div className="flex-1">
                <p className="text-sm text-gray-600 mb-3">
                  Laden Sie ein Logo für Ihr Dashboard hoch. Das Logo wird oben links im Dashboard angezeigt.
                </p>

                <div className="flex items-center gap-3 mb-2">
                  <input
                    ref={fileInputRef}
                    type="file"
                    accept=".svg,.png,image/svg+xml,image/png"
                    onChange={handleFileSelect}
                    className="hidden"
                  />

                  <button
                    type="button"
                    onClick={() => fileInputRef.current?.click()}
                    disabled={logoUploading}
                    className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 disabled:opacity-50 flex items-center gap-2 text-sm"
                  >
                    <FaUpload />
                    {logoUploading ? 'Wird hochgeladen...' : 'Logo hochladen'}
                  </button>

                  {dashboard?.logoUrl && (
                    <button
                      type="button"
                      onClick={handleLogoDelete}
                      disabled={logoUploading}
                      className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 disabled:opacity-50 flex items-center gap-2 text-sm"
                    >
                      <FaTrash />
                      Logo entfernen
                    </button>
                  )}
                </div>

                <p className="text-xs text-gray-500">
                  Erlaubte Formate: SVG, PNG • Maximale Größe: 5MB
                </p>

                {logoError && (
                  <div className="mt-2 text-sm text-red-600 flex items-center gap-1">
                    <span>⚠️</span>
                    {logoError}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Dashboard Name */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Dashboard Name *
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              placeholder="z.B. Solar Dashboard Hauptstandort"
              required
            />
          </div>

          {/* Description */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Beschreibung
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              rows={3}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              placeholder="Optionale Beschreibung des Dashboards"
            />
          </div>

          {/* SolarEdge Configuration */}
          <div className="mb-6 p-6 bg-orange-50 border border-orange-200 rounded-lg">
            <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
              <span className="text-orange-600">🌞</span>
              SolarEdge API-Konfiguration
            </h3>
            <p className="text-sm text-gray-600 mb-4">
              Konfigurieren Sie die SolarEdge API für echte Daten. Ohne diese Konfiguration werden Dummy-Daten verwendet.
            </p>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <FaKey className="inline mr-1" />
                  API Key
                </label>
                <input
                  type="text"
                  value={formData.solarEdgeApiKey}
                  onChange={(e) => handleInputChange('solarEdgeApiKey', e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  placeholder="SolarEdge API Key"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <FaIdCard className="inline mr-1" />
                  Site ID
                </label>
                <input
                  type="text"
                  value={formData.solarEdgeSiteId}
                  onChange={(e) => handleInputChange('solarEdgeSiteId', e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  placeholder="SolarEdge Site ID"
                />
              </div>
            </div>
          </div>

          {/* Formula Configuration */}
          <div className="mb-8 p-6 bg-gray-50 rounded-lg">
            <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
              <span>🧮</span>
              Berechnungsformeln (Info-Screen)
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Fuel Savings Formula */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Kraftstoffeinsparung (Liter pro kWh)
                </label>
                <input
                  type="number"
                  step="0.01"
                  min="0"
                  max="1"
                  value={formData.settings.fuelSavingsFormula}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    settings: {
                      ...prev.settings,
                      fuelSavingsFormula: parseFloat(e.target.value) || 0.25
                    }
                  }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  placeholder="0.25"
                />
                <p className="text-xs text-gray-500 mt-1">Standard: 0.25 Liter pro kWh (basierend auf EV-Effizienz vs. Verbrenner)</p>
              </div>

              {/* CO2 Grid Factor */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  CO₂-Faktor Stromnetz (kg CO₂/kWh)
                </label>
                <input
                  type="number"
                  step="0.01"
                  min="0"
                  max="1"
                  value={formData.settings.co2SavingsFormula.electricityGridFactor}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    settings: {
                      ...prev.settings,
                      co2SavingsFormula: {
                        ...prev.settings.co2SavingsFormula,
                        electricityGridFactor: parseFloat(e.target.value) || 0.4
                      }
                    }
                  }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  placeholder="0.4"
                />
                <p className="text-xs text-gray-500 mt-1">Standard: 0.4 kg CO₂/kWh (deutscher Strommix)</p>
              </div>

              {/* Gasoline Emission Factor */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Benzin CO₂-Emissionen (kg CO₂/Liter)
                </label>
                <input
                  type="number"
                  step="0.1"
                  min="0"
                  max="5"
                  value={formData.settings.co2SavingsFormula.gasolineEmissionFactor}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    settings: {
                      ...prev.settings,
                      co2SavingsFormula: {
                        ...prev.settings.co2SavingsFormula,
                        gasolineEmissionFactor: parseFloat(e.target.value) || 2.3
                      }
                    }
                  }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  placeholder="2.3"
                />
                <p className="text-xs text-gray-500 mt-1">Standard: 2.3 kg CO₂/Liter (Benzin-Verbrennung)</p>
              </div>

              {/* Efficiency Factor */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Effizienz-Faktor (Liter/kWh)
                </label>
                <input
                  type="number"
                  step="0.01"
                  min="0"
                  max="1"
                  value={formData.settings.co2SavingsFormula.efficiencyFactor}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    settings: {
                      ...prev.settings,
                      co2SavingsFormula: {
                        ...prev.settings.co2SavingsFormula,
                        efficiencyFactor: parseFloat(e.target.value) || 0.25
                      }
                    }
                  }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  placeholder="0.25"
                />
                <p className="text-xs text-gray-500 mt-1">Standard: 0.25 Liter/kWh (EV vs. Verbrenner Effizienz)</p>
              </div>
            </div>

            {/* Hourly Chart Configuration */}
            <div className="space-y-4">
              <h4 className="font-medium text-gray-800 border-b pb-2">📊 Stündliches Produktionsdiagramm</h4>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Maximaler kWh-Wert für Diagramm-Skalierung
                </label>
                <input
                  type="number"
                  step="1"
                  min="10"
                  max="500"
                  value={formData.settings.hourlyChartMaxKWh}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    settings: {
                      ...prev.settings,
                      hourlyChartMaxKWh: parseInt(e.target.value) || 50
                    }
                  }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  placeholder="50"
                />
                <p className="text-xs text-gray-500 mt-1">
                  Standard: 50 kWh. Das Diagramm skaliert automatisch höher, wenn Werte diesen Maximalwert überschreiten.
                </p>
              </div>
            </div>

            {/* Formula Preview */}
            <div className="mt-6 p-4 bg-blue-50 rounded-lg">
              <h4 className="font-medium text-blue-800 mb-2">📊 Formel-Vorschau (pro kWh geladen):</h4>
              <div className="text-sm text-blue-700 space-y-1">
                <div>• Kraftstoffeinsparung: {formData.settings.fuelSavingsFormula} Liter</div>
                <div>• CO₂-Einsparung Strom: {formData.settings.co2SavingsFormula.electricityGridFactor} kg</div>
                <div>• CO₂-Einsparung Benzin: {(formData.settings.co2SavingsFormula.efficiencyFactor * formData.settings.co2SavingsFormula.gasolineEmissionFactor).toFixed(3)} kg</div>
                <div className="font-medium">• Gesamt CO₂-Einsparung: {(formData.settings.co2SavingsFormula.electricityGridFactor + formData.settings.co2SavingsFormula.efficiencyFactor * formData.settings.co2SavingsFormula.gasolineEmissionFactor).toFixed(3)} kg</div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-4 pt-6 border-t">
            <button
              type="button"
              onClick={() => router.back()}
              className="flex-1 px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors flex items-center justify-center gap-2"
              disabled={saving}
            >
              <FaTimes />
              Abbrechen
            </button>
            <button
              type="submit"
              disabled={saving || !formData.name}
              className="flex-1 px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors flex items-center justify-center gap-2"
            >
              {saving ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  Speichere...
                </>
              ) : (
                <>
                  <FaSave />
                  Änderungen speichern
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
