"use client";

import React, { useEffect, useState } from 'react';
import { useParams } from 'next/navigation';
import SolarDashboard from '../components/SolarDashboard';
import { SolarDashboardData, SolarDashboardError } from '../types';

export default function SolarDashboardPage() {
  const params = useParams();
  const dashboardId = params.id as string;

  const [dashboardData, setDashboardData] = useState<SolarDashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);
        setError(null);

        const response = await fetch(`/api/solar-dashboard/${dashboardId}/data`);

        if (!response.ok) {
          if (response.status === 404) {
            setError('Dashboard nicht gefunden');
          } else if (response.status === 403) {
            setError('Zugriff verweigert oder Dashboard nicht aktiv');
          } else if (response.status === 503) {
            // Handle service unavailable (no data available)
            const errorData = await response.json();
            setError(errorData.error || 'Keine Daten verfügbar');
          } else {
            setError('Fehler beim Laden der Dashboard-Daten');
          }
          return;
        }

        const data = await response.json();

        // Check if the response contains an error (shouldn't happen with 200 status, but just in case)
        if (data.error) {
          setError(data.error);
          return;
        }

        setDashboardData(data);
      } catch (err) {
        console.error('Error fetching dashboard data:', err);
        setError('Netzwerkfehler beim Laden der Daten');
      } finally {
        setLoading(false);
      }
    };

    if (dashboardId) {
      fetchDashboardData();

      // Set up auto-refresh every 5 minutes (300 seconds)
      const interval = setInterval(fetchDashboardData, 300000);

      return () => clearInterval(interval);
    }
  }, [dashboardId]);

  if (loading) {
    return (
      <div className="h-screen bg-gradient-to-br from-gray-50 to-gray-200 flex items-center justify-center overflow-hidden">
        <div className="text-center">
          <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-green-600 mx-auto mb-4"></div>
          <p className="text-gray-600 text-lg">Dashboard wird geladen...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="h-screen bg-gradient-to-br from-gray-50 to-gray-200 flex items-center justify-center overflow-hidden">
        <div className="text-center bg-white rounded-2xl p-8 shadow-lg max-w-md">
          <div className="text-red-500 text-6xl mb-4">⚠️</div>
          <h2 className="text-2xl font-bold text-gray-800 mb-4">Fehler</h2>
          <p className="text-gray-600 mb-6">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors"
          >
            Erneut versuchen
          </button>
        </div>
      </div>
    );
  }

  if (!dashboardData) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-200 flex items-center justify-center">
        <div className="text-center bg-white rounded-2xl p-8 shadow-lg max-w-md">
          <div className="text-gray-400 text-6xl mb-4">📊</div>
          <h2 className="text-2xl font-bold text-gray-800 mb-4">Keine Daten</h2>
          <p className="text-gray-600">Dashboard-Daten konnten nicht geladen werden.</p>
        </div>
      </div>
    );
  }

  return <SolarDashboard data={dashboardData} />;
}
