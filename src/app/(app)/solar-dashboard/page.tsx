"use client";

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { SolarDashboardListItem } from './types';
import { FaPlus, FaEye, FaCog, FaToggleOn, FaToggleOff, FaGlobe, FaLock, FaCopy, FaTrash, FaExternalLinkAlt } from 'react-icons/fa';

export default function SolarDashboardManagementPage() {
  const router = useRouter();
  const [dashboards, setDashboards] = useState<SolarDashboardListItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [actionLoading, setActionLoading] = useState<string | null>(null);

  useEffect(() => {
    fetchDashboards();
  }, []);

  const fetchDashboards = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/solar-dashboard');

      if (!response.ok) {
        if (response.status === 401) {
          setError('Nicht autorisiert');
        } else {
          setError('<PERSON><PERSON> beim <PERSON>den der Dashboards');
        }
        return;
      }

      const data = await response.json();
      setDashboards(data);
    } catch (err) {
      console.error('Error fetching dashboards:', err);
      setError('Netzwerkfehler beim Laden der Dashboards');
    } finally {
      setLoading(false);
    }
  };

  const toggleDashboardActive = async (dashboardId: string, isActive: boolean) => {
    setActionLoading(dashboardId);
    try {
      const response = await fetch(`/api/solar-dashboard/${dashboardId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ isActive: !isActive }),
      });

      if (response.ok) {
        await fetchDashboards(); // Refresh the list
      } else {
        const errorData = await response.json();
        setError(errorData.error || 'Fehler beim Aktualisieren des Dashboard-Status');
      }
    } catch (err) {
      console.error('Error toggling dashboard status:', err);
      setError('Netzwerkfehler beim Aktualisieren des Status');
    } finally {
      setActionLoading(null);
    }
  };

  const toggleDashboardPublic = async (dashboardId: string, isPublic: boolean) => {
    setActionLoading(dashboardId);
    try {
      const response = await fetch(`/api/solar-dashboard/${dashboardId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ isPublic: !isPublic }),
      });

      if (response.ok) {
        await fetchDashboards(); // Refresh the list
      } else {
        const errorData = await response.json();
        setError(errorData.error || 'Fehler beim Aktualisieren der Öffentlichkeits-Einstellung');
      }
    } catch (err) {
      console.error('Error toggling dashboard public status:', err);
      setError('Netzwerkfehler beim Aktualisieren der Einstellung');
    } finally {
      setActionLoading(null);
    }
  };

  const deleteDashboard = async (dashboardId: string, dashboardName: string) => {
    if (!confirm(`Sind Sie sicher, dass Sie das Dashboard "${dashboardName}" löschen möchten? Diese Aktion kann nicht rückgängig gemacht werden.`)) {
      return;
    }

    setActionLoading(dashboardId);
    try {
      const response = await fetch(`/api/solar-dashboard/${dashboardId}`, {
        method: 'DELETE',
      });

      if (response.ok) {
        await fetchDashboards(); // Refresh the list
      } else {
        const errorData = await response.json();
        setError(errorData.error || 'Fehler beim Löschen des Dashboards');
      }
    } catch (err) {
      console.error('Error deleting dashboard:', err);
      setError('Netzwerkfehler beim Löschen des Dashboards');
    } finally {
      setActionLoading(null);
    }
  };

  const copyPublicLink = async (dashboardId: string, includeToken: boolean = false) => {
    try {
      // Generate truly public URL (no token required)
      const publicUrl = `${window.location.origin}/public-solar/${dashboardId}`;
      await navigator.clipboard.writeText(publicUrl);

      // Show temporary success message
      const button = document.getElementById(`copy-btn-${dashboardId}`);
      if (button) {
        const originalText = button.innerHTML;
        button.innerHTML = '✅ Kopiert!';
        setTimeout(() => {
          button.innerHTML = originalText;
        }, 2000);
      }
    } catch (err) {
      console.error('Error copying link:', err);
      alert('Fehler beim Kopieren des Links');
    }
  };

  const openPublicLink = (dashboardId: string) => {
    // Generate truly public URL (no token required)
    const publicUrl = `${window.location.origin}/public-solar/${dashboardId}`;
    // Open in new tab
    window.open(publicUrl, '_blank', 'noopener,noreferrer');
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Dashboards werden geladen...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-8">
        <div className="flex items-center gap-4">
          <div className="w-12 h-12 bg-green-600 rounded-lg flex items-center justify-center text-white text-2xl">
            🌞
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-800">Solar Dashboards</h1>
            <p className="text-gray-600 mt-1">Verwalten Sie Ihre Solar-Energie Dashboards</p>
          </div>
        </div>
        <button
          onClick={() => router.push('/solar-dashboard/create')}
          className="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors flex items-center gap-2 shadow-lg"
        >
          <FaPlus />
          Neues Dashboard
        </button>
      </div>

      {/* Error Message */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <span className="text-red-500 text-xl">⚠️</span>
              <p className="text-red-700">{error}</p>
            </div>
            <button
              onClick={() => setError(null)}
              className="text-red-500 hover:text-red-700"
            >
              ✕
            </button>
          </div>
        </div>
      )}

      {/* Dashboard List */}
      {dashboards.length === 0 ? (
        <div className="text-center bg-white rounded-lg p-12 shadow-lg">
          <div className="text-gray-400 text-6xl mb-4">🌞</div>
          <h2 className="text-2xl font-bold text-gray-800 mb-4">Keine Dashboards</h2>
          <p className="text-gray-600 mb-6">
            Sie haben noch keine Solar Dashboards erstellt. Erstellen Sie Ihr erstes Dashboard für einen Standort.
          </p>
          <button
            onClick={() => router.push('/solar-dashboard/create')}
            className="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors flex items-center gap-2 mx-auto"
          >
            <FaPlus />
            Erstes Dashboard erstellen
          </button>
        </div>
      ) : (
        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {dashboards.map((dashboard) => {
            const isLoading = actionLoading === dashboard.id;

            return (
              <div key={dashboard.id} className="bg-white rounded-lg shadow-lg hover:shadow-xl transition-shadow duration-200 overflow-hidden relative">
                {/* Header */}
                <div className="p-6 border-b border-gray-100">
                  <div className="flex justify-between items-start mb-3">
                    <div className="flex-1">
                      <h3 className="text-xl font-bold text-gray-800 mb-1">{dashboard.name}</h3>
                      {dashboard.description && (
                        <p className="text-gray-600 text-sm mb-3">{dashboard.description}</p>
                      )}
                    </div>
                    <div className="flex flex-col gap-2 ml-4">
                      <span className={`px-3 py-1 rounded-full text-xs font-semibold whitespace-nowrap ${
                        dashboard.isActive
                          ? 'bg-green-100 text-green-800'
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        {dashboard.isActive ? '✅ Aktiv' : '⏸️ Inaktiv'}
                      </span>
                      {dashboard.isPublic && (
                        <span className="px-3 py-1 rounded-full text-xs font-semibold bg-blue-100 text-blue-800 whitespace-nowrap">
                          🌐 Öffentlich
                        </span>
                      )}
                    </div>
                  </div>

                  {/* Location Info */}
                  <div className="text-sm text-gray-500 space-y-1">
                    <p className="flex items-center gap-2">
                      <span>📍</span>
                      <span>{dashboard.location.name}, {dashboard.location.city}</span>
                    </p>
                    <p className="flex items-center gap-2">
                      <span>🏢</span>
                      <span>{dashboard.ou.name}</span>
                    </p>
                    <p className="flex items-center gap-2">
                      <span>📅</span>
                      <span>Erstellt: {new Date(dashboard.createdAt).toLocaleDateString('de-DE')}</span>
                    </p>
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="p-6 bg-gray-50">
                  <div className="grid grid-cols-2 gap-3 mb-3">
                    {/* View Dashboard */}
                    <button
                      onClick={() => router.push(`/solar-dashboard/${dashboard.id}`)}
                      disabled={isLoading}
                      className="flex items-center justify-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 disabled:bg-gray-400 transition-colors text-sm font-medium"
                    >
                      <FaEye />
                      Anzeigen
                    </button>

                    {/* Edit Dashboard */}
                    <button
                      onClick={() => router.push(`/solar-dashboard/${dashboard.id}/edit`)}
                      disabled={isLoading}
                      className="flex items-center justify-center gap-2 bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 disabled:bg-gray-400 transition-colors text-sm font-medium"
                    >
                      <FaCog />
                      Bearbeiten
                    </button>
                  </div>

                  <div className="grid grid-cols-2 gap-3 mb-3">
                    {/* Toggle Active */}
                    <button
                      onClick={() => toggleDashboardActive(dashboard.id, dashboard.isActive)}
                      disabled={isLoading}
                      className={`flex items-center justify-center gap-2 px-4 py-2 rounded-lg transition-colors text-sm font-medium ${
                        dashboard.isActive
                          ? 'bg-red-100 text-red-700 hover:bg-red-200'
                          : 'bg-green-100 text-green-700 hover:bg-green-200'
                      } disabled:bg-gray-200 disabled:text-gray-500`}
                    >
                      {dashboard.isActive ? <FaToggleOff /> : <FaToggleOn />}
                      {dashboard.isActive ? 'Deaktivieren' : 'Aktivieren'}
                    </button>

                    {/* Toggle Public */}
                    <button
                      onClick={() => toggleDashboardPublic(dashboard.id, dashboard.isPublic)}
                      disabled={isLoading}
                      className={`flex items-center justify-center gap-2 px-4 py-2 rounded-lg transition-colors text-sm font-medium ${
                        dashboard.isPublic
                          ? 'bg-orange-100 text-orange-700 hover:bg-orange-200'
                          : 'bg-blue-100 text-blue-700 hover:bg-blue-200'
                      } disabled:bg-gray-200 disabled:text-gray-500`}
                    >
                      {dashboard.isPublic ? <FaLock /> : <FaGlobe />}
                      {dashboard.isPublic ? 'Privat' : 'Öffentlich'}
                    </button>
                  </div>

                  {/* Delete Button */}
                  <button
                    onClick={() => deleteDashboard(dashboard.id, dashboard.name)}
                    disabled={isLoading}
                    className="w-full flex items-center justify-center gap-2 bg-red-100 text-red-700 px-4 py-2 rounded-lg hover:bg-red-200 disabled:bg-gray-200 disabled:text-gray-500 transition-colors text-sm font-medium"
                  >
                    <FaTrash />
                    Dashboard löschen
                  </button>
                </div>

                {/* Public Link Section */}
                {dashboard.isPublic && (
                  <div className="p-4 bg-green-50 border-t border-green-100">
                    <p className="text-sm font-medium text-green-800 mb-2">🌐 Öffentlicher Link:</p>
                    <div className="flex gap-2">
                      <input
                        type="text"
                        value={`${window.location.origin}/public-solar/${dashboard.id}`}
                        readOnly
                        className="flex-1 text-sm bg-white border border-green-200 rounded px-3 py-2 text-gray-600"
                      />
                      <button
                        id={`copy-btn-${dashboard.id}`}
                        onClick={() => copyPublicLink(dashboard.id)}
                        className="bg-green-600 text-white px-3 py-2 rounded hover:bg-green-700 transition-colors flex items-center gap-1"
                        title="Link kopieren"
                      >
                        <FaCopy />
                      </button>
                      <button
                        onClick={() => openPublicLink(dashboard.id)}
                        className="bg-blue-600 text-white px-3 py-2 rounded hover:bg-blue-700 transition-colors flex items-center gap-1"
                        title="In neuem Tab öffnen"
                      >
                        <FaExternalLinkAlt />
                      </button>
                    </div>
                    <p className="text-xs text-green-600 mt-2">
                      ✅ Dieser Link ist wirklich öffentlich - kein Token erforderlich!
                    </p>
                  </div>
                )}

                {/* Loading Overlay */}
                {isLoading && (
                  <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center">
                    <div className="flex items-center gap-2 text-gray-600">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-green-600"></div>
                      <span className="text-sm">Wird aktualisiert...</span>
                    </div>
                  </div>
                )}
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
}
