# Solar Dashboard Implementation

## 🌞 Übersicht

Das Solar Dashboard ist eine vollständige Implementierung zur Anzeige von Solar-Energie-Daten basierend auf dem HTML-Beispiel in `example/deme_solar_dashboard.html`. Es bietet sowohl private als auch öffentliche Zugriffsmöglichkeiten auf Solar-Dashboards.

## 📁 Dateistruktur

```
src/app/(app)/solar-dashboard/
├── components/
│   └── SolarDashboard.tsx          # Haupt-Dashboard-Komponente
├── utils/
│   ├── dummyData.ts                # Dummy-Daten Generator
│   └── tokens.ts                   # Token-Utilities für öffentliche URLs
├── types.ts                        # TypeScript-Typen
├── page.tsx                        # Dashboard-Management-Seite
├── [id]/
│   └── page.tsx                    # Einzelnes Dashboard (privat)
└── README.md                       # Diese Dokumentation

src/app/public/solar-dashboard/
└── [id]/
    └── page.tsx                    # Öffentliches Dashboard

src/app/api/solar-dashboard/
├── route.ts                        # Dashboard CRUD API
├── [id]/
│   ├── route.ts                    # Einzelnes Dashboard API
│   └── data/
│       └── route.ts                # Dashboard-Daten API
```

## 🗄️ Datenbank Schema

Das neue `SolarDashboard` Model wurde zu `prisma/schema.prisma` hinzugefügt:

```prisma
model SolarDashboard {
  id                String    @id @default(cuid())
  name              String
  description       String?   @db.Text
  ou                Ou        @relation(fields: [ouId], references: [id])
  ouId              String
  location          Location  @relation(fields: [locationId], references: [id])
  locationId        String
  isActive          Boolean   @default(false)
  isPublic          Boolean   @default(false)
  publicToken       String?   @unique
  solarEdgeApiKey   String?
  solarEdgeSiteId   String?
  settings          Json?     // Dashboard-spezifische Einstellungen
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt

  @@unique([ouId, locationId]) // Ein Dashboard pro OU-Location Kombination
}
```

## 🚀 Setup & Installation

### 1. Datenbank Migration

```bash
npx prisma db push
```

### 2. Dependencies

Alle notwendigen Dependencies sind bereits in der bestehenden Next.js-App vorhanden:
- React 18+
- Next.js 14+
- Prisma
- NextAuth.js
- Tailwind CSS

## 📊 Features

### ✅ Implementierte Features

1. **OU-basierte Struktur**
   - Jedes Dashboard ist einer Organizational Unit zugeordnet
   - Jedes Dashboard ist an einen spezifischen Standort gebunden
   - Eine OU kann mehrere Dashboards haben (verschiedene Standorte)

2. **Dashboard-Aktivierung**
   - Dashboards können aktiviert/deaktiviert werden
   - Nur aktive Dashboards sind zugänglich

3. **Public URL Funktionalität**
   - Generierung öffentlicher URLs mit sicheren Tokens
   - Zugriff ohne Login-Anforderung
   - Token-basierte Authentifizierung

4. **Dashboard-Komponenten**
   - Aktuelle Leistung/Power Flow mit animierter Visualisierung
   - Energieproduktion (heute/Monat/Jahr/Gesamt)
   - Umweltdaten (CO2-Einsparung, Baumäquivalent)
   - Eigenverbrauchsstatistiken
   - Wetterdaten-Integration
   - Responsive Design

5. **Dummy-Daten System**
   - Realistische Testdaten basierend auf Tageszeit, Saison und Wetter
   - Konfigurierbare Parameter (Peak Power, Charging, etc.)
   - Automatische Aktualisierung alle 30 Sekunden

### 🔄 API Endpoints

#### Dashboard Management
- `GET /api/solar-dashboard` - Liste aller Dashboards der User-OU
- `POST /api/solar-dashboard` - Neues Dashboard erstellen
- `GET /api/solar-dashboard/[id]` - Einzelnes Dashboard abrufen
- `PUT /api/solar-dashboard/[id]` - Dashboard aktualisieren
- `DELETE /api/solar-dashboard/[id]` - Dashboard löschen

#### Dashboard Data
- `GET /api/solar-dashboard/[id]/data` - Dashboard-Daten (privat)
- `GET /api/solar-dashboard/[id]/data?token=xxx` - Dashboard-Daten (öffentlich)
- `POST /api/solar-dashboard/[id]/data/refresh` - Daten-Refresh erzwingen

## 🔐 Sicherheit & Zugriffskontrolle

### Private Dashboards
- NextAuth.js Session-basierte Authentifizierung
- OU-basierte Zugriffskontrolle
- Nur Benutzer der gleichen OU können Dashboards verwalten

### Öffentliche Dashboards
- Sichere 64-Zeichen Hex-Tokens
- Token-basierte Authentifizierung ohne Session
- Nur aktive und als öffentlich markierte Dashboards zugänglich
- Keine Exposition sensibler Daten (API-Keys, etc.)

## 🎨 UI/UX Features

### Dashboard-Visualisierung
- **Animierte Energiefluss-Darstellung** mit Canvas
- **Responsive Design** für Desktop und Mobile
- **Echtzeit-Updates** alle 30 Sekunden
- **Farbkodierte Energieknoten** (PV, Büro, Ladestationen, Netz)
- **Interaktive Partikel-Animation** für Energiefluss

### Management-Interface
- **Dashboard-Übersicht** mit Status-Anzeigen
- **Ein-Klick Aktivierung/Deaktivierung**
- **Öffentliche URL-Generierung** und -Verwaltung
- **Responsive Karten-Layout**

## 🔧 Konfiguration

### Dashboard Settings (JSON)
```typescript
{
  refreshInterval?: number;     // Aktualisierungsintervall in Sekunden
  showWeather?: boolean;        // Wetterdaten anzeigen
  showCharging?: boolean;       // Ladestationen anzeigen
  theme?: 'light' | 'dark';     // Theme (aktuell nur light)
  units?: 'metric' | 'imperial'; // Einheiten
  language?: 'de' | 'en';       // Sprache
}
```

## 🚧 Nächste Schritte

### Geplante Erweiterungen

1. **SolarEdge API Integration**
   - Echte Daten statt Dummy-Daten
   - API-Key Validierung
   - Fehlerbehandlung für API-Ausfälle

2. **Dashboard-Erstellung UI**
   - Formular für neue Dashboards
   - Location-Auswahl
   - SolarEdge-Konfiguration

3. **Erweiterte Visualisierungen**
   - Historische Charts
   - Vergleichsdiagramme
   - Export-Funktionen

4. **Benachrichtigungen**
   - E-Mail-Alerts bei Problemen
   - Performance-Berichte
   - Wartungserinnerungen

## 📝 Verwendung

### Dashboard erstellen
1. Navigieren Sie zu `/solar-dashboard`
2. Klicken Sie auf "Neues Dashboard"
3. Füllen Sie die erforderlichen Felder aus
4. Aktivieren Sie das Dashboard
5. Optional: Machen Sie es öffentlich zugänglich

### Öffentlichen Link teilen
1. Dashboard als öffentlich markieren
2. Öffentlichen Link kopieren
3. Link teilen (funktioniert ohne Login)

### Dashboard anzeigen
- **Privat**: `/solar-dashboard/[id]`
- **Öffentlich**: `/public/solar-dashboard/[id]?token=xxx`

## 🐛 Bekannte Limitierungen

1. **Dummy-Daten**: Aktuell werden nur Testdaten verwendet
2. **Keine Persistierung**: Dashboard-Daten werden nicht gespeichert
3. **Basis-Styling**: Kann weiter angepasst werden
4. **Keine Benutzer-Präferenzen**: Theme/Sprache sind fest kodiert

## 🤝 Integration

Das Solar Dashboard ist vollständig in die bestehende App integriert:
- Verwendet bestehende Authentifizierung
- Folgt etablierte Coding-Standards
- Nutzt vorhandene UI-Komponenten-Struktur
- Kompatibel mit bestehender OU/Location-Hierarchie
