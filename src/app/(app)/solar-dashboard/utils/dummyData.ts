import { SolarDashboardData, DummyDataOptions } from '../types';

export function generateDummyData(options: DummyDataOptions = {}): SolarDashboardData {
  const {
    peakPower = 52.29,
    timeOfDay = getCurrentTimeOfDay(),
    season = getCurrentSeason(),
    weather = 'sunny',
    hasCharging = true,
    installationDate = new Date('2023-01-15')
  } = options;

  // Calculate solar production based on time and weather
  const solarMultiplier = getSolarMultiplier(timeOfDay, season, weather);
  const currentPV = Math.max(0, peakPower * solarMultiplier + (Math.random() - 0.5) * 2);
  
  // Office consumption (varies by time of day)
  const officeMultiplier = getOfficeMultiplier(timeOfDay);
  const currentConsumption = 3 + officeMultiplier * 8 + (Math.random() - 0.5) * 1;
  
  // EV charging (random but realistic)
  const currentCharging = hasCharging ? Math.random() * 15 : 0;
  
  // Grid calculation (positive = import, negative = export)
  const totalConsumption = currentConsumption + currentCharging;
  const gridFlow = totalConsumption - currentPV;
  
  // Calculate daily/monthly/yearly production
  const daysSinceInstallation = Math.floor((Date.now() - installationDate.getTime()) / (1000 * 60 * 60 * 24));
  const monthsSinceInstallation = Math.floor(daysSinceInstallation / 30);
  const yearsSinceInstallation = Math.floor(daysSinceInstallation / 365);
  
  // Realistic production values
  const dailyAverage = peakPower * 4.5; // 4.5 hours equivalent full sun per day
  const todayProduction = dailyAverage * solarMultiplier * 2 + Math.random() * 10;
  const monthProduction = dailyAverage * 30 * 0.8 + Math.random() * 100;
  const yearProduction = dailyAverage * 365 * 0.75 + Math.random() * 1000;
  const totalProduction = dailyAverage * daysSinceInstallation * 0.7;
  
  // Self consumption calculations
  const selfConsumptionToday = Math.min(95, 60 + Math.random() * 25);
  const selfConsumptionMonth = Math.min(95, 55 + Math.random() * 25);
  const selfConsumptionYear = Math.min(95, 50 + Math.random() * 25);
  const selfConsumptionTotal = Math.min(95, 45 + Math.random() * 25);
  
  // CO2 savings (0.4 kg CO2 per kWh)
  const co2Factor = 0.4;
  const co2SavedToday = todayProduction * co2Factor;
  const co2SavedMonth = monthProduction * co2Factor;
  const co2SavedYear = yearProduction * co2Factor;
  const co2SavedTotal = totalProduction * co2Factor / 1000; // in tons
  
  // Weather data
  const temperature = getTemperature(season, weather);
  
  return {
    id: 'dummy-dashboard-1',
    name: 'Nordsee Nassbagger- und Tiefbau GmbH',
    description: 'Solar Dashboard für Hauptstandort Bremen',
    ouId: 'dummy-ou-1',
    locationId: 'dummy-location-1',
    isActive: true,
    isPublic: false,
    solarEdgeApiKey: 'U8N36XURRU010QDNTW4P8FN83G2XW6VQ',
    solarEdgeSiteId: '4537334',
    
    currentPower: {
      pv: Math.round(currentPV * 10) / 10,
      consumption: Math.round(currentConsumption * 10) / 10,
      charging: hasCharging ? Math.round(currentCharging * 10) / 10 : undefined,
      grid: Math.round(gridFlow * 10) / 10
    },
    
    energy: {
      todayProduction: Math.round(todayProduction),
      monthProduction: Math.round(monthProduction),
      yearProduction: Math.round(yearProduction),
      totalProduction: Math.round(totalProduction)
    },
    
    efficiency: {
      selfConsumptionToday: Math.round(selfConsumptionToday),
      selfConsumptionMonth: Math.round(selfConsumptionMonth),
      selfConsumptionYear: Math.round(selfConsumptionYear),
      selfConsumptionTotal: Math.round(selfConsumptionTotal),
      systemEfficiency: Math.round(85 + Math.random() * 10)
    },
    
    environmental: {
      co2SavedToday: Math.round(co2SavedToday * 10) / 10,
      co2SavedMonth: Math.round(co2SavedMonth),
      co2SavedYear: Math.round(co2SavedYear),
      co2SavedTotal: Math.round(co2SavedTotal * 10) / 10,
      treesEquivalent: Math.round(co2SavedTotal * 50) // rough estimate
    },
    
    weather: {
      temperature: Math.round(temperature),
      irradiance: weather === 'sunny' ? 800 + Math.random() * 200 : 
                  weather === 'cloudy' ? 300 + Math.random() * 300 : 
                  100 + Math.random() * 200,
      cloudCover: weather === 'sunny' ? Math.random() * 20 : 
                  weather === 'cloudy' ? 50 + Math.random() * 30 : 
                  80 + Math.random() * 20
    },
    
    lastUpdate: Math.floor(Math.random() * 30), // 0-30 seconds ago
    createdAt: installationDate,
    updatedAt: new Date()
  };
}

function getCurrentTimeOfDay(): 'morning' | 'noon' | 'afternoon' | 'evening' | 'night' {
  const hour = new Date().getHours();
  if (hour >= 6 && hour < 10) return 'morning';
  if (hour >= 10 && hour < 14) return 'noon';
  if (hour >= 14 && hour < 18) return 'afternoon';
  if (hour >= 18 && hour < 22) return 'evening';
  return 'night';
}

function getCurrentSeason(): 'spring' | 'summer' | 'autumn' | 'winter' {
  const month = new Date().getMonth();
  if (month >= 2 && month <= 4) return 'spring';
  if (month >= 5 && month <= 7) return 'summer';
  if (month >= 8 && month <= 10) return 'autumn';
  return 'winter';
}

function getSolarMultiplier(
  timeOfDay: string, 
  season: string, 
  weather: string
): number {
  let multiplier = 0;
  
  // Base multiplier by time of day
  switch (timeOfDay) {
    case 'morning': multiplier = 0.3; break;
    case 'noon': multiplier = 0.9; break;
    case 'afternoon': multiplier = 0.7; break;
    case 'evening': multiplier = 0.2; break;
    case 'night': multiplier = 0; break;
  }
  
  // Season adjustment
  switch (season) {
    case 'spring': multiplier *= 0.8; break;
    case 'summer': multiplier *= 1.0; break;
    case 'autumn': multiplier *= 0.6; break;
    case 'winter': multiplier *= 0.4; break;
  }
  
  // Weather adjustment
  switch (weather) {
    case 'sunny': multiplier *= 1.0; break;
    case 'cloudy': multiplier *= 0.5; break;
    case 'rainy': multiplier *= 0.2; break;
  }
  
  return Math.max(0, multiplier);
}

function getOfficeMultiplier(timeOfDay: string): number {
  switch (timeOfDay) {
    case 'morning': return 0.7;
    case 'noon': return 1.0;
    case 'afternoon': return 0.9;
    case 'evening': return 0.3;
    case 'night': return 0.1;
    default: return 0.5;
  }
}

function getTemperature(season: string, weather: string): number {
  let baseTemp = 15;
  
  switch (season) {
    case 'spring': baseTemp = 15; break;
    case 'summer': baseTemp = 25; break;
    case 'autumn': baseTemp = 12; break;
    case 'winter': baseTemp = 5; break;
  }
  
  switch (weather) {
    case 'sunny': baseTemp += 3; break;
    case 'cloudy': baseTemp += 0; break;
    case 'rainy': baseTemp -= 2; break;
  }
  
  return baseTemp + (Math.random() - 0.5) * 6;
}

// Generate multiple dashboard configs for testing
export function generateDummyDashboards(count: number = 3): SolarDashboardData[] {
  const dashboards: SolarDashboardData[] = [];
  
  for (let i = 0; i < count; i++) {
    const options: DummyDataOptions = {
      peakPower: 30 + Math.random() * 50,
      hasCharging: Math.random() > 0.3,
      installationDate: new Date(2022 + Math.floor(Math.random() * 2), Math.floor(Math.random() * 12), 1)
    };
    
    const dashboard = generateDummyData(options);
    dashboard.id = `dummy-dashboard-${i + 1}`;
    dashboard.name = `Solar Dashboard ${i + 1}`;
    dashboard.ouId = `dummy-ou-${i + 1}`;
    dashboard.locationId = `dummy-location-${i + 1}`;
    
    dashboards.push(dashboard);
  }
  
  return dashboards;
}
