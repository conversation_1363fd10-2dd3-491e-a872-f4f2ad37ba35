import { randomBytes } from 'crypto';

/**
 * Generate a secure public token for dashboard access
 */
export function generatePublicToken(): string {
  return randomBytes(32).toString('hex');
}

/**
 * Generate a public URL for a dashboard
 * @param dashboardId - The dashboard ID
 * @param publicToken - Optional token for additional security
 * @param includeToken - Whether to include the token in the URL (default: false for truly public access)
 */
export function generatePublicUrl(dashboardId: string, publicToken?: string, includeToken: boolean = false): string {
  const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
  const basePublicUrl = `${baseUrl}/public-solar/${dashboardId}`;

  if (includeToken && publicToken) {
    return `${basePublicUrl}?token=${publicToken}`;
  }

  return basePublicUrl;
}

/**
 * Generate a secure URL with token (for backwards compatibility)
 */
export function generateSecurePublicUrl(dashboardId: string, publicToken: string): string {
  return generatePublicUrl(dashboardId, publicToken, true);
}

/**
 * Validate a public token format
 */
export function isValidTokenFormat(token: string): boolean {
  return /^[a-f0-9]{64}$/.test(token);
}
