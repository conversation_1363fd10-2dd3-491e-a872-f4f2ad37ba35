/* Solar Dashboard responsive styles */
.dashboard {
  height: 100vh;
  height: 100dvh; /* Dynamic viewport height for mobile */
  overflow: hidden;
}

.dashboardContainer {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.energyFlowContainer {
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 0; /* Allow flex shrinking */
}

.energyNode {
  position: absolute;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  color: white;
  font-weight: bold;
  text-align: center;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  border: 2px solid white;
  z-index: 20;
  /* Removed hover effects for info screen display */
}

/* Responsive node sizes */
@media (max-width: 640px) {
  .energyNode {
    border-width: 2px;
  }
}

@media (min-width: 1024px) {
  .energyNode {
    border-width: 4px;
  }
}

/* Node positioning */
.pvNode {
  top: 8%;
  left: 50%;
  transform: translateX(-50%);
  background: linear-gradient(135deg, #fb923c, #ea580c);
}

.centralNode {
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: linear-gradient(135deg, #14b8a6, #059669);
  z-index: 30;
}

.officeNode {
  top: 50%;
  left: 8%;
  transform: translateY(-50%);
  background: linear-gradient(135deg, #1e3a8a, #1d4ed8);
}

.chargingNode {
  top: 50%;
  right: 8%;
  transform: translateY(-50%);
  background: linear-gradient(135deg, #059669, #10b981);
}

.gridNode {
  bottom: 8%;
  left: 50%;
  transform: translateX(-50%);
  background: linear-gradient(135deg, #4b5563, #6b7280);
}

/* Grid node with dark red background for feed-in (positive values) */
.gridNodeFeedIn {
  background: linear-gradient(135deg, #991b1b, #dc2626) !important;
}

/* Grid power flow arrows */
.gridArrow {
  position: absolute;
  top: -8px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 16px;
  font-weight: bold;
  z-index: 25;
}

.gridArrowFeedIn {
  color: #ffffff; /* White for better visibility on red background */
}

.gridArrowConsumption {
  color: #f59e0b; /* Orange for consumption */
}

/* Responsive positioning adjustments */
@media (min-width: 640px) {
  .officeNode {
    left: 12%;
  }

  .chargingNode {
    right: 12%;
  }
}

@media (min-width: 1024px) {
  .officeNode {
    left: 15%;
  }

  .chargingNode {
    right: 15%;
  }
}

/* Sidebar scrolling */
.sidebar {
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: #d1d5db #f3f4f6;
}

.sidebar::-webkit-scrollbar {
  width: 4px;
}

.sidebar::-webkit-scrollbar-track {
  background: #f3f4f6;
  border-radius: 2px;
}

.sidebar::-webkit-scrollbar-thumb {
  background: #d1d5db;
  border-radius: 2px;
}

.sidebar::-webkit-scrollbar-thumb:hover {
  background: #9ca3af;
}

/* Animation optimizations */
.energyCanvas {
  position: absolute;
  inset: 0;
  width: 100%;
  height: 100%;
  z-index: 10;
  pointer-events: none;
}

/* Sidebar styling for info screen optimization */
.sidebar {
  width: 100%;
  max-height: 100vh;
  overflow: hidden; /* Prevent scrolling for info screen */
  /* Remove fixed widths to allow grid system to work */
}

/* Prevent text selection on dashboard elements */
.dashboard * {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* Ensure proper aspect ratio maintenance */
@media (max-aspect-ratio: 1/1) {
  /* Portrait mode adjustments */
  .energyFlowContainer {
    padding: 1rem;
  }
}

@media (min-aspect-ratio: 2/1) {
  /* Wide screen adjustments */
  .energyFlowContainer {
    padding: 2rem;
  }
}
