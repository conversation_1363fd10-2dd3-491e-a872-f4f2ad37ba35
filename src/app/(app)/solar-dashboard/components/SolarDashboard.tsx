"use client";

import React, { useEffect, useRef, useState } from "react";
import { SolarDashboardData } from "../types";
import styles from "./SolarDashboard.module.css";
import SunVisualization from "./SunVisualization";

interface SolarDashboardProps {
  data: SolarDashboardData;
  isPublic?: boolean;
}

const SolarDashboard: React.FC<SolarDashboardProps> = ({ data, isPublic = false }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const [timestamp, setTimestamp] = useState(new Date());
  const animationFrameRef = useRef(0);

  // Update timestamp every second
  useEffect(() => {
    const interval = setInterval(() => {
      setTimestamp(new Date());
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  // Canvas animation for energy flow
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    const resizeCanvas = () => {
      const container = canvas.parentElement;
      if (container) {
        const rect = container.getBoundingClientRect();
        canvas.width = rect.width;
        canvas.height = rect.height;

        // Set canvas style to match container
        canvas.style.width = rect.width + "px";
        canvas.style.height = rect.height + "px";
      }
    };

    const drawFlowLine = (
      x1: number,
      y1: number,
      x2: number,
      y2: number,
      power: number,
      offset = 0,
    ) => {
      const gradient = ctx.createLinearGradient(x1, y1, x2, y2);
      gradient.addColorStop(0, "#00A651");
      gradient.addColorStop(1, "#4CAF50");

      ctx.strokeStyle = gradient;
      // Thinner lines for minimal power flow, thicker for significant flow
      ctx.lineWidth = Math.abs(power) < 0.1 ? 1 : Math.max(2, Math.abs(power) * 0.5);
      ctx.lineCap = "round";

      ctx.beginPath();
      ctx.moveTo(x1, y1);
      ctx.lineTo(x2, y2);
      ctx.stroke();

      // Animated flow particles
      drawFlowParticles(x1, y1, x2, y2, power, offset);

      // Power label
      const midX = (x1 + x2) / 2;
      const midY = (y1 + y2) / 2;
      drawPowerLabel(midX, midY, power);
    };

    const drawFlowParticles = (
      x1: number,
      y1: number,
      x2: number,
      y2: number,
      power: number,
      offset: number,
    ) => {
      // Only show animation for meaningful power flow (≥ 0.1 kW)
      if (Math.abs(power) < 0.1) {
        return; // No animation for minimal or zero power flow
      }

      const numParticles = Math.max(2, Math.floor(Math.abs(power) / 2));
      const dx = x2 - x1;
      const dy = y2 - y1;

      for (let i = 0; i < numParticles; i++) {
        const progress = (animationFrameRef.current * 0.008 + i * 0.3 + offset) % 1; // Slower animation
        const x = x1 + dx * progress;
        const y = y1 + dy * progress;

        const opacity = Math.sin(progress * Math.PI);
        ctx.fillStyle = `rgba(255, 255, 255, ${opacity * 0.8})`;
        ctx.beginPath();
        ctx.arc(x, y, 3, 0, Math.PI * 2);
        ctx.fill();
      }
    };

    const drawPowerLabel = (x: number, y: number, power: number) => {
      const text = `${power.toFixed(1)} kW`;

      // Background
      ctx.fillStyle = "white";
      ctx.shadowColor = "rgba(0,0,0,0.1)";
      ctx.shadowBlur = 4;
      ctx.shadowOffsetY = 2;

      const metrics = ctx.measureText(text);
      const padding = 8;
      const width = metrics.width + padding * 2;
      const height = 20;

      ctx.fillRect(x - width / 2, y - height / 2, width, height);

      // Text
      ctx.shadowColor = "transparent";
      ctx.fillStyle = "#00A651";
      ctx.font = "bold 12px system-ui";
      ctx.textAlign = "center";
      ctx.textBaseline = "middle";
      ctx.fillText(text, x, y);
    };

    const drawEnergyFlow = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      const centerX = canvas.width / 2;
      const centerY = canvas.height / 2;

      // Calculate node positions
      const pvX = centerX;
      const pvY = centerY * 0.3;

      const officeX = centerX * 0.3;
      const officeY = centerY;

      const chargingX = centerX * 1.7;
      const chargingY = centerY;

      const gridX = centerX;
      const gridY = centerY * 1.7;

      // Draw flow lines
      drawFlowLine(pvX, pvY + 70, centerX, centerY - 60, data.currentPower.pv, 0);
      drawFlowLine(
        centerX - 60,
        centerY,
        officeX + 70,
        officeY,
        data.currentPower.consumption,
        0.3,
      );
      drawFlowLine(
        centerX + 60,
        centerY,
        chargingX - 70,
        chargingY,
        data.currentPower.charging || 0,
        0.6,
      );
      drawFlowLine(centerX, centerY + 60, gridX, gridY - 70, data.currentPower.grid, 0.9);
    };

    const animate = () => {
      animationFrameRef.current++;
      drawEnergyFlow();
      requestAnimationFrame(animate);
    };

    resizeCanvas();
    animate();

    window.addEventListener("resize", resizeCanvas);

    // Use ResizeObserver for better responsiveness
    let resizeObserver: ResizeObserver | null = null;
    if (typeof ResizeObserver !== "undefined") {
      resizeObserver = new ResizeObserver(() => {
        resizeCanvas();
      });
      resizeObserver.observe(canvas.parentElement!);
    }

    return () => {
      window.removeEventListener("resize", resizeCanvas);
      if (resizeObserver) {
        resizeObserver.disconnect();
      }
    };
  }, [data]);

  const formatTimestamp = (date: Date) => {
    const options: Intl.DateTimeFormatOptions = {
      weekday: "long",
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
    };
    return date.toLocaleDateString("de-DE", options).replace(",", " -");
  };

  return (
    <div className={`${styles.dashboard} bg-gradient-to-br from-gray-50 to-gray-200 text-gray-800`}>
      <div className={`${styles.dashboardContainer} gap-2 p-2 sm:gap-3 sm:p-3 lg:gap-4 lg:p-4`}>
        {/* Main Content Area - Full height grid with header integrated */}
        <div className="grid min-h-0 flex-1 grid-cols-1 gap-2 overflow-hidden lg:gap-3 xl:grid-cols-5">
          {/* Left Column: Header + Energy Flow Visualization - Takes 3/5 on XL screens */}
          <div className="flex flex-col gap-2 xl:col-span-3">
            {/* Header - Now in left column */}
            <div className="flex flex-shrink-0 items-center justify-between rounded-xl border-l-4 border-green-600 bg-white p-3 shadow-lg sm:p-4 lg:rounded-2xl lg:p-6">
              <div className="flex items-center gap-3 lg:gap-5">
                <div className="flex items-center gap-2 lg:gap-4">
                  {/* Logo or Default Icon */}
                  {data.logoUrl ? (
                    <img
                      src={data.logoUrl}
                      alt={`${data.name} Logo`}
                      className="h-12 w-12 object-contain sm:h-16 sm:w-16 lg:h-20 lg:w-20 xl:h-24 xl:w-24"
                    />
                  ) : (
                    <div className="flex h-12 w-12 items-center justify-center rounded bg-blue-900 text-lg font-bold text-white sm:h-16 sm:w-16 sm:text-xl lg:h-20 lg:w-20 lg:text-2xl xl:h-24 xl:w-24 xl:text-3xl">
                      D
                    </div>
                  )}
                  <h1 className="truncate text-lg font-light text-blue-900 sm:text-xl lg:text-2xl xl:text-3xl">
                    {data.name || "Solar Energy Dashboard"}
                  </h1>
                </div>
              </div>
              <div className="whitespace-nowrap text-xs text-gray-600 sm:text-sm lg:text-base">
                {formatTimestamp(timestamp)}
              </div>
            </div>

            {/* Energy Flow Visualization */}
            <div className="relative flex-1 overflow-hidden rounded-xl bg-white shadow-lg">
              <div className={styles.energyFlowContainer}>
              <canvas ref={canvasRef} className={styles.energyCanvas} />

              {/* Energy Nodes - Responsive sizing */}
              {/* PV Node - Increased size */}
              <div
                className={`${styles.energyNode} ${styles.pvNode} h-20 w-20 sm:h-24 sm:w-24 lg:h-32 lg:w-32 xl:h-36 xl:w-36`}
              >
                <div className="mb-1 text-lg sm:text-xl lg:text-2xl xl:text-3xl">☀️</div>
                <div className="text-[8px] opacity-90 sm:text-[9px] lg:text-xs">PV-Anlage</div>
                <div className="text-xs font-bold sm:text-sm lg:text-base xl:text-lg">
                  {data.currentPower.pv.toFixed(1)} kW
                </div>
              </div>

              {/* Central NSVHT Node - Increased size */}
              <div
                className={`${styles.energyNode} ${styles.centralNode} w-18 h-18 sm:h-20 sm:w-20 lg:h-28 lg:w-28 xl:h-32 xl:w-32`}
              >
                <div className="mb-1 text-base sm:text-lg lg:text-xl xl:text-2xl">⚡</div>
                <div className="text-[7px] opacity-90 sm:text-[8px] lg:text-[9px] xl:text-xs">
                  NSVHT
                </div>
                <div className="text-[10px] font-bold sm:text-xs lg:text-sm xl:text-base">
                  {data.currentPower.pv.toFixed(1)} kW
                </div>
              </div>

              {/* Office Node - Shows corrected office consumption (excluding charging) - Increased size */}
              <div
                className={`${styles.energyNode} ${styles.officeNode} h-20 w-20 sm:h-24 sm:w-24 lg:h-32 lg:w-32 xl:h-36 xl:w-36`}
              >
                <div className="mb-1 text-lg sm:text-xl lg:text-2xl xl:text-3xl">🏢</div>
                <div className="text-[8px] opacity-90 sm:text-[9px] lg:text-xs">Büro</div>
                <div className="text-xs font-bold sm:text-sm lg:text-base xl:text-lg">
                  {data.currentPower.consumption.toFixed(1)} kW
                </div>
              </div>

              {/* Charging Node - Increased size */}
              <div
                className={`${styles.energyNode} ${styles.chargingNode} h-20 w-20 sm:h-24 sm:w-24 lg:h-32 lg:w-32 xl:h-36 xl:w-36`}
              >
                <div className="mb-1 text-lg sm:text-xl lg:text-2xl xl:text-3xl">🔌</div>
                <div className="text-[8px] opacity-90 sm:text-[9px] lg:text-xs">
                  Ladestationen
                  {data.chargingMetadata && (
                    <div className="text-[6px] opacity-75 sm:text-[7px] lg:text-[8px]">
                      {data.chargingMetadata.activeSessionsCount} aktiv
                    </div>
                  )}
                </div>
                <div className="text-xs font-bold sm:text-sm lg:text-base xl:text-lg">
                  {(data.currentPower.charging || 0).toFixed(1)} kW
                </div>
              </div>

              {/* Grid Node with directional arrows - Increased size */}
              <div
                className={`${styles.energyNode} ${styles.gridNode} ${
                  data.currentPower.grid > 0 ? styles.gridNodeFeedIn : ""
                } h-20 w-20 sm:h-24 sm:w-24 lg:h-32 lg:w-32 xl:h-36 xl:w-36`}
              >
                {/* Power flow direction arrow - SolarEdge API: Positive = Export to grid (↓), Negative = Import from grid (↑) */}
                <div
                  className={`${styles.gridArrow} ${
                    data.currentPower.grid > 0
                      ? styles.gridArrowFeedIn
                      : styles.gridArrowConsumption
                  }`}
                >
                  {data.currentPower.grid > 0 ? "↓" : "↑"}
                </div>
                <div className="mb-1 text-lg sm:text-xl lg:text-2xl xl:text-3xl">⚡</div>
                <div className="text-[8px] opacity-90 sm:text-[9px] lg:text-xs">
                  {data.currentPower.grid > 0 ? "Einspeisung" : "Netzbezug"}
                </div>
                <div
                  className={`text-xs font-bold sm:text-sm lg:text-base xl:text-lg ${
                    data.currentPower.grid > 0 ? "text-white" : "text-orange-700"
                  }`}
                >
                  {Math.abs(data.currentPower.grid).toFixed(1)} kW
                </div>
              </div>
            </div>
          </div>
          </div>

          {/* Compact Sidebar for Info Screen - Takes 2/5 on XL screens for more space */}
          <div className={`${styles.sidebar} flex flex-col gap-1 sm:gap-2 lg:gap-2 xl:col-span-2`}>
            {/* Current PV Production - Compact */}
            <div className="flex-shrink-0 rounded-lg border-l-4 border-orange-500 bg-white p-2 shadow-lg lg:p-3">
              <h3 className="mb-1 text-xs font-semibold text-blue-900 lg:text-sm">
                ☀️ Aktuelle PV-Produktion
              </h3>
              <div className="text-xl font-bold text-orange-500 lg:text-2xl">
                {data.currentPower.pv.toFixed(1)} kW
              </div>
            </div>

            {/* Environmental Impact - Combined Card */}
            {data.calculatedMetrics && (
              <div className="flex-shrink-0 rounded-lg border-l-4 border-green-600 bg-white p-2 shadow-lg lg:p-3">
                <h3 className="mb-1 text-xs font-semibold text-blue-900 lg:text-sm">
                  🌱 Umwelt-Einsparungen
                </h3>
                <div className="grid grid-cols-2 gap-1">
                  <div className="rounded bg-gray-50 p-1 text-center">
                    <div className="text-lg font-bold text-amber-500 lg:text-xl">
                      {data.calculatedMetrics.fuelSavedLitersTotal.toFixed(0)}
                    </div>
                    <div className="text-[8px] text-gray-600 lg:text-[9px]">Liter Benzin</div>
                  </div>
                  <div className="rounded bg-gray-50 p-1 text-center">
                    <div className="text-lg font-bold text-green-600 lg:text-xl">
                      {(data.calculatedMetrics.co2SavedKgTotal / 1000).toFixed(1)}
                    </div>
                    <div className="text-[8px] text-gray-600 lg:text-[9px]">Tonnen CO₂</div>
                  </div>
                </div>
              </div>
            )}

            {/* Energy Production - Compact */}
            <div className="flex-shrink-0 rounded-lg border-l-4 border-blue-900 bg-white p-2 shadow-lg lg:p-3">
              <h3 className="mb-1 text-xs font-semibold text-blue-900 lg:text-sm">
                ⚡ Energieproduktion
              </h3>
              <div className="grid grid-cols-3 gap-1">
                <div className="rounded bg-gray-50 p-1 text-center">
                  <div className="text-sm font-bold text-blue-900 lg:text-base">
                    {data.energy.todayProduction.toFixed(0)}
                  </div>
                  <div className="text-[8px] text-gray-600 lg:text-[9px]">kWh heute</div>
                </div>
                <div className="rounded bg-gray-50 p-1 text-center">
                  <div className="text-sm font-bold text-blue-900 lg:text-base">
                    {(data.energy.yearProduction / 1000).toFixed(1)}
                  </div>
                  <div className="text-[8px] text-gray-600 lg:text-[9px]">MWh Jahr</div>
                </div>
                <div className="rounded bg-gray-50 p-1 text-center">
                  <div className="text-sm font-bold text-blue-900 lg:text-base">
                    {(data.energy.totalProduction / 1000).toFixed(1)}
                  </div>
                  <div className="text-[8px] text-gray-600 lg:text-[9px]">MWh Total</div>
                </div>
              </div>
            </div>

            {/* Removed separate fuel savings card - now combined with environmental impact */}

            {/* Sun Visualization */}
            {data.sunData && (
              <SunVisualization
                sunData={data.sunData}
                hourlyProduction={data.energy.todayHourlyProduction}
                settings={data.settings}
              />
            )}

            {/* Removed Charging Stations card for info screen optimization */}
          </div>
        </div>

        {/* Data Age Information */}
        <div className="flex-shrink-0 rounded-xl border-l-4 border-gray-400 bg-white p-3 shadow-lg sm:p-4 lg:rounded-2xl">
          <div className="flex flex-col items-start justify-between gap-2 sm:flex-row sm:items-center sm:gap-4">
            <div className="flex flex-col gap-2 text-xs text-gray-600 sm:flex-row sm:gap-4 sm:text-sm">
              {/* SolarEdge Data Age */}
              {data.solarEdgeMetadata && (
                <div className="flex items-center gap-1">
                  <span>🌞</span>
                  <span>
                    PV-Daten:{" "}
                    {data.solarEdgeMetadata.lastApiCall
                      ? `${Math.round(
                          (new Date().getTime() -
                            new Date(data.solarEdgeMetadata.lastApiCall).getTime()) /
                            60000,
                        )} min alt`
                      : "Cache"}
                  </span>
                </div>
              )}

              {/* Charging Data Age */}
              {data.chargingMetadata && (
                <div className="flex items-center gap-1">
                  <span>🔌</span>
                  <span>
                    Ladedaten:{" "}
                    {`${Math.round(
                      (new Date().getTime() -
                        new Date(data.chargingMetadata.lastUpdated).getTime()) /
                        60000,
                    )} min alt`}
                  </span>
                </div>
              )}
            </div>

            <div className="flex items-center gap-2 text-xs text-gray-500 sm:text-sm">
              {data.weather.source === "error" ? (
                <span className="text-red-500">
                  🌤️ Wetter: {data.weather.error || "Nicht verfügbar"}
                </span>
              ) : (
                <span>
                  {data.weather.isDay ? "☀️" : "🌙"} {data.weather.temperature}°C
                  {data.weather.description && ` • ${data.weather.description}`}
                  {data.weather.source === "cache" && " (Cache)"}
                </span>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SolarDashboard;
