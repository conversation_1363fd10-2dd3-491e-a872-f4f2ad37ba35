"use client";

import { SunriseSunsetData } from "~/services/weather/sunriseSunsetService";

interface SunVisualizationProps {
  sunData: SunriseSunsetData;
  hourlyProduction?: Array<{ hour: number; energy: number }>;
  settings?: {
    hourlyChartMaxKWh?: number;
  };
}

export default function SunVisualization({
  sunData,
  hourlyProduction,
  settings,
}: SunVisualizationProps) {
  // Validate sun data and provide fallbacks
  const isValidTime = (time: string) =>
    time && time !== "Invalid Date" && time.match(/^\d{2}:\d{2}$/);

  const sunrise = isValidTime(sunData.sunrise) ? sunData.sunrise : "06:00";
  const sunset = isValidTime(sunData.sunset) ? sunData.sunset : "20:00";
  const currentTime = isValidTime(sunData.currentTime)
    ? sunData.currentTime
    : new Date().toLocaleTimeString("de-DE", { hour: "2-digit", minute: "2-digit", hour12: false });

  // Calculate sun position on the arc (0 = sunrise, 1 = sunset)
  const sunPosition = isNaN(sunData.sunPosition) ? 0.5 : sunData.sunPosition;

  // SVG dimensions - Elliptical arc for better visibility
  const width = 360; // Full width
  const height = 80; // More height for labels
  const centerX = width / 2;
  const baseY = height - 25; // More space for labels
  const radiusX = 150; // Horizontal radius (wider)
  const radiusY = 30; // Vertical radius (flatter)

  // Calculate sun position on the elliptical arc
  const angle = Math.PI * sunPosition; // 0 to π (180 degrees)
  const sunX = centerX - Math.cos(angle) * radiusX;
  const sunY = baseY - Math.sin(angle) * radiusY;

  // Create the elliptical arc path
  const arcPath = `M ${centerX - radiusX} ${baseY} A ${radiusX} ${radiusY} 0 0 1 ${
    centerX + radiusX
  } ${baseY}`;

  // Determine sun icon and colors based on time
  const getSunIcon = () => {
    if (!sunData.isDaytime) {
      return "🌙"; // Moon for nighttime
    } else if (sunPosition < 0.2 || sunPosition > 0.8) {
      return "🌅"; // Sunrise/sunset
    } else {
      return "☀️"; // Sun for daytime
    }
  };

  const getSunColor = () => {
    if (!sunData.isDaytime) {
      return "#4A5568"; // Gray for nighttime
    } else if (sunPosition < 0.2 || sunPosition > 0.8) {
      return "#F6AD55"; // Orange for sunrise/sunset
    } else {
      return "#F6E05E"; // Yellow for daytime
    }
  };

  return (
    <div className="w-full flex-shrink-0 rounded-lg border-l-4 border-yellow-500 bg-white p-2 shadow-lg lg:p-3">
      <h3 className="mb-1 text-xs font-semibold text-blue-900 lg:text-sm">
        🌅 Sonnenzeiten & Produktion
      </h3>

      {/* Visual Sun Arc */}
      <div className="relative mb-1 py-1">
        <svg
          width={width}
          height={height}
          className="h-auto w-full"
          viewBox={`0 0 ${width} ${height}`}
        >
          {/* Background arc (horizon) */}
          <path d={arcPath} stroke="#E2E8F0" strokeWidth="3" fill="none" strokeLinecap="round" />

          {/* Daylight arc (if daytime) */}
          {sunData.isDaytime && (
            <path
              d={arcPath}
              stroke="#FED7AA"
              strokeWidth="6"
              fill="none"
              strokeLinecap="round"
              strokeDasharray={`${sunPosition * Math.PI * radiusX} ${Math.PI * radiusX}`}
            />
          )}

          {/* Sunrise marker */}
          <circle
            cx={centerX - radiusX}
            cy={baseY}
            r="4"
            fill="#F97316"
            className="drop-shadow-sm"
          />

          {/* Sunset marker */}
          <circle
            cx={centerX + radiusX}
            cy={baseY}
            r="4"
            fill="#DC2626"
            className="drop-shadow-sm"
          />

          {/* Sun position */}
          <circle cx={sunX} cy={sunY} r="8" fill={getSunColor()} className="drop-shadow-md" />

          {/* Sun icon */}
          <text
            x={sunX}
            y={sunY + 2}
            textAnchor="middle"
            fontSize="12"
            className="pointer-events-none"
          >
            {getSunIcon()}
          </text>

          {/* Time labels with better positioning */}
          <text
            x={centerX - radiusX}
            y={baseY + 20}
            textAnchor="middle"
            fontSize="11"
            fill="#F97316"
            className="font-semibold"
          >
            {sunrise}
          </text>
          <text
            x={centerX - radiusX}
            y={baseY + 32}
            textAnchor="middle"
            fontSize="9"
            fill="#6B7280"
            className="font-medium"
          >
            Aufgang
          </text>

          <text
            x={centerX + radiusX}
            y={baseY + 20}
            textAnchor="middle"
            fontSize="11"
            fill="#DC2626"
            className="font-semibold"
          >
            {sunset}
          </text>
          <text
            x={centerX + radiusX}
            y={baseY + 32}
            textAnchor="middle"
            fontSize="9"
            fill="#6B7280"
            className="font-medium"
          >
            Untergang
          </text>

          {/* Current time at sun position */}
          <text
            x={sunX}
            y={sunY - 12}
            textAnchor="middle"
            fontSize="11"
            fill="#1F2937"
            className="font-bold"
          >
            {currentTime}
          </text>
        </svg>
      </div>

      {/* Compact Time Information & Status in one line */}
      <div className="flex items-center justify-between rounded bg-gray-50 px-2 py-1 text-[10px] lg:text-xs">
        <div className="flex items-center gap-1">
          <span className="font-bold text-orange-500">{sunrise}</span>
          <span className="text-gray-600">Aufgang</span>
        </div>

        <div className="text-center font-medium text-blue-800">
          {sunData.isDaytime ? (
            <>🌞 noch {sunData.timeUntilSunset}</>
          ) : (
            <>🌙 noch {sunData.timeUntilSunrise}</>
          )}
        </div>

        <div className="flex items-center gap-1">
          <span className="font-bold text-red-500">{sunset}</span>
          <span className="text-gray-600">Untergang</span>
        </div>
      </div>

      {/* Hourly Production Chart - Always show, even if no data */}
      <div className="mt-2 flex min-h-0 w-full flex-1 flex-col">
        <h4 className="mb-1 text-xs font-medium text-gray-700">⚡ Stündliche Produktion (kWh)</h4>
        <div className="flex min-h-[250px] w-full flex-1 flex-col overflow-hidden rounded bg-gray-50">
          <HourlyProductionChart
            data={hourlyProduction || []}
            currentHour={new Date().getHours()}
            settings={settings}
            sunData={sunData}
          />
        </div>
      </div>
    </div>
  );
}

// Separate component for the hourly production chart
function HourlyProductionChart({
  data,
  currentHour,
  settings,
  sunData,
}: {
  data: Array<{ hour: number; energy: number }>;
  currentHour: number;
  settings?: { hourlyChartMaxKWh?: number };
  sunData?: any;
}) {
  // Get the configured maximum or default to 50 kWh
  const configuredMax = settings?.hourlyChartMaxKWh || 50;

  // Find the actual maximum value in the data
  const actualMaxEnergy = Math.max(...data.map((d) => d.energy), 0);

  // Use the higher of configured max or actual max for dynamic scaling
  const maxEnergy = Math.max(configuredMax, actualMaxEnergy, 1);

  // Calculate relevant hours based on sun data
  const getSunHours = () => {
    if (sunData?.sunrise && sunData?.sunset) {
      const sunriseHour = parseInt(sunData.sunrise.split(":")[0]);
      const sunsetHour = parseInt(sunData.sunset.split(":")[0]);
      const startHour = Math.max(0, sunriseHour - 1);
      const endHour = Math.min(23, sunsetHour + 1);
      return { startHour, endHour };
    }
    return { startHour: 6, endHour: 18 }; // Default fallback
  };

  const { startHour, endHour } = getSunHours();

  // Create array for relevant hours (sun hours + 1h buffer)
  const relevantHours = Array.from({ length: endHour - startHour + 1 }, (_, i) => {
    const hour = startHour + i;
    const dataPoint = data.find((d) => d.hour === hour);
    return {
      hour,
      energy: dataPoint ? dataPoint.energy : 0,
      hasData: !!dataPoint,
    };
  });

  // Use relevant hours instead of all 24 hours
  const allHours = relevantHours;

  // Calculate total production for the day so far
  const totalProduction = data.reduce((sum, h) => sum + h.energy, 0);

  return (
    <div className="flex w-full flex-1 flex-col p-2">
      {/* Chart container with Y-axis labels */}
      <div className="relative flex h-64 w-full">
        {/* Y-axis labels */}
        <div className="flex w-8 flex-col justify-between py-1 pr-2 text-[8px] text-gray-500">
          <span>{maxEnergy.toFixed(0)}</span>
          <span>{(maxEnergy * 0.75).toFixed(0)}</span>
          <span>{(maxEnergy * 0.5).toFixed(0)}</span>
          <span>{(maxEnergy * 0.25).toFixed(0)}</span>
          <span>0</span>
        </div>

        {/* Chart area */}
        <div className="relative h-full flex-1">
          {/* Background grid lines */}
          <div className="pointer-events-none absolute inset-0 flex flex-col justify-between">
            <div className="border-t border-gray-200 opacity-50"></div>
            <div className="border-t border-gray-200 opacity-50"></div>
            <div className="border-t border-gray-200 opacity-50"></div>
            <div className="border-t border-gray-200 opacity-50"></div>
          </div>

          {/* Chart bars - proper bar chart */}
          <div className="absolute bottom-0 left-0 right-0 flex h-full items-end justify-between gap-[1px]">
            {allHours.map(({ hour, energy, hasData }) => {
              const heightPercent = maxEnergy > 0 ? (energy / maxEnergy) * 100 : 0;
              const isCurrentHour = hour === currentHour;
              const isFutureHour = hour > currentHour;

              return (
                <div key={hour} className="relative flex h-full flex-1 flex-col justify-end">
                  {/* Bar - starts from bottom */}
                  <div
                    className={`w-full ${
                      isFutureHour
                        ? "bg-gray-200"
                        : isCurrentHour
                        ? "bg-gradient-to-t from-orange-600 to-orange-400 shadow-lg"
                        : hasData && energy > 0
                        ? "bg-gradient-to-t from-yellow-600 to-yellow-400"
                        : "bg-gray-300"
                    }`}
                    style={{
                      height: energy > 0 ? `${heightPercent}%` : "0%",
                    }}
                  />
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Time axis labels */}
      <div className="mt-1 flex justify-between text-[8px] text-gray-500">
        <span>{startHour}:00</span>
        {startHour + 3 <= endHour && <span>{startHour + 3}:00</span>}
        {startHour + 6 <= endHour && <span>{startHour + 6}:00</span>}
        {startHour + 9 <= endHour && <span>{startHour + 9}:00</span>}
        <span>{endHour}:00</span>
      </div>

      {/* Scaling indicator only if needed */}
      {actualMaxEnergy > configuredMax && (
        <div className="mt-1 text-center text-[8px] font-medium text-orange-600">
          📈 Skalierung erweitert: {configuredMax} → {maxEnergy.toFixed(1)} kWh
        </div>
      )}
    </div>
  );
}
