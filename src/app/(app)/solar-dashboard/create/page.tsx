"use client";

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import Dropdown from '~/app/(app)/util/Dropdown';
import { FaMapMarkerAlt, FaSolarPanel, FaKey, FaIdCard } from 'react-icons/fa';

interface Location {
  id: string;
  name: string;
  city: string;
  street?: string;
  houseNumber?: string;
  postal_code?: string;
}

interface CreateDashboardForm {
  name: string;
  description: string;
  locationId: string;
  solarEdgeApiKey: string;
  solarEdgeSiteId: string;
  settings?: {
    fuelSavingsFormula?: number;
    co2SavingsFormula?: {
      electricityGridFactor?: number;
      gasolineEmissionFactor?: number;
      efficiencyFactor?: number;
    };
  };
}

export default function CreateSolarDashboardPage() {
  const router = useRouter();
  const [locations, setLocations] = useState<Location[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState<CreateDashboardForm>({
    name: '',
    description: '',
    locationId: '',
    solarEdgeApiKey: '',
    solarEdgeSiteId: '',
    settings: {
      fuelSavingsFormula: 0.25,
      co2SavingsFormula: {
        electricityGridFactor: 0.4,
        gasolineEmissionFactor: 2.3,
        efficiencyFactor: 0.25
      }
    }
  });

  useEffect(() => {
    fetchLocations();
  }, []);

  const fetchLocations = async () => {
    try {
      const response = await fetch('/api/location');
      if (response.ok) {
        const data = await response.json();
        setLocations(data);
      } else {
        setError('Fehler beim Laden der Standorte');
      }
    } catch (err) {
      console.error('Error fetching locations:', err);
      setError('Netzwerkfehler beim Laden der Standorte');
    }
  };

  const getLocationOptions = () => {
    return locations.map(location => ({
      id: location.id,
      label: `${location.name} - ${location.city}`,
      selected: location.id === formData.locationId
    }));
  };

  const handleInputChange = (field: keyof CreateDashboardForm, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name || !formData.locationId) {
      setError('Name und Standort sind erforderlich');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await fetch('/api/solar-dashboard', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          name: formData.name,
          description: formData.description,
          locationId: formData.locationId,
          solarEdgeApiKey: formData.solarEdgeApiKey || undefined,
          solarEdgeSiteId: formData.solarEdgeSiteId || undefined,
          settings: {
            refreshInterval: 30,
            showWeather: true,
            showCharging: true,
            theme: 'light',
            units: 'metric',
            language: 'de',
            // Include configurable formulas
            fuelSavingsFormula: formData.settings?.fuelSavingsFormula || 0.25,
            co2SavingsFormula: formData.settings?.co2SavingsFormula || {
              electricityGridFactor: 0.4,
              gasolineEmissionFactor: 2.3,
              efficiencyFactor: 0.25
            }
          }
        }),
      });

      if (response.ok) {
        const dashboard = await response.json();
        router.push(`/solar-dashboard/${dashboard.id}`);
      } else {
        const errorData = await response.json();
        setError(errorData.error || 'Fehler beim Erstellen des Dashboards');
      }
    } catch (err) {
      console.error('Error creating dashboard:', err);
      setError('Netzwerkfehler beim Erstellen des Dashboards');
    } finally {
      setLoading(false);
    }
  };

  const selectedLocation = locations.find(loc => loc.id === formData.locationId);

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-2xl mx-auto">
        <div className="flex items-center gap-4 mb-8">
          <div className="w-12 h-12 bg-green-600 rounded-lg flex items-center justify-center text-white text-2xl">
            🌞
          </div>
          <div>
            <h1 className="text-3xl font-bold text-gray-800">Neues Solar Dashboard</h1>
            <p className="text-gray-600">Erstellen Sie ein neues Solar-Energie Dashboard für einen Standort</p>
          </div>
        </div>

        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <div className="flex items-center gap-2">
              <span className="text-red-500 text-xl">⚠️</span>
              <p className="text-red-700">{error}</p>
            </div>
          </div>
        )}

        <form onSubmit={handleSubmit} className="bg-white rounded-lg shadow-lg p-8">
          {/* Dashboard Name */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Dashboard Name *
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              placeholder="z.B. Solar Dashboard Hauptstandort"
              required
            />
          </div>

          {/* Description */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Beschreibung
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => handleInputChange('description', e.target.value)}
              rows={3}
              className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
              placeholder="Optionale Beschreibung des Dashboards"
            />
          </div>

          {/* Location Selection */}
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Standort *
            </label>
            <Dropdown
              options={getLocationOptions()}
              onChange={(id) => handleInputChange('locationId', id)}
              onDelete={() => {}} // Not used
              canDelete={false}
              icon={<FaMapMarkerAlt className="mr-1" />}
              placeHolder="Standort auswählen..."
              className="w-full max-w-full"
            />
            {selectedLocation && (
              <div className="mt-2 p-3 bg-gray-50 rounded border">
                <p className="text-sm text-gray-600">
                  <strong>Ausgewählter Standort:</strong> {selectedLocation.name}
                </p>
                <p className="text-sm text-gray-500">
                  {selectedLocation.street} {selectedLocation.houseNumber}, {selectedLocation.postal_code} {selectedLocation.city}
                </p>
              </div>
            )}
          </div>

          {/* SolarEdge Configuration */}
          <div className="mb-6 p-6 bg-orange-50 border border-orange-200 rounded-lg">
            <h3 className="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
              <FaSolarPanel className="text-orange-600" />
              SolarEdge API-Konfiguration
            </h3>
            <p className="text-sm text-gray-600 mb-4">
              Optional: Konfigurieren Sie die SolarEdge API für echte Daten. Ohne diese Konfiguration werden Dummy-Daten verwendet.
            </p>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <FaKey className="inline mr-1" />
                  API Key
                </label>
                <input
                  type="text"
                  value={formData.solarEdgeApiKey}
                  onChange={(e) => handleInputChange('solarEdgeApiKey', e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  placeholder="SolarEdge API Key"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  <FaIdCard className="inline mr-1" />
                  Site ID
                </label>
                <input
                  type="text"
                  value={formData.solarEdgeSiteId}
                  onChange={(e) => handleInputChange('solarEdgeSiteId', e.target.value)}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                  placeholder="SolarEdge Site ID"
                />
              </div>
            </div>

            <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded">
              <p className="text-sm text-blue-700">
                <strong>Hinweis:</strong> Sie können diese Einstellungen später in den Dashboard-Einstellungen ändern.
              </p>
            </div>
          </div>

          {/* Formula Configuration for Info Screen */}
          <div className="mb-8 p-6 bg-gray-50 rounded-lg">
            <h3 className="text-lg font-semibold text-gray-800 mb-4">📊 Berechnungsformeln (Info-Screen)</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Fuel Savings Formula */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Kraftstoffeinsparung (Liter pro kWh)
                </label>
                <input
                  type="number"
                  step="0.01"
                  min="0"
                  max="1"
                  value={formData.settings?.fuelSavingsFormula || 0.25}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    settings: {
                      ...prev.settings,
                      fuelSavingsFormula: parseFloat(e.target.value) || 0.25
                    }
                  }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  placeholder="0.25"
                />
                <p className="text-xs text-gray-500 mt-1">Standard: 0.25 Liter pro kWh (basierend auf EV-Effizienz vs. Verbrenner)</p>
              </div>

              {/* CO2 Grid Factor */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  CO₂-Faktor Stromnetz (kg CO₂/kWh)
                </label>
                <input
                  type="number"
                  step="0.01"
                  min="0"
                  max="1"
                  value={formData.settings?.co2SavingsFormula?.electricityGridFactor || 0.4}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    settings: {
                      ...prev.settings,
                      co2SavingsFormula: {
                        ...prev.settings?.co2SavingsFormula,
                        electricityGridFactor: parseFloat(e.target.value) || 0.4,
                        gasolineEmissionFactor: prev.settings?.co2SavingsFormula?.gasolineEmissionFactor || 2.3,
                        efficiencyFactor: prev.settings?.co2SavingsFormula?.efficiencyFactor || 0.25
                      }
                    }
                  }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  placeholder="0.4"
                />
                <p className="text-xs text-gray-500 mt-1">Standard: 0.4 kg CO₂/kWh (deutscher Strommix)</p>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-4 pt-6 border-t">
            <button
              type="button"
              onClick={() => router.back()}
              className="flex-1 px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
              disabled={loading}
            >
              Abbrechen
            </button>
            <button
              type="submit"
              disabled={loading || !formData.name || !formData.locationId}
              className="flex-1 px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors flex items-center justify-center gap-2"
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  Erstelle Dashboard...
                </>
              ) : (
                <>
                  <span>🌞</span>
                  Dashboard erstellen
                </>
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
