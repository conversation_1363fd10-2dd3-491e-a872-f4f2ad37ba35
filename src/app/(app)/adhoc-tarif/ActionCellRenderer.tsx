'use client';

import Link from "next/link";
import { ICellRendererParams } from "ag-grid-community";
import { BsPencilSquare } from "react-icons/bs";
import { AiOutlineDelete } from "react-icons/ai";
import React from "react";

type ParamsWithDelete = ICellRendererParams & {
  onDelete?: (row: any) => void;
};

export default function ActionCellRenderer(params: ParamsWithDelete) {
  const id = params?.data?.id as string | undefined;
  if (!id) return null;

  const href = `/adhoc-tarif/new/${encodeURIComponent(id)}/editShortCut`;

  const handleDeleteClick = () => {
    if (typeof params.onDelete === "function") {
      // Übergibt die ganze Row, damit der Dialog mehr Kontext anzeigen kann
      params.onDelete(params.data);
    } else {
      // Fallback: falls mal kein Callback übergeben ist
      const ok = window.confirm("Diesen Standortpreis wirklich löschen?");
      if (!ok) return;
      fetch(`/api/locationPrice/${encodeURIComponent(id)}`, { method: "DELETE" })
        .then(res => {
          if (!res.ok && res.status !== 204) return res.text().then(t => Promise.reject(t));
          // Hard Fallback: Seite neu laden (ohne Router)
          window.location.reload();
        })
        .catch(e => {
          console.error(e);
          alert("Löschen fehlgeschlagen.");
        });
    }
  };

  return (
    <div className="flex items-center gap-2 justify-center">
      <AiOutlineDelete
        className="cursor-pointer text-red-600 hover:text-red-800"
        size="1.2rem"
        title="Standortpreis löschen"
        onClick={handleDeleteClick}
      />
      <Link href={href} title="Standortpreis bearbeiten" className="inline-flex items-center">
        <BsPencilSquare className="cursor-pointer text-green-600 hover:text-green-800" />
      </Link>
    </div>
  );
}
