'use client'

import { EmpPrice, LocationPrice } from "../../../../prismaMongoAdhoc/client";
import { useForm, Controller } from "react-hook-form";
import Button from "~/component/button";
import React, { useState } from "react";
import { useRouter } from "next/navigation";
import {toDateString} from "~/utils/date/date";

interface Props {
  price: LocationPrice | EmpPrice;
  disableCurrentType?: boolean;
}


const AdhocPriceForm = ({ price, disableCurrentType }: Props) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitMessage, setSubmitMessage] = useState<string | null>(null);

  const router = useRouter();
  const isLocation = "locationId" in price;

  // Wir gehen davon aus, dass price.start und price.end Date | string sind
  // Das wird zu Date für die Form
  const defaultValues = {
    ...price,
    start: price.start ? new Date(price.start) : undefined,
    end: price.end ? new Date(price.end) : undefined,
    //energy_price: price.energy_price ?? 0,
    session_fee: price.session_fee ?? 0,
    tax_rate: price.tax_rate ?? 0,
    blocking_fee: price.blocking_fee ?? 0,
    blocking_fee_max: price.blocking_fee_max ?? 0,
    blocking_fee_start: price.blocking_fee_start ?? 0,
    current_type: price.current_type ?? "AC",
  };

  const { control, register, handleSubmit } = useForm<EmpPrice | LocationPrice>({
    defaultValues,
  });

  const onSubmit = async (data: EmpPrice | LocationPrice) => {
    setIsSubmitting(true);
    setSubmitMessage(null);

    const endpoint = isLocation
      ? `/api/location-price/${price.id}`
      : `/api/emp-price/${price.id}`;

    // Wenn die Daten noch Date-Objekte sind, vor dem Senden zu YYYY-MM-DD wandeln!
    const payload = {
      ...data,
      start: data.start ? toDateString(data.start) : undefined,
      end: data.end ? toDateString(data.end) : undefined,
      current_type: data.current_type || price.current_type || "AC",
    };



    try {
      const res = await fetch(endpoint, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload),
      });

      if (!res.ok) {
        throw new Error('Update fehlgeschlagen');
      }
      router.refresh();
      setSubmitMessage('Erfolgreich gespeichert!');
    } catch (error) {
      console.error('Update fehlgeschlagen:', error);
      setSubmitMessage('Fehler beim Speichern!');
      setTimeout(() => setSubmitMessage(null), 3000);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="p-6">
      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* kWh Preis */}
          <div className="flex flex-col">
            <label className="mb-2 text-sm font-bold text-slate-700 dark:text-white/80">
              kWh Preis (€)
            </label>
            <input
              {...register("energy_price")}
              type="number"
              step={0.01}
              className="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-white dark:bg-slate-800 dark:border-slate-600"
            />
          </div>

          {/* Parkgebühr */}
          <div className="flex flex-col">
            <label className="mb-2 text-sm font-bold text-slate-700 dark:text-white/80">
              Startgebühr (€)
            </label>
            <input
              {...register("session_fee")}
              type="number"
              step={0.01}
              className="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-white dark:bg-slate-800 dark:border-slate-600"
            />
          </div>

          {/* Steuer */}
          <div className="flex flex-col">
            <label className="mb-2 text-sm font-bold text-slate-700 dark:text-white/80">
              Steuersatz (%)
            </label>
            <input
              {...register("tax_rate")}
              type="number"
              step={0.01}
              className="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-white dark:bg-slate-800 dark:border-slate-600"
            />
          </div>

          {/* Blockiergebühr */}
          <div className="flex flex-col">
            <label className="mb-2 text-sm font-bold text-slate-700 dark:text-white/80">
              Blockiergebühr (€/min)
            </label>
            <input
              {...register("blocking_fee")}
              type="number"
              step={0.01}
              className="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-white dark:bg-slate-800 dark:border-slate-600"
            />
          </div>

          {/* Max Blockiergebühr */}
          <div className="flex flex-col">
            <label className="mb-2 text-sm font-bold text-slate-700 dark:text-white/80">
              Max. Blockiergebühr (€)
            </label>
            <input
              {...register("blocking_fee_max")}
              type="number"
              step={0.01}
              className="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-white dark:bg-slate-800 dark:border-slate-600"
            />
          </div>

          {/* Startzeit Blockiergebühr */}
          <div className="flex flex-col">
            <label className="mb-2 text-sm font-bold text-slate-700 dark:text-white/80">
              Blockiergebühr ab (min)
            </label>
            <input
              {...register("blocking_fee_start")}
              type="number"
              className="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-white dark:bg-slate-800 dark:border-slate-600"
            />
          </div>

          {/* Startdatum */}
          <div className="flex flex-col">
            <label className="mb-2 text-sm font-bold text-slate-700 dark:text-white/80">
              Gültig ab
            </label>
            <Controller
              name="start"
              control={control}
              render={({ field }) => (
                <input
                  type="date"
                  value={field.value ? toDateString(field.value) : ""}
                  onChange={e => field.onChange(new Date(e.target.value))}
                  className="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-white dark:bg-slate-800 dark:border-slate-600"
                />
              )}
            />
            <span className="text-xs text-slate-500 mt-1">
Das Datum darf nicht in der Vergangenheit und nicht vor dem zuletzt gesetzten Startpunkt liegen.
            </span>
          </div>

          {/* Enddatum */}
          <div className="flex flex-col">
            <label className="mb-2 text-sm font-bold text-slate-700 dark:text-white/80">
              Gültig bis
            </label>
            <Controller
              name="end"
              control={control}
              render={({ field }) => (
                <input
                  type="date"
                  value={field.value ? toDateString(field.value) : ""}
                  onChange={e => field.onChange(new Date(e.target.value))}
                  className="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-white dark:bg-slate-800 dark:border-slate-600"
                />
              )}
            />
            <span className="text-xs text-slate-500 mt-1">
          Das Datum muss nach dem festgelegten Startpunkt liegen.
            </span>
          </div>

          {/* Stromart */}
          <div className="flex flex-col">
            <label className="mb-2 text-sm font-bold text-slate-700 dark:text-white/80">
              Stromart
            </label>
            <select
              {...register("current_type")}
              defaultValue={defaultValues.current_type}
              disabled={disableCurrentType}
              className="border border-gray-300 rounded-lg px-3 py-2 bg-white dark:bg-slate-800 dark:border-slate-600 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <option value="AC">AC (Wechselstrom)</option>
              <option value="DC">DC (Gleichstrom)</option>
            </select>
          </div>
        </div>

        {submitMessage && (
          <div className={`mt-4 p-3 rounded-lg text-sm ${
            submitMessage.includes('Fehler')
              ? 'bg-red-100 text-red-700 border border-red-300'
              : 'bg-green-100 text-green-700 border border-green-300'
          }`}>
            {submitMessage}
          </div>
        )}

        <div className="mt-8 flex justify-end">
          <Button
            type="submit"
            disabled={isSubmitting}
            className={isSubmitting ? 'opacity-50 cursor-not-allowed' : ''}
          >
            {isSubmitting ? 'Speichert...' : 'Speichern'}
          </Button>
        </div>
      </form>
    </div>
  );
};

export default AdhocPriceForm;
