import React from "react";
import Card from "~/component/card";
import { getServerSession } from "next-auth";
import prisma from "../../../server/db/prisma";
import AdhocPriceForm from "~/app/(app)/adhoc-tarif/AdhocPriceForm";
import Button from "~/component/button";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import prismaMongo from "~/server/db/mongo";
import TarifCard from "~/app/(app)/emp/card/component/TarifCard";
import { Role } from "@prisma/client";
import Headline from "~/component/Headline";
import PageContentWrapper from "~/component/PageContentWrapper";
import Link from "next/link";
import { MdElectricBolt, MdAdd, MdLocationOn } from "react-icons/md";
import { Chip } from "~/app/(app)/component/chip";
import { formatDaysPeriod } from "~/utils/date/date";

import TableWithActionRenderer from "~/app/(app)/adhoc-tarif/TableWithActionRenderer";
import DownloadCsvButton from "~/app/(app)/component/DownloadCsvButton";
import {CsvRow, CsvRowExport} from "~/types/adhocTarif/adhocEmpPriceTypes";
import {mapLocationPriceToCsvRow, mapEmpPriceToCsvRow} from "~/utils/adhocTarif/helpersCSV";


export const revalidate = 0;

export default async function Page() {
  const session = await getServerSession(authOptions);
  let empId = session?.user?.selectedOu?.adhocEmpId;
  const operatorId = session?.user?.selectedOu?.operatorId;
  const hasOperatorId = !!operatorId;
  const selectedId = session?.user?.selectedOu?.id;
  const ouName = session?.user?.selectedOu?.name;

  function getDaysDiff(from: Date, to: Date): number {
    const ms = to.getTime() - from.getTime();
    return Math.ceil(ms / (1000 * 60 * 60 * 24));
  }


  const locations = await prisma.location.findMany({
    where: { ouId: selectedId },
    orderBy: { name: "asc" },
  });
  const locationIdsOfOu = locations.map((location) => location.id)


  let allPrices = await prismaMongo.empPrice.findMany({
    where: { empId },
    orderBy: { current_type: "asc" },
    include:{Emp:true}
  });

  const now = new Date();
  allPrices = allPrices.filter(price => {
    if (!price.end) return true;
    return new Date(price.end) >= now;
  });

  const hasAC = allPrices.some((p) => p.current_type === "AC");
  const hasDC = allPrices.some((p) => p.current_type === "DC");
  const showNewTypeButton = allPrices.length === 1 && (!hasAC || !hasDC);
  const missingType = !hasAC ? "AC" : !hasDC ? "DC" : null;
  const twoPricesExist = hasAC && hasDC;


  const eulektroPublicEmp = await prismaMongo.emp.findFirst({
    where: {
      OR: [
        { name: { contains: "Eulektro", mode: "insensitive" } },
        { name: { contains: "Public", mode: "insensitive" } },
        { party_id: "EUL" }, // Falls party_id bekannt ist
      ]
    },
  });

  if (empId == null){
    empId = eulektroPublicEmp?.id;
  }

  let locPrices = await prismaMongo.locationPrice.findMany({
    where: { empId },
    orderBy: { locationId: "asc" },
    include:{Emp:true}
  });


  locPrices = locPrices.filter(locPrices => locationIdsOfOu.includes( locPrices.locationId ??""));


  const basisPrice = eulektroPublicEmp ? await prismaMongo.empPrice.findFirst({
    where: { empId: eulektroPublicEmp.id,
       current_type: "AC",
    },
    orderBy: { start: "desc" },

  }) : null;

  const columns = [
    { field: "id", headerName: "ID", width: 100 },
    { field: "locationId", headerName: "LocationId", width: 100 },
    { field: "energy_price", headerName: "Energie Preis", width: 100 },
    { field: "session_fee", headerName: "Start Gebühr", width: 100 },
    { field: "tax_rate", headerName: "Steuer rate", width: 100 },
    { field: "blocking_fee", headerName: "Blockier gebühr", width: 100 },
    { field: "blocking_fee_max", headerName: "Maximale Blockier Gebühr", width: 100 },
    { field: "blocking_fee_start", headerName: "Start Gebühr zum Blockieren", width: 100 },
    {field: "Emp.name",headerName: "Emp.name",},
    {field: "current_type",headerName: "current_type",},
    {
      field: "actions",
      headerName: "Aktionen",
      cellRenderer: "ActionCellRenderer",
      width: 90,
      sortable: false,
      filter: false,
      resizable: false,
      pinned: "right" as const,
    },
  ];


// … nachdem du `locPrices` und `allPrices` berechnet/gefiltert hast:
  const rowsForCsv: CsvRow[] = [
    ...locPrices.map(mapLocationPriceToCsvRow),
    ...allPrices.map(mapEmpPriceToCsvRow),
  ];



  const rowsForCsvExport: CsvRowExport[] = rowsForCsv.map(({ _id, empId, ...rest }) => rest);
  return (
    <PageContentWrapper>

      <div className="flex justify-between items-center mb-6">
        <Headline title="Adhoc-Tarif Verwaltung" />
        {<DownloadCsvButton rows={rowsForCsvExport} csvFileName={ouName} />}
      </div>

      <div className="space-y-6">
        {allPrices.length > 0 ? (
          <>
            {allPrices.map((price) => {
              const now = new Date();
              const start = price.start ? new Date(price.start) : undefined;
              const end = price.end ? new Date(price.end) : undefined;

              let chipLabel: string | undefined = undefined;
              if (start && start > now) {
                const days = getDaysDiff(now, start);
                chipLabel = `${formatDaysPeriod(days)} bis zum Beginn`;
              } else if (end && end > now) {
                const days = getDaysDiff(now, end);
                chipLabel = `${formatDaysPeriod(days)} bis der EMP Price abläuft`;
              }

              return (
                <Card
                  key={price.id}
                  header_left={
                    <div className="flex items-center gap-2">
                      <MdElectricBolt size={20} />
                      Gennereller Adhoc-Preis ({price.current_type})
                      {chipLabel && (
                        <Chip label={chipLabel} className="bg-blue-100 text-blue-800 ml-2" />
                      )}
                    </div>
                  }
                >
                  <AdhocPriceForm price={price} disableCurrentType={twoPricesExist} />
                </Card>
              );
            })}



            {showNewTypeButton && missingType && (
              <Card
                header_left="Neuen Tarif hinzufügen"
                header_right={
                  <Link href={`/adhoc-tarif/newEmpPrice?type=${missingType}`}>
                    <Button className="flex items-center gap-2">
                      <MdAdd size={16} />
                      Neuer {missingType} Anbieter Preis
                    </Button>
                  </Link>
                }
              >
                <div className="p-4 text-center text-gray-600">
                  Fügen Sie einen {missingType} Tarif hinzu, um alle Ladearten abzudecken.
                </div>
              </Card>
            )}
          </>
        ) : (
          <Card
            header_left="Standard-Tarif"
            header_right={
              hasOperatorId ? (
                <Link href="/adhoc-tarif/newEmpPrice">
                  <Button className="flex items-center gap-2">
                    <MdAdd size={16} />
                    Eigenen Standardpreis anlegen
                  </Button>
                </Link>
              ) : undefined // kein Button ohne OperatorId!
            }
          >

          <div className="flex flex-col items-center gap-4 p-6">
              {basisPrice ? (
                <TarifCard
                  title="Eulektro Public Tarif"
                  description="Öffentlicher Basis-Tarif für alle Kunden."
                  pricekWh={basisPrice.energy_price}
                  priceSession={basisPrice.session_fee}
                  tarifName="Eulektro Public"
                  optional={false}
                  interactive={false}
                  tarifId="eulektro-public"
                  currentType="AC"
                  basicFee={0}
                  oneTimeFee={0}
                  oneTimeFeePayer={Role.CARD_HOLDER}
                  vat={basisPrice.tax_rate}
                  blockingFee={basisPrice.blocking_fee}
                  blockingFeeBeginAtMin={basisPrice.blocking_fee_start}
                  blockingFeeMax={basisPrice.blocking_fee_max}
                  size="normal"
                  internal={false}
                />
              ) : (
                <div className="p-6 text-center text-gray-600">
                  <p>Kein Basis-Tarif verfügbar</p>
                  <p className="text-sm text-gray-500 mt-2">
                    Eulektro Public EMP nicht gefunden
                  </p>
                </div>
              )}
            </div>
          </Card>
        )}

        <Card
          header_left={
            <div className="flex items-center gap-2">
              <MdLocationOn size={20} />
              Standort-spezifische Preise
            </div>
          }
          header_right={
            <Link href="/adhoc-tarif/new">
              <Button className="flex items-center gap-2">
                <MdAdd size={16} />
                Neuer Standort Preis
              </Button>
            </Link>
          }
        >
          {locPrices.length > 0 ? (
            <TableWithActionRenderer columnDefs={columns} rowData={locPrices} />
          ) : (
            <div className="p-6 text-center text-gray-600">
              <p className="mb-4">Noch keine standort-spezifischen Preise konfiguriert.</p>
              <p className="text-sm text-gray-500">
                Klicken Sie auf &quot;Neuer Standort Preis&quot; um einen hinzuzufügen.
              </p>
            </div>
          )}
        </Card>
      </div>
    </PageContentWrapper>
  );
}
