import React from "react";
import Card from "~/component/card";

import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import Headline from "~/component/Headline";
import PageContentWrapper from "~/component/PageContentWrapper";
import NewEmpPriceForm from "~/app/(app)/adhoc-tarif/newEmpPrice/newEmpPriceForm"
import { MdAdd, MdElectricBolt } from "react-icons/md";

export const revalidate = 0;

export default async function Page() {
  const session = await getServerSession(authOptions);
  const empId = session?.user?.selectedOu?.adhocEmpId;

  return (
    <PageContentWrapper>
      <Headline title="Neuen Anbieter-Preis erstellen" />
      <Card
        header_left={
          <div className="flex items-center gap-2">
            <MdElectricBolt size={20} />
            Anbieter-Preis konfigurieren
          </div>
        }
      >
        <NewEmpPriceForm empId={empId ?? undefined} />
      </Card>
    </PageContentWrapper>
  );
}
