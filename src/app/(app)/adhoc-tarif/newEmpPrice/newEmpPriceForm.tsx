
'use client';
import React, { useState } from 'react';
import { useForm } from 'react-hook-form';
import Button from '~/component/button';
import type { EmpPrice } from '../../../../../prismaMongoAdhoc/client';
import { useSearchParams, useRouter } from "next/navigation";

interface Props {
  empId?: string;
}

const NewEmpPriceForm: React.FC<Props> = ({ empId }) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitMessage, setSubmitMessage] = useState<string | null>(null);

  const [empPriceId, setEmpPriceId] = useState<string | null>(null)
  const router = useRouter();

  const searchParams = useSearchParams();
  const typeFromUrl = searchParams?.get("type") as "AC" | "DC" | null;
  const isTypeLocked = typeFromUrl === "AC" || typeFromUrl === "DC";

  const { register, handleSubmit } = useForm<EmpPrice>({
    defaultValues: {
      start: new Date(),
      end: new Date(),
      //energy_price: 0.35,
      blocking_fee: 0.10,
      blocking_fee_start: 120,
      blocking_fee_max: 12.00,
      session_fee: 0.00,
      tax_rate: 19.00,
      current_type: typeFromUrl ?? "AC",
      empId: empId,
    },
  });



  const onSubmit = async (data: EmpPrice) => {
    setIsSubmitting(true);
    setSubmitMessage(null);

    // Stelle sicher, dass alle erforderlichen Felder gesetzt sind
    const payload = {
      ...data,
      empId: empId,
      current_type: data.current_type || typeFromUrl || "AC",
    };



    try {
      if(!empPriceId ){
      const res = await fetch('/api/emp-price', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(payload),
      });
        const resData = await res.json();
        setEmpPriceId(resData.id)
        if (!res.ok) {
          throw new Error('Fehler beim Speichern!');
        }
        else {
          router.refresh();
        }
      }

      else{

        const res = await fetch(`/api/emp-price/${empPriceId}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(payload),
      });
         const resData = await res.json();
        if (!res.ok) {
          throw new Error('Fehler beim Speichern!');
        }
        else {
          router.refresh();
        }
      }




      setSubmitMessage('EmpPrice erfolgreich gespeichert!');
      setTimeout(() => {
        router.push('/adhoc-tarif');
      }, 2000);

    } catch (err) {
      console.error('Fehler:', err);
      setSubmitMessage('Fehler beim Speichern oder Netzwerkfehler!');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="p-6">
      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="flex flex-col">
            <label className="mb-2 text-sm font-bold text-slate-700 dark:text-white/80">
              kWh Preis (€)
            </label>
            <input
              {...register("energy_price", {
                required: "Bitte gib einen Energiepreis an.",
                min: {
                  value: 0.0001,
                  message: "Der Energiepreis muss größer als 0 sein.",
                },
              })}
              type="number"
              step={0.01}
              className="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-white dark:bg-slate-800 dark:border-slate-600"
            />
          </div>

          <div className="flex flex-col">
            <label className="mb-2 text-sm font-bold text-slate-700 dark:text-white/80">
              Startgebühr (€)
            </label>
            <input
              {...register("session_fee")}
              type="number"
              step={0.01}
              className="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-white dark:bg-slate-800 dark:border-slate-600"
            />
          </div>

          <div className="flex flex-col">
            <label className="mb-2 text-sm font-bold text-slate-700 dark:text-white/80">
              Steuersatz (%)
            </label>
            <input
              {...register("tax_rate")}
              type="number"
              step={0.01}
              className="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-white dark:bg-slate-800 dark:border-slate-600"
            />
          </div>

          <div className="flex flex-col">
            <label className="mb-2 text-sm font-bold text-slate-700 dark:text-white/80">
              Blockiergebühr (€/min)
            </label>
            <input
              {...register("blocking_fee")}
              type="number"
              step={0.01}
              className="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-white dark:bg-slate-800 dark:border-slate-600"
            />
          </div>

          <div className="flex flex-col">
            <label className="mb-2 text-sm font-bold text-slate-700 dark:text-white/80">
              Max. Blockiergebühr (€)
            </label>
            <input
              {...register("blocking_fee_max")}
              type="number"
              step={0.01}
              className="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-white dark:bg-slate-800 dark:border-slate-600"
            />
          </div>

          <div className="flex flex-col">
            <label className="mb-2 text-sm font-bold text-slate-700 dark:text-white/80">
              Blockiergebühr ab (min)
            </label>
            <input
              {...register("blocking_fee_start")}
              type="number"
              className="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-white dark:bg-slate-800 dark:border-slate-600"
            />
          </div>

          <div className="flex flex-col">
            <label className="mb-2 text-sm font-bold text-slate-700 dark:text-white/80">
              Gültig ab
            </label>
            <input
              {...register("start")}
              type="date"
              className="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-white dark:bg-slate-800 dark:border-slate-600"
            />
            <span className="text-xs text-slate-500 mt-1">
Das Datum darf nicht in der Vergangenheit und nicht vor dem zuletzt gesetzten Startpunkt liegen.
            </span>
          </div>

          <div className="flex flex-col">
            <label className="mb-2 text-sm font-bold text-slate-700 dark:text-white/80">
              Gültig bis
            </label>
            <input
              {...register("end")}
              type="date"
              className="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent bg-white dark:bg-slate-800 dark:border-slate-600"
            />
            <span className="text-xs text-slate-500 mt-1">
Das Datum muss nach dem festgelegten Startpunkt liegen.
            </span>
          </div>

          <div className="flex flex-col">
            <label className="mb-2 text-sm font-bold text-slate-700 dark:text-white/80">
              Stromart
            </label>
            <select
              {...register("current_type")}
              defaultValue={typeFromUrl ?? "AC"}
              disabled={isTypeLocked}
              className="border border-gray-300 rounded-lg px-3 py-2 bg-white dark:bg-slate-800 dark:border-slate-600 focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <option value="AC">AC (Wechselstrom)</option>
              <option value="DC">DC (Gleichstrom)</option>
            </select>
          </div>
        </div>

        {submitMessage && (
          <div className={`mt-6 p-4 rounded-lg text-sm ${
            submitMessage.includes('Fehler')
              ? 'bg-red-100 text-red-700 border border-red-300'
              : 'bg-green-100 text-green-700 border border-green-300'
          }`}>
            {submitMessage}
          </div>
        )}

        <div className="mt-8 flex justify-end gap-4">
          <Button
            type="button"
            onClick={() => router.back()}
            className="bg-gray-500 hover:bg-gray-600"
          >
            Abbrechen
          </Button>
          <Button
            type="submit"
            disabled={isSubmitting}
            className={isSubmitting ? 'opacity-50 cursor-not-allowed' : ''}

          >
            {isSubmitting ? 'Speichert...' : 'Speichern'}
          </Button>
        </div>
      </form>
    </div>
  );
};

export default NewEmpPriceForm;
