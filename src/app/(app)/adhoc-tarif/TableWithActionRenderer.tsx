'use client';

import React, { useMemo, useEffect, useState } from "react";
import Table from "~/utils/table/table";
import ActionCellRenderer from "./ActionCellRenderer";
import { useRouter } from "next/navigation";
import MessageDialog from "~/app/(app)/util/MessageDialog";

type Props = {
  columnDefs: any[];
  rowData: any[];
};

export default function TableWithActionRenderer({ columnDefs, rowData }: Props) {
  const router = useRouter();

  // --- Refresh on focus (wie gehabt) ---
  useEffect(() => {
    const onFocus = () => router.refresh();
    window.addEventListener("focus", onFocus);
    return () => window.removeEventListener("focus", onFocus);
  }, [router]);

  // --- Dialog State ---
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [rowToDelete, setRowToDelete] = useState<any | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  // --- Trigger aus CellRenderer ---
  const handleAskDelete = (row: any) => {
    setRowToDelete(row);
    setShowDeleteDialog(true);
  };

  const handleDeleteConfirm = async () => {
    if (!rowToDelete?.id) return;
    setIsDeleting(true);
    try {
      const res = await fetch(`/api/locationPrice/${encodeURIComponent(rowToDelete.id)}`, {
        method: "DELETE",
      });
      if (!res.ok && res.status !== 204) {
        const msg = await res.text().catch(() => "");
        throw new Error(msg || `Delete failed (${res.status})`);
      }
      // UI aktualisieren
      setShowDeleteDialog(false);
      setRowToDelete(null);
      router.refresh();
    } catch (e) {
      console.error(e);
      alert("Löschen fehlgeschlagen.");
    } finally {
      setIsDeleting(false);
    }
  };

  const handleDeleteCancel = () => {
    setShowDeleteDialog(false);
    setRowToDelete(null);
  };

  // --- actions-Spalte mit echtem React-Renderer + Param ---
  const cols = useMemo(() => (
    columnDefs.map(col =>
      col.field === "actions"
        ? {
          ...col,
          cellRenderer: ActionCellRenderer,
          cellRendererParams: {
            onDelete: handleAskDelete, // <- WICHTIG: Callback in Renderer verfügbar
          },
        }
        : col
    )
  ), [columnDefs]);

  return (
    <>
      <Table columnDefs={cols} rowData={rowData} />

      {showDeleteDialog && rowToDelete && (
        <MessageDialog
          title="Standortpreis löschen"
          message={`Möchten Sie den Standortpreis wirklich löschen?

Location ID: "${rowToDelete.locationId}"

Diese Aktion kann nicht rückgängig gemacht werden.`}
          onYes={handleDeleteConfirm}
          onNo={handleDeleteCancel}
          yesLabel={isDeleting ? "Wird gelöscht..." : "Löschen"}
          noLabel="Abbrechen"
        />
      )}
    </>
  );
}
