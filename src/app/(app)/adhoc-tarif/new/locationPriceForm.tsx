'use client';

import React from "react";
import { useForm, Controller } from "react-hook-form";
import { useRouter } from "next/navigation";
import Button from "~/component/button";
import type { Location } from "@prisma/client";
import type { LocationPrice } from "../../../../../prismaMongoAdhoc/client";
import { getDateStringFromDateObject } from "~/utils/date/date";

type FormValues = Omit<LocationPrice, 'id'> & { id?: string };

interface Props {
  locations: Location[];
  empId: string;
  typeSlots: Record<string, { allowAC: boolean; allowDC: boolean }>;
}

const DEFAULTS = {
  energy_price: 0.35,
  session_fee: 0.0,
  tax_rate: 19.0,
  blocking_fee: 0.1,
  blocking_fee_max: 12.0,
  blocking_fee_start: 120,
};

const LocationPriceForm: React.FC<Props> = ({ locations, empId, typeSlots }) => {
  const router = useRouter();
  const [selectedLocId, setSelectedLocId] = React.useState<string>("");
  const [isSubmitting, setIsSubmitting] = React.useState(false);
  const [submitMessage, setSubmitMessage] = React.useState<string | null>(null);
  const [submitStatus, setSubmitStatus] = React.useState<"idle" | "success" | "error">("idle");

  // Merkt sich, welche Typen wir gerade (in dieser Session) erstellt haben,
  // damit wir sofort auf den fehlenden Typ umschalten können.
  const [createdByLoc, setCreatedByLoc] = React.useState<Record<string, { AC?: boolean; DC?: boolean }>>({});

  const defaultDate = React.useMemo(() => new Date(), []);


  const {
    register,
    handleSubmit,
    reset,
    control,
    formState: { errors },
  } = useForm<FormValues>({
    defaultValues: {
      ...DEFAULTS,
      start: defaultDate,
      end: defaultDate,
      current_type: "AC",
      empId,
      locationId: "",
    },
  });

  // Effektive Slots = Server-Info ∧ (nicht schon gerade erstellt)
  const effectiveSlot = React.useMemo(() => {
    if (!selectedLocId) return undefined;
    const base = typeSlots[selectedLocId] || { allowAC: true, allowDC: true };
    const created = createdByLoc[selectedLocId] || {};
    return {
      allowAC: base.allowAC && !created.AC,
      allowDC: base.allowDC && !created.DC,
    };
  }, [selectedLocId, typeSlots, createdByLoc]);

  const canPickType = !!effectiveSlot && (effectiveSlot.allowAC && effectiveSlot.allowDC);

  // Wenn Location gewählt/wechseln: Type vorbelegen (fehlt nur einer? -> genau den)
  React.useEffect(() => {
    if (!selectedLocId) return;
    const s = effectiveSlot;
    const preType =
      s && s.allowAC && !s.allowDC ? "AC" :
        s && !s.allowAC && s.allowDC ? "DC" : "AC";

    reset({
      ...DEFAULTS,
      empId,
      locationId: selectedLocId,
      current_type: preType,
      start: defaultDate,
      end: defaultDate,
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedLocId, effectiveSlot]);

  const onSubmit = async (data: FormValues) => {
    setIsSubmitting(true);
    setSubmitMessage(null);
    setSubmitStatus("idle");
    try {
      const res = await fetch("/api/locationPrice/create", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ ...data, id: undefined }),
      });
      if (!res.ok) {
        const j = await res.json().catch(() => null);
        throw new Error(j?.error || "Fehler beim Speichern!");
      }

      // Lokalen Status setzen: der eben gewählte Typ ist jetzt „vorhanden“
      const justCreatedType = data.current_type; // "AC" | "DC"
      const otherType = justCreatedType === "AC" ? "DC" : "AC";

      setCreatedByLoc(prev => ({
        ...prev,
        [selectedLocId]: { ...(prev[selectedLocId] || {}), [justCreatedType]: true }
      }));

      // Sofort auf fehlenden Typ umschalten und Formular zurücksetzen
      setSubmitStatus("success");
      setSubmitMessage(`Erfolgreich gespeichert! Jetzt ${otherType} für diesen Standort anlegen.`);
      reset({
        ...DEFAULTS,
        empId,
        locationId: selectedLocId,
        current_type: otherType,
        start: new Date(),
        end: new Date(),
      });
      // WICHTIG: kein router.refresh() – sonst verlierst du den lokalen Umschalt-Status
    } catch (e) {
      setSubmitStatus("error");
      setSubmitMessage((e as Error).message || "Fehler beim Speichern oder Netzwerkfehler!");
    } finally {
      setIsSubmitting(false);
    }
  };


  return (
    <div className="p-6">
      <form onSubmit={handleSubmit(onSubmit)}>
        {/* Standort */}
        <div className="mb-6">
          <label className="mb-2 block text-sm font-bold text-slate-700 dark:text-white/80">
            Standort auswählen
          </label>
          <select
            {...register("locationId", { required: true })}
            value={selectedLocId}
            onChange={(e) => setSelectedLocId(e.target.value)}
            className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary bg-white dark:bg-slate-800"
          >
            <option value="">Bitte wählen…</option>
            {locations.map((loc) => (
              <option key={loc.id} value={loc.id}>
                {loc.name || loc.id}
              </option>
            ))}
          </select>
          {errors.locationId && <span className="text-red-600 text-sm mt-1">Pflichtfeld</span>}
        </div>

        {/* Preise */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {/* valueAsNumber sorgt dafür, dass Zahlen nicht als String ankommen */}
          <div className="flex flex-col">
            <label className="mb-2 text-sm font-bold text-slate-700 dark:text-white/80">kWh Preis (€)</label>
            <input
              type="number"
              step={0.01}
              {...register("energy_price", { valueAsNumber: true })}
              className="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary bg-white dark:bg-slate-800"
            />
          </div>

          <div className="flex flex-col">
            <label className="mb-2 text-sm font-bold text-slate-700 dark:text-white/80">Startgebühr (€)</label>
            <input
              type="number"
              step={0.01}
              {...register("session_fee", { valueAsNumber: true })}
              className="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary bg-white dark:bg-slate-800"
            />
          </div>

          <div className="flex flex-col">
            <label className="mb-2 text-sm font-bold text-slate-700 dark:text-white/80">Steuersatz (%)</label>
            <input
              type="number"
              step={0.01}
              {...register("tax_rate", { valueAsNumber: true })}
              className="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary bg-white dark:bg-slate-800"
            />
          </div>

          <div className="flex flex-col">
            <label className="mb-2 text-sm font-bold text-slate-700 dark:text-white/80">Blockiergebühr (€/min)</label>
            <input
              type="number"
              step={0.01}
              {...register("blocking_fee", { valueAsNumber: true })}
              className="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary bg-white dark:bg-slate-800"
            />
          </div>

          <div className="flex flex-col">
            <label className="mb-2 text-sm font-bold text-slate-700 dark:text-white/80">Max. Blockiergebühr (€)</label>
            <input
              type="number"
              step={0.01}
              {...register("blocking_fee_max", { valueAsNumber: true })}
              className="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary bg-white dark:bg-slate-800"
            />
          </div>

          <div className="flex flex-col">
            <label className="mb-2 text-sm font-bold text-slate-700 dark:text-white/80">Blockiergebühr ab (min)</label>
            <input
              type="number"
              step={1}
              {...register("blocking_fee_start", { valueAsNumber: true })}
              className="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary bg-white dark:bg-slate-800"
            />
          </div>

          {/* Stromtyp */}
          <div className="mb-4">
            <label className="mb-2 text-sm font-bold text-slate-700 dark:text-white/80">
              Stromtyp
            </label>
            <select
              {...register("current_type", { required: true })}
              disabled={!!effectiveSlot && !canPickType}
              className={`w-full border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary bg-white dark:bg-slate-800 ${effectiveSlot && !canPickType ? "opacity-70 cursor-not-allowed" : ""}`}
            >
              <option value="AC" disabled={!!effectiveSlot && !effectiveSlot.allowAC}>AC</option>
              <option value="DC" disabled={!!effectiveSlot && !effectiveSlot.allowDC}>DC</option>
            </select>
            {effectiveSlot && !canPickType && (
              <div className="text-xs text-slate-500 mt-1">
                Für diesen Standort existiert bereits ein Tarif – es kann nur der fehlende Typ angelegt werden.
              </div>
            )}
            {errors.current_type && <span className="text-red-600 text-sm mt-1">Pflichtfeld</span>}
          </div>

          {/* Gültig ab */}
          <div className="flex flex-col">
            <label className="mb-2 text-sm font-bold text-slate-700 dark:text-white/80">
              Gültig ab
            </label>
            <Controller
              name="start"
              control={control}
              defaultValue={defaultDate}
              rules={{ required: true }}
              render={({ field }) => (
                <input
                  type="date"
                  value={getDateStringFromDateObject(field.value)}
                  onChange={(e) => field.onChange(new Date(e.target.value))}
                  className="border border-gray-300 rounded-lg px-2 py-1 h-10 focus:outline-none focus:ring-2 focus:ring-primary bg-white dark:bg-slate-800"
                />
              )}
            />
            <span className="text-xs text-slate-500 mt-1">
              Darf nicht in der Vergangenheit liegen und muss nach dem bisherigen Start liegen.
            </span>
            {errors.start && <span className="text-red-600 text-sm mt-1">Pflichtfeld</span>}
          </div>

          {/* Gültig bis */}
          <div className="flex flex-col">
            <label className="mb-2 text-sm font-bold text-slate-700 dark:text-white/80">
              Gültig bis
            </label>
            <Controller
              name="end"
              control={control}
              defaultValue={defaultDate}
              rules={{ required: true }}
              render={({ field }) => (
                <input
                  type="date"
                  value={getDateStringFromDateObject(field.value)}
                  onChange={(e) => field.onChange(new Date(e.target.value))}
                  className="border border-gray-300 rounded-lg px-2 py-1 h-10 focus:outline-none focus:ring-2 focus:ring-primary bg-white dark:bg-slate-800"
                />
              )}
            />
            <span className="text-xs text-slate-500 mt-1">
              Muss nach dem Startdatum liegen.
            </span>
            {errors.end && <span className="text-red-600 text-sm mt-1">Pflichtfeld</span>}
          </div>
        </div>

        {/* Submit-Message (korrekte Farben per Status) */}
        {submitMessage && (
          <div
            className={`mt-6 p-4 rounded-lg text-sm ${
              submitStatus === "success"
                ? "bg-green-100 text-green-700 border border-green-300"
                : "bg-red-100 text-red-700 border border-red-300"
            }`}
          >
            {submitMessage}
          </div>
        )}

        {/* Aktionen */}
        <div className="mt-8 flex justify-end gap-4">
          <Button type="button" onClick={() => router.back()} className="bg-gray-500 hover:bg-gray-600">
            Abbrechen
          </Button>
          <Button
            type="submit"
            disabled={isSubmitting || !selectedLocId}
            className={isSubmitting || !selectedLocId ? "opacity-50 cursor-not-allowed" : ""}
          >
            {isSubmitting ? "Speichert..." : "Speichern"}
          </Button>
        </div>
      </form>
    </div>
  );
};

export default LocationPriceForm;
