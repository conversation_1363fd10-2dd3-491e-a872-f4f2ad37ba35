import React from "react";
import prisma from "../../../../server/db/prisma";
import Card from "~/component/card";
import LocationPriceForm from "~/app/(app)/adhoc-tarif/new/locationPriceForm";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import Headline from "~/component/Headline";
import PageContentWrapper from "~/component/PageContentWrapper";
import { MdLocationOn } from "react-icons/md";
import prismaMongo from "~/server/db/mongo";

export const revalidate = 0;

export default async function Page() {
  const session = await getServerSession(authOptions);
  const selectedOuId = session?.user?.selectedOu?.id;
  let empId = session?.user?.selectedOu?.adhocEmpId;

  const eulektroPublicEmp = await prismaMongo.emp.findFirst({
    where: {
      OR: [
        { name: { contains: "Eulektro", mode: "insensitive" } },
        { name: { contains: "Public", mode: "insensitive" } },
        { party_id: "EUL" },
      ],
    },
  });
  if (!empId) empId = eulektroPublicEmp?.id;

  const locations = await prisma.location.findMany({
    where: { ouId: selectedOuId },
    orderBy: { name: "asc" },
  });
  const locationIds = locations.map(l => l.id);

  // Alle bestehenden LocationPrices für diese OU & EMP
  const existing = await prismaMongo.locationPrice.findMany({
    where: { empId },
    orderBy: { locationId: "asc" },
  });

  // Nur die Locations der OU berücksichtigen
  const locPrices = existing.filter(lp => locationIds.includes(lp.locationId ?? ""));

  // availability: locationId -> { hasAC, hasDC }
  const availability = new Map<string, { hasAC: boolean; hasDC: boolean }>();
  for (const l of locations) availability.set(l.id, { hasAC: false, hasDC: false });
  for (const lp of locPrices) {
    const a = availability.get(lp.locationId ?? "");
    if (!a) continue;
    if (lp.current_type === "AC") a.hasAC = true;
    if (lp.current_type === "DC") a.hasDC = true;
  }

  // Nur Locations anzeigen, die NICHT beide Typen bereits haben
  const creatableLocations = locations.filter(l => {
    const a = availability.get(l.id)!;
    return !(a.hasAC && a.hasDC);
  });

  // typeSlots: pro Location erlaubte Typen (für die Form zur Sperrung)
  const typeSlots = Object.fromEntries(
    creatableLocations.map(l => {
      const a = availability.get(l.id)!;
      return [
        l.id,
        { allowAC: !a.hasAC, allowDC: !a.hasDC },
      ];
    })
  );

  return (
    <PageContentWrapper>
      <Headline title="Standort-Preis anlegen" />
      <Card
        header_left={
          <div className="flex items-center gap-2">
            <MdLocationOn size={20} />
            Neuen Standort-Preis konfigurieren
          </div>
        }
      >
        <LocationPriceForm
          locations={creatableLocations}
          empId={empId!}
          typeSlots={typeSlots}
        />
      </Card>
    </PageContentWrapper>
  );
}
