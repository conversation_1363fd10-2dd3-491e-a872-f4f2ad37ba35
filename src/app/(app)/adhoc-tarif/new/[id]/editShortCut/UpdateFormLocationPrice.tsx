'use client';

import React from "react";
import { useForm, Controller } from "react-hook-form";
import { useRouter } from "next/navigation";
import Button from "~/component/button";
import {PropsUpdateLocationPrice, LocationPriceFormValues} from "~/types/adhocTarif/updateLocationPriceTypes";
import { ensureDate, getDateStringFromDateObject } from "~/utils/date/date";


const UpdateFormLocationPrice: React.FC<PropsUpdateLocationPrice> = ({ id, initial, locations }) => {
  const router = useRouter();
  const [isSubmitting, setIsSubmitting] = React.useState(false);
  const [submitMessage, setSubmitMessage] = React.useState<string | null>(null);
  const [submitStatus, setSubmitStatus] = React.useState<"idle" | "success" | "error">("idle");


  const defaultValues: LocationPriceFormValues = {
    locationId: initial.locationId ?? "",
    energy_price: initial.energy_price,
    session_fee: initial.session_fee,
    tax_rate: initial.tax_rate,
    blocking_fee: initial.blocking_fee,
    blocking_fee_max: initial.blocking_fee_max,
    blocking_fee_start: initial.blocking_fee_start,
    start: new Date(),            // Startdatum beim Update default = heute
    end: ensureDate(initial.end),
    current_type: initial.current_type,
  };

  const {
    register,
    handleSubmit,
    control,
    formState: { errors },
  } = useForm<LocationPriceFormValues>({ defaultValues });

  const onSubmit = async (data: LocationPriceFormValues) => {
    setIsSubmitting(true);
    setSubmitMessage(null);
    setSubmitStatus("idle");
    try {
      const res = await fetch(`/api/locationPrice/${encodeURIComponent(id)}`, {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          // Zahlen kommen dank valueAsNumber als number an
          energy_price: data.energy_price,
          session_fee: data.session_fee,
          tax_rate: data.tax_rate,
          blocking_fee: data.blocking_fee,
          blocking_fee_max: data.blocking_fee_max,
          blocking_fee_start: data.blocking_fee_start,
          start: data.start,
          end: data.end,
        }),
      });
      if (!res.ok) {
        const j = await res.json().catch(() => null);
        throw new Error(j?.error || "Fehler beim Speichern!");
      }
      setSubmitStatus("success");
      setSubmitMessage("Erfolgreich gespeichert!");
      router.refresh();
    } catch (e) {
      setSubmitStatus("error");
      setSubmitMessage((e as Error).message || "Fehler beim Speichern oder Netzwerkfehler!");
    } finally {
      setIsSubmitting(false);
    }
  };



  return (
    <div className="p-6">
      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">

          {/* Standort (gesperrt) */}
          <div className="flex flex-col">
            <label className="mb-2 block text-sm font-bold text-slate-700 dark:text-white/80">
              Standort
            </label>
            <select
              {...register("locationId")}
              disabled
              className="w-full border border-gray-300 rounded-lg px-3 py-2 bg-slate-100 dark:bg-slate-900/40 cursor-not-allowed"
            >
              <option value={initial.locationId ?? ""}>
                {locations.find((l) => l.id === initial.locationId)?.name ||
                  initial.locationId ||
                  "Unbekannt"}
              </option>
            </select>
            <span className="text-xs text-slate-500 mt-1">
              Der Standort kann beim Update nicht geändert werden.
            </span>
          </div>

          {/* Stromtyp (gesperrt) */}
          <div className="flex flex-col">
            <label className="mb-2 text-sm font-bold text-slate-700 dark:text-white/80">
              Stromtyp
            </label>
            <select
              {...register("current_type")}
              disabled
              className="w-full border border-gray-300 rounded-lg px-3 py-2 bg-slate-100 dark:bg-slate-900/40 cursor-not-allowed"
            >
              <option value={initial.current_type}>{initial.current_type}</option>
            </select>
            <span className="text-xs text-slate-500 mt-1">
              Der Stromtyp kann beim Update nicht geändert werden.
            </span>
          </div>

          {/* kWh Preis */}
          <div className="flex flex-col">
            <label className="mb-2 text-sm font-bold text-slate-700 dark:text-white/80">
              kWh Preis (€)
            </label>
            <input
              type="number"
              step={0.01}
              {...register("energy_price", { valueAsNumber: true })}
              className="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary bg-white dark:bg-slate-800"
            />
          </div>

          {/* Startgebühr */}
          <div className="flex flex-col">
            <label className="mb-2 text-sm font-bold text-slate-700 dark:text-white/80">
              Startgebühr (€)
            </label>
            <input
              type="number"
              step={0.01}
              {...register("session_fee", { valueAsNumber: true })}
              className="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary bg-white dark:bg-slate-800"
            />
          </div>

          {/* Steuersatz */}
          <div className="flex flex-col">
            <label className="mb-2 text-sm font-bold text-slate-700 dark:text-white/80">
              Steuersatz (%)
            </label>
            <input
              type="number"
              step={0.01}
              {...register("tax_rate", { valueAsNumber: true })}
              className="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary bg-white dark:bg-slate-800"
            />
          </div>

          {/* Blockiergebühr */}
          <div className="flex flex-col">
            <label className="mb-2 text-sm font-bold text-slate-700 dark:text-white/80">
              Blockiergebühr (€/min)
            </label>
            <input
              type="number"
              step={0.01}
              {...register("blocking_fee", { valueAsNumber: true })}
              className="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary bg-white dark:bg-slate-800"
            />
          </div>

          {/* Max. Blockiergebühr */}
          <div className="flex flex-col">
            <label className="mb-2 text-sm font-bold text-slate-700 dark:text-white/80">
              Max. Blockiergebühr (€)
            </label>
            <input
              type="number"
              step={0.01}
              {...register("blocking_fee_max", { valueAsNumber: true })}
              className="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary bg-white dark:bg-slate-800"
            />
          </div>

          {/* Blockiergebühr ab */}
          <div className="flex flex-col">
            <label className="mb-2 text-sm font-bold text-slate-700 dark:text-white/80">
              Blockiergebühr ab (min)
            </label>
            <input
              type="number"
              step={1}
              {...register("blocking_fee_start", { valueAsNumber: true })}
              className="border border-gray-300 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-primary bg-white dark:bg-slate-800"
            />
          </div>

          {/* Gültig ab */}
          <div className="flex flex-col">
            <label className="mb-2 text-sm font-bold text-slate-700 dark:text-white/80">
              Gültig ab
            </label>
            <Controller
              name="start"
              control={control}
              rules={{ required: true }}
              render={({ field }) => (
                <input
                  type="date"
                  value={getDateStringFromDateObject(field.value as unknown as Date)}
                  onChange={(e) => field.onChange(new Date(e.target.value))}
                  className="border border-gray-300 rounded-lg px-2 py-1 h-10 focus:outline-none focus:ring-2 focus:ring-primary bg-white dark:bg-slate-800"
                />
              )}
            />
            <span className="text-xs text-slate-500 mt-1">
              Darf nicht in der Vergangenheit liegen und muss nach dem bisherigen Start liegen.
            </span>
            {errors.start && <span className="text-red-600 text-sm mt-1">Pflichtfeld</span>}
          </div>

          {/* Gültig bis */}
          <div className="flex flex-col">
            <label className="mb-2 text-sm font-bold text-slate-700 dark:text-white/80">
              Gültig bis
            </label>
            <Controller
              name="end"
              control={control}
              rules={{ required: true }}
              render={({ field }) => (
                <input
                  type="date"
                  value={getDateStringFromDateObject(field.value as unknown as Date)}
                  onChange={(e) => field.onChange(new Date(e.target.value))}
                  className="border border-gray-300 rounded-lg px-2 py-1 h-10 focus:outline-none focus:ring-2 focus:ring-primary bg-white dark:bg-slate-800"
                />
              )}
            />
            <span className="text-xs text-slate-500 mt-1">Muss nach dem Startdatum liegen.</span>
            {errors.end && <span className="text-red-600 text-sm mt-1">Pflichtfeld</span>}
          </div>

          {/* Submit-Message */}
          {submitMessage && (
            <div
              className={`md:col-span-2 lg:col-span-3 p-4 rounded-lg text-sm ${
                submitStatus === "success"
                  ? "bg-green-100 text-green-700 border border-green-300"
                  : "bg-red-100 text-red-700 border border-red-300"
              }`}
            >
              {submitMessage}
            </div>
          )}

          {/* Aktionen */}
          <div className="md:col-span-2 lg:col-span-3 flex justify-end gap-4">
            <Button
              type="button"
              onClick={() => history.back()}
              className="bg-gray-500 hover:bg-gray-600"
            >
              Abbrechen
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting}
              className={isSubmitting ? "opacity-50 cursor-not-allowed" : ""}
            >
              {isSubmitting ? "Speichert..." : "Speichern"}
            </Button>
          </div>
        </div>
      </form>
    </div>
  );

};

export default UpdateFormLocationPrice;
