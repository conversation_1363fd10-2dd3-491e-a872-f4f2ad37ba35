import React from "react";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import prisma from "~/server/db/prisma";
import prismaMongo from "~/server/db/mongo";
import Card from "~/component/card";
import Headline from "~/component/Headline";
import PageContentWrapper from "~/component/PageContentWrapper";
import { MdLocationOn } from "react-icons/md";
import UpdateFormLocationPrice from "./UpdateFormLocationPrice";

export const revalidate = 0;

export default async function Page({ params }: { params: { id: string } }) {
  const session = await getServerSession(authOptions);
  const selectedOuId = session?.user?.selectedOu?.id;

  const lp = await prismaMongo.locationPrice.findUnique({
    where: { id: params.id },
    include: {
      location: true,
      Emp: true,
    },
  });

  if (!lp) {
    return (
      <PageContentWrapper>
        <Headline title="Standort-Preis bearbeiten" />
        <Card>Datensatz nicht gefunden.</Card>
      </PageContentWrapper>
    );
  }

  const locations = await prisma.location.findMany({
    where: { ouId: selectedOuId },
    orderBy: { name: "asc" },
  });

  return (
    <PageContentWrapper>
      <Headline title="Standort-Preis bearbeiten" />
      <Card
        header_left={
          <div className="flex items-center gap-2">
            <MdLocationOn size={20} />
            Standort-Preis (Update)
          </div>
        }
      >
        <UpdateFormLocationPrice
          id={lp.id}
          initial={lp}
          locations={locations}
        />
      </Card>
    </PageContentWrapper>
  );
}
