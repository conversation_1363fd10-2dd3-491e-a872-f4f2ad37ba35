import UpsertForm from "./form";
import prisma from "~/server/db/prisma";
export const revalidate = 0;

const getLocations = async () => {
  const location = await prisma.location.findMany({
    include: {
      powerContract: {
        select: {
          id: true,
          typeOfContract: true,
          start: true,
          end: true,
          monthlyFixCost: true,
          baseKWhPrice: true,
          kwhPrice: true,
          contractWith: true,
          contractNumber: true,
          customerNumber: true,
          supplyNetworkOperator: true,
          locationId: true,
        },
      },
    },
  });
  return JSON.parse(JSON.stringify(location));
};

const OfferPage = async ({ params }: { params: { slug: string } }) => {
  const { slug } = params;
  if (!params.slug) {
    return <>Missing Params</>;
  }

  const locations = await getLocations();

  return (
    <>
      <UpsertForm locations={locations} id={slug}></UpsertForm>
    </>
  );
};

export default OfferPage;
