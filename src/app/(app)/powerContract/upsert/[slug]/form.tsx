"use client";
import type { Location, PowerContract } from "@prisma/client";
import type { SubmitHandler } from "react-hook-form";
import { useForm } from "react-hook-form";
import React, { startTransition } from "react";
import { useRouter } from "next/navigation";

interface Props {
  id?: string | undefined;
  locations: Location[];
}

const UpsertForm = ({ locations, id }: Props) => {
  const router = useRouter();
  let location;
  if (id !== undefined) {
    location = locations?.find((x) => x.id == id);
  }

  const {
    getValues,
    register,
    handleSubmit,
    reset,
    formState: { isDirty, dirtyFields, errors },
  } = useForm<PowerContract>({ defaultValues: {} });

  const onSubmit: SubmitHandler<PowerContract> = async (data) => {
    // Mutate external data source
    await fetch(`/api/powerContract/upsert`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ data }),
    });
    reset(data);

    startTransition(() => {
      // Refresh the current route and fetch new data from the server without
      // losing client-side browser or React state.
      router.refresh();
    });
  };

  return (
    <form onSubmit={handleSubmit(onSubmit)}>
      <input disabled={!getValues("id")} type={"text"} {...register("id")} className={"hidden"} />
      <div className="mb-0 rounded-t-2xl p-6">
        <h5 id={"basic-data"} className="dark:text-white">
          Location / {location?.id}
        </h5>
        <ul>
          <li>Name: {location?.name}</li>
        </ul>
      </div>
      <div className="flex-auto p-6 pt-0">
        <h2>Neuer Vertrag</h2>
        <div className="-mx-3 flex flex-wrap">
          <div className="w-6/12 max-w-full flex-0 px-3">
            <label
              className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80"
              htmlFor="Name"
            >
              Standort
            </label>
            <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
              <select
                {...register("locationId")}
                id="locationId"
                className="block w-full rounded-lg border border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500"
              >
                {locations?.map((location) => {
                  return (
                    <option key={location.id} value={location.id}>
                      {location.name}
                    </option>
                  );
                })}
              </select>
            </div>
          </div>
          <div className="w-6/12 max-w-full flex-0 px-3">
            <label
              className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80"
              htmlFor="Name"
            >
              kindOfContact
            </label>
            <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
              <select
                {...register("typeOfContract")}
                id="countries"
                className="block w-full rounded-lg border border-gray-300 bg-gray-50 p-2.5 text-sm text-gray-900 focus:border-blue-500 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:placeholder-gray-400 dark:focus:border-blue-500 dark:focus:ring-blue-500"
              >
                <option value="fixed">Contract Price</option>
                <option value="stock">EPEX-Spotmarkt</option>
              </select>
            </div>
          </div>
          <div className="w-3/12 max-w-full flex-0 px-3">
            <label
              className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80"
              htmlFor="gültig ab"
            >
              Gültig ab
            </label>
            <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
              <input
                {...register("start", { valueAsDate: true })}
                type="date"
                className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
              />
            </div>
          </div>
          <div className="w-3/12 max-w-full flex-0 px-3">
            <label
              className="mb-2 ml-1 text-xs font-bold text-slate-700 dark:text-white/80"
              htmlFor="Gültig bis"
            >
              Gültig bis
            </label>
            <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
              <input
                {...register("end", { valueAsDate: true })}
                type="date"
                className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
              />
            </div>
          </div>
        </div>
        <div className="-mx-3 flex flex-wrap">
          <div className="w-2/12 max-w-full flex-0 px-3">
            <label
              className="mb-2 ml-1 mt-6 text-xs font-bold text-slate-700 dark:text-white/80"
              htmlFor="Firmen Name"
            >
              Preis pro kWh in Euro (Netto)
            </label>
            <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
              <input
                {...register("kwhPrice")}
                type="number"
                step={"0.0001"}
                className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
              />
            </div>
          </div>
          <div className="w-2/12 max-w-full flex-0 px-3">
            <label
              className="mb-2 ml-1 mt-6 text-xs font-bold text-slate-700 dark:text-white/80"
              htmlFor="monthlyFixCost"
            >
              Monatliche Grundgebühr in Euro
            </label>
            <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
              <input
                {...register("monthlyFixCost")}
                type="number"
                step="0.01"
                className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
              />
            </div>
          </div>
          <div className="w-2/12 max-w-full flex-0 px-3">
            <label
              className="mb-2 ml-1 mt-6 text-xs font-bold text-slate-700 dark:text-white/80"
              htmlFor="baseKWhPrice"
            >
              Basis kWh-Preis (Offset zu EPEX)
            </label>
            <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
              <input
                {...register("baseKWhPrice")}
                type="number"
                step="0.0001"
                className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
              />
            </div>
          </div>
          <div className="w-2/12 max-w-full flex-0 px-3">
            <label
              className="mb-2 ml-1 mt-6 text-xs font-bold text-slate-700 dark:text-white/80"
              htmlFor="Stromlieferant"
            >
              Stromlieferant
            </label>
            <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
              <input
                {...register("contractWith")}
                type="text"
                className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
              />
            </div>
          </div>
          <div className="w-2/12 max-w-full flex-0 px-3">
            <label
              className="mb-2 ml-1 mt-6 text-xs font-bold text-slate-700 dark:text-white/80"
              htmlFor="contractNumber"
            >
              Vertragsnummer
            </label>
            <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
              <input
                {...register("contractNumber")}
                type="text"
                className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
              />
            </div>
          </div>
          <div className="w-2/12 max-w-full flex-0 px-3">
            <label
              className="mb-2 ml-1 mt-6 text-xs font-bold text-slate-700 dark:text-white/80"
              htmlFor="customerNumber"
            >
              Kundennummer
            </label>
            <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
              <input
                {...register("customerNumber")}
                type="text"
                className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
              />
            </div>
          </div>
          <div className="w-2/12 max-w-full flex-0 px-3">
            <label
              className="mb-2 ml-1 mt-6 text-xs font-bold text-slate-700 dark:text-white/80"
              htmlFor="supplyNetworkOperator"
            >
              Netzbetreiber
            </label>
            <div className="relative flex w-full flex-wrap items-stretch rounded-lg">
              <input
                {...register("supplyNetworkOperator")}
                type="text"
                className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:border-fuchsia-300 focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
              />
            </div>
          </div>
        </div>
      </div>
      <div className="-mx-3 flex">
        <div className={"flex-3 w-full max-w-full px-3"}>
          <button
            type={"submit"}
            className="float-right mb-0 mt-16 inline-block cursor-pointer rounded-lg border-0 bg-primary bg-150 bg-x-25 px-8 py-2 text-right align-middle text-xs font-bold uppercase leading-pro tracking-tight-soft text-white shadow-soft-md transition-all ease-soft-in hover:scale-102 hover:shadow-soft-xs active:opacity-85 dark:bg-gradient-to-tl dark:from-slate-850 dark:to-gray-850"
          >
            Speichern
          </button>
        </div>
      </div>
    </form>
  );
};

export default UpsertForm;
