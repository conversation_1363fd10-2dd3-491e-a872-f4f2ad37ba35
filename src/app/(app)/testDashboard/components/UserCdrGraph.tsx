"use client";

import React from "react";
import ReactECharts from "echarts-for-react";

import Card from "~/component/card";
import Button from "~/component/button";
import Dropdown from "~/app/(app)/util/Dropdown";
import { MdElectricBolt, MdOutlineCalendarMonth, MdOutlineEuro } from "react-icons/md";
import { TbSum } from "react-icons/tb";
import { FaFileInvoiceDollar } from "react-icons/fa";
import { ouColors } from "~/styles/oucolors/oucolors";
import { userStore } from "~/server/zustand/store";
import { startOfWeekMonday, getFirstDayOfMonth, pad, getFirstDayOfYear } from "~/utils/date/date";
import {Cdr, Metric, Agg, Granularity } from "~/types/CardHolderDashboardTypes/UserCdrGraphTypes";



// --- Date helpers ohne externe Libs ---
function asDate(d?: string | Date | null): Date | null {
  if (!d) return null;
  return d instanceof Date ? d : new Date(d);
}

function pickStart(c: Cdr): Date {
  return (asDate(c.LocalStart_datetime) ?? asDate(c.Start_datetime) ?? new Date(0));
}

function startOfDay(d: Date): Date {
  return new Date(d.getFullYear(), d.getMonth(), d.getDate());
}





function bucketStart(d: Date, g: Granularity): Date {
  switch (g) {
    case "day":
      return startOfDay(d);
    case "week":
      return startOfWeekMonday(d);
    case "month":
      return getFirstDayOfMonth(d);
    case "year":
    default:
      return new Date(d.getFullYear(), 0, 1); // statt getFirstDayOfYear(d)
  }
}



function isoWeek(d: Date): number {
  // ISO-8601 Kalenderwoche
  const tmp = new Date(Date.UTC(d.getFullYear(), d.getMonth(), d.getDate()));
  // Donnerstag der Woche finden
  const dayNum = (tmp.getUTCDay() + 6) % 7; // Mo=0..So=6
  tmp.setUTCDate(tmp.getUTCDate() - dayNum + 3);
  const firstThursday = new Date(Date.UTC(tmp.getUTCFullYear(), 0, 4));
  const weekNum = 1 + Math.round(((+tmp - +firstThursday) / 86400000 - 3) / 7);
  return weekNum;
}

function bucketLabel(d: Date, g: Granularity): string {
  switch (g) {
    case "day":
      return `${d.getFullYear()}-${pad(d.getMonth() + 1)}-${pad(d.getDate())}`;
    case "week": {
      const wk = isoWeek(d);
      return `${d.getFullYear()}-W${pad(wk)}`;
    }
    case "month":
      return `${d.getFullYear()}-${pad(d.getMonth() + 1)}`;
    case "year":
    default:
      return `${d.getFullYear()}`;
  }
}


function computeCost(c: Cdr): number {
  const parts = (c.EnergyCosts ?? 0) + (c.Charging_Time_Cost ?? 0) + (c.Parking_Time_Cost ?? 0);
  if (parts > 0) return parts;
  return c.Calculated_Cost ?? 0;
}

function round2(n: number): number { return Math.round(n * 100) / 100; }

// --- Gap helpers ---
function addDays(d: Date, days: number): Date { const x = new Date(d); x.setDate(x.getDate() + days); return x; }
function addWeeks(d: Date, weeks: number): Date { return addDays(d, 7 * weeks); }
function addMonths(d: Date, months: number): Date { const x = new Date(d); x.setMonth(x.getMonth() + months); return x; }
function addYears(d: Date, years: number): Date { const x = new Date(d); x.setFullYear(x.getFullYear() + years); return x; }

function nextBucket(d: Date, g: Granularity): Date {
  switch (g) {
    case "day": return addDays(d, 1);
    case "week": return addWeeks(d, 1);
    case "month": return addMonths(d, 1);
    case "year": default: return addYears(d, 1);
  }
}

function getRangeFromCdrs(cdrs: Cdr[], granularity: Granularity): { start: Date; end: Date } | null {
  if (!cdrs || cdrs.length === 0) return null;
  let min = Number.POSITIVE_INFINITY;
  let max = Number.NEGATIVE_INFINITY;
  for (const c of cdrs) {
    const t = pickStart(c).getTime();
    if (t < min) min = t;
    if (t > max) max = t;
  }
  const start = bucketStart(new Date(min), granularity);
  const end = bucketStart(new Date(max), granularity);
  return { start, end };
}

function fillMissingBuckets(
  aggregated: Array<{ bucket: string; date: Date; energy: number; sessions: number; cost: number }>,
  cdrs: Cdr[],
  granularity: Granularity,
): Array<{ bucket: string; date: Date; energy: number; sessions: number; cost: number }> {
  const range = getRangeFromCdrs(cdrs, granularity);
  if (!range) return aggregated; // keine Daten -> nichts zu füllen

  const byKey = new Map(aggregated.map(r => [r.bucket, r] as const));
  const out: Array<{ bucket: string; date: Date; energy: number; sessions: number; cost: number }> = [];

  for (let d = range.start; d.getTime() <= range.end.getTime(); d = nextBucket(d, granularity)) {
    const key = bucketLabel(d, granularity);
    const hit = byKey.get(key);
    out.push(hit ?? { bucket: key, date: d, energy: 0, sessions: 0, cost: 0 });
  }
  return out;
}


function aggregateCdrs(
  cdrs: Cdr[],
  granularity: Granularity,
): Array<{ bucket: string; date: Date; energy: number; sessions: number; cost: number }> {
  const map = new Map<string, { date: Date; energy: number; sessions: number; cost: number }>();
  for (const c of cdrs) {
    const d = pickStart(c);
    const bDate = bucketStart(d, granularity);
    const key = bucketLabel(bDate, granularity);
    const val = map.get(key) ?? { date: bDate, energy: 0, sessions: 0, cost: 0 };
    val.energy += c.Volume ?? 0;
    val.sessions += 1;
    val.cost += computeCost(c);
    map.set(key, val);
  }
  return Array.from(map.entries())
    .sort((a, b) => a[1].date.getTime() - b[1].date.getTime())
    .map(([bucket, v]) => ({ bucket, date: v.date, energy: round2(v.energy), sessions: v.sessions, cost: round2(v.cost) }));
}

// CSV Export ohne Lib
function toCsv(rows: Record<string, string | number>[]): string {
  if (!rows || rows.length === 0) return "";
  const headers = Object.keys(rows[0] ?? {}); // <- rows[0] kann nie undefined sein, aber TS meckert sonst
  const escape = (val: string | number) =>
    typeof val === "string" && (val.includes(",") || val.includes("\n") || val.includes("\""))
      ? `"${val.replaceAll("\"", "\"\"")}"`
      : String(val);

  const body = rows
    .map(r =>
      headers
        // r[h] ist für TS evtl. undefined -> defensiv abfangen
        .map(h => escape((r as Record<string, string | number>)[h] ?? ""))
        .join(",")
    )
    .join("\n");

  return headers.join(",") + "\n" + body;
}

function downloadCsv(filename: string, content: string) {
  const blob = new Blob([content], { type: "text/csv;charset=utf-8;" });
  const url = URL.createObjectURL(blob);
  const a = document.createElement("a");
  a.href = url;
  a.download = filename;
  a.click();
  URL.revokeObjectURL(url);
}

// Helper: baue Options-Arrays für eure Dropdown-API
function buildOptions<T extends string>(current: T, items: Array<{ id: T; label: string }>) {
  return items.map(({ id, label }) => ({ id, label, selected: id === current }));
}

// --- Component ---
export default function UserCdrGraph({ cdrs }: { cdrs: Cdr[] }) {
  const [granularity, setGranularity] = React.useState<Granularity>("month");
  const [metric, setMetric] = React.useState<Metric>("energy");
  const [agg, setAgg] = React.useState<Agg>("sum");
  const state = userStore();


  const aggregated = React.useMemo(() => {
    const base = aggregateCdrs(cdrs ?? [], granularity);
    return fillMissingBuckets(base, cdrs ?? [], granularity);
  }, [cdrs, granularity]);


  const chartData = React.useMemo(() => {
    if (agg === "sum") return aggregated;
    return aggregated.map(r => ({
      ...r,
      energy: r.sessions ? round2(r.energy / r.sessions) : 0,
      cost: r.sessions ? round2(r.cost / r.sessions) : 0,
    }));
  }, [aggregated, agg]);

  const yName = metric === "energy" ? (agg === "sum" ? "kWh" : "kWh / Session") : metric === "cost" ? (agg === "sum" ? "€" : "€ / Session") : "Sessions";

  const options = React.useMemo(() => ({
    grid: { left: 40, right: 20, top: 30, bottom: 30 },
    tooltip: { trigger: "axis", axisPointer: { type: "shadow" } }, // für Bars angenehmer
    xAxis: {
      type: "category",
      data: chartData.map(d => d.bucket),
      axisLabel: { fontSize: 10 },
      axisTick: { alignWithLabel: true }, // hübschere Tick-Ausrichtung für Bars
    },
    yAxis: {
      type: "value",
      name: yName,
      nameTextStyle: { fontSize: 10 },
      axisLabel: {
        fontSize: 10,
        formatter: (val: number) => metric === "sessions" ? Math.round(val).toString() : String(val),
      },
      splitLine: { show: true },
    },

    series: [
      {
        name: yName,
        type: "bar",                                    // <- war "line"
        data: chartData.map(d => d[metric as "energy" | "sessions" | "cost"]),
        barMaxWidth: 36,
        emphasis: { focus: "series" },
        color: ouColors[state.selectedOuName ?? "Eulektro GmbH"]?.["--color-primary"] ?? "#4B5563",
      },
    ],
  }), [chartData, metric, yName]);


  function handleExportCsv() {
    const rows = chartData.map(r => ({
      bucket: r.bucket,
      energy: r.energy,
      sessions: r.sessions,
      cost: r.cost,
    }));
    const csv = toCsv(rows);
    downloadCsv(`cdr-aggregation-${granularity}-${agg}.csv`, csv);
  }

  const metricIcon =
    metric === "energy"   ? <MdElectricBolt /> :
      metric === "sessions" ? <FaFileInvoiceDollar />
        :
        "";

  const aggIcon = agg === "sum" ? <TbSum /> : <span>Ø</span>;
  // Dropdown-Optionen nach eurer API
  const granularityOpts = React.useMemo(() => buildOptions(granularity, [
    { id: "year", label: "Jahre" },
    { id: "month", label: "Monate" },
    { id: "week", label: "Wochen" },
    { id: "day", label: "Tage" },
  ]), [granularity]);

  const metricOpts = React.useMemo(() => buildOptions(metric, [
    { id: "energy", label: "Energie (kWh)" },
    { id: "sessions", label: "Ladevorgänge" },
    { id: "cost", label: "Kosten (€)" },
  ]), [metric]);

  const aggOpts = React.useMemo(() => buildOptions(agg, [
    { id: "sum", label: "Summe" },
    { id: "avg", label: " pro Session" },
  ]), [agg]);

  return (
    <Card>
      <div className="mb-2 flex flex-col gap-2 sm:flex-row sm:items-center sm:justify-between">
        <div>
          <div className="text-base font-semibold">Ladeverhalten</div>
          <div className="text-xs text-gray-500">Granularität, Metrik und Aggregation wählbar</div>
        </div>
        <div className="flex flex-wrap items-center gap-2">
          <Dropdown
            title="Granularität"
            options={granularityOpts}
            onChange={(id) => setGranularity(id as Granularity)}
            onDelete={() => {""}}
            canDelete={false}
            icon={<MdOutlineCalendarMonth />}
            small
          />

          <Dropdown
            title="Metrik"
            options={metricOpts}
            onChange={(id) => setMetric(id as Metric)}
            onDelete={() => {""}}
            canDelete={false}
            icon={metricIcon}
            small
          />

          <Dropdown
            title="Aggregation"
            options={aggOpts}
            onChange={(id) => setAgg(id as Agg)}
            onDelete={() => {""}}
            canDelete={false}
            icon = {aggIcon}
            small
          />

          <Button small title="CSV" onClick={handleExportCsv} className="px-2 py-1 text-sm">
            CSV
          </Button>
        </div>
      </div>

      <div className="h-[320px] w-full">
        <ReactECharts option={options} style={{ height: "100%", width: "100%" }} />
      </div>

      <div className="mt-2 text-xs text-gray-500">{chartData.length === 0 ? "Keine Daten für die Auswahl." : `Y-Achse: ${yName}`}</div>
    </Card>
  );
}

// Verwendung:
// <UserCdrGraph cdrs={userCdrs} />
// Du bringst alle CDRs mit; die Komponente aggregiert clientseitig ohne zusätzliche Packages.
