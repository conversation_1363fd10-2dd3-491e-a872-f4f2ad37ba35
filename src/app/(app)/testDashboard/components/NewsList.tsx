// components/NewsList.tsx
"use client";

import Link from "next/link";
import * as React from "react";
import {NewsPost} from "~/types/CardHolderDashboardTypes/NewsListTypes";



type PropsNewsList = {
    posts: NewsPost[];
    openInNewTab?: boolean; // default: true
    className?: string;     // optionales Wrapper-Styling
};

export default function NewsList({
                                     posts,
                                     openInNewTab = true,
                                     className = "",
                                 }: PropsNewsList) {
    return (
        <div className={`mx-auto w-full max-w-3xl space-y-4 ${className}`}>
            {posts.map((p) => {
                const target = openInNewTab ? "_blank" : undefined;
                const rel = openInNewTab ? "noopener noreferrer" : undefined;

                return (
                    <article
                        key={p.href}
                        className="rounded-2xl border p-4 transition hover:shadow-md"
                    >
                        <Link
                            href={p.href}
                            target={target}
                            rel={rel}
                            className="group flex gap-3"
                        >
                            {p.image && (
                                <img
                                    src={p.image}
                                    alt=""
                                    loading="lazy"
                                    className="h-20 w-28 flex-none rounded-lg object-cover"
                                />
                            )}

                            <div className="min-w-0">
                                <h2 className="truncate text-lg font-semibold group-hover:underline">
                                    {p.title}
                                </h2>
                                {p.subtitle && (
                                    <p className="mt-1 line-clamp-2 text-sm text-gray-600">
                                        {p.subtitle}
                                    </p>
                                )}
                                {p.date && (
                                    <time
                                        dateTime={p.date}
                                        className="mt-2 block text-xs text-gray-500"
                                    >
                                        {new Date(p.date).toLocaleDateString("de-DE")}
                                    </time>
                                )}
                            </div>
                        </Link>
                    </article>
                );
            })}
        </div>
    );
}
