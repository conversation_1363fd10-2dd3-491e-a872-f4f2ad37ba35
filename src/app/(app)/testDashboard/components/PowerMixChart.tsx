"use client";

import React, { useMemo } from "react";
import ReactECharts from "echarts-for-react";
import Card from "~/component/card";
import { PowerMixDatum, PowerMixChartProps} from "~/types/CardHolderDashboardTypes/PowerMixChartTypes";

/**
 * PowerMixChart – Donut-/Ringdiagramm für den aktuellen Strommix
 *
 * Idee:
 * - Du gibst den Mix als Prozentwerte rein (muss nicht exakt 100 sein; wird normalisiert).
 * - Optional kannst du gCO₂/kWh (Emissionsintensität) und einen Zeitstempel anzeigen.
 * - Farben werden über eine interne Mapping-Funktion vorbelegt; du kannst sie aber überschreiben.
 *
 * Minimalbeispiel:
 * <PowerMixChart
 *   data={[
 *     { carrier: "Wind", percent: 36 },
 *     { carrier: "Solar", percent: 18 },
 *     { carrier: "Biomasse", percent: 6 },
 *     { carrier: "Wasserkraft", percent: 5 },
 *     { carrier: "Kohle", percent: 20 },
 *     { carrier: "Gas", percent: 10 },
 *     { carrier: "Sonstiges", percent: 5 },
 *   ]}
 *   asOf={new Date()}
 *   intensity_g_per_kwh={325}
 * />
 */



const DEFAULT_COLORS: Record<string, string> = {
  Wind: "#4CAF50",
  Solar: "#FFC107",
  Biomasse: "#8BC34A",
  Wasserkraft: "#03A9F4",
  Kernenergie: "#9C27B0",
  Kohle: "#795548",
  Gas: "#607D8B",
  "Import/Export": "#9E9E9E",
  Sonstiges: "#BDBDBD",
};

function formatAsOf(asOf?: string | Date): string | undefined {
  if (!asOf) return undefined;
  const d = typeof asOf === "string" ? new Date(asOf) : asOf;
  if (Number.isNaN(d.getTime())) return undefined;
  // Europe/Berlin Format
  return new Intl.DateTimeFormat("de-DE", {
    year: "numeric",
    month: "2-digit",
    day: "2-digit",
    hour: "2-digit",
    minute: "2-digit",
    timeZone: "Europe/Berlin",
  }).format(d);
}

function normalizePercent(data: PowerMixDatum[]): PowerMixDatum[] {
  const total = data.reduce((s, x) => s + Math.max(0, x.percent), 0);
  if (!total) return data.map((d) => ({ ...d, percent: 0 }));
  return data.map((d) => ({ ...d, percent: (Math.max(0, d.percent) / total) * 100 }));
}

function collapseSmall(data: PowerMixDatum[], threshold = 2): PowerMixDatum[] {
  const small = data.filter((d) => d.percent < threshold);
  const big = data.filter((d) => d.percent >= threshold);
  const smallSum = small.reduce((s, x) => s + x.percent, 0);
  if (smallSum === 0) return data;


// Falls es schon "Sonstiges" gibt, addieren (typsicher ohne Direktindex-Zugriff)
  const idx = big.findIndex((d) => d.carrier === "Sonstiges");
  if (idx > -1) {
    return big.map((d, i) => (i === idx ? { ...d, percent: d.percent + smallSum } : d));
  }
  return [...big, { carrier: "Sonstiges", percent: smallSum }];
}

const useSeriesData = (
  raw: PowerMixDatum[],
  collapseSmallSlices?: boolean
) => {
  return useMemo(() => {
    const normalized = normalizePercent(raw);
    const finalData = collapseSmallSlices ? collapseSmall(normalized) : normalized;
    return finalData.map((d) => ({
      name: String(d.carrier),
      value: Number(d.percent.toFixed(2)),
      itemStyle: { color: d.color ?? DEFAULT_COLORS[d.carrier] ?? undefined },
    }));
  }, [raw, collapseSmallSlices]);
};



export const PowerMixChart: React.FC<PowerMixChartProps> = ({
                                                              data,
                                                              title = "Aktueller Strommix",
                                                              asOf,
                                                              intensity_g_per_kwh,
                                                              showLegend = true,
                                                              height = 320,
                                                              collapseSmallSlices = true,
                                                            }) => {
  const subtitle = useMemo(() => {
    const parts: string[] = [];
    const ts = formatAsOf(asOf);
    if (ts) parts.push(`Stand: ${ts} (Europe/Berlin)`);
    if (typeof intensity_g_per_kwh === "number") parts.push(`${Math.round(intensity_g_per_kwh)} gCO₂/kWh`);
    return parts.join(" • ");
  }, [asOf, intensity_g_per_kwh]);

  const seriesData = useSeriesData(data, collapseSmallSlices);

  const option = useMemo(() => {
    return {
      tooltip: {
        trigger: "item",
        formatter: (params: any) => {
          const { name, value, percent } = params;
          return `${name}: ${value.toFixed(2)}% (${percent.toFixed ? percent.toFixed(0) : Math.round(percent)}%)`;
        },
      },
      legend: showLegend
        ? {
          orient: "vertical" as const,
          right: 0,
          top: "middle",
          textStyle: { fontSize: 12 },
        }
        : undefined,
      series: [
        {
          name: "Strommix",
          type: "pie" as const,
          radius: ["55%", "80%"],
          avoidLabelOverlap: true,
          label: {
            show: true,
            position: "center" as const,
            formatter: () => {
              const total = seriesData.reduce((s, x: any) => s + x.value, 0);
              return total ? `100%\nMix` : `–`;
            },
            fontSize: 14,
            lineHeight: 18,
          },
          emphasis: {
            label: {
              show: true,
              fontSize: 16,
              fontWeight: "bold" as const,
            },
          },
          labelLine: { show: false },
          data: seriesData,
        },
      ],
      grid: { containLabel: true },
    };
  }, [seriesData, showLegend]);

  return (
    <Card header_left={title}>
      <div style={{ width: "100%", height }}>
        <ReactECharts
          option={option}
          style={{ height: "100%", width: "100%" }}
          notMerge
          lazyUpdate
        />
      </div>
    </Card>

  );
};

export default PowerMixChart;
