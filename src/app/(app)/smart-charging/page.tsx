"use client";

import React, { useState, useEffect } from "react";
import Button from "~/component/button";
import { userStore } from "~/server/zustand/store";
import Dropdown, { Option } from "~/app/(app)/util/Dropdown";
import {
  MdElectricBolt,
  MdSettings,
  MdSchedule,
  MdRepeat,
  MdPower,
  MdOutlineElectricBolt,
} from "react-icons/md";
import { TbPlugConnected } from "react-icons/tb";
import { FaChargingStation, FaMapMarkerAlt } from "react-icons/fa";
import {
  ChargingProfilePurposeType,
  ChargingProfileKindType,
  RecurrencyKindType,
  ChargingRateUnitType,
  ChargingProfileFormData,
  ClearChargingProfileFormData,
  ChargingProfileTargetType,
  getChargingProfilePurposeLabel,
  getChargingProfileKindLabel,
  getRecurrencyKindLabel,
  getChargingRateUnitLabel,
  getChargingProfileTargetTypeLabel,
} from "~/types/ocpp-smart-charging";
import { FaGears } from "react-icons/fa6";
import { ImPower } from "react-icons/im";

interface ChargePoint {
  chargePointId: string;
  displayName: string;
}

interface Location {
  id: string;
  name: string;
  city: string;
  street: string;
}

/**
 * Converts a datetime-local string to ISO 8601 UTC format with Z suffix
 * @param datetimeLocal - String from datetime-local input (YYYY-MM-DDTHH:mm)
 * @returns ISO 8601 UTC string with Z suffix (YYYY-MM-DDTHH:mm:ssZ)
 */
const formatDatetimeToUTC = (datetimeLocal: string): string => {
  if (!datetimeLocal) return datetimeLocal;

  // If already has Z suffix, return as is
  if (datetimeLocal.endsWith('Z')) return datetimeLocal;

  // If already has timezone info (+XX:XX or -XX:XX), convert to UTC
  if (datetimeLocal.includes('+') || datetimeLocal.match(/-\d{2}:\d{2}$/)) {
    return new Date(datetimeLocal).toISOString();
  }

  // Assume local datetime-local input is in local timezone, convert to UTC
  // Add seconds if not present
  const withSeconds = datetimeLocal.includes(':') && datetimeLocal.split(':').length === 2
    ? datetimeLocal + ':00'
    : datetimeLocal;

  // Add Z to indicate UTC (assuming the input is meant to be UTC)
  return withSeconds + 'Z';
};

const SmartChargingPage = () => {
  const state = userStore();
  const [chargePoints, setChargePoints] = useState<ChargePoint[]>([]);
  const [locations, setLocations] = useState<Location[]>([]);
  const [locationChargePoints, setLocationChargePoints] = useState<ChargePoint[]>([]);
  const [loading, setLoading] = useState(false);
  const [existingProfiles, setExistingProfiles] = useState<any[]>([]);
  const [selectedChargePointForProfiles, setSelectedChargePointForProfiles] = useState<string>("");
  const [successMessage, setSuccessMessage] = useState<string>("");
  const [errorMessage, setErrorMessage] = useState<string>("");
  const [jsonCopied, setJsonCopied] = useState<boolean>(false);
  const [showClearForm, setShowClearForm] = useState<boolean>(false);
  const [clearLoading, setClearLoading] = useState<boolean>(false);
  const [formData, setFormData] = useState<ChargingProfileFormData>({
    targetType: ChargingProfileTargetType.SingleChargePoint,
    chargePointId: "",
    locationId: "",
    connectorId: 0,
    chargingProfileId: 1,
    stackLevel: 0,
    chargingProfilePurpose: ChargingProfilePurposeType.TxDefaultProfile,
    chargingProfileKind: ChargingProfileKindType.Absolute,
    chargingRateUnit: ChargingRateUnitType.W,
    schedulePeriods: [
      {
        startPeriod: 0,
        limit: 11000,
        numberPhases: 3,
      },
    ],
  });

  const [clearFormData, setClearFormData] = useState<ClearChargingProfileFormData>({
    targetType: ChargingProfileTargetType.SingleChargePoint,
    chargePointId: "",
    locationId: "",
    connectorId: 0,
  });

  // Load charge points on component mount and when OU changes
  useEffect(() => {
    console.log(state);
    const fetchChargePoints = async () => {
      try {
        // Pass OU ID as query parameter to ensure correct data
        const url = state.selectedOuId
          ? `/api/chargepoints?ouId=${state.selectedOuId}`
          : "/api/chargepoints";

        const response = await fetch(url);
        if (response.ok) {
          const data = await response.json();
          console.log(data);
          setChargePoints(data);
        }
      } catch (error) {
        console.error("Error fetching charge points:", error);
        setErrorMessage("Fehler beim Laden der Ladepunkte");
      }
    };

    const fetchLocations = async () => {
      try {
        const response = await fetch("/api/location");
        if (response.ok) {
          const data = await response.json();
          setLocations(data);
        }
      } catch (error) {
        console.error("Error fetching locations:", error);
        setErrorMessage("Fehler beim Laden der Standorte");
      }
    };

    if (state.selectedOuId) {
      fetchChargePoints();
      fetchLocations();
      // Reset form data when OU changes to avoid stale charge point selections
      setFormData((prev) => ({
        ...prev,
        chargePointId: "",
        locationId: "",
      }));
      setSelectedChargePointForProfiles("");
      setExistingProfiles([]);
      setLocationChargePoints([]);
    }
  }, [state.selectedOuId]);

  // Load charge points for selected location
  const fetchLocationChargePoints = async (locationId: string) => {
    try {
      const response = await fetch(`/api/chargepoints/by-location?locationId=${locationId}`);
      if (response.ok) {
        const data = await response.json();
        setLocationChargePoints(data);
      }
    } catch (error) {
      console.error("Error fetching location charge points:", error);
      setErrorMessage("Fehler beim Laden der Ladepunkte für den Standort");
    }
  };

  // Auto-hide messages after 5 seconds
  useEffect(() => {
    if (successMessage) {
      const timer = setTimeout(() => setSuccessMessage(""), 5000);
      return () => clearTimeout(timer);
    }
  }, [successMessage]);

  useEffect(() => {
    if (errorMessage) {
      const timer = setTimeout(() => setErrorMessage(""), 8000);
      return () => clearTimeout(timer);
    }
  }, [errorMessage]);

  // Load existing profiles for selected charge point
  const fetchExistingProfiles = async (chargePointId: string) => {
    if (!chargePointId) {
      setExistingProfiles([]);
      return;
    }

    try {
      const response = await fetch(
        `/api/smart-charging/get-profiles?chargePointId=${chargePointId}`,
      );
      if (response.ok) {
        const data = await response.json();
        setExistingProfiles(data.data || []);
      } else {
        console.error("Error fetching profiles");
        setExistingProfiles([]);
      }
    } catch (error) {
      console.error("Error fetching profiles:", error);
      setExistingProfiles([]);
    }
  };

  const handleInputChange = (field: keyof ChargingProfileFormData, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleSchedulePeriodChange = (index: number, field: string, value: number) => {
    setFormData((prev) => ({
      ...prev,
      schedulePeriods: prev.schedulePeriods.map((period, i) =>
        i === index ? { ...period, [field]: value } : period,
      ),
    }));
  };

  const addSchedulePeriod = () => {
    setFormData((prev) => ({
      ...prev,
      schedulePeriods: [
        ...prev.schedulePeriods,
        {
          startPeriod: 0,
          limit: 11000,
          numberPhases: 3,
        },
      ],
    }));
  };

  const removeSchedulePeriod = (index: number) => {
    if (formData.schedulePeriods.length > 1) {
      setFormData((prev) => ({
        ...prev,
        schedulePeriods: prev.schedulePeriods.filter((_, i) => i !== index),
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    setSuccessMessage("");
    setErrorMessage("");

    // Validate based on target type
    if (
      formData.targetType === ChargingProfileTargetType.SingleChargePoint &&
      !formData.chargePointId
    ) {
      setErrorMessage("Bitte wählen Sie einen Ladepunkt aus");
      return;
    }

    if (
      formData.targetType === ChargingProfileTargetType.LocationChargePoints &&
      !formData.locationId
    ) {
      setErrorMessage("Bitte wählen Sie einen Standort aus");
      return;
    }

    // Validate transaction ID for TxProfile
    if (
      formData.chargingProfilePurpose === ChargingProfilePurposeType.TxProfile &&
      !formData.transactionId
    ) {
      setErrorMessage("Transaction ID ist erforderlich für transaktionsspezifische Profile");
      return;
    }

    // Show confirmation for bulk operations
    if (formData.targetType !== ChargingProfileTargetType.SingleChargePoint) {
      let targetCount = 0;
      let targetDescription = "";

      if (formData.targetType === ChargingProfileTargetType.LocationChargePoints) {
        targetCount = locationChargePoints.length;
        targetDescription = `alle ${targetCount} Ladepunkte am ausgewählten Standort`;
      } else if (formData.targetType === ChargingProfileTargetType.AllChargePoints) {
        targetCount = chargePoints.length;
        targetDescription = `alle ${targetCount} Ladepunkte in Ihrer OU`;
      }

      if (targetCount === 0) {
        setErrorMessage("Keine Ladepunkte gefunden für die ausgewählte Option");
        return;
      }

      if (
        !confirm(
          `Sind Sie sicher, dass Sie das Charging Profile an ${targetDescription} senden möchten?`,
        )
      ) {
        return;
      }
    }

    setLoading(true);

    try {
      const response = await fetch("/api/smart-charging/set-profile", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setSuccessMessage(result.message);
        } else {
          setErrorMessage(result.message);
        }

        // Refresh existing profiles if viewing the same charge point
        if (selectedChargePointForProfiles === formData.chargePointId) {
          fetchExistingProfiles(formData.chargePointId);
        }
      } else {
        const error = await response.json();
        setErrorMessage(error.message || "Fehler beim Setzen des Charging Profiles");
      }
    } catch (error) {
      console.error("Error setting charging profile:", error);
      setErrorMessage("Fehler beim Setzen des Charging Profiles");
    } finally {
      setLoading(false);
    }
  };

  const handleClearProfile = async (chargePointId: string, profileId?: number) => {
    if (!confirm("Sind Sie sicher, dass Sie dieses Charging Profile löschen möchten?")) {
      return;
    }

    try {
      const response = await fetch("/api/smart-charging/clear-profile", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          targetType: ChargingProfileTargetType.SingleChargePoint,
          chargePointId,
          connectorId: 0,
          ...(profileId && { id: profileId }),
        }),
      });

      if (response.ok) {
        setSuccessMessage("Charging Profile erfolgreich gelöscht");
        fetchExistingProfiles(chargePointId);
      } else {
        const error = await response.json();
        setErrorMessage(error.message || "Fehler beim Löschen des Charging Profiles");
      }
    } catch (error) {
      console.error("Error clearing charging profile:", error);
      setErrorMessage("Fehler beim Löschen des Charging Profiles");
    }
  };

  const handleClearInputChange = (field: keyof ClearChargingProfileFormData, value: any) => {
    setClearFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleClearSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    setSuccessMessage("");
    setErrorMessage("");

    // Validate based on target type
    if (
      clearFormData.targetType === ChargingProfileTargetType.SingleChargePoint &&
      !clearFormData.chargePointId
    ) {
      setErrorMessage("Bitte wählen Sie einen Ladepunkt aus");
      return;
    }

    if (
      clearFormData.targetType === ChargingProfileTargetType.LocationChargePoints &&
      !clearFormData.locationId
    ) {
      setErrorMessage("Bitte wählen Sie einen Standort aus");
      return;
    }

    // Show confirmation for bulk operations
    if (clearFormData.targetType !== ChargingProfileTargetType.SingleChargePoint) {
      let targetCount = 0;
      let targetDescription = "";

      if (clearFormData.targetType === ChargingProfileTargetType.LocationChargePoints) {
        targetCount = locationChargePoints.length;
        targetDescription = `alle ${targetCount} Ladepunkte am ausgewählten Standort`;
      } else if (clearFormData.targetType === ChargingProfileTargetType.AllChargePoints) {
        targetCount = chargePoints.length;
        targetDescription = `alle ${targetCount} Ladepunkte in Ihrer OU`;
      }

      if (targetCount === 0) {
        setErrorMessage("Keine Ladepunkte gefunden für die ausgewählte Option");
        return;
      }

      if (
        !confirm(
          `Sind Sie sicher, dass Sie Charging Profile von ${targetDescription} löschen möchten?`,
        )
      ) {
        return;
      }
    }

    setClearLoading(true);

    try {
      const response = await fetch("/api/smart-charging/clear-profile", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(clearFormData),
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          setSuccessMessage(result.message);
        } else {
          setErrorMessage(result.message);
        }

        // Refresh existing profiles if viewing the same charge point
        if (selectedChargePointForProfiles === clearFormData.chargePointId) {
          fetchExistingProfiles(clearFormData.chargePointId);
        }
      } else {
        const error = await response.json();
        setErrorMessage(error.message || "Fehler beim Löschen des Charging Profiles");
      }
    } catch (error) {
      console.error("Error clearing charging profile:", error);
      setErrorMessage("Fehler beim Löschen des Charging Profiles");
    } finally {
      setClearLoading(false);
    }
  };

  const loadTemplate = (templateName: string) => {
    const templates = {
      "11kw-limit": {
        chargingProfileId: 1,
        stackLevel: 0,
        chargingProfilePurpose: ChargingProfilePurposeType.TxDefaultProfile,
        chargingProfileKind: ChargingProfileKindType.Absolute,
        chargingRateUnit: ChargingRateUnitType.W,
        schedulePeriods: [
          {
            startPeriod: 0,
            limit: 11000,
            numberPhases: 3,
          },
        ],
      },
      "6kw-night": {
        chargingProfileId: 2,
        stackLevel: 0,
        chargingProfilePurpose: ChargingProfilePurposeType.TxDefaultProfile,
        chargingProfileKind: ChargingProfileKindType.Recurring,
        recurrencyKind: RecurrencyKindType.Daily,
        chargingRateUnit: ChargingRateUnitType.W,
        schedulePeriods: [
          {
            startPeriod: 0,
            limit: 6000,
            numberPhases: 3,
          },
          {
            startPeriod: 28800, // 8:00 AM
            limit: 11000,
            numberPhases: 3,
          },
          {
            startPeriod: 72000, // 8:00 PM
            limit: 6000,
            numberPhases: 3,
          },
        ],
      },
      "16a-limit": {
        chargingProfileId: 3,
        stackLevel: 0,
        chargingProfilePurpose: ChargingProfilePurposeType.ChargePointMaxProfile,
        chargingProfileKind: ChargingProfileKindType.Absolute,
        chargingRateUnit: ChargingRateUnitType.A,
        schedulePeriods: [
          {
            startPeriod: 0,
            limit: 16,
            numberPhases: 3,
          },
        ],
      },
      "tx-profile": {
        chargingProfileId: 4,
        stackLevel: 1,
        chargingProfilePurpose: ChargingProfilePurposeType.TxProfile,
        chargingProfileKind: ChargingProfileKindType.Relative,
        chargingRateUnit: ChargingRateUnitType.W,
        transactionId: 12345,
        schedulePeriods: [
          {
            startPeriod: 0,
            limit: 7400, // 7.4kW
            numberPhases: 3,
          },
        ],
      },
    };

    const template = templates[templateName as keyof typeof templates];
    if (template) {
      setFormData((prev) => ({
        ...prev,
        ...template,
        targetType: prev.targetType, // Keep target type
        chargePointId: prev.chargePointId, // Keep selected charge point
        locationId: prev.locationId, // Keep selected location
        connectorId: prev.connectorId, // Keep connector ID
      }));
      setSuccessMessage(`Template "${templateName}" geladen`);
    }
  };

  // Generate preview JSON for the current form data
  const generatePreviewJson = () => {
    // For single charge point, require charge point selection
    if (
      formData.targetType === ChargingProfileTargetType.SingleChargePoint &&
      !formData.chargePointId
    ) {
      return null;
    }

    // For location charge points, require location selection
    if (
      formData.targetType === ChargingProfileTargetType.LocationChargePoints &&
      !formData.locationId
    ) {
      return null;
    }

    const chargingProfile = {
      chargingProfileId: formData.chargingProfileId,
      stackLevel: formData.stackLevel,
      chargingProfilePurpose: formData.chargingProfilePurpose,
      chargingProfileKind: formData.chargingProfileKind,
      chargingSchedule: {
        chargingRateUnit: formData.chargingRateUnit,
        chargingSchedulePeriod: formData.schedulePeriods.map((period) => ({
          startPeriod: period.startPeriod,
          limit: period.limit,
          ...(period.numberPhases && { numberPhases: period.numberPhases }),
        })),
        ...(formData.duration && { duration: formData.duration }),
        ...(formData.startSchedule && { startSchedule: formatDatetimeToUTC(formData.startSchedule) }),
        ...(formData.minChargingRate && { minChargingRate: formData.minChargingRate }),
      },
      ...(formData.transactionId && { transactionId: formData.transactionId }),
      ...(formData.recurrencyKind && { recurrencyKind: formData.recurrencyKind }),
      ...(formData.validFrom && { validFrom: formatDatetimeToUTC(formData.validFrom) }),
      ...(formData.validTo && { validTo: formatDatetimeToUTC(formData.validTo) }),
    };

    return {
      connectorId: formData.connectorId,
      csChargingProfiles: chargingProfile,
    };
  };

  const copyJsonToClipboard = async () => {
    const json = generatePreviewJson();
    if (json) {
      try {
        await navigator.clipboard.writeText(JSON.stringify(json, null, 2));
        setJsonCopied(true);
        setTimeout(() => setJsonCopied(false), 2000);
      } catch (error) {
        console.error("Failed to copy JSON:", error);
      }
    }
  };

  // Helper functions to create dropdown options
  const getTargetTypeOptions = (forClearForm: boolean = false): Option[] => {
    const selectedType = forClearForm ? clearFormData.targetType : formData.targetType;
    return Object.values(ChargingProfileTargetType).map((targetType) => ({
      id: targetType,
      label: getChargingProfileTargetTypeLabel(targetType),
      selected: selectedType === targetType,
    }));
  };

  const getLocationOptions = (): Option[] => {
    return locations.map((location) => ({
      id: location.id,
      label: `${location.name} (${location.city})`,
      selected: formData.locationId === location.id,
    }));
  };

  const getChargePointOptions = (): Option[] => {
    return chargePoints.map((cp) => ({
      id: cp.chargePointId,
      label:
        cp.displayName !== cp.chargePointId
          ? `${cp.chargePointId} (${cp.displayName})`
          : cp.chargePointId,
      selected: formData.chargePointId === cp.chargePointId,
    }));
  };

  const getLocationChargePointOptions = (): Option[] => {
    return locationChargePoints.map((cp) => ({
      id: cp.chargePointId,
      label:
        cp.displayName !== cp.chargePointId
          ? `${cp.chargePointId} (${cp.displayName})`
          : cp.chargePointId,
      selected: formData.chargePointId === cp.chargePointId,
    }));
  };

  const getChargingProfilePurposeOptions = (): Option[] => {
    return Object.values(ChargingProfilePurposeType).map((purpose) => ({
      id: purpose,
      label: getChargingProfilePurposeLabel(purpose),
      selected: formData.chargingProfilePurpose === purpose,
    }));
  };

  const getChargingProfileKindOptions = (): Option[] => {
    return Object.values(ChargingProfileKindType).map((kind) => ({
      id: kind,
      label: getChargingProfileKindLabel(kind),
      selected: formData.chargingProfileKind === kind,
    }));
  };

  const getRecurrencyKindOptions = (): Option[] => {
    return [
      { id: "", label: "Keine Wiederholung", selected: !formData.recurrencyKind },
      ...Object.values(RecurrencyKindType).map((recurrency) => ({
        id: recurrency,
        label: getRecurrencyKindLabel(recurrency),
        selected: formData.recurrencyKind === recurrency,
      })),
    ];
  };

  const getChargingRateUnitOptions = (): Option[] => {
    return Object.values(ChargingRateUnitType).map((unit) => ({
      id: unit,
      label: getChargingRateUnitLabel(unit),
      selected: formData.chargingRateUnit === unit,
    }));
  };

  const getChargePointOptionsForProfiles = (): Option[] => {
    return chargePoints.map((cp) => ({
      id: cp.chargePointId,
      label:
        cp.displayName !== cp.chargePointId
          ? `${cp.chargePointId} (${cp.displayName})`
          : cp.chargePointId,
      selected: selectedChargePointForProfiles === cp.chargePointId,
    }));
  };

  return (
    <div className="mx-auto p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900">Smart Charging</h1>
        <p className="mt-2 text-gray-600">
          Konfigurieren Sie OCPP Smart Charging Profile für Ihre Ladepunkte
        </p>
      </div>

      {/* Success Message */}
      {successMessage && (
        <div className="mb-4 rounded-lg border border-green-200 bg-green-50 p-4">
          <p className="text-green-700">{successMessage}</p>
          <Button
            onClick={() => setSuccessMessage("")}
            className="mt-2 bg-green-600 hover:bg-green-700"
            small
          >
            Schließen
          </Button>
        </div>
      )}

      {/* Error Message */}
      {errorMessage && (
        <div className="mb-4 rounded-lg border border-red-200 bg-red-50 p-4">
          <p className="text-red-700">{errorMessage}</p>
          <Button
            onClick={() => setErrorMessage("")}
            className="mt-2 bg-red-600 hover:bg-red-700"
            small
          >
            Schließen
          </Button>
        </div>
      )}

      <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
        {/* Left Column - Form */}
        <div className="rounded-lg bg-white p-6 shadow-md">
          {/* Mode Toggle - Tab Style */}
          <div className="mb-6">
            {/* Tab Headers */}
            <div className="border-b border-gray-200">
              <div className="-mb-px flex">
                <button
                  type="button"
                  onClick={() => setShowClearForm(false)}
                  className={`border-b-2 px-4 py-2 text-sm font-medium transition-colors ${
                    !showClearForm
                      ? "rounded-md border-gray-400 bg-gray-200 text-slate-500"
                      : "border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700"
                  }`}
                >
                  Profile Setzen
                </button>
                <button
                  type="button"
                  onClick={() => setShowClearForm(true)}
                  className={`border-b-2 px-4 py-2 text-sm font-medium transition-colors ${
                    showClearForm
                      ? "rounded-md border-gray-400 bg-gray-200 text-slate-500"
                      : "border-transparent text-gray-500 hover:border-gray-300 hover:text-gray-700"
                  }`}
                >
                  Profile Löschen
                </button>
              </div>
            </div>
            {/* Tab Content Area */}
            <div className="pt-6">
              {!showClearForm && (
                <>
                  {/* Template Buttons */}
                  <div className="mb-6">
                    <h3 className="mb-3 text-lg font-medium text-gray-900">
                      Vordefinierte Templates
                    </h3>
                    <div className="flex flex-wrap gap-2">
                      <Button
                        type="button"
                        onClick={() => loadTemplate("11kw-limit")}
                        className="bg-blue-600 hover:bg-blue-700"
                        small
                      >
                        11kW Limit
                      </Button>
                      <Button
                        type="button"
                        onClick={() => loadTemplate("6kw-night")}
                        className="bg-green-600 hover:bg-green-700"
                        small
                      >
                        6kW Nacht / 11kW Tag
                      </Button>
                      <Button
                        type="button"
                        onClick={() => loadTemplate("16a-limit")}
                        className="bg-purple-600 hover:bg-purple-700"
                        small
                      >
                        16A Strom-Limit
                      </Button>
                      <Button
                        type="button"
                        onClick={() => loadTemplate("tx-profile")}
                        className="bg-orange-600 hover:bg-orange-700"
                        small
                      >
                        Transaktions-Profil
                      </Button>
                    </div>
                  </div>

                  <form onSubmit={handleSubmit} className="space-y-6">
                    {/* Target Type Selection */}
                    <div>
                      <label className="mb-2 flex items-center text-sm font-medium text-gray-700">
                        Ziel
                      </label>
                      <Dropdown
                        options={getTargetTypeOptions()}
                        onChange={(id) => {
                          handleInputChange("targetType", id as ChargingProfileTargetType);
                          // Reset dependent fields when target type changes
                          setFormData((prev) => ({
                            ...prev,
                            targetType: id as ChargingProfileTargetType,
                            chargePointId: "",
                            locationId: "",
                          }));
                          setLocationChargePoints([]);
                        }}
                        onDelete={() => {}} // Not used
                        canDelete={false}
                        icon={<FaGears className={"mr-1"} />}
                        placeHolder="Ziel auswählen..."
                        className="max-w-64"
                      />
                    </div>

                    {/* Location Selection - Only show for LocationChargePoints */}
                    {formData.targetType === ChargingProfileTargetType.LocationChargePoints && (
                      <div>
                        <label className="mb-2 flex items-center text-sm font-medium text-gray-700">
                          Standort
                        </label>
                        <Dropdown
                          options={getLocationOptions()}
                          onChange={(id) => {
                            handleInputChange("locationId", id);
                            fetchLocationChargePoints(id);
                          }}
                          onDelete={() => {}} // Not used
                          canDelete={false}
                          icon={<FaMapMarkerAlt className={"mr-1"} />}
                          placeHolder="Standort auswählen..."
                          className="max-w-64"
                        />
                        {formData.locationId && (
                          <p className="mt-1 text-xs text-gray-500">
                            {locationChargePoints.length} Ladepunkte an diesem Standort gefunden
                          </p>
                        )}
                      </div>
                    )}

                    {/* Charge Point Selection - Only show for SingleChargePoint */}
                    {formData.targetType === ChargingProfileTargetType.SingleChargePoint && (
                      <div>
                        <label className="mb-2 flex items-center text-sm font-medium text-gray-700">
                          Ladepunkt
                        </label>
                        <Dropdown
                          options={getChargePointOptions()}
                          onChange={(id) => handleInputChange("chargePointId", id)}
                          onDelete={() => {}} // Not used
                          canDelete={false}
                          icon={<FaChargingStation className={"mr-1"} />}
                          placeHolder="Ladepunkt auswählen..."
                          className="max-w-64"
                        />
                      </div>
                    )}

                    {/* All Charge Points Info - Show for AllChargePoints */}
                    {formData.targetType === ChargingProfileTargetType.AllChargePoints && (
                      <div className="rounded-lg bg-blue-50 p-4">
                        <p className="text-sm text-blue-700">
                          Das Profil wird an alle {chargePoints.length} Ladepunkte in Ihrer OU
                          gesendet.
                        </p>
                      </div>
                    )}

                    {/* Basic Profile Settings */}
                    <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                      <div>
                        <label className="block text-sm font-medium text-gray-700">
                          Connector ID
                        </label>
                        <input
                          type="number"
                          min="0"
                          value={formData.connectorId}
                          onChange={(e) =>
                            handleInputChange("connectorId", parseInt(e.target.value))
                          }
                          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:shadow-soft-primary-outline focus:outline-none"
                          placeholder="0 für gesamten Ladepunkt"
                        />
                        <p className="mt-1 text-xs text-gray-500">
                          0 = Gesamter Ladepunkt, 1+ = Spezifischer Connector
                        </p>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700">
                          Profile ID
                        </label>
                        <input
                          type="number"
                          min="1"
                          value={formData.chargingProfileId}
                          onChange={(e) =>
                            handleInputChange("chargingProfileId", parseInt(e.target.value))
                          }
                          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:shadow-soft-primary-outline focus:outline-none"
                          required
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700">
                          Stack Level
                        </label>
                        <input
                          type="number"
                          min="0"
                          value={formData.stackLevel}
                          onChange={(e) =>
                            handleInputChange("stackLevel", parseInt(e.target.value))
                          }
                          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:shadow-soft-primary-outline focus:outline-none"
                          required
                        />
                        <p className="mt-1 text-xs text-gray-500">0 = Höchste Priorität</p>
                      </div>

                      <div>
                        <label className="mb-2 flex items-center text-sm font-medium text-gray-700">
                          Profile Purpose
                        </label>
                        <Dropdown
                          options={getChargingProfilePurposeOptions()}
                          onChange={(id) =>
                            handleInputChange(
                              "chargingProfilePurpose",
                              id as ChargingProfilePurposeType,
                            )
                          }
                          icon={<FaGears className={"mr-1"} />}
                          onDelete={() => {}} // Not used
                          canDelete={false}
                          className="max-w-64"
                        />
                      </div>

                      {/* Transaction ID - Only show for TxProfile */}
                      {formData.chargingProfilePurpose === ChargingProfilePurposeType.TxProfile && (
                        <div>
                          <label className="block text-sm font-medium text-gray-700">
                            Transaction ID
                          </label>
                          <input
                            type="number"
                            min="1"
                            value={formData.transactionId || ""}
                            onChange={(e) =>
                              handleInputChange(
                                "transactionId",
                                e.target.value ? parseInt(e.target.value) : undefined,
                              )
                            }
                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:shadow-soft-primary-outline focus:outline-none"
                            placeholder="Transaction ID eingeben..."
                            required={
                              formData.chargingProfilePurpose ===
                              ChargingProfilePurposeType.TxProfile
                            }
                          />
                          <p className="mt-1 text-xs text-gray-500">
                            Erforderlich für transaktionsspezifische Profile
                          </p>
                        </div>
                      )}

                      <div>
                        <label className="mb-2 flex items-center text-sm font-medium text-gray-700">
                          Profile Kind
                        </label>
                        <Dropdown
                          options={getChargingProfileKindOptions()}
                          onChange={(id) =>
                            handleInputChange("chargingProfileKind", id as ChargingProfileKindType)
                          }
                          onDelete={() => {}} // Not used
                          canDelete={false}
                          className="max-w-64"
                          icon={<MdSchedule className={"mr-1"} />}
                        />
                      </div>

                      <div>
                        <label className="mb-2 block text-sm font-medium text-gray-700">
                          Charging Rate Unit
                        </label>
                        <Dropdown
                          options={getChargingRateUnitOptions()}
                          onChange={(id) =>
                            handleInputChange("chargingRateUnit", id as ChargingRateUnitType)
                          }
                          onDelete={() => {}} // Not used
                          canDelete={false}
                          className="max-w-52"
                          icon={<ImPower className={"mr-1"} />}
                        />
                      </div>
                    </div>

                    {/* Conditional fields based on profile kind */}
                    {formData.chargingProfileKind === ChargingProfileKindType.Recurring && (
                      <div>
                        <label className="mb-2 block text-sm font-medium text-gray-700">
                          Recurrency Kind
                        </label>
                        <Dropdown
                          options={getRecurrencyKindOptions()}
                          onChange={(id) =>
                            handleInputChange("recurrencyKind", id as RecurrencyKindType)
                          }
                          onDelete={() => {}} // Not used
                          canDelete={false}
                          className="w-full"
                        />
                      </div>
                    )}

                    {/* Optional fields */}
                    <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
                      <div>
                        <label className="block text-sm font-medium text-gray-700">
                          Valid From (optional)
                        </label>
                        <input
                          type="datetime-local"
                          value={formData.validFrom || ""}
                          onChange={(e) => handleInputChange("validFrom", e.target.value)}
                          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:shadow-soft-primary-outline focus:outline-none"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700">
                          Valid To (optional)
                        </label>
                        <input
                          type="datetime-local"
                          value={formData.validTo || ""}
                          onChange={(e) => handleInputChange("validTo", e.target.value)}
                          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:shadow-soft-primary-outline focus:outline-none"
                        />
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700">
                          Duration (seconds, optional)
                        </label>
                        <input
                          type="number"
                          min="0"
                          value={formData.duration || ""}
                          onChange={(e) =>
                            handleInputChange(
                              "duration",
                              e.target.value ? parseInt(e.target.value) : undefined,
                            )
                          }
                          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:shadow-soft-primary-outline focus:outline-none"
                          placeholder="86400 für 24h"
                        />
                      </div>
                    </div>

                    {/* Schedule Periods */}
                    <div>
                      <div className="flex items-center justify-between">
                        <h3 className="text-lg font-medium text-gray-900">
                          Charging Schedule Periods
                        </h3>
                        <Button
                          type="button"
                          onClick={addSchedulePeriod}
                          className="bg-green-600 hover:bg-green-700"
                          small
                        >
                          + Period hinzufügen
                        </Button>
                      </div>

                      <div className="mt-4 space-y-4">
                        {formData.schedulePeriods.map((period, index) => (
                          <div key={index} className="rounded-lg border border-gray-200 p-4">
                            <div className="mb-3 flex items-center justify-between">
                              <h4 className="text-sm font-medium text-gray-700">
                                Period {index + 1}
                              </h4>
                              {formData.schedulePeriods.length > 1 && (
                                <Button
                                  type="button"
                                  onClick={() => removeSchedulePeriod(index)}
                                  className="bg-red-600 px-2 py-1 text-xs hover:bg-red-700"
                                  small
                                >
                                  Entfernen
                                </Button>
                              )}
                            </div>

                            <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                              <div>
                                <label className="block text-sm font-medium text-gray-700">
                                  Start Period (seconds)
                                </label>
                                <input
                                  type="number"
                                  min="0"
                                  value={period.startPeriod}
                                  onChange={(e) =>
                                    handleSchedulePeriodChange(
                                      index,
                                      "startPeriod",
                                      parseInt(e.target.value),
                                    )
                                  }
                                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:shadow-soft-primary-outline focus:outline-none"
                                  required
                                />
                              </div>

                              <div>
                                <label className="block text-sm font-medium text-gray-700">
                                  Limit ({formData.chargingRateUnit})
                                </label>
                                <input
                                  type="number"
                                  min="0"
                                  value={period.limit}
                                  onChange={(e) =>
                                    handleSchedulePeriodChange(
                                      index,
                                      "limit",
                                      parseInt(e.target.value),
                                    )
                                  }
                                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:shadow-soft-primary-outline focus:outline-none"
                                  required
                                />
                              </div>

                              <div>
                                <label className="block text-sm font-medium text-gray-700">
                                  Number of Phases (optional)
                                </label>
                                <input
                                  type="number"
                                  min="1"
                                  max="3"
                                  value={period.numberPhases || ""}
                                  onChange={(e) =>
                                    handleSchedulePeriodChange(
                                      index,
                                      "numberPhases",
                                      e.target.value ? parseInt(e.target.value) : undefined,
                                    )
                                  }
                                  className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:shadow-soft-primary-outline focus:outline-none"
                                  placeholder="1-3"
                                />
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Submit Button */}
                    <div className="flex justify-end">
                      <Button type="submit" disabled={loading} className="disabled:opacity-50">
                        {loading ? "Wird gesetzt..." : "Charging Profile setzen"}
                      </Button>
                    </div>
                  </form>
                </>
              )}

              {showClearForm && (
                <form onSubmit={handleClearSubmit} className="space-y-6">
                  <h3 className="text-lg font-medium text-gray-900">Charging Profile Löschen</h3>

                  {/* Target Type Selection */}
                  <div>
                    <label className="mb-2 flex items-center text-sm font-medium text-gray-700">
                      Ziel
                    </label>
                    <Dropdown
                      options={getTargetTypeOptions(true)}
                      onChange={(id) => {
                        handleClearInputChange("targetType", id as ChargingProfileTargetType);
                        // Reset dependent fields when target type changes
                        setClearFormData((prev) => ({
                          ...prev,
                          targetType: id as ChargingProfileTargetType,
                          chargePointId: "",
                          locationId: "",
                        }));
                      }}
                      onDelete={() => {}} // Not used
                      canDelete={false}
                      icon={<FaGears className={"mr-1"} />}
                      placeHolder="Ziel auswählen..."
                      className="max-w-64"
                    />
                  </div>

                  {/* Location Selection - Only show for LocationChargePoints */}
                  {clearFormData.targetType === ChargingProfileTargetType.LocationChargePoints && (
                    <div>
                      <label className="mb-2 flex items-center text-sm font-medium text-gray-700">
                        Standort
                      </label>
                      <Dropdown
                        options={getLocationOptions()}
                        onChange={(id) => {
                          handleClearInputChange("locationId", id);
                          fetchLocationChargePoints(id);
                        }}
                        onDelete={() => {}} // Not used
                        canDelete={false}
                        icon={<FaMapMarkerAlt className={"mr-1"} />}
                        placeHolder="Standort auswählen..."
                        className="max-w-64"
                      />
                      {clearFormData.locationId && (
                        <p className="mt-1 text-xs text-gray-500">
                          {locationChargePoints.length} Ladepunkte an diesem Standort gefunden
                        </p>
                      )}
                    </div>
                  )}

                  {/* Charge Point Selection - Only show for SingleChargePoint */}
                  {clearFormData.targetType === ChargingProfileTargetType.SingleChargePoint && (
                    <div>
                      <label className="mb-2 flex items-center text-sm font-medium text-gray-700">
                        Ladepunkt
                      </label>
                      <Dropdown
                        options={getChargePointOptions()}
                        onChange={(id) => handleClearInputChange("chargePointId", id)}
                        onDelete={() => {}} // Not used
                        canDelete={false}
                        icon={<FaChargingStation className={"mr-1"} />}
                        placeHolder="Ladepunkt auswählen..."
                        className="max-w-64"
                      />
                    </div>
                  )}

                  {/* All Charge Points Info - Show for AllChargePoints */}
                  {clearFormData.targetType === ChargingProfileTargetType.AllChargePoints && (
                    <div className="rounded-lg bg-red-50 p-4">
                      <p className="text-sm text-red-700">
                        Profile werden von allen {chargePoints.length} Ladepunkten in Ihrer OU
                        gelöscht.
                      </p>
                    </div>
                  )}

                  {/* Clear Parameters */}
                  <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
                    <div>
                      <label className="block text-sm font-medium text-gray-700">
                        Connector ID
                      </label>
                      <input
                        type="number"
                        min="0"
                        value={clearFormData.connectorId}
                        onChange={(e) =>
                          handleClearInputChange("connectorId", parseInt(e.target.value))
                        }
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:shadow-soft-primary-outline focus:outline-none"
                        placeholder="0 für gesamten Ladepunkt"
                      />
                      <p className="mt-1 text-xs text-gray-500">
                        0 = Gesamter Ladepunkt, 1+ = Spezifischer Connector
                      </p>
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700">
                        Profile ID (optional)
                      </label>
                      <input
                        type="number"
                        min="1"
                        value={clearFormData.id || ""}
                        onChange={(e) =>
                          handleClearInputChange(
                            "id",
                            e.target.value ? parseInt(e.target.value) : undefined,
                          )
                        }
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:shadow-soft-primary-outline focus:outline-none"
                        placeholder="Spezifische Profile ID..."
                      />
                      <p className="mt-1 text-xs text-gray-500">
                        Leer lassen um alle Profile zu löschen
                      </p>
                    </div>

                    <div>
                      <label className="mb-2 flex items-center text-sm font-medium text-gray-700">
                        Profile Purpose (optional)
                      </label>
                      <Dropdown
                        options={[
                          {
                            id: "",
                            label: "Alle Profile Purpose",
                            selected: !clearFormData.chargingProfilePurpose,
                          },
                          ...Object.values(ChargingProfilePurposeType).map((purpose) => ({
                            id: purpose,
                            label: getChargingProfilePurposeLabel(purpose),
                            selected: clearFormData.chargingProfilePurpose === purpose,
                          })),
                        ]}
                        onChange={(id) =>
                          handleClearInputChange(
                            "chargingProfilePurpose",
                            id ? (id as ChargingProfilePurposeType) : undefined,
                          )
                        }
                        icon={<FaGears className={"mr-1"} />}
                        onDelete={() => {}} // Not used
                        canDelete={false}
                        className="max-w-64"
                      />
                    </div>

                    <div>
                      <label className="block text-sm font-medium text-gray-700">
                        Stack Level (optional)
                      </label>
                      <input
                        type="number"
                        min="0"
                        value={
                          clearFormData.stackLevel !== undefined ? clearFormData.stackLevel : ""
                        }
                        onChange={(e) =>
                          handleClearInputChange(
                            "stackLevel",
                            e.target.value ? parseInt(e.target.value) : undefined,
                          )
                        }
                        className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:shadow-soft-primary-outline focus:outline-none"
                        placeholder="Spezifisches Stack Level..."
                      />
                      <p className="mt-1 text-xs text-gray-500">
                        Leer lassen um alle Stack Level zu löschen
                      </p>
                    </div>
                  </div>

                  {/* Submit Button */}
                  <div className="flex justify-end">
                    <Button
                      type="submit"
                      disabled={clearLoading}
                      className="bg-red-600 hover:bg-red-700 disabled:opacity-50"
                    >
                      {clearLoading ? "Wird gelöscht..." : "Charging Profile löschen"}
                    </Button>
                  </div>
                </form>
              )}
            </div>{" "}
            {/* Close tab content area */}
          </div>
        </div>

        {/* Right Column - JSON Preview */}
        <div className="rounded-lg bg-white p-6 shadow-md">
          <div className="mb-4 flex items-center justify-between">
            <h3 className="text-lg font-medium text-gray-900">JSON Preview</h3>
            {generatePreviewJson() && (
              <Button onClick={copyJsonToClipboard} className="bg-gray-600 hover:bg-gray-700" small>
                {jsonCopied ? "✓ Kopiert" : "JSON kopieren"}
              </Button>
            )}
          </div>

          {/* Target Information */}
          <div className="mb-4">
            <p className="text-sm text-gray-600">
              <strong>Ziel:</strong> {getChargingProfileTargetTypeLabel(formData.targetType)}
            </p>
            {formData.targetType === ChargingProfileTargetType.LocationChargePoints &&
              formData.locationId && (
                <p className="text-sm text-gray-600">
                  <strong>Ladepunkte:</strong> {locationChargePoints.length} am ausgewählten
                  Standort
                </p>
              )}
            {formData.targetType === ChargingProfileTargetType.AllChargePoints && (
              <p className="text-sm text-gray-600">
                <strong>Ladepunkte:</strong> {chargePoints.length} in Ihrer OU
              </p>
            )}
          </div>

          <p className="mb-4 text-sm text-gray-600">
            Vorschau des JSON-Bodys, der an die Longship API gesendet wird:
          </p>

          {generatePreviewJson() ? (
            <div className="max-h-96 overflow-auto rounded-lg bg-gray-50 p-4">
              <pre className="font-mono whitespace-pre-wrap text-sm text-gray-800">
                {JSON.stringify(generatePreviewJson(), null, 2)}
              </pre>
            </div>
          ) : (
            <div className="rounded-lg bg-gray-50 p-4 text-center text-gray-500">
              {formData.targetType === ChargingProfileTargetType.SingleChargePoint
                ? "Wählen Sie einen Ladepunkt aus, um die JSON-Vorschau zu sehen"
                : formData.targetType === ChargingProfileTargetType.LocationChargePoints
                ? "Wählen Sie einen Standort aus, um die JSON-Vorschau zu sehen"
                : "JSON-Vorschau wird angezeigt, sobald alle Felder ausgefüllt sind"}
            </div>
          )}

          {formData.chargePointId && (
            <div className="mt-4 rounded-lg bg-blue-50 p-3">
              <p className="text-sm text-blue-700">
                <strong>API Endpoint:</strong>
                <br />
                <code className="rounded bg-blue-100 px-1 text-xs">
                  POST /v1/chargepoints/{formData.chargePointId}/setchargingprofile
                </code>
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SmartChargingPage;
