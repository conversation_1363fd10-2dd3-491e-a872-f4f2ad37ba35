"use client";
import React, { useState } from "react";
import { KindOfInvoice, StateOfInvoice } from "@prisma/client";
import { FaExclamationTriangle, FaPaperPlane } from "react-icons/fa";
import { FiLoader } from "react-icons/fi";
import Modal from "~/component/modal/modal";

interface ReminderButtonProps {
  invoice: {
    id: string;
    invoiceNumber: string | null;
    invoiceDate: Date | null;
    sumGross: number;
    kindOfInvoice: KindOfInvoice;
    stateOfInvoice: StateOfInvoice;
    paidOnDate: Date | null;
    contact?: {
      invoiceMail: string;
      name?: string;
      invoiceLanguageCode: string;
    } | null;
    user?: {
      email: string;
      name: string;
      lastName: string;
    } | null;
  };
}

const ReminderButton = ({ invoice }: ReminderButtonProps) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isSending, setIsSending] = useState(false);
  const [customMessage, setCustomMessage] = useState("");

  // Check if invoice is overdue (only for unpaid invoices)
  const isOverdue = () => {
    if (
      invoice.paidOnDate || 
      invoice.stateOfInvoice !== StateOfInvoice.CREATED ||
      invoice.kindOfInvoice !== KindOfInvoice.INVOICE ||
      !invoice.invoiceDate
    ) {
      return false;
    }

    const invoiceDate = new Date(invoice.invoiceDate);
    const dueDate = new Date(invoiceDate);
    dueDate.setDate(dueDate.getDate() + 14); // 14 days payment term
    
    return new Date() > dueDate;
  };

  const getDaysOverdue = () => {
    if (!invoice.invoiceDate) return 0;
    
    const invoiceDate = new Date(invoice.invoiceDate);
    const dueDate = new Date(invoiceDate);
    dueDate.setDate(dueDate.getDate() + 14);
    
    const today = new Date();
    const diffTime = today.getTime() - dueDate.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    return Math.max(0, diffDays);
  };

  const sendReminder = async () => {
    setIsSending(true);
    try {
      const response = await fetch("/api/invoice/sendReminder", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          invoiceId: invoice.id,
          customMessage: customMessage.trim() || undefined,
        }),
      });

      if (response.ok) {
        setIsModalOpen(false);
        setCustomMessage("");
        // You might want to show a success message here
        alert("Mahnung wurde erfolgreich versendet!");
      } else {
        const error = await response.text();
        alert(`Fehler beim Versenden der Mahnung: ${error}`);
      }
    } catch (error) {
      console.error("Error sending reminder:", error);
      alert("Fehler beim Versenden der Mahnung");
    } finally {
      setIsSending(false);
    }
  };

  const getRecipientEmail = () => {
    if (invoice.contact) {
      return invoice.contact.invoiceMail;
    }
    if (invoice.user) {
      return invoice.user.email;
    }
    return "";
  };

  const getRecipientName = () => {
    if (invoice.contact) {
      return invoice.contact.name || "Kunde";
    }
    if (invoice.user) {
      return `${invoice.user.name} ${invoice.user.lastName}`;
    }
    return "Kunde";
  };

  if (!isOverdue()) {
    return null;
  }

  const daysOverdue = getDaysOverdue();

  return (
    <>
      <li className="block rounded-lg px-4 py-2 hover:bg-red-50 transition-colors">
        <div className="flex flex-row items-center gap-2">
          <FaExclamationTriangle className="text-red-500" size={16} />
          <button
            onClick={() => setIsModalOpen(true)}
            className="leading-normal text-red-600 hover:text-red-800 font-medium"
            title={`Rechnung ist ${daysOverdue} Tage überfällig`}
          >
            Mahnung senden
          </button>
        </div>
        <div className="text-xs text-red-500 ml-6">
          {daysOverdue} Tage überfällig
        </div>
      </li>

      <Modal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        title="Mahnung senden"
      >
        <div className="space-y-4">
          <div>
            <p className="text-sm text-gray-600">
              <strong>Rechnung:</strong> {invoice.invoiceNumber || `ID: ${invoice.id.slice(-8)}`}
            </p>
            <p className="text-sm text-gray-600">
              <strong>Betrag:</strong> {invoice.sumGross.toLocaleString("de-DE", {
                style: "currency",
                currency: "EUR",
              })}
            </p>
            <p className="text-sm text-gray-600">
              <strong>Empfänger:</strong> {getRecipientName()} ({getRecipientEmail()})
            </p>
            <p className="text-sm text-red-600">
              <strong>Überfällig seit:</strong> {daysOverdue} Tagen
            </p>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Zusätzliche Nachricht (optional)
            </label>
            <textarea
              value={customMessage}
              onChange={(e) => setCustomMessage(e.target.value)}
              placeholder="Hier können Sie eine persönliche Nachricht hinzufügen..."
              className="w-full h-24 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent resize-none"
            />
            <p className="text-xs text-gray-500 mt-1">
              Die Standard-Mahnungstext wird automatisch verwendet. Ihre Nachricht wird zusätzlich eingefügt.
            </p>
          </div>

          <div className="flex justify-end gap-3 pt-4">
            <button
              onClick={() => setIsModalOpen(false)}
              disabled={isSending}
              className="px-4 py-2 text-gray-600 bg-gray-100 rounded-md hover:bg-gray-200 transition-colors disabled:opacity-50"
            >
              Abbrechen
            </button>
            <button
              onClick={sendReminder}
              disabled={isSending}
              className="px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors disabled:opacity-50 flex items-center gap-2"
            >
              {isSending ? (
                <>
                  <FiLoader className="animate-spin" size={16} />
                  Wird gesendet...
                </>
              ) : (
                <>
                  <FaPaperPlane size={14} />
                  Mahnung senden
                </>
              )}
            </button>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default ReminderButton;
