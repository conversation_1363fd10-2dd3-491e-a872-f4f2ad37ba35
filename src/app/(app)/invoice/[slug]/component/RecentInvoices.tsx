"use client";
import React, { useEffect, useState } from "react";
import { KindOfInvoice, StateOfInvoice } from "@prisma/client";
import Link from "next/link";
import { FaFileInvoice, FaFileInvoiceDollar, FaChevronDown, FaChevronRight } from "react-icons/fa";
import { MdCancel } from "react-icons/md";

interface RecentInvoice {
  id: string;
  invoiceNumber: string | null;
  sumGross: number;
  invoiceDate: Date | null;
  kindOfInvoice: KindOfInvoice;
  stateOfInvoice: StateOfInvoice;
  contact?: {
    name: string;
  } | null;
  user?: {
    name: string;
    lastName: string;
  } | null;
}

interface RecentInvoicesProps {
  currentInvoiceId: string;
  contactId?: string | null;
  userId?: string | null;
}

const RecentInvoices = ({ currentInvoiceId, contactId, userId }: RecentInvoicesProps) => {
  const [recentInvoices, setRecentInvoices] = useState<RecentInvoice[]>([]);
  const [loading, setLoading] = useState(true);
  const [isCollapsed, setIsCollapsed] = useState(false);

  useEffect(() => {
    const fetchRecentInvoices = async () => {
      try {
        const params = new URLSearchParams();
        if (contactId) params.append('contactId', contactId);
        if (userId) params.append('userId', userId);

        const response = await fetch(`/api/invoice/recent?${params.toString()}`, {
          method: "GET",
          cache: "no-store",
        });

        if (response.ok) {
          const invoices = await response.json();
          // Filter out the current invoice and limit to 10 most recent
          const filteredInvoices = invoices
            .filter((invoice: RecentInvoice) => invoice.id !== currentInvoiceId)
            .slice(0, 10);
          setRecentInvoices(filteredInvoices);
        }
      } catch (error) {
        console.error("Error fetching recent invoices:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchRecentInvoices();
  }, [currentInvoiceId, contactId, userId]);

  const getInvoiceIcon = (kindOfInvoice: KindOfInvoice, stateOfInvoice: StateOfInvoice) => {
    if (stateOfInvoice === StateOfInvoice.CANCEL) {
      return <MdCancel className="text-red-500" size={16} />;
    }
    
    if (kindOfInvoice === KindOfInvoice.CREDIT) {
      return <FaFileInvoiceDollar className="text-green-600" size={16} />;
    }
    
    return <FaFileInvoice className="text-blue-600" size={16} />;
  };

  const getCustomerName = (invoice: RecentInvoice) => {
    if (invoice.contact) {
      return invoice.contact.name;
    }
    if (invoice.user) {
      return `${invoice.user.name} ${invoice.user.lastName}`;
    }
    return "Unbekannt";
  };

  const formatDate = (date: Date | null) => {
    if (!date) return "";
    return new Date(date).toLocaleDateString("de-DE", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
    });
  };

  const formatAmount = (amount: number) => {
    return amount.toLocaleString("de-DE", {
      style: "currency",
      currency: "EUR",
    });
  };

  if (loading) {
    return (
      <div className="animate-pulse">
        <div className="h-4 bg-gray-200 rounded mb-2"></div>
        <div className="space-y-2">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="h-8 bg-gray-100 rounded"></div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div>
      <li className="block rounded-lg px-4 pt-1 font-bold">
        <button
          onClick={() => setIsCollapsed(!isCollapsed)}
          className="flex items-center gap-2 w-full text-left hover:text-primary transition-colors"
        >
          {isCollapsed ? (
            <FaChevronRight size={12} />
          ) : (
            <FaChevronDown size={12} />
          )}
          Letzte Rechnungen
        </button>
      </li>
      {!isCollapsed && (
        <>
          {recentInvoices.length === 0 ? (
            <li className="block rounded-lg px-4 py-2 text-gray-500">
              Keine weiteren Rechnungen gefunden
            </li>
          ) : (
            recentInvoices.map((invoice) => (
              <li
                key={invoice.id}
                className="block rounded-lg px-4 py-2 hover:bg-gray-200 transition-colors"
              >
                <Link
                  href={`/invoice/${invoice.id}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="flex items-center justify-between gap-2 text-sm"
                >
              <div className="flex items-center gap-2 min-w-0 flex-1">
                {getInvoiceIcon(invoice.kindOfInvoice, invoice.stateOfInvoice)}
                <div className="min-w-0 flex-1">
                  <div className="truncate font-medium">
                    {invoice.invoiceNumber || `ID: ${invoice.id.slice(-8)}`}
                  </div>
                  <div className="truncate text-xs text-gray-600">
                    {getCustomerName(invoice)}
                  </div>
                  <div className="text-xs text-gray-500">
                    {formatDate(invoice.invoiceDate)}
                  </div>
                </div>
              </div>
              <div className="text-right">
                <div className="font-medium text-sm">
                  {formatAmount(invoice.sumGross)}
                </div>
                <div className="text-xs text-gray-500">
                  {invoice.kindOfInvoice === KindOfInvoice.CREDIT ? "Gutschrift" : "Rechnung"}
                </div>
              </div>
            </Link>
          </li>
        ))
      )}
        </>
      )}
    </div>
  );
};

export default RecentInvoices;
