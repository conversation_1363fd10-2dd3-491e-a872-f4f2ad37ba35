"use client";
import React, { FC, useState } from "react";
import { useForm, useFieldArray } from "react-hook-form";
import Button from "~/component/button";
import { FaPlusCircle, FaTrash } from "react-icons/fa";
import Modal from "~/component/modal/modal";
import type { InvoicePosition } from "@prisma/client";

export interface EditableInvoicePosition {
  id?: string;
  pos: number;
  title: string;
  unit: string;
  unitPrice: number;
  amount: number;
  description: string;
  taxRate: number;
}

interface InvoicePositionEditorProps {
  isOpen: boolean;
  onClose: () => void;
  invoiceId: string;
  positions: InvoicePosition[];
  onSave: () => void;
}

interface FormData {
  positions: EditableInvoicePosition[];
}

export const InvoicePositionEditor: FC<InvoicePositionEditorProps> = ({
  isOpen,
  onClose,
  invoiceId,
  positions,
  onSave,
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string>("");

  const {
    register,
    control,
    handleSubmit,
    getValues,
    setValue,
    formState: { errors },
  } = useForm<FormData>({
    defaultValues: {
      positions: positions.map((pos, index) => ({
        id: pos.id,
        pos: index + 1,
        title: pos.title,
        unit: pos.unit || "",
        unitPrice: pos.unitPrice,
        amount: pos.amount,
        description: pos.description || "",
        taxRate: pos.taxRate,
      })),
    },
  });

  const { fields, append, remove } = useFieldArray({
    control,
    name: "positions",
  });

  const addPosition = () => {
    const currentPositions = getValues("positions");
    append({
      pos: currentPositions.length + 1,
      title: "",
      unit: "",
      unitPrice: 0,
      amount: 0,
      description: "",
      taxRate: 19,
    });
  };

  const removePosition = (index: number) => {
    remove(index);
    // Re-number positions
    const updatedFields = getValues("positions");
    updatedFields.forEach((field, fieldIndex) => {
      setValue(`positions.${fieldIndex}.pos`, fieldIndex + 1);
    });
  };

  const onSubmit = async (data: FormData) => {
    setIsSubmitting(true);
    setErrorMessage("");

    try {
      const response = await fetch(`/api/invoice/${invoiceId}/positions`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ positions: data.positions }),
      });

      if (!response.ok) {
        throw new Error("Fehler beim Speichern der Positionen");
      }

      onSave();
      onClose();
    } catch (error) {
      setErrorMessage(error instanceof Error ? error.message : "Ein Fehler ist aufgetreten");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="Invoice-Positionen bearbeiten"
      className="w-4/5 max-w-none"
    >
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
        {errorMessage && (
          <div className="rounded-md bg-red-50 p-4">
            <div className="text-sm text-red-700">{errorMessage}</div>
          </div>
        )}

        <div className="max-h-96 overflow-y-auto">
          {fields.map((field, index) => (
            <div key={field.id} className="mb-4 flex flex-col gap-2 border-b pb-4">
              <div className="flex flex-row items-end gap-2">
                <div className="w-1/12 max-w-full">
                  <label className="block text-sm font-medium">Pos</label>
                  <input
                    {...register(`positions.${index}.pos`, { valueAsNumber: true })}
                    readOnly
                    className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-gray-100 px-3 py-2 text-sm"
                  />
                </div>
                <div className="w-3/12 max-w-full">
                  <label className="block text-sm font-medium">Titel</label>
                  <input
                    {...register(`positions.${index}.title`, {
                      required: "Titel ist erforderlich",
                    })}
                    className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white px-3 py-2 text-sm focus:shadow-soft-primary-outline focus:outline-none"
                  />
                  {errors.positions?.[index]?.title && (
                    <span className="text-xs text-red-500">
                      {errors.positions[index]?.title?.message}
                    </span>
                  )}
                </div>
                <div className="w-1/12 max-w-full">
                  <label className="block text-sm font-medium">Anzahl</label>
                  <input
                    {...register(`positions.${index}.amount`, {
                      required: "Anzahl ist erforderlich",
                      valueAsNumber: true,
                    })}
                    type="number"
                    step="0.01"
                    className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white px-3 py-2 text-sm focus:shadow-soft-primary-outline focus:outline-none"
                  />
                  {errors.positions?.[index]?.amount && (
                    <span className="text-xs text-red-500">
                      {errors.positions[index]?.amount?.message}
                    </span>
                  )}
                </div>
                <div className="w-1/12 max-w-full">
                  <label className="block text-sm font-medium">Einheit</label>
                  <input
                    {...register(`positions.${index}.unit`)}
                    className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white px-3 py-2 text-sm focus:shadow-soft-primary-outline focus:outline-none"
                  />
                </div>
                <div className="w-2/12 max-w-full">
                  <label className="block text-sm font-medium">Einzelpreis</label>
                  <input
                    {...register(`positions.${index}.unitPrice`, {
                      required: "Einzelpreis ist erforderlich",
                      valueAsNumber: true,
                    })}
                    type="number"
                    step="0.001"
                    className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white px-3 py-2 text-sm focus:shadow-soft-primary-outline focus:outline-none"
                  />
                  {errors.positions?.[index]?.unitPrice && (
                    <span className="text-xs text-red-500">
                      {errors.positions[index]?.unitPrice?.message}
                    </span>
                  )}
                </div>
                <div className="w-1/12 max-w-full">
                  <label className="block text-sm font-medium">Ust.</label>
                  <input
                    {...register(`positions.${index}.taxRate`, {
                      required: "Steuersatz ist erforderlich",
                      valueAsNumber: true,
                      min: { value: 0, message: "Steuersatz muss mindestens 0 sein" },
                      max: { value: 100, message: "Steuersatz darf maximal 100 sein" },
                    })}
                    type="number"
                    className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white px-3 py-2 text-sm focus:shadow-soft-primary-outline focus:outline-none"
                  />
                  {errors.positions?.[index]?.taxRate && (
                    <span className="text-xs text-red-500">
                      {errors.positions[index]?.taxRate?.message}
                    </span>
                  )}
                </div>
                <div className="w-1/12 max-w-full">
                  <Button
                    type="button"
                    color="light-red"
                    onClick={() => removePosition(index)}
                    disabled={fields.length === 1}
                  >
                    <FaTrash />
                  </Button>
                </div>
              </div>
              <div className="w-full">
                <label className="block text-sm font-medium">Beschreibung</label>
                <textarea
                  {...register(`positions.${index}.description`)}
                  rows={2}
                  className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white px-3 py-2 text-sm focus:shadow-soft-primary-outline focus:outline-none"
                />
              </div>
            </div>
          ))}
        </div>

        <div className="flex justify-between">
          <Button type="button" color="light-green" onClick={addPosition}>
            <FaPlusCircle className="mr-2" />
            Position hinzufügen
          </Button>
        </div>

        <div className="flex justify-end space-x-2 border-t pt-4">
          <Button type="button" color="gray" onClick={onClose} disabled={isSubmitting}>
            Abbrechen
          </Button>
          <Button type="submit" color="primary" disabled={isSubmitting}>
            {isSubmitting ? "Speichern..." : "Speichern"}
          </Button>
        </div>
      </form>
    </Modal>
  );
};
