"use client";

import { useState, useEffect } from "react";
import { MaintenanceTargetType } from "@prisma/client";
import Button from "~/component/button";
import { AiOutlineSave, AiOutlineCheck } from "react-icons/ai";
import Dropdown, { Option } from "~/app/(app)/util/Dropdown";
import { BiCategory } from "react-icons/bi";
import { FaMapMarkerAlt } from "react-icons/fa";

interface MaintenanceCategory {
  id: string;
  name: string;
  description?: string;
  isDefault: boolean;
}

interface Location {
  id: string;
  name: string;
  city: string;
  street: string;
  evses: {
    uid: string;
    evse_id: string;
    status: string;
  }[];
}

interface FormData {
  date: string;
  description: string;
  notes: string;
  targetType: MaintenanceTargetType;
  locationId: string;
  evseId: string;
  categoryId: string;
}

interface Props {
  categories: MaintenanceCategory[];
  locations: Location[];
  onSubmit: () => void;
}

const QuickMaintenanceForm = ({ categories, locations, onSubmit }: Props) => {
  const [formData, setFormData] = useState<FormData>({
    date: "",
    description: "",
    notes: "",
    targetType: MaintenanceTargetType.LOCATION,
    locationId: "",
    evseId: "",
    categoryId: "",
  });
  const [loading, setLoading] = useState(false);
  const [selectedLocation, setSelectedLocation] = useState<Location | null>(null);
  const [showAllEvses, setShowAllEvses] = useState(false);

  useEffect(() => {
    // Set default date to today
    const today = new Date().toISOString().split("T")[0] ?? new Date().toISOString();
    setFormData((prev) => ({ ...prev, date: today }));
  }, []);

  const handleLocationChange = (locationId: string) => {
    const location = locations.find((l) => l.id === locationId);
    setSelectedLocation(location || null);
    setFormData((prev) => ({
      ...prev,
      locationId,
      evseId: "",
      targetType: MaintenanceTargetType.LOCATION,
    }));
    setShowAllEvses(false);
  };

  // Convert categories to dropdown options
  const categoryOptions: Option[] = categories.map((category) => ({
    id: category.id,
    label: category.name,
    selected: formData.categoryId === category.id,
  }));

  // Convert locations to dropdown options
  const locationOptions: Option[] = locations.map((location) => ({
    id: location.id,
    label: `${location.name} - ${location.city}`,
    selected: formData.locationId === location.id,
  }));

  const handleAllEvsesToggle = () => {
    if (showAllEvses) {
      setFormData((prev) => ({
        ...prev,
        targetType: MaintenanceTargetType.LOCATION,
        evseId: "",
      }));
    } else {
      setFormData((prev) => ({
        ...prev,
        targetType: MaintenanceTargetType.ALL_EVSES,
        evseId: "",
      }));
    }
    setShowAllEvses(!showAllEvses);
  };

  const handleEvseSelect = (evseUid: string) => {
    setFormData((prev) => ({
      ...prev,
      evseId: evseUid,
      targetType: MaintenanceTargetType.EVSE,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);

    try {
      const response = await fetch("/api/maintenance/records", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        // Reset form
        setFormData({
          date: new Date().toISOString().split("T")[0] ?? new Date().toISOString(),
          description: "",
          notes: "",
          targetType: MaintenanceTargetType.LOCATION,
          locationId: "",
          evseId: "",
          categoryId: "",
        });
        setSelectedLocation(null);
        setShowAllEvses(false);
        onSubmit();
      } else {
        const error = await response.json();
        alert(`Fehler: ${error.error}`);
      }
    } catch (error) {
      console.error("Error submitting form:", error);
      alert("Fehler beim Speichern");
    } finally {
      setLoading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      {/* Date and Category Row */}
      <div className="grid grid-cols-1 gap-4 sm:grid-cols-2">
        <div>
          <label className="mb-1 block text-sm font-medium text-gray-700">Datum</label>
          <input
            type="date"
            value={formData.date}
            onChange={(e) => setFormData((prev) => ({ ...prev, date: e.target.value }))}
            className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:shadow-soft-primary-outline focus:outline-none"
            required
          />
        </div>

        <div>
          <label className="mb-1 block text-sm font-medium text-gray-700">Kategorie</label>
          <Dropdown
            title="Kategorie auswählen"
            options={categoryOptions}
            onChange={(categoryId) => setFormData((prev) => ({ ...prev, categoryId }))}
            onDelete={() => {}} // Not used for categories
            canDelete={false}
            icon={<BiCategory size={18} />}
            placeHolder="Kategorie auswählen"
            searchable={true}
            searchPlaceholder="Kategorie suchen..."
            className="w-full"
          />
        </div>
      </div>

      {/* Location Selection */}
      <div>
        <label className="mb-1 block text-sm font-medium text-gray-700">Standort</label>
        <Dropdown
          title="Standort auswählen"
          options={locationOptions}
          onChange={handleLocationChange}
          onDelete={() => {}} // Not used for locations
          canDelete={false}
          icon={<FaMapMarkerAlt size={18} />}
          placeHolder="Standort auswählen"
          searchable={true}
          searchPlaceholder="Standort suchen..."
          className="w-full"
        />
      </div>

      {/* All EVSEs Toggle - only show when location is selected */}
      {selectedLocation && (
        <div className="flex items-center">
          <label className="flex cursor-pointer items-center">
            <input
              type="checkbox"
              checked={showAllEvses}
              onChange={handleAllEvsesToggle}
              className="mr-2"
            />
            <span className="text-sm font-medium text-gray-700">Alle Ladesäulen des Standorts</span>
          </label>
        </div>
      )}

      {/* EVSE Selection */}
      {selectedLocation && selectedLocation.evses.length > 0 && !showAllEvses && (
        <div>
          <label className="mb-2 block text-sm font-medium text-gray-700">
            Ladesäule (optional - leer lassen für gesamten Standort)
          </label>
          <div className="grid max-h-40 grid-cols-1 gap-2 overflow-y-auto sm:grid-cols-2 lg:grid-cols-3">
            {selectedLocation.evses.map((evse) => (
              <button
                key={evse.uid}
                type="button"
                onClick={() => handleEvseSelect(evse.uid)}
                className={`rounded-md border p-2 text-left text-xs transition-colors ${
                  formData.evseId === evse.uid
                    ? "border-primary bg-primary text-white"
                    : "border-gray-300 bg-gray-50 hover:bg-gray-100"
                }`}
              >
                <div className="font-medium">{evse.evse_id}</div>
                <div className="text-xs opacity-75">{evse.status}</div>
              </button>
            ))}
          </div>
          {formData.evseId && (
            <button
              type="button"
              onClick={() =>
                setFormData((prev) => ({
                  ...prev,
                  evseId: "",
                  targetType: MaintenanceTargetType.LOCATION,
                }))
              }
              className="mt-2 text-xs text-gray-500 hover:text-gray-700"
            >
              Auswahl aufheben
            </button>
          )}
        </div>
      )}

      {/* Description */}
      <div>
        <label className="mb-1 block text-sm font-medium text-gray-700">Was wurde gemacht? *</label>
        <textarea
          value={formData.description}
          onChange={(e) => setFormData((prev) => ({ ...prev, description: e.target.value }))}
          className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:shadow-soft-primary-outline focus:outline-none"
          rows={2}
          required
          placeholder="Kurze Beschreibung der durchgeführten Arbeiten..."
        />
      </div>

      {/* Notes */}
      <div>
        <label className="mb-1 block text-sm font-medium text-gray-700">Notizen (optional)</label>
        <textarea
          value={formData.notes}
          onChange={(e) => setFormData((prev) => ({ ...prev, notes: e.target.value }))}
          className="w-full rounded-md border border-gray-300 px-3 py-2 text-sm focus:shadow-soft-primary-outline focus:outline-none"
          rows={2}
          placeholder="Zusätzliche Informationen..."
        />
      </div>

      {/* Submit Button */}
      <Button
        type="submit"
        disabled={loading}
        className="flex w-full items-center justify-center gap-2 sm:max-w-52"
      >
        {loading ? (
          <>Speichern...</>
        ) : (
          <>
            <AiOutlineSave size={16} />
            Wartung eintragen
          </>
        )}
      </Button>
    </form>
  );
};

export default QuickMaintenanceForm;
