import { notFound } from "next/navigation";
import prisma from "~/server/db/prisma";
import { Role } from "@prisma/client";
import ThgSignupForm from "./form";

interface Params {
  hash: string;
  email: string;
}

const getValidThgUser = async (params: Params) => {
  const decodedEmail = decodeURIComponent(params.email);

  const user = await prisma.user.findFirst({
    where: {
      email: decodedEmail,
      signUpHash: params.hash,
      role: Role.THG_BUYER,
      emailVerified: null,
    },
    include: {
      ou: true,
    },
  });

  if (!user) {
    throw new Error("Invalid signup link or user already verified");
  }

  return user;
};

const ThgRegistrationPage = async ({ params }: { params: Params }) => {
  try {
    const user = await getValidThgUser(params);

    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-50 dark:bg-gray-900 p-4">
        <div className="w-full max-w-4xl rounded-2xl bg-white p-8 shadow-soft-xl dark:bg-gray-950 dark:shadow-soft-dark-xl">
          <div className="mb-8 text-center">
            <h2 className="mb-2 text-3xl font-bold text-primary">THG-Quoten Portal</h2>
            <h3 className="mb-2 text-xl font-semibold text-gray-800 dark:text-white">
              Willkommen, {user.name}!
            </h3>
            <p className="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
              Sie wurden eingeladen, sich als THG-Quotenkäufer zu registrieren. Nach der
              Registrierung können Sie Angebote für THG-Quoten abgeben.
            </p>
          </div>

          <ThgSignupForm user={user} />
        </div>
      </div>
    );
  } catch (error) {
    console.error("THG signup error:", error);
    return notFound();
  }
};

export default ThgRegistrationPage;
