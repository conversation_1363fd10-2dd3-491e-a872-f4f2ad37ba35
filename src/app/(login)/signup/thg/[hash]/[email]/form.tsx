"use client";

import React, { useState } from "react";
import type { SubmitHandler } from "react-hook-form";
import { useForm } from "react-hook-form";
import But<PERSON> from "~/component/button";
import { FiLoader } from "react-icons/fi";
import { FaCheckCircle, FaExclamationTriangle } from "react-icons/fa";
import type { User, Ou } from "@prisma/client";

interface FormProps {
  password: string;
  passwordRepeat: string;
  phone?: string;
  contactPerson: string;
  website?: string;
}

interface ThgSignupFormProps {
  user: User & { ou: Ou };
}

const ThgSignupForm = ({ user }: ThgSignupFormProps) => {
  const [submissionSuccess, setSubmissionSuccess] = useState(false);
  const [submissionError, setSubmissionError] = useState<string | null>(null);

  const {
    register,
    handleSubmit,
    watch,
    formState: { errors, isSubmitting },
  } = useForm<FormProps>();

  const password = watch("password");

  const onSubmit: SubmitHandler<FormProps> = async (data) => {
    setSubmissionError(null);

    try {
      const res = await fetch("/api/user/verify-thg-invite", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ...data,
          email: user.email,
          signUpHash: user.signUpHash,
        }),
      });

      if (res.ok) {
        setSubmissionSuccess(true);
      } else {
        const errorData = await res.json();
        setSubmissionError(
          errorData.error || "Registrierung fehlgeschlagen. Bitte versuchen Sie es erneut.",
        );
        console.error("Registration failed:", errorData);
      }
    } catch (error) {
      setSubmissionError("Netzwerkfehler. Bitte überprüfen Sie Ihre Internetverbindung.");
      console.error("Registration error:", error);
    }
  };

  if (submissionSuccess) {
    return (
      <div className="text-center">
        <div className="relative mb-6 rounded-lg border border-l-4 border-green-200 border-green-500 bg-green-50 shadow-sm dark:border-green-800 dark:bg-green-900/20">
          <div className="flex flex-col items-center p-6">
            <FaCheckCircle className="mb-4 text-4xl text-green-500" />
            <h3 className="mb-2 text-lg font-semibold text-green-800 dark:text-green-200">
              Registrierung erfolgreich!
            </h3>
            <p className="text-sm text-green-700 dark:text-green-300">
              Ihr Account wurde erfolgreich erstellt. Sie können sich jetzt anmelden.
            </p>
          </div>
        </div>
        <Button onClick={() => (window.location.href = "/login/thg")}>Zur Anmeldung</Button>
      </div>
    );
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
      {submissionError && (
        <div className="relative rounded-lg border border-l-4 border-red-200 border-red-500 bg-red-50 shadow-sm dark:border-red-800 dark:bg-red-900/20">
          <div className="flex items-center p-4">
            <FaExclamationTriangle className="mr-3 h-5 w-5 text-red-500" />
            <p className="text-sm font-medium text-red-800 dark:text-red-200">{submissionError}</p>
          </div>
        </div>
      )}

      <div className="space-y-6">
        {/* Firmeninformationen Section */}
        <div className="rounded-lg border border-gray-200 bg-gray-50 p-4 dark:border-gray-700 dark:bg-gray-800/50">
          <h3 className="mb-3 flex items-center text-sm font-semibold text-gray-900 dark:text-white">
            <svg
              className="mr-2 h-4 w-4 text-blue-500"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h3M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
              />
            </svg>
            Firmeninformationen
          </h3>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div>
              <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
                E-Mail
              </label>
              <input
                value={user.email}
                disabled={true}
                readOnly={true}
                className="input cursor-not-allowed bg-gray-100 dark:bg-gray-700"
              />
            </div>

            <div>
              <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
                Firmenname
              </label>
              <input
                value={user.companyName || ""}
                disabled={true}
                readOnly={true}
                className="input cursor-not-allowed bg-gray-100 dark:bg-gray-700"
              />
            </div>
          </div>
        </div>

        {/* Kontaktinformationen Section */}
        <div className="rounded-lg border border-gray-200 bg-gray-50 p-4 dark:border-gray-700 dark:bg-gray-800/50">
          <h3 className="mb-3 flex items-center text-sm font-semibold text-gray-900 dark:text-white">
            <svg
              className="mr-2 h-4 w-4 text-green-500"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
              />
            </svg>
            Kontaktinformationen
          </h3>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div>
              <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
                Ansprechpartner *
              </label>
              <input
                {...register("contactPerson", { required: "Ansprechpartner ist erforderlich" })}
                type="text"
                placeholder="Max Mustermann"
                className="input"
              />
              {errors.contactPerson && (
                <p className="mt-1 flex items-center text-sm text-red-500">
                  <svg className="mr-1 h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                    <path
                      fillRule="evenodd"
                      d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                      clipRule="evenodd"
                    />
                  </svg>
                  {errors.contactPerson.message}
                </p>
              )}
            </div>

            <div>
              <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
                Telefon
              </label>
              <input
                {...register("phone")}
                type="tel"
                placeholder="+49 123 456789"
                className="input"
              />
            </div>

            <div className="md:col-span-2">
              <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
                Website
              </label>
              <input
                {...register("website")}
                type="url"
                placeholder="https://www.ihre-firma.de"
                className="input"
              />
            </div>
          </div>
        </div>

        {/* Sicherheit Section */}
        <div className="rounded-lg border border-gray-200 bg-gray-50 p-4 dark:border-gray-700 dark:bg-gray-800/50">
          <h3 className="mb-3 flex items-center text-sm font-semibold text-gray-900 dark:text-white">
            <svg
              className="mr-2 h-4 w-4 text-red-500"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
              />
            </svg>
            Passwort festlegen
          </h3>
          <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
            <div>
              <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
                Passwort *
              </label>
              <input
                {...register("password", {
                  required: "Passwort ist erforderlich",
                  minLength: { value: 8, message: "Passwort muss mindestens 8 Zeichen lang sein" },
                })}
                type="password"
                placeholder="••••••••"
                className="input"
              />
              {errors.password && (
                <p className="mt-1 flex items-center text-sm text-red-500">
                  <svg className="mr-1 h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                    <path
                      fillRule="evenodd"
                      d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                      clipRule="evenodd"
                    />
                  </svg>
                  {errors.password.message}
                </p>
              )}
            </div>

            <div>
              <label className="mb-2 block text-sm font-medium text-gray-700 dark:text-gray-300">
                Passwort wiederholen *
              </label>
              <input
                {...register("passwordRepeat", {
                  required: "Passwort wiederholen ist erforderlich",
                  validate: (value) => value === password || "Passwörter stimmen nicht überein",
                })}
                type="password"
                placeholder="••••••••"
                className="input"
              />
              {errors.passwordRepeat && (
                <p className="mt-1 flex items-center text-sm text-red-500">
                  <svg className="mr-1 h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                    <path
                      fillRule="evenodd"
                      d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                      clipRule="evenodd"
                    />
                  </svg>
                  {errors.passwordRepeat.message}
                </p>
              )}
            </div>
          </div>

          {/* Passwort-Hinweise */}
          <div className="mt-3 rounded-lg border border-blue-200 bg-blue-50 p-3 dark:border-blue-800 dark:bg-blue-900/20">
            <p className="flex items-start text-xs text-blue-800 dark:text-blue-200">
              <svg
                className="mr-2 mt-0.5 h-4 w-4 flex-shrink-0"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fillRule="evenodd"
                  d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                  clipRule="evenodd"
                />
              </svg>
              Das Passwort muss mindestens 8 Zeichen lang sein und sollte Buchstaben, Zahlen und
              Sonderzeichen enthalten.
            </p>
          </div>
        </div>
      </div>

      {/* Submit Button */}
      <div className="pt-4">
        <Button type="submit" disabled={isSubmitting} className={"w-full"}>
          <span className="flex items-center justify-center">
            {isSubmitting ? (
              <>
                <FiLoader className="mr-2 animate-spin" />
                Registrierung wird verarbeitet...
              </>
            ) : (
              <>
                <svg className="mr-2 h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
                Registrierung abschließen
              </>
            )}
          </span>
        </Button>

        <p className="mt-3 text-center text-xs text-gray-500 dark:text-gray-400">
          Mit der Registrierung stimmen Sie den Nutzungsbedingungen zu.
        </p>
      </div>
    </form>
  );
};

export default ThgSignupForm;
