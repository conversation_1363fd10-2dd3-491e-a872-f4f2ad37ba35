"use client";
import React, { useState } from "react";
import <PERSON><PERSON> from "~/component/button";
import { signIn } from "next-auth/react";
import { useForm } from "react-hook-form";
import { FiLoader } from "react-icons/fi";

interface Props {
  searchParams: {
    error?: string;
    callbackUrl?: string;
  };
}

interface LoginFormValues {
  email: string;
  password: string;
}

const ThgLoginPage = (props: Props) => {
  const {
    register,
    formState: { isSubmitting, isSubmitted, errors },
    handleSubmit,
  } = useForm<LoginFormValues>();

  const onSubmit = async (data: LoginFormValues) => {
    await signIn("credentials", {
      ...data,
      redirect: true,
      callbackUrl: "/thg",
    });
  };

  return (
    <div className={"flex flex-col justify-center gap-1"}>
      <h1 className={"text-primary text-center mb-4"}>THG-Quoten Portal</h1>
      <p className="text-center text-gray-600 mb-6">
        Will<PERSON>mmen im THG-Quoten Verwaltungsportal für Energieanbieter
      </p>
      
      <div className={"flex w-full justify-center"}>
        <form role="form" className={"w-full max-w-md"} method="post" onSubmit={handleSubmit(onSubmit)}>
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              E-Mail Adresse
            </label>
            <input
              {...register("email", { required: "E-Mail ist erforderlich" })}
              type="email"
              name={"email"}
              placeholder="<EMAIL>"
              className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
            />
            {errors.email && (
              <p className="text-red-500 text-sm mt-1">{errors.email.message}</p>
            )}
          </div>
          
          <div className="mb-6">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Passwort
            </label>
            <input
              {...register("password", { required: "Passwort ist erforderlich" })}
              type="password"
              name={"password"}
              placeholder="••••••••"
              className="block w-full appearance-none rounded-lg border border-solid border-gray-300 bg-white bg-clip-padding px-3 py-2 text-sm font-normal leading-5.6 text-gray-700 outline-none transition-all ease-soft placeholder:text-gray-500 focus:shadow-soft-primary-outline focus:outline-none dark:bg-gray-950 dark:text-white/80 dark:placeholder:text-white/80"
            />
            {errors.password && (
              <p className="text-red-500 text-sm mt-1">{errors.password.message}</p>
            )}
          </div>

          {props.searchParams.error && (
            <div className="relative border-l-4 border-red-500 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg shadow-sm mb-4">
              <div className="flex items-center p-4">
                <span className="text-sm font-medium text-red-800 dark:text-red-200">
                  Anmeldung fehlgeschlagen. Bitte überprüfen Sie Ihre Eingaben.
                </span>
              </div>
            </div>
          )}

          <div className="flex flex-col gap-3 text-center">
            <Button
              disabled={isSubmitting || (isSubmitted && Object.keys(errors).length == 0)}
              className={"w-full bg-primary text-white"}
            >
              Anmelden
              {isSubmitting ||
                (isSubmitted && Object.keys(errors).length == 0 && (
                  <FiLoader className="ml-1 animate-spin" />
                ))}
            </Button>

            <a className={"text-sm text-gray-600 hover:text-primary"} href={"/passwortReset"}>
              Passwort vergessen?
            </a>
          </div>
        </form>
      </div>
      
      <div className="mt-8 text-center text-sm text-gray-500">
        <p>Noch kein Zugang? Kontaktieren Sie uns für eine Einladung.</p>
      </div>
    </div>
  );
};

export default ThgLoginPage;
