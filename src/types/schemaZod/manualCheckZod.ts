import { z } from "zod";

// Einzel-Item: trims überall, expected/error sind optional -> leere Strings
export const manualCheckItemSchema = z.object({
  chargePointId: z.string().min(1).transform(s => s.trim()),
  monitoringKey: z.string().min(1).transform(s => s.trim()),
  expectedValues: z.string().optional().transform(s => (s ?? "").trim()),
  errorValues: z.string().optional().transform(s => (s ?? "").trim()),
});

// Body: entweder ein Item oder ein Array von Items (max 100)
export const manualCheckBodySchema = z.union([
  manualCheckItemSchema,
  z.array(manualCheckItemSchema).min(1).max(100),
]);

// Abgeleitete TS-Typen (für API/Client)
export type ManualCheckItem = z.infer<typeof manualCheckItemSchema>;
export type ManualCheckBody = z.infer<typeof manualCheckBodySchema>;

// Helper: Immer als Array zurückgeben (vereinfacht die API-Logik)
export function normalizeManualCheckBody(input: ManualCheckBody): ManualCheckItem[] {
  return Array.isArray(input) ? input : [input];
}
