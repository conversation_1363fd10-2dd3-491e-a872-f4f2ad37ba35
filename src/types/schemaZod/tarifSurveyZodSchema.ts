import { z } from "zod";

export const tarifSurveySchema = z.object({
  sessionFee: z.coerce.number().nonnegative().optional(),
  kwh: z.coerce.number().nonnegative(),
  blockingFee: z.coerce.number().nonnegative().optional(),
  blockingFeeBeginAtMin: z.coerce.number().int().nonnegative().optional(),
  blockingFeeMax: z.coerce.number().nonnegative().optional(),
  currentType: z.enum(["AC", "DC"]).optional(),
  description: z.string().optional(),


  });
