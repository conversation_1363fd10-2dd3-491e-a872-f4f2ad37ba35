import { Option } from "~/app/(app)/util/Dropdown";

export interface OccpMonitorKeyFormData {
  chargePointVendor: string;
  chargePointModel: string;
  configKeyName: string;
  expectedValues: string;   // wird aus expectedTags generiert
  errorValues: string;      // wird aus errorTags generiert
}

export type InitialKey = {
  id: string;
  chargePointVendor: string;
  chargePointModel: string;
  configKeyName: string;
  expectedValues?: string | string[] | null;
  errorValues?: string | string[] | null;
  // optional, z. B. ["CP-01","CP-05"] oder "CP-01,CP-05"
  excludedChargePoints?: string[] | string | null;
};

export interface Props {
  initial: InitialKey;
  chargePointVendors: Option[];
  vendorModelMap: Record<string, Option[]>;
  allChargePoints: { chargePointId: string; displayName: string }[];
}