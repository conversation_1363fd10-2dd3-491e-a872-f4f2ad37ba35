import { Option } from "~/app/(app)/util/Dropdown";

export interface OccpMonitorKeyFormData {
  chargePointVendor: string;
  chargePointModel: string;
  configKeyName: string;
  expectedValues: string;   // wird aus expectedTags generiert
  errorValues: string;      // wird aus errorTags generiert
}

export interface Props {
  chargePointVendors: Option[];
  vendorModelMap: Record<string, Option[]>;
  allChargePoints: { chargePointId: string; displayName: string }[];
}

