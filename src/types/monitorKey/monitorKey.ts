export type ChargePoint = {
  chargePointId: string;
  displayName: string;
  chargePointVendor?: string;
  chargePointModel?: string;
};

export type LocationForUi = {
  id: string;
  name: string;
  chargePoints: ChargePoint[];
};

export type QueueItem = {
  id: string;
  chargePointId: string;
  monitoringKey: string;
  expectedValue?: string;
  errorValue?: string;
};

//api response
export type ApiResult =
  | { status: "ok"; key: string; value: string; chargePointId: string }
  | { status: "error"; key: string; value: string; chargePointId: string; reason: "listed-error" | "unexpected" }
  | { status: "not-found"; key: string; chargePointId: string; reason: "key-not-in-payload" }
  | { status: "no-payload"; key: string; chargePointId: string }
  | { status: "fetch-failed"; key: string; chargePointId: string; message: string };






