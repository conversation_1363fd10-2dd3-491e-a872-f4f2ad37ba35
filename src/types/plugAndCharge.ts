import { PlugAndChargeEventType } from "@prisma/client";
import { z } from "zod";

export const IncomingPlugAndChargeEventSchema = z.object({
  emaid: z.string(),
  evseid: z.string(),
  pcid: z.string(),
  sessionId: z.string(),
  validatedContract: z.boolean().optional(),
  matchingConfidence: z.number().min(0).max(1),
  plugInEventTimestamp: z.coerce.date(),
  incomingRequestTimestamp: z.coerce.date(),
  uuid: z.string(),
});

export type IncomingPlugAndChargeEvent = z.infer<typeof IncomingPlugAndChargeEventSchema>;

// Interface für ausgehende PlugAndCharge Events
export interface OutgoingPlugAndChargeEvent {
  evseid: string;
  plugInEventTimestamp: Date; // ISO DateTime string
  locationid?: string; // Note: lowercase 'id' as per your example
  latitude?: number;
  longitude?: number;
}

// Interface für die Datenbank-Entity
export interface PlugAndChargeEventEntity {
  id: string;
  raw: any; // JSON object
  evseid: string;
  locationId?: string;
  plugInEventTimestamp: Date;
  latitude?: number;
  longitude?: number;
  type: PlugAndChargeEventType;
  emaid?: string | null; // Optional for outgoing events
  pcid?: string | null; // Optional for outgoing events
  sessionId?: string | null; // When OUTGOING is linked, contains the incoming sessionId
  matchingConfidence?: number | null; // Optional for outgoing events
  ouId?: string | null; // OU assignment for multi-tenant filtering
  createdAt: Date;
  updatedAt: Date;
}

// Interface für API Responses
export interface PlugAndChargeEventResponse {
  success: boolean;
  data?: PlugAndChargeEventEntity | PlugAndChargeEventEntity[];
  count?: number;
  error?: string;
  message?: string;
  details?: any;
}

// Interface für Query Parameter beim GET Request
export interface PlugAndChargeEventQueryParams {
  evseid?: string;
  type?: "INCOMING" | "OUTGOING";
  emaid?: string;
  limit?: number;
  offset?: number;
}
