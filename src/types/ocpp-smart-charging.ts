// OCPP 1.6 Smart Charging Profile Types and Enums

/**
 * ChargingProfilePurposeType defines the purpose of the charging profile
 */
export enum ChargingProfilePurposeType {
  /** Limits the power or current of the entire Charge Point */
  ChargePointMaxProfile = "ChargePointMaxProfile",
  /** Default profile to be used for new transactions */
  TxDefaultProfile = "TxDefaultProfile", 
  /** Profile for a specific transaction */
  TxProfile = "TxProfile"
}

/**
 * ChargingProfileKindType defines how the schedule periods are interpreted
 */
export enum ChargingProfileKindType {
  /** Schedule periods are relative to a fixed point in time */
  Absolute = "Absolute",
  /** Schedule restarts at the beginning of each recurrence period */
  Recurring = "Recurring",
  /** Schedule periods are relative to the start of charging */
  Relative = "Relative"
}

/**
 * RecurrencyKindType defines the recurrence pattern for recurring profiles
 */
export enum RecurrencyKindType {
  /** Schedule restarts every 24 hours */
  Daily = "Daily",
  /** Schedule restarts every week */
  Weekly = "Weekly"
}

/**
 * ChargingRateUnitType defines the unit of the charging rate limit
 */
export enum ChargingRateUnitType {
  /** Ampere (current limit) */
  A = "A",
  /** Watt (power limit) */
  W = "W"
}

/**
 * ChargingSchedulePeriod defines a single period in the charging schedule
 */
export interface ChargingSchedulePeriod {
  /** Start of the period in seconds from the beginning of schedule (must be integer) */
  startPeriod: number;
  /** Charging rate limit during this period (must be integer) */
  limit: number;
  /** Number of phases that can be used for charging (optional, must be integer 1-3) */
  numberPhases?: number;
}

/**
 * ChargingSchedule defines the charging schedule
 */
export interface ChargingSchedule {
  /** Duration of the schedule in seconds (optional, must be integer) */
  duration?: number;
  /** Starting point of an absolute schedule (optional, ISO 8601 format with Z suffix) */
  startSchedule?: string;
  /** Unit of the charging rate */
  chargingRateUnit: ChargingRateUnitType;
  /** List of charging schedule periods */
  chargingSchedulePeriod: ChargingSchedulePeriod[];
  /** Minimum charging rate supported by the EV (optional, must be integer) */
  minChargingRate?: number;
}

/**
 * ChargingProfile defines a complete charging profile
 */
export interface ChargingProfile {
  /** Unique identifier for this profile (must be integer) */
  chargingProfileId: number;
  /** Transaction ID for TxProfile purpose (optional, must be integer) */
  transactionId?: number;
  /** Stack level for profile priority (0 = highest priority, must be integer) */
  stackLevel: number;
  /** Purpose of the charging profile */
  chargingProfilePurpose: ChargingProfilePurposeType;
  /** Kind of charging profile */
  chargingProfileKind: ChargingProfileKindType;
  /** Recurrence pattern for recurring profiles (optional) */
  recurrencyKind?: RecurrencyKindType;
  /** Start time for the profile validity (optional, ISO 8601 format with Z suffix) */
  validFrom?: string;
  /** End time for the profile validity (optional, ISO 8601 format with Z suffix) */
  validTo?: string;
  /** The charging schedule */
  chargingSchedule: ChargingSchedule;
}

/**
 * SetChargingProfileRequest as expected by Longship API
 */
export interface SetChargingProfileRequest {
  /** Connector ID (0 for entire charge point, must be integer) */
  connectorId: number;
  /** The charging profile to set */
  csChargingProfiles: ChargingProfile;
}

/**
 * Target type for charging profile operations
 */
export enum ChargingProfileTargetType {
  /** Single charge point */
  SingleChargePoint = "SingleChargePoint",
  /** All charge points at a location */
  LocationChargePoints = "LocationChargePoints",
  /** All charge points in the OU */
  AllChargePoints = "AllChargePoints"
}

/**
 * Form data for creating a charging profile
 */
export interface ChargingProfileFormData {
  // Target selection
  targetType: ChargingProfileTargetType;
  chargePointId: string;
  locationId?: string;

  connectorId: number;
  chargingProfileId: number;
  transactionId?: number;
  stackLevel: number;
  chargingProfilePurpose: ChargingProfilePurposeType;
  chargingProfileKind: ChargingProfileKindType;
  recurrencyKind?: RecurrencyKindType;
  validFrom?: string;
  validTo?: string;
  duration?: number;
  startSchedule?: string;
  chargingRateUnit: ChargingRateUnitType;
  minChargingRate?: number;
  // Schedule periods as a simplified array for form handling
  schedulePeriods: {
    startPeriod: number;
    limit: number;
    numberPhases?: number;
  }[];
}

/**
 * Form data for clearing charging profiles
 */
export interface ClearChargingProfileFormData {
  // Target selection
  targetType: ChargingProfileTargetType;
  chargePointId: string;
  locationId?: string;

  // Clear profile parameters
  connectorId: number;
  id?: number; // Profile ID to clear (optional)
  chargingProfilePurpose?: ChargingProfilePurposeType;
  stackLevel?: number;
}

/**
 * Helper function to get display labels for enum values
 */
export const getChargingProfilePurposeLabel = (purpose: ChargingProfilePurposeType): string => {
  switch (purpose) {
    case ChargingProfilePurposeType.ChargePointMaxProfile:
      return "Charge Point Maximum";
    case ChargingProfilePurposeType.TxDefaultProfile:
      return "Transaction Default";
    case ChargingProfilePurposeType.TxProfile:
      return "Transaction Specific";
    default:
      return purpose;
  }
};

export const getChargingProfileKindLabel = (kind: ChargingProfileKindType): string => {
  switch (kind) {
    case ChargingProfileKindType.Absolute:
      return "Absolute (Fixed Time)";
    case ChargingProfileKindType.Recurring:
      return "Recurring (Repeating)";
    case ChargingProfileKindType.Relative:
      return "Relative (Start of Charging)";
    default:
      return kind;
  }
};

export const getRecurrencyKindLabel = (recurrency: RecurrencyKindType): string => {
  switch (recurrency) {
    case RecurrencyKindType.Daily:
      return "Daily (Every 24 hours)";
    case RecurrencyKindType.Weekly:
      return "Weekly (Every 7 days)";
    default:
      return recurrency;
  }
};

export const getChargingRateUnitLabel = (unit: ChargingRateUnitType): string => {
  switch (unit) {
    case ChargingRateUnitType.A:
      return "Ampere (Current)";
    case ChargingRateUnitType.W:
      return "Watt (Power)";
    default:
      return unit;
  }
};

export const getChargingProfileTargetTypeLabel = (targetType: ChargingProfileTargetType): string => {
  switch (targetType) {
    case ChargingProfileTargetType.SingleChargePoint:
      return "Einzelner Ladepunkt";
    case ChargingProfileTargetType.LocationChargePoints:
      return "Alle Ladepunkte am Standort";
    case ChargingProfileTargetType.AllChargePoints:
      return "Alle Ladepunkte";
    default:
      return targetType;
  }
};
