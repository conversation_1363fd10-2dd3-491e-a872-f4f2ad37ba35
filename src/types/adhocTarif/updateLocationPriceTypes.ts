import type { Location } from "@prisma/client";

export type PropsUpdateLocationPrice = {
  id: string;
  initial: {
    id: string;
    locationId: string | null;
    start: string | Date;
    end: string | Date;
    energy_price: number;
    blocking_fee: number;
    blocking_fee_start: number;
    blocking_fee_max: number;
    session_fee: number;
    tax_rate: number;
    current_type: "AC" | "DC";
    location?: { id: string; name?: string | null } | null;
  };
  locations: Location[];
};

export type LocationPriceFormValues = {
  locationId: string;
  energy_price: number;
  session_fee: number;
  tax_rate: number;
  blocking_fee: number;
  blocking_fee_max: number;
  blocking_fee_start: number;
  start: Date;
  end: Date;
  current_type: "AC" | "DC";
};


