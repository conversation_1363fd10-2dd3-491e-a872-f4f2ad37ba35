

export type Tier = {
  threshold: number;   // CO₂ in kg
  key: string;
  label: string;       // Anzeige im Modal
  imgSrc?: string;
  color?: string;
  bonus?: string;
};

export type UserDataAchievment = {
  isCollector: boolean;
  userId: string;
  cdrCount?: number;
  co2Kg?: number;
  nachtsLaden?: number;
  laengsteLadeStreak?: number;
  besuchteStandorte?: number;
  besuchteEulektroStandorte?: number;
  finishedBadgeImageUrls?: string[];
} & Record<string, unknown>;