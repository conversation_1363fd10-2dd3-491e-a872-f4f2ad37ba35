 export type EventStatus = "UPCOMING" | "ONGOING" | "ENDED";
 type EnrollmentStatus = "PARTICIPATING" | "COMPLETED" | "FAILED" | null;
 type Metric = { label: string; param: string; target: number; start?: number; window?: "event"|"month"|"week"|"rolling30d"; color?: string; value?: number };
export type Reward = { type: "discount_percent"|"badge"|"text"; value?: number; description?: string };

export type EventDTO = {
  id: string;
  code: string;
  title: string;
  description?: string | null;
  badgeImageUrl?: string | null;
  startsAt?: string | null;
  endsAt?: string | null;
  phase: EventStatus;
  enrollmentStatus: EnrollmentStatus;
  stats?: Metric[] | null;
  reward?: Reward | null;
};



//api
 export type MetricDTO = {
   label?: string;
   param: string;
   target: number;
   start?: number;
   window?: "event" | "month" | "week" | "rolling30d";
   color?: string;
   order?: number;
 };
 export type RewardDTO= { type?: "discount_percent" | "badge" | "text"; value?: number; description?: string };
