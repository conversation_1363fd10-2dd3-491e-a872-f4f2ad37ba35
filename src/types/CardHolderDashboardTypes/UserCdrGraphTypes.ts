export type Cdr = {
  CDR_ID: string;
  Start_datetime: string | Date;
  End_datetime: string | Date;
  LocalStart_datetime?: string | Date | null;
  LocalEnd_datetime?: string | Date | null;
  Volume?: number | null; // kWh
  Calculated_Cost?: number | null;
  EnergyCosts?: number | null;
  Charging_Time_Cost?: number | null;
  Parking_Time_Cost?: number | null;
};

export type Granularity = "year" | "month" | "week" | "day";
export type Metric = "energy" | "sessions" | "cost";
export type Agg = "sum" | "avg";