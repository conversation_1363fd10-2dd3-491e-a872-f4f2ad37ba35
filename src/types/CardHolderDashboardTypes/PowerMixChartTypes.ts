export type PowerCarrier =
  | "Wind"
  | "Solar"
  | "Biomasse"
  | "Wasserkraft"
  | "Kernenergie"
  | "Kohle"
  | "Gas"
  | "Import/Export"
  | "Sonstiges";

export type AnyString = string & Record<never, never>; // vermeidet ESLint-Warnung zu {}

export type PowerMixDatum = {
  carrier: PowerCarrier | AnyString; // bekannte + beliebige Labels erlaubt
  percent: number;
  color?: string;
};

export type PowerMixChartProps = {
  data: PowerMixDatum[];
  title?: string;
  asOf?: string | Date; // Zeitstempel der Daten
  intensity_g_per_kwh?: number; // z.B. 325 gCO2/kWh
  showLegend?: boolean;
  height?: number | string; // default 320
  /**
   * Falls true, werden sehr kleine Segmente (<2%) zu "Sonstiges" aggregiert.
   * Praktisch, wenn du viele Träger hast.
   */
  collapseSmallSlices?: boolean;
};