import type { ReactNode } from "react";

export type FaqItem = {
  id?: string;
  question: string;
  answer: ReactNode;
  category?: string;
  tags?: string[];
  updatedAt?: string | Date;
  priority?: 0 | 1 | 2;
};

export type FaqProps = {
  items?: FaqItem[];
  categories?: string[];
  hotline?: {
    phone?: string;
    email?: string;
    hours?: string;
    note?: string;
  };
  className?: string;
  initialQuery?: string;
};
