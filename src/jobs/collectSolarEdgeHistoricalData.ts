import { Queue } from 'quirrel/next';
import prisma from '~/server/db/prisma';
import { collectHistoricalData } from '~/services/solarEdge/historicalDataService';

interface CollectHistoricalDataPayload {
  dashboardId?: string; // If specified, collect only for this dashboard
  daysBack?: number; // How many days back to collect (default: 1 for daily job)
  force?: boolean; // Force collection even if data exists
}

/**
 * Scheduled job to collect historical SolarEdge data
 * Runs daily at 6:00 AM to collect previous day's data
 */
export default Queue(
  'collectSolarEdgeHistoricalData',
  async (payload: CollectHistoricalDataPayload) => {
    const { dashboardId, daysBack = 1, force = false } = payload;
    
    console.log('🔄 Starting SolarEdge historical data collection job');
    console.log(`📊 Parameters: dashboardId=${dashboardId}, daysBack=${daysBack}, force=${force}`);

    try {
      // Get dashboards to process
      const dashboards = await prisma.solarDashboard.findMany({
        where: {
          ...(dashboardId ? { id: dashboardId } : {}),
          isActive: true,
          solarEdgeApiKey: { not: null },
          solarEdgeSiteId: { not: null }
        },
        select: {
          id: true,
          name: true,
          solarEdgeApiKey: true,
          solarEdgeSiteId: true,
          locationId: true
        }
      });

      console.log(`📋 Found ${dashboards.length} active dashboards with SolarEdge configuration`);

      if (dashboards.length === 0) {
        console.log('⚠️ No dashboards found for historical data collection');
        return { success: true, message: 'No dashboards to process' };
      }

      let totalCollected = 0;
      let successCount = 0;
      let errorCount = 0;

      // Calculate date range
      const endDate = new Date();
      endDate.setDate(endDate.getDate() - 1); // Yesterday
      
      const startDate = new Date(endDate);
      startDate.setDate(startDate.getDate() - (daysBack - 1));

      console.log(`📅 Collecting data from ${startDate.toISOString().split('T')[0]} to ${endDate.toISOString().split('T')[0]}`);

      // Process each dashboard
      for (const dashboard of dashboards) {
        try {
          console.log(`\n🔄 Processing dashboard: ${dashboard.name} (${dashboard.id})`);

          // Check if data already exists (unless forced)
          if (!force) {
            const existingData = await prisma.solarEdgeHistoricalData.findFirst({
              where: {
                dashboardId: dashboard.id,
                date: {
                  gte: startDate,
                  lte: endDate
                }
              }
            });

            if (existingData) {
              console.log(`⏭️ Data already exists for dashboard ${dashboard.id}, skipping`);
              continue;
            }
          }

          // Collect historical data
          const collected = await collectHistoricalData(
            dashboard.id,
            dashboard.solarEdgeApiKey!,
            dashboard.solarEdgeSiteId!,
            startDate,
            endDate
          );

          if (collected > 0) {
            totalCollected += collected;
            successCount++;
            console.log(`✅ Successfully collected ${collected} data points for ${dashboard.name}`);
          } else {
            console.log(`⚠️ No data collected for ${dashboard.name}`);
          }

          // Add delay between dashboards to respect rate limits
          await new Promise(resolve => setTimeout(resolve, 2000));

        } catch (error) {
          errorCount++;
          console.error(`❌ Error processing dashboard ${dashboard.id}:`, error);
        }
      }

      // Log summary
      console.log('\n📊 Historical data collection job completed');
      console.log(`✅ Successful dashboards: ${successCount}`);
      console.log(`❌ Failed dashboards: ${errorCount}`);
      console.log(`📈 Total data points collected: ${totalCollected}`);

      // Store job execution log
      await prisma.solarEdgeApiCall.create({
        data: {
          dashboardId: 'system',
          endpoint: 'historical-data-job',
          success: errorCount === 0,
          responseTime: null,
          errorMessage: errorCount > 0 ? `${errorCount} dashboards failed` : null,
          requestedAt: new Date()
        }
      });

      return {
        success: true,
        summary: {
          dashboardsProcessed: dashboards.length,
          successCount,
          errorCount,
          totalDataPointsCollected: totalCollected,
          dateRange: {
            startDate: startDate.toISOString().split('T')[0],
            endDate: endDate.toISOString().split('T')[0]
          }
        }
      };

    } catch (error) {
      console.error('❌ Historical data collection job failed:', error);
      
      // Log job failure
      await prisma.solarEdgeApiCall.create({
        data: {
          dashboardId: 'system',
          endpoint: 'historical-data-job',
          success: false,
          responseTime: null,
          errorMessage: error instanceof Error ? error.message : 'Unknown error',
          requestedAt: new Date()
        }
      });

      throw error;
    }
  }
);

// Schedule the job to run daily at 6:00 AM
export const scheduleHistoricalDataCollection = async () => {
  try {
    // Schedule daily collection
    await Queue.enqueue(
      'collectSolarEdgeHistoricalData',
      {},
      {
        id: 'daily-historical-data-collection',
        repeat: {
          cron: '0 6 * * *' // Every day at 6:00 AM
        }
      }
    );

    console.log('✅ Scheduled daily SolarEdge historical data collection at 6:00 AM');
  } catch (error) {
    console.error('❌ Failed to schedule historical data collection:', error);
  }
};

// Manual trigger for immediate collection
export const triggerHistoricalDataCollection = async (
  dashboardId?: string,
  daysBack: number = 7,
  force: boolean = false
) => {
  try {
    await Queue.enqueue('collectSolarEdgeHistoricalData', {
      dashboardId,
      daysBack,
      force
    });

    console.log(`✅ Triggered historical data collection for ${dashboardId || 'all dashboards'}`);
  } catch (error) {
    console.error('❌ Failed to trigger historical data collection:', error);
    throw error;
  }
};

// Backfill job for existing dashboards
export const triggerBackfillJob = async (
  dashboardId?: string,
  daysBack: number = 30
) => {
  try {
    await Queue.enqueue('collectSolarEdgeHistoricalData', {
      dashboardId,
      daysBack,
      force: true
    }, {
      id: `backfill-${dashboardId || 'all'}-${Date.now()}`
    });

    console.log(`✅ Triggered backfill job for ${daysBack} days`);
  } catch (error) {
    console.error('❌ Failed to trigger backfill job:', error);
    throw error;
  }
};
