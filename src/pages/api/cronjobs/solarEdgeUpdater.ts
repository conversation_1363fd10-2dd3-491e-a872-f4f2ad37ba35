import { Queue } from "quirrel/next";
import { type NextApiRequest, type NextApiResponse } from "next";

// Import the actual job function
import fetchSolarEdgeData from "./fetchSolarEdgeData";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";

// Create Quirrel queue for SolarEdge data updates
export default Queue(
  "api/cronjobs/solarEdgeUpdater", // Queue name
  async (job: { dashboardId?: string; force?: boolean }) => {
    console.log("🌞 SolarEdge updater job triggered:", job);
    
    // Create mock request/response objects for the job function
    const mockReq = {
      method: 'POST',
      body: job,
      query: {},
      headers: {}
    } as NextApiRequest;

    const mockRes = {
      status: (code: number) => ({
        json: (data: any) => {
          console.log(`📊 SolarEdge job completed with status ${code}:`, data);
          return data;
        }
      })
    } as any as NextApiResponse;

    // Execute the actual job
    await fetchSolarEdgeData(mockReq, mockRes);
  }
);

// Schedule the cron job to run every 10 minutes
export const config = {
  api: {
    bodyParser: {
      sizeLimit: '1mb',
    },
  },
};

// Auto-schedule the job when the server starts
if (process.env.NODE_ENV === 'production' || process.env.ENABLE_CRON === 'true') {
  // Schedule to run every 10 minutes
  const queue = require('./solarEdgeUpdater').default;
  
  // Initial schedule
  queue.enqueue(
    { force: false },
    {
      repeat: {
        every: "10 minutes"
      }
    }
  ).catch((error: Error) => {
    {
      const msg = "❌ Failed to schedule SolarEdge updater:";
      const errorMsg = (error as Error)?.message || String(error);
      Logger(`${msg}: ${errorMsg}`, "API 500", "src/pages/api/cronjobs/solarEdgeUpdater.ts", LogType.ERROR);
    }
  });

  console.log("✅ SolarEdge updater scheduled to run every 10 minutes");
}
