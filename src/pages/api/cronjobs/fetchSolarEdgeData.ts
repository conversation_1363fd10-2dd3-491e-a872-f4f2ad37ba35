import { type NextApiRequest, type NextApiResponse } from "next";
import prisma from "~/server/db/prisma";
import { getSolarData, canMakeApiRequest } from "~/services/solarEdge/api";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";

/**
 * Background job to fetch SolarEdge data for active dashboards
 * Runs every 10 minutes via Quirrel cron
 * Intelligently distributes API calls across dashboards based on priority
 */
export default async function fetchSolarEdgeData(
  req: NextApiRequest,
  res: NextApiResponse
) {
  console.log('🌞 Starting SolarEdge data fetch job...');
  
  try {
    // Get all active dashboards with SolarEdge configuration
    const dashboards = await prisma.solarDashboard.findMany({
      where: {
        isActive: true,
        solarEdgeApiKey: {
          not: null
        },
        solarEdgeSiteId: {
          not: null
        }
      },
      include: {
        _count: {
          select: {
            apiCalls: {
              where: {
                requestedAt: {
                  gte: new Date(Date.now() - 24 * 60 * 60 * 1000) // Last 24 hours
                }
              }
            }
          }
        }
      },
      orderBy: [
        { isPublic: 'desc' }, // Prioritize public dashboards
        { updatedAt: 'desc' }  // Then by recent activity
      ]
    });

    console.log(`Found ${dashboards.length} active dashboards with SolarEdge configuration`);

    let successCount = 0;
    let errorCount = 0;
    let skippedCount = 0;

    // Process dashboards with intelligent prioritization
    for (const dashboard of dashboards) {
      try {
        // Check if dashboard can make API requests (rate limiting)
        const canMakeRequest = await canMakeApiRequest(dashboard.id);
        
        if (!canMakeRequest) {
          console.log(`⏭️ Skipping dashboard ${dashboard.id} - rate limit exceeded`);
          skippedCount++;
          continue;
        }

        // Check if we should fetch data based on priority and last update
        const shouldFetch = await shouldFetchDataForDashboard(dashboard.id, dashboard.isPublic);
        
        if (!shouldFetch) {
          console.log(`⏭️ Skipping dashboard ${dashboard.id} - data is fresh`);
          skippedCount++;
          continue;
        }

        console.log(`🔄 Fetching data for dashboard ${dashboard.id} (${dashboard.name})`);

        // Fetch solar data
        const solarData = await getSolarData(
          dashboard.id,
          dashboard.solarEdgeApiKey!,
          dashboard.solarEdgeSiteId!
        );

        // Log results
        const apiCalls = [
          solarData.currentPower?.source === 'api' ? 'currentPower' : null,
          solarData.todayEnergy?.source === 'api' ? 'todayEnergy' : null,
          solarData.yearEnergy?.source === 'api' ? 'yearEnergy' : null
        ].filter(Boolean);

        if (apiCalls.length > 0) {
          console.log(`✅ Dashboard ${dashboard.id}: Made ${apiCalls.length} API calls (${apiCalls.join(', ')})`);
          console.log(`   Remaining API calls: ${solarData.apiCallsRemaining}`);
        } else {
          console.log(`📋 Dashboard ${dashboard.id}: Used cached data only`);
        }

        successCount++;

        // Add small delay between dashboards to avoid overwhelming the API
        await new Promise(resolve => setTimeout(resolve, 1000));

      } catch (error) {
        {
      const msg = `❌ Error processing dashboard ${dashboard.id}:`;
      const errorMsg = (error as Error)?.message || String(error);
      Logger(`${msg}: ${errorMsg}`, "API 500", "src/pages/api/cronjobs/fetchSolarEdgeData.ts", LogType.ERROR);
    }
        errorCount++;
      }
    }

    // Clean up old data (older than 7 days)
    await cleanupOldData();

    const summary = {
      totalDashboards: dashboards.length,
      successful: successCount,
      errors: errorCount,
      skipped: skippedCount,
      timestamp: new Date().toISOString()
    };

    console.log('🌞 SolarEdge data fetch job completed:', summary);

    res.status(200).json({
      success: true,
      message: 'SolarEdge data fetch completed',
      ...summary
    });

  } catch (error) {
    {
      const msg = '❌ SolarEdge data fetch job failed:';
      const errorMsg = (error as Error)?.message || String(error);
      Logger(`${msg}: ${errorMsg}`, "API 500", "src/pages/api/cronjobs/fetchSolarEdgeData.ts", LogType.ERROR);
    }
    
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    });
  }
}

/**
 * Determine if we should fetch data for a dashboard based on priority and cache status
 */
async function shouldFetchDataForDashboard(
  dashboardId: string,
  isPublic: boolean
): Promise<boolean> {
  // Get the most recent cache entries
  const recentCache = await prisma.solarEdgeDataCache.findMany({
    where: {
      dashboardId,
      expiresAt: {
        gt: new Date()
      }
    },
    orderBy: {
      cachedAt: 'desc'
    },
    take: 3
  });

  // If no valid cache, definitely fetch
  if (recentCache.length === 0) {
    return true;
  }

  // For public dashboards, be more aggressive about updates
  if (isPublic) {
    // Fetch if any cache is older than 15 minutes
    const fifteenMinutesAgo = new Date(Date.now() - 15 * 60 * 1000);
    return recentCache.some(cache => cache.cachedAt < fifteenMinutesAgo);
  }

  // For private dashboards, be more conservative
  // Fetch if any cache is older than 30 minutes
  const thirtyMinutesAgo = new Date(Date.now() - 30 * 60 * 1000);
  return recentCache.some(cache => cache.cachedAt < thirtyMinutesAgo);
}

/**
 * Clean up old API logs and expired cache entries
 */
async function cleanupOldData(): Promise<void> {
  const sevenDaysAgo = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
  
  try {
    // Clean up old API call logs
    const deletedApiCalls = await prisma.solarEdgeApiCall.deleteMany({
      where: {
        requestedAt: {
          lt: sevenDaysAgo
        }
      }
    });

    // Clean up expired cache entries
    const deletedCache = await prisma.solarEdgeDataCache.deleteMany({
      where: {
        expiresAt: {
          lt: new Date()
        }
      }
    });

    // Clean up old rate limit entries (keep only last 30 days)
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    const deletedRateLimits = await prisma.solarEdgeRateLimit.deleteMany({
      where: {
        date: {
          lt: thirtyDaysAgo
        }
      }
    });

    console.log(`🧹 Cleanup completed: ${deletedApiCalls.count} API calls, ${deletedCache.count} cache entries, ${deletedRateLimits.count} rate limits`);
  } catch (error) {
    {
      const msg = '❌ Error during cleanup:';
      const errorMsg = (error as Error)?.message || String(error);
      Logger(`${msg}: ${errorMsg}`, "API 500", "src/pages/api/cronjobs/fetchSolarEdgeData.ts", LogType.ERROR);
    }
  }
}

// Quirrel configuration for cron scheduling
export const config = {
  api: {
    bodyParser: false,
  },
};
