import { type NextApiRequest, type NextApiResponse } from "next";
import RedisClient from "~/utils/redis/redisClient";
import prisma from "~/server/db/prisma";
import type { Cdr, Ou } from "@prisma/client";
import { getAllData } from "~/utils/longship";
import getChargepointstatus from "~/server/task/chargepointstatus";
import getCharger from "~/server/task/charger";
import { env } from "~/env.js";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";
import { endpoints } from "~/server/api/longship/constants";
import sessionData from "~/server/task/sessionData";
import { getOusBelowOu } from "~/server/model/ou/func";
import { NextResponse } from "next/server";
import { todayEnd, yesterdayEnd } from "~/utils/date/date";

const redisClient = RedisClient.getInstance();

export function findLocationByChargePointId(
  locations: any[],
  chargePointId: string,
): any | undefined {
  return locations.find((location) =>
    location.evses.some((evse: any) => evse.chargepointid === chargePointId),
  );
}

export const loadCDR = async (ouIds: string[]) => {
  let realtime_cdr = await redisClient.get("realtime_cdr", env.DEBUG ? 0 : 1);
  let cdr;
  if (!realtime_cdr || (Array.isArray(realtime_cdr) && realtime_cdr.length == 0)) {
    const yesterday = new Date();
    yesterday.setDate(yesterday.getDate() - 2);
    yesterday.setHours(0, 0, 0, 0);

    cdr = await prisma.cdr.findMany({
      where: {
        Start_datetime: {
          gte: yesterday,
        },
      },
    });
    realtime_cdr = await redisClient.set("realtime_cdr", cdr);
  }
  return realtime_cdr.filter((item: any) => ouIds.includes(item.ouId)) || [];
};

export const loadLocation = async (ouIds: string[]) => {
  const realtime_location = await redisClient.get("realtime_location", 10);
  if (realtime_location) {
    return realtime_location;
  }
  const locationData = await getAllData(endpoints.LOCATIONS);
  return redisClient.set("realtime_location", locationData);
};

export const findEvseFromChargePointId = (locations: any[], chargePointId: string): string => {
  for (const location of locations) {
    for (const evse of location.evses) {
      if (evse.chargepointid === chargePointId) {
        return evse.evse_id;
      }
    }
  }
  return "";
};

export const calcKwhFromCdr = (cdrs: Cdr[], evse: string) => {
  const today_current_kWh_charging = cdrs
    .filter(
      (cdr) =>
        evse == cdr.Charge_Point_ID &&
        new Date(cdr.Start_datetime).getTime() > yesterdayEnd().getTime() &&
        new Date(cdr.Start_datetime).getTime() < todayEnd().getTime(),
    )
    .reduce((previousValue: number, currentValue: any) => {
      return previousValue + currentValue.Volume;
    }, 0);

  return today_current_kWh_charging;
};

export const loadChargepointstatus = async (
  ouIds: string[] | undefined,
  chargePointIds: string[] | undefined,
) => {
  let data = await redisClient.get("chargepointstatus", env.DEBUG ? 0 : 1);
  if (!data) {
    data = await getChargepointstatus();
  }
  if (chargePointIds && chargePointIds?.length > 0) {
    return data.filter((item: any) => chargePointIds.includes(item.id));
  }
  return data.filter((item: any) => ouIds.includes(item.ouId) && item.connectors.length > 0);
};

export const loadSessionData = async (ouIds: string[]) => {
  let data = await redisClient.get("sessionData", env.DEBUG ? 0 : 1);
  if (!data) {
    Logger(
      `No sessionData found in redis, fetching from longship`,
      "realtime2",
      "realtime",
      LogType.DEBUG,
    );
    data = await sessionData(false);
    //suche in den daten nach einer session am ladepunkt EUL_0006_01 die 5.06 kWh hat
  }

  return data.filter((item: any) => ouIds.includes(item.ouId));
};

export const loadCharger = async (ouIds: string[]) => {
  let data = await redisClient.get("charger", env.DEBUG ? 0 : 10);
  if (!data) {
    data = await getCharger();
  }
  return data.filter((item: any) => ouIds.includes(item.ouId));
};

export const getCurrentKw = (charging_session: any) => {
  if (!Array.isArray(charging_session)) {
    Logger(
      `Invalid charging_session: ${JSON.stringify(charging_session)}`,
      "getCurrentKw",
      "function",
      LogType.ERROR,
    );
    return 0;
  }

  return charging_session.reduce((previousValue: number, currentValue: any) => {
    if (!Array.isArray(currentValue.chargingMeterValues)) {
      Logger(
        `Invalid chargingMeterValues: ${JSON.stringify(currentValue.chargingMeterValues)}`,
        "getCurrentKw",
        "function",
        LogType.ERROR,
      );
      return previousValue;
    }

    const current_wh = currentValue.chargingMeterValues
      .slice()
      .reverse()
      .find((item: any) => item.measurand == "Power.Active.Import");

    if (current_wh && current_wh.value) {
      if (current_wh.unit == "kW") {
        return previousValue + parseInt(current_wh.value) * 1000;
      }
      return previousValue + parseInt(current_wh.value);
    }

    return previousValue;
  }, 0);
};

export const getCurrentKwBySession = (session: any) => {
  const data = session.chargingMeterValues
    .slice()
    .reverse()
    .find((item: any) => item.measurand == "Power.Active.Import");

  if (data && data?.value) {
    if (data.unit == "kW") {
      return parseInt(data.value) * 1000;
    }
    return parseInt(data.value);
  }
  return 0;
};
