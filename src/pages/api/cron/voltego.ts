import { <PERSON><PERSON><PERSON><PERSON> } from "quirrel/next";
import getExchangeElectricityPrice, {
  COPY_MODE,
} from "../../../server/task/getExchangeElectricityPrice";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";

export default CronJob(
  "api/cron/voltego", // 👈 the route that it's reachable on
  "*/15 * * * *", // same as @monthly (see https://crontab.guru/)
  async () => {
    Logger("fetch stock price", "Fetch stock price from voltego", "cron", LogType.DEBUG);
    await getExchangeElectricityPrice(COPY_MODE.DELTA);
  },
);
