import { createNextApiHandler } from "@trpc/server/adapters/next";

import { createContext } from "../../../server/trpc/context";
import { appRouter } from "../../../server/trpc/router/_app";
import { env } from "../../../env.js";
import Logger from "~/server/logger/logger";
import { LogType } from "@prisma/client";

// export API handler
export default createNextApiHandler({
  router: appRouter,
  createContext,
  onError:
    env.NODE_ENV === "development"
      ? ({ path, error }) => {
          {
      const msg = `❌ tRPC failed on ${path}: ${error}`;
      Logger(msg, "API 500", "src/pages/api/trpc/[trpc].ts", LogType.ERROR);
    }
        }
      : undefined,
});
