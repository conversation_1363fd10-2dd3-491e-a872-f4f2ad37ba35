/**
 * Utility functions to clean Prisma create data objects and prevent empty string IDs
 * from interfering with default(cuid()) generation.
 * 
 * Since a recent Prisma update, empty strings ("") are considered valid values for ID fields,
 * which prevents the default(cuid()) function from being executed.
 */

/**
 * List of all models that have id fields with default(cuid())
 */
const MODELS_WITH_CUID_DEFAULT = [
  'Account',
  'Session', 
  'User',
  'EMPCard',
  'PaymentMethod',
  'Contact',
  'Invoice',
  'Tarif',
  'UserGroup',
  'SolarDashboard',
  'SolarEdgeDataCache',
  'ContactAddress',
  'ParkingSensor',
  'ChargePointError',
  'Token',
  'TokenGroup',
  'CPOContract'
] as const;

/**
 * Cleans a Prisma create data object by removing empty string ID fields
 * to allow default(cuid()) to generate proper IDs.
 * 
 * @param data - The data object for Prisma create operation
 * @returns Cleaned data object with empty string IDs removed
 */
export function cleanPrismaCreateData<T extends Record<string, any>>(data: T): T {
  if (!data || typeof data !== 'object') {
    return data;
  }

  const cleaned = { ...data };

  // Remove id field if it's an empty string
  if ('id' in cleaned && cleaned.id === '') {
    delete cleaned.id;
  }

  return cleaned;
}

/**
 * Recursively cleans nested create data objects (for nested creates)
 * 
 * @param data - The data object that may contain nested create operations
 * @returns Cleaned data object with all empty string IDs removed
 */
export function deepCleanPrismaCreateData<T extends Record<string, any>>(data: T): T {
  if (!data || typeof data !== 'object') {
    return data;
  }

  const cleaned = { ...data };

  // Clean the main object
  if ('id' in cleaned && cleaned.id === '') {
    delete cleaned.id;
  }

  // Recursively clean nested objects
  for (const [key, value] of Object.entries(cleaned)) {
    if (value && typeof value === 'object') {
      if (Array.isArray(value)) {
        // Handle arrays (like create: [...])
        cleaned[key] = value.map(item => 
          typeof item === 'object' ? deepCleanPrismaCreateData(item) : item
        );
      } else if (key === 'create' || key === 'createMany') {
        // Handle nested create operations
        if ('data' in value) {
          // createMany: { data: [...] }
          cleaned[key] = {
            ...value,
            data: Array.isArray(value.data) 
              ? value.data.map(item => deepCleanPrismaCreateData(item))
              : deepCleanPrismaCreateData(value.data)
          };
        } else {
          // create: { ... } or create: [...]
          cleaned[key] = Array.isArray(value)
            ? value.map(item => deepCleanPrismaCreateData(item))
            : deepCleanPrismaCreateData(value);
        }
      } else {
        // Handle other nested objects
        cleaned[key] = deepCleanPrismaCreateData(value);
      }
    }
  }

  return cleaned;
}

/**
 * Validates that an ID is not an empty string before using it in Prisma operations
 * 
 * @param id - The ID to validate
 * @param fieldName - Name of the field for error messages
 * @returns The ID if valid, undefined if empty string
 * @throws Error if ID is invalid
 */
export function validatePrismaId(id: string | undefined | null, fieldName = 'id'): string | undefined {
  if (id === '') {
    // Return undefined to let Prisma generate a new ID
    return undefined;
  }
  
  if (id === null) {
    return undefined;
  }
  
  return id;
}

/**
 * Type-safe wrapper for cleaning create data based on model type
 */
export function cleanCreateDataForModel<T extends Record<string, any>>(
  modelName: string, 
  data: T
): T {
  // Always clean the data regardless of model
  return cleanPrismaCreateData(data);
}

/**
 * Utility to check if a value is an empty string that would interfere with cuid() generation
 */
export function isEmptyStringId(value: any): boolean {
  return typeof value === 'string' && value === '';
}

/**
 * Helper function to safely extract and clean ID from request data
 */
export function extractAndCleanId(requestData: any, idField = 'id'): string | undefined {
  const id = requestData?.[idField];
  return validatePrismaId(id, idField);
}
