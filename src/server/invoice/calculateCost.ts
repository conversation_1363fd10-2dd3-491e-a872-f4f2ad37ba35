import prisma from "../db/prisma";
import { CompanyTarif, CdrPayout, LogType, Prisma } from "@prisma/client";
import type { Cdr } from "@prisma/client";
import { getEMPLookupTable } from "~/server/invoice/invoiceUtils";
import { getPowerType } from "~/utils/longship";
import { getPriceForStationByEvseId } from "~/utils/adhoc/pricing";
import EMPCard from "~/app/(app)/emp/card/component/EMPCard";
import Logger from "~/server/logger/logger";

export type CdrWithIncludes = Prisma.CdrGetPayload<{
  include: { cost: true; tarif: true };
}>;

type ReturnType<T> = T extends CdrWithIncludes[] ? CdrWithIncludes[] : Cdr[];

export const computeCardHolderCosts = async <T extends CdrWithIncludes[] | Cdr[]>(
  cdrs: T,
): Promise<ReturnType<T>> => {
  // fetch all used Authentication tokens in order to fetch all possible cardHolderCards (physical cards)
  const authTokensFromCdrs = cdrs
    .map((cdr) => cdr.Authentication_ID)
    .filter((item): item is string => Boolean(item));

  const cardHolderCards = await prisma.physicalCard.findMany({
    where: { uid: { in: authTokensFromCdrs } },
    include: {
      EMPCard: {
        include: {
          tarifs: { include: { tarif: { include: { ou: true } } } },
        },
      },
    },
  });
  const cardHolderTokenUids = cardHolderCards.map((card) => card.uid.toUpperCase());

  // ignore tarifs which already have a tarifId attached (like roaming cdrs) and cdrs
  // not matching any cardholder tokens
  const cdrsNoTarifAndOnlyFromCardHolderTokenUids = cdrs.filter(
    (cdr) =>
      !cdr.tarifId &&
      cdr.Authentication_ID &&
      cardHolderTokenUids.includes(cdr.Authentication_ID.toUpperCase()),
  );

  // fetch all possible tariffs from the cardholder card to avoid requesting it in the for loop
  const tariffs = cardHolderCards
    .flatMap((physicalCard) => physicalCard?.EMPCard?.tarifs.map((tarif) => tarif.tarif))
    .filter(
      (
        tarif,
      ): tarif is Prisma.CompanyTarifGetPayload<{
        include: { ou: true };
      }> => tarif !== undefined,
    );

  // iterate over the cdrs without tarif to find cdrs matching a cardHolderCard
  // if a card was found, check whether a tarif can be found
  //if tarif can be found, calculate the costs a cardholder needs to pay
  for (const cdr of cdrsNoTarifAndOnlyFromCardHolderTokenUids) {
    if (cdr.Authentication_ID && tariffs) {
      const matchingToken = cardHolderCards.find(
        (token) => token.uid.toUpperCase() == cdr.Authentication_ID?.toUpperCase(),
      );
      if (!matchingToken) {
        cdr.Tariff_Name = `No matching token found for Authentication_ID ${cdr.Authentication_ID}`;
        cdr.billable = false;
        continue;
      }

      const tariffsOfCard = matchingToken?.EMPCard?.tarifs.map((item) => item.tarif);
      const matchingTarif = tariffsOfCard?.find(
        (tarif) =>
          tarif.ou?.code === cdr?.OU_Code?.replaceAll("'", "") &&
          getPowerType(cdr.Charge_Point_Type) == tarif.currentType &&
          cdr.Start_datetime >= tarif.validFrom &&
          (tarif.validTo == null || cdr.End_datetime <= tarif.validTo),
      );

      if (!matchingTarif) {
        cdr.Tariff_Name = `No matching company tarif found for token uid ${matchingToken?.uid}`;
        cdr.billable = false;
        continue;
      } else {
        if (
          cdr.Volume &&
          matchingTarif.minChargingEnergy <= cdr.Volume &&
          matchingTarif.minChargingTime <= (cdr?.DurationInSec || 0)
        ) {
          let blockingFeeInEur = 0;
          if (
            matchingTarif.blockingFee &&
            cdr.DurationInSec &&
            matchingTarif.blockingFeeBeginAtMin
          ) {
            const durationInMin = cdr.DurationInSec / 60;
            const actualBlocking = durationInMin - matchingTarif.blockingFeeBeginAtMin;
            if (actualBlocking > 0) {
              blockingFeeInEur = actualBlocking * matchingTarif.blockingFee;
            }
            if (
              blockingFeeInEur > matchingTarif.blockingFeeMax &&
              matchingTarif.blockingFeeMax > 0
            ) {
              blockingFeeInEur = matchingTarif.blockingFeeMax;
            }
          }
          const charging_cost =
            cdr.Volume * matchingTarif.energyPrice + matchingTarif.sessionPrice + blockingFeeInEur;

          cdr.Start_Tariff = matchingTarif.sessionPrice;
          cdr.Calculated_Cost = parseFloat(charging_cost.toFixed(2));
          cdr.Parking_Time_Cost = blockingFeeInEur;
          cdr.billable = true;
          cdr.EnergyCosts = cdr.Volume * matchingTarif.energyPrice;
          cdr.companyTarifId = matchingTarif.id;
          cdr.Tariff_kWh = matchingTarif.energyPrice;
          cdr.Tariff_Name = matchingTarif.name;
          cdr.companyTarifId = matchingTarif.id;
          cdr.Service_Provider_ID = "DEEUL";
        } else {
          cdr.billable = false;
          cdr.Calculated_Cost = 0;
          cdr.EnergyCosts = 0;
          cdr.companyTarifId = matchingTarif.id;
          cdr.Tariff_Name = matchingTarif.name;
          cdr.Service_Provider_ID = "DEEUL";
        }
      }
    }
  }

  return cdrs as ReturnType<T>;
};

export const computeAdhocCosts = async <T extends CdrWithIncludes[] | Cdr[]>(
  cdrs: T,
): Promise<ReturnType<T>> => {
  const adhocCdrs = cdrs.filter(
    (cdr) =>
      cdr.Service_Provider_ID == "DEEUL" &&
      cdr.Tariff_Name != "Adhoc" && // already calculated
      (cdr?.Contract_ID == "n/a" || cdr.Contract_ID == "ADHOC"),
  ); // n/a = adhoc

  try {
    for (const cdr of adhocCdrs) {
      const adhocTarif = await getPriceForStationByEvseId(
        cdr.Charge_Point_ID ?? "none",
        cdr.Start_datetime,
        cdr?.Charge_Point_Type ?? 0,
      );
      if (
        adhocTarif &&
        adhocTarif.energy_price &&
        adhocTarif.tax_rate &&
        cdr.Volume &&
        cdr?.DurationInSec
      ) {
        const startNet = +(adhocTarif.session_fee / (1 + adhocTarif.tax_rate / 100)).toFixed(2);
        const energyNet = +(adhocTarif.energy_price / (1 + adhocTarif.tax_rate / 100)).toFixed(2);
        const blockingFeeNet = +(adhocTarif.blocking_fee / (1 + adhocTarif.tax_rate / 100)).toFixed(
          2,
        );
        const blockingFeeMaxNet = +(
          adhocTarif.blocking_fee_max /
          (1 + adhocTarif.tax_rate / 100)
        ).toFixed(2);

        if (cdr.Volume < 0.2 || cdr.DurationInSec < 120) {
          cdr.Tariff_kWh = energyNet;
          cdr.Start_Tariff = startNet;
          cdr.EnergyCosts = 0;
          cdr.Tariff_Name = `Adhoc`;
          cdr.Parking_Time_Cost = 0;
          cdr.Calculated_Cost = 0;
          cdr.tarifId = null;
          cdr.billable = false;
        } else {
          cdr.Tariff_kWh = energyNet;
          cdr.Start_Tariff = startNet;
          cdr.EnergyCosts = +(energyNet * cdr.Volume).toFixed(2);
          cdr.Tariff_Name = `Adhoc`;
          let blockingFeeInEur = 0;
          if (blockingFeeNet > 0) {
            const durationInMin = cdr.DurationInSec / 60;
            const actualBlocking = durationInMin - adhocTarif.blocking_fee_start;
            if (actualBlocking > 0) {
              blockingFeeInEur = actualBlocking * blockingFeeNet;
            }
            if (blockingFeeInEur > blockingFeeMaxNet && blockingFeeMaxNet > 0) {
              blockingFeeInEur = blockingFeeMaxNet;
            }
          }
          cdr.Parking_Time_Cost = +blockingFeeInEur.toFixed(2);
          cdr.Calculated_Cost = +(blockingFeeInEur + startNet + energyNet * cdr.Volume).toFixed(2);
          cdr.tarifId = null;
          cdr.billable = true;
        }
      } else {
        cdr.Tariff_kWh = 0;
        cdr.Start_Tariff = 0;
        cdr.EnergyCosts = 0;
        cdr.Tariff_Name = `Adhoc Tarif (EMP Price or Location Price) not found or threshold for kWh,duration to low`;
        cdr.Parking_Time_Cost = 0;
        cdr.Calculated_Cost = 0;
        cdr.tarifId = null;
      }
    }
  } catch (e) {
    const errorMsg = (e as Error).message;
    Logger(errorMsg, "Calc Adhoc exception", "Credit", LogType.WARN);
  }
  return cdrs as ReturnType<T>;
};

export const computeRoamingCostByEmpPrice = async <T extends CdrWithIncludes[] | Cdr[]>(
  cdrs: T,
): Promise<ReturnType<T>> => {
  //flotten tokens
  const tokens = await prisma.token.findMany({
    include: {
      tarifs: true,
      tokenGroup: {
        include: {
          billingOUs: true,
          contact: {
            include: {
              providers: true,
              tarifs: { include: { tarif: true } },
              contactEnergyResellers: true,
            },
          },
        },
      },
    },
  });

  const mitarbeiterTokenUids = (await prisma.physicalCard.findMany({ select: { uid: true } })).map(
    (token) => token.uid,
  );

  const empLookupTable = await getEMPLookupTable();

  for (const cdr of cdrs) {
    if (
      cdr.companyTarifId ||
      (cdr.Authentication_ID && mitarbeiterTokenUids.includes(cdr.Authentication_ID))
    ) {
      //ignore cdrs which already has a company tariff attached
      continue;
    }
    let matchingEmp = null;
    let matchingToken = null;
    // first check whether roaming or not
    if (cdr?.Service_Provider_ID) {
      // if service provider is set but not  found in db, cdr not billable
      if (cdr.Service_Provider_ID == "DEEUL") {
        // skip because it is a eulektro OCPI non adhoc cdr which will be calculated otherwise
        continue;
      }

      matchingEmp = empLookupTable[cdr.Service_Provider_ID];
      if (matchingEmp && matchingEmp.noRoaming) {
        //if there is a service provider and no roaming flag is set, just ignore.
        Logger(
          `cdr ${cdr.CDR_ID} ignored becasue noRoaming flag for ${matchingEmp.name}`,
          "No roaming flag",
          "Cost",
          LogType.INFO,
        );
        cdr.Tariff_Name = `Ignored by noRoaming flag`;
        continue;
      }
      if (!matchingEmp) {
        cdr.Tariff_Name = `⚠️ EMP ${cdr.Service_Provider_ID} not found or mapped to contact`;
        cdr.billable = false;
        cdr.Calculated_Cost = 0;
        cdr.Parking_Time_Cost = 0;
        cdr.Start_Tariff = 0;
        cdr.tarifId = null;
        cdr.EnergyCosts = 0;
        continue; // no EMP found so go to next CDR
      }
    } else {
      matchingToken = tokens.find(
        (token) => token.authenticationId?.toUpperCase() == cdr.Authentication_ID?.toUpperCase(),
      );

      matchingEmp = matchingToken?.tokenGroup?.contact;
      if (!matchingEmp) {
        cdr.Tariff_Name = `⚠️ Tokengroup for token ${cdr.Authentication_ID} not found`;
        cdr.billable = false;
        cdr.Calculated_Cost = 0;
        cdr.Start_Tariff = 0;
        cdr.Parking_Time_Cost = 0;
        cdr.tarifId = null;
        cdr.EnergyCosts = 0;
        continue; // no EMP found so go to next CDR
      } else {
        const defaultTokenGroupProvider = `${matchingEmp?.providers[0]?.providerCountryId}${matchingEmp?.providers[0]?.providerId}`;
        // add contact mapped to token as Ssrvice_Provider_ID
        cdr.Service_Provider_ID = defaultTokenGroupProvider;
        const strippedOu = cdr.OU_Code?.replaceAll("'", "");
        const billingOus = matchingToken?.tokenGroup?.billingOUs.map((ou) => ou.code) ?? [];

        if (strippedOu && !billingOus.includes(strippedOu)) {
          cdr.Tariff_Name = `OU ${cdr.OU_Name} ${strippedOu} not configured to be charged`;
          cdr.billable = false;
          cdr.Calculated_Cost = 0;
          cdr.Start_Tariff = 0;
          cdr.EnergyCosts = 0;
          cdr.Parking_Time_Cost = 0;
          cdr.tarifId = null;
          continue;
        }
      }
    }
    const powerType = getPowerType(cdr.Charge_Point_Type);
    // Suche nach dem passenden Preis für das Datum des CDR
    const matchingTariffOnContact = matchingEmp?.tarifs.find((tariffOnContact) => {
      return (
        new Date(cdr.Start_datetime) >= tariffOnContact.tarif.validFrom &&
        new Date(cdr.Start_datetime) <= tariffOnContact.tarif.validTo &&
        powerType == tariffOnContact.tarif.currentType
      );
    });
    const matchingTariff = matchingTariffOnContact?.tarif;
    if (!matchingTariff) {
      cdr.Tariff_Name = "⚠️ No valid Tarif from EMP";
      cdr.billable = false;
      cdr.Calculated_Cost = 0;
      cdr.EnergyCosts = 0;
      cdr.Start_Tariff = 0;
      cdr.tarifId = null;
      cdr.Parking_Time_Cost = 0;
      continue;
    }

    // Berechne den Preis aus dem passenden EMP-Preis

    if (
      cdr.Volume != null &&
      matchingTariff.minChargingEnergy <= cdr.Volume &&
      matchingTariff.minChargingTime <= (cdr?.DurationInSec || 0)
    ) {
      let blockingFeeInEur = 0;
      if (matchingTariff.blockingFee && cdr.DurationInSec && matchingTariff.blockingFeeBeginAtMin) {
        const durationInMin = cdr.DurationInSec / 60;
        const actualBlocking = durationInMin - matchingTariff.blockingFeeBeginAtMin;
        if (actualBlocking > 0) {
          blockingFeeInEur = actualBlocking * matchingTariff.blockingFee;
        }
        if (blockingFeeInEur > matchingTariff.blockingFeeMax && matchingTariff.blockingFeeMax > 0) {
          blockingFeeInEur = matchingTariff.blockingFeeMax;
        }
      }
      const charging_cost =
        cdr.Volume * matchingTariff.kwh + (matchingTariff.sessionFee || 0) + blockingFeeInEur;

      cdr.Calculated_Cost = +charging_cost.toFixed(2);
      cdr.Parking_Time_Cost = +blockingFeeInEur.toFixed(2);
      cdr.billable = true;
      cdr.EnergyCosts = +(cdr.Volume * matchingTariff.kwh).toFixed(2);
    } else {
      cdr.Calculated_Cost = 0;
      cdr.billable = false;
      cdr.EnergyCosts = 0;
      cdr.Parking_Time_Cost = 0;
    }
    cdr.Start_Tariff = matchingTariff.sessionFee;
    cdr.Tariff_kWh = matchingTariff.kwh;
    cdr.Tariff_Name = matchingTariff.name || "";
    cdr.tarifId = matchingTariff.id;
  }
  return cdrs as ReturnType<T>;
};

export const computeDirectPaymentCosts = async <T extends CdrWithIncludes[] | Cdr[]>(
  cdrs: T,
): Promise<ReturnType<T>> => {
  const contracts = await prisma?.cPOContract.findMany({ include: { tarifs: true } });
  const directPaymentTokens = contracts
    ?.map((contract) => contract.directPaymentToken)
    .filter((id) => id != "none" && id);

  const directPaymentCdrs = cdrs.filter(
    (cdr) => cdr?.Authentication_ID && directPaymentTokens.includes(cdr.Authentication_ID),
  );
  const transactionIds = directPaymentCdrs
    .map((cdr) => cdr.transactionId)
    .filter((id): id is string => id != null);

  const enerchargeSessions = await prisma.enerchargeSession.findMany({
    where: { OCPP_TransactionID: { in: transactionIds } },
  });

  try {
    for (const cdr of directPaymentCdrs) {
      const matchingContract = contracts.find(
        (contract) =>
          contract.directPaymentToken.toUpperCase() == cdr.Authentication_ID?.toUpperCase() &&
          contract.start <= cdr.Start_datetime &&
          contract.end >= cdr.Start_datetime,
      );
      let matchingTariff;
      if (matchingContract) {
        if (matchingContract.tarifs?.length > 0) {
          matchingTariff = matchingContract.tarifs.find(
            (tarif) =>
              cdr.Start_datetime >= tarif.validFrom &&
              cdr.Start_datetime <= tarif.validTo &&
              getPowerType(cdr.Charge_Point_Type) == tarif.currentType,
          );
        }
      }

      if (cdr.Volume && cdr?.DurationInSec && matchingTariff && matchingTariff.kwh) {
        if (
          cdr.Volume >= matchingTariff.minChargingEnergy &&
          cdr.DurationInSec >= matchingTariff.minChargingTime
        ) {
          let blockingFeeInEur = 0;
          if (
            matchingTariff.blockingFee &&
            cdr.DurationInSec &&
            matchingTariff.blockingFeeBeginAtMin
          ) {
            const durationInMin = cdr.DurationInSec / 60;
            const actualBlocking = durationInMin - matchingTariff.blockingFeeBeginAtMin;
            if (actualBlocking > 0) {
              blockingFeeInEur = actualBlocking * matchingTariff.blockingFee;
            }
            if (
              blockingFeeInEur > matchingTariff.blockingFeeMax &&
              matchingTariff.blockingFeeMax > 0
            ) {
              blockingFeeInEur = matchingTariff.blockingFeeMax;
            }
          }

          const matchingSession = enerchargeSessions.find(
            (es) => es.OCPP_TransactionID == cdr.transactionId,
          );
          if (matchingSession) {
            //brutto preis in netto
            const costNet = +(matchingSession.Preis / (1 + 19 / 100)).toFixed(2);

            cdr.Calculated_Cost = costNet;
          } else {
            Logger(
              `No Enercharge Session found for CDR transactionID ${cdr.transactionId}`,
              "Direct Payment Compute",
              "Finance",
              LogType.WARN,
            );
            continue;
          }

          cdr.Parking_Time_Cost = +blockingFeeInEur.toFixed(2);
          cdr.billable = true;
          cdr.EnergyCosts = +(cdr.Volume * matchingTariff.kwh).toFixed(2);
          cdr.Service_Provider_ID = "DEEUL";
        } else {
          cdr.Tariff_Name = `Threshold to low`;
          cdr.Tariff_kWh = 0;
          cdr.Service_Provider_ID = "DEEUL";
          cdr.Start_Tariff = 0;
          cdr.EnergyCosts = 0;
          cdr.Parking_Time_Cost = 0;
          cdr.Calculated_Cost = 0;
          cdr.tarifId = null;
          cdr.billable = false;
        }
        cdr.Start_Tariff = matchingTariff.sessionFee;
        cdr.Tariff_kWh = matchingTariff.kwh;
        cdr.Tariff_Name = matchingTariff.name || "";
        cdr.tarifId = matchingTariff.id;
      } else {
        cdr.Tariff_kWh = 0;
        cdr.Start_Tariff = 0;
        cdr.EnergyCosts = 0;
        cdr.Tariff_Name = `Direct Payment Tariff not found or threshold for kWh,duration to low`;
        cdr.Parking_Time_Cost = 0;
        cdr.Calculated_Cost = 0;
        cdr.tarifId = null;
      }
    }
  } catch (e) {
    const errorMsg = (e as Error).message;
    Logger(errorMsg, "Calc Direct Payment exception", "Credit", LogType.WARN);
  }
  return cdrs as ReturnType<T>;
};
