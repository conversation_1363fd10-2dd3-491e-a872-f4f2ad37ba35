// src/server/achievements/progress.ts
import prisma from "~/server/db/prisma";
import {getFirstDayOfMonth, getLastDayOfMonth, startOfWeekMonday, endOfWeekSunday } from "~/utils/date/date";
import {MetricInputAchievmentProgress, MetricWindow} from "~/types/achievment/EventProgressUpdate";



export function windowRange(
  window: MetricWindow,
  eventStartsAt?: Date | null,
  eventEndsAt?: Date | null,
  now: Date = new Date()
) {
  switch (window) {
    case "MONTH":
      return { from: getFirstDayOfMonth(now), to: getLastDayOfMonth(now) };
    case "WEEK":
      return { from: startOfWeekMonday(now), to: endOfWeekSunday(now) };
    case "ROLLING30D": {
      const from = new Date(now);
      from.setDate(now.getDate() - 30);
      from.setHours(0, 0, 0, 0);
      const to = new Date(now);
      return { from, to };
    }
    case "EVENT":
    default: {
      const from = eventStartsAt ?? new Date(0);
      const to = eventEndsAt ?? now;
      return { from, to };
    }
  }
}

export async function getUserTokenUids(userId: string): Promise<string[]> {
  const cards = await prisma.eMPCard.findMany({
    where: { userId },
    select: { physicalCard: { select: { uid: true } } },
  });
  return cards
    .map(c => c.physicalCard?.uid)
    .filter((uid): uid is string => !!uid);
}


export async function computeMetricValue({
                                           metric, event, tokenUids, now = new Date(),
                                         }: MetricInputAchievmentProgress): Promise<number> {
  if (tokenUids.length === 0) return 0;

  const { from, to } = windowRange(metric.window, event.startsAt, event.endsAt, now);

  switch (metric.param) {
    case "sessions_count": {
      // Anzahl CDRs im Fenster
      const cnt = await prisma.cdr.count({
        where: {
          Authentication_ID: { in: tokenUids },
          billable: true,
          Start_datetime: { gte: from, lte: to },
        },
      });
      return cnt;
    }

    case "energy_kwh_month": {
      // Summe kWh im Fenster
      // ⚠️ Spaltenname anpassen (z.B. "Total_energy_kWh" / "kWh" / "Consumed_Wh")
      const agg = await prisma.cdr.aggregate({
        _sum: { Volume: true as any }, // <-- HIER deinen Spaltennamen einsetzen
        where: {
          Authentication_ID: { in: tokenUids },
          billable: true,
          Start_datetime: { gte: from, lte: to },
        },
      });
      const kwh = Number(agg._sum.Volume ?? 0);
      return kwh;
    }

    // weitere Metriken hier ergänzen …
    // case "referrals": …

    default:
      return 0; // unbekannte Metrik
  }
}


// src/server/achievements/progress.ts (weiter)
export async function recalcProfileProgress(profileId: string) {
  // Profil + User
  const profile = await prisma.achievementProfile.findUnique({
    where: { id: profileId },
    select: { id: true, userId: true, enrollments: { select: { eventId: true } } },
  });
  if (!profile) throw new Error("Profile not found");

  const tokenUids = await getUserTokenUids(profile.userId);
  if (!tokenUids.length) return;

  // Events + Metriken laden
  const events = await prisma.achievementEvent.findMany({
    where: { id: { in: profile.enrollments.map(e => e.eventId) } },
    select: {
      id: true, startsAt: true, endsAt: true,
      metrics: { select: { id: true, param: true, window: true, start: true } },
    },
  });

  for (const ev of events) {
    for (const m of ev.metrics) {
      const value = await computeMetricValue({
        metric: { id: m.id, param: m.param, window: m.window as any, start: m.start },
        event: { startsAt: ev.startsAt, endsAt: ev.endsAt },
        profileId: profile.id,
        userId: profile.userId,
        tokenUids,
      });

      await prisma.achievementMetricProgress.upsert({
        where: { metricId_profileId: { metricId: m.id, profileId: profile.id } },
        create: { metricId: m.id, profileId: profile.id, value },
        update: { value },
      });
    }
  }
}
