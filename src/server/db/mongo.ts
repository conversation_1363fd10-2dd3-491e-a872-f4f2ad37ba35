import { PrismaClient } from "~/../prismaMongoAdhoc/client";
import { env } from "~/env.js";

declare global {
  // eslint-disable-next-line no-var
  var prismaMongo: PrismaClient | undefined;
}
const prismaLogoParams = ["query", "error", "warn"];
const noLog: any[] = [];
const prismaMongo =
  global.prismaMongo ||
  new PrismaClient({
    log: env.NODE_ENV === "development" ? noLog : ["error"],
  });

if (env.NODE_ENV !== "production") {
  global.prismaMongo = prismaMongo;
}

export default prismaMongo;
