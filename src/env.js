import { createEnv } from "@t3-oss/env-nextjs";
import { z } from "zod";
import { LogType } from "@prisma/client";

export const env = createEnv({
  /**
   * Specify your server-side environment variables schema here. This way you can ensure the app
   * isn't built with invalid env vars.
   */
  server: {
    LOGLEVEL: z.enum([LogType.WARN, LogType.DEBUG, LogType.ERROR, LogType.INFO]),
    DATABASE_URL: z.string().url(),
    NODE_ENV: z.enum(["development", "test", "production"]),
    NEXTAUTH_SECRET:
      process.env.NODE_ENV === "production" ? z.string().min(1) : z.string().min(1).optional(),
    NEXTAUTH_URL: z.preprocess(
      // This makes Vercel deployments not fail if you don't set NEXTAUTH_URL
      // Since NextAuth.js automatically uses the VERCEL_URL if present.
      (str) => process.env.VERCEL_URL ?? str,
      // VERCEL_URL doesn't include `https` so it cant be validated as a URL
      process.env.VERCEL ? z.string().min(1) : z.string().url(),
    ),
    // Add `.min(1) on ID and SECRET if you want to make sure they're not empty
    EMAIL_SERVER_USER: z.string(),
    EMAIL_SERVER_PASSWORD: z.string(),
    EMAIL_SERVER_HOST: z.string(),
    EMAIL_SERVER_PORT: z.string(),
    EMAIL_FROM: z.string(),
    CHAT_WEBHOOK: z.string(),
    DEBUG: z.string().optional().default(""),
    JWT_SECRET: z.string(),
    SALT: z.string(),
    PASSPHRASES: z.string(),
    LONGSHIP_API: z.string(),
    LONGSHIP_API_OCP_KEY: z.string(),
    LONGSHIP_API_X_API_KEY: z.string(),
    LONGSHIP_PORTAL_URL: z.string(),
    LONGSHIP_EXPORT_FOLDER: z.string(),
    INVOICE_FOLDER: z.string(),
    REDIS_HOST: z.string(),
    REDIS_PASS: z.string(),
    REDIS_URL: z.string(),
    QUIRREL_API_URL: z.string(),
    QUIRREL_ENCRYPTION_SECRET: z.string(),
    DISABLE_TELEMETRY: z.string().optional().default(""),
    MAIL_FOR_LOGGING_MESSAGES: z.string(),
    STRIPE_SECRET_KEY: z.string(),
    DATABASE_URL_MONGODB: z.string(),
    STRIPE_PAYOUT_REPORTS: z.string(),
    QONTO_LOGIN: z.string(),
    QONTO_SECRET: z.string(),
    QONTO_API_URL: z.string(),
    QONTO_MAIN_IBAN: z.string(),
    QONTO_STRIPE_IBAN: z.string(),
    STRIPE_WEBHOOK_SECRET: z.string(),
    ROAMING_TAX_EXPORT_DIR: z.string(),
    OCPI_DOMAIN: z.string(),
    LONGSHIP_DOMAIN: z.string(),
    CPO_TOKEN: z.string(),
    EMP_TOKEN: z.string(),
    WELCOME_LETTER_FOLDER: z.string(),
    VOLTEGO_MATCHER_EMAIL_USER: z.string(),
    VOLTEGO_MATCHER_EMAIL_PASSWORD: z.string(),
    VOLTEGO_MATCHER_EMAIL_HOST: z.string(),
    VOLTEGO_MATCHER_EMAIL_PORT: z.preprocess((val) => parseInt(val), z.number().int()),
    VOLTEGO_MATCHER_PDF_SAVE_FOLDER: z.string(),
    VOLTEGO_MATCHER_DAYS_BACK: z.preprocess((val) => parseInt(val), z.number().int()),
    KEBA_BELEGE_FOLDER: z.string(),
    PAYONE_CSV_FOLDER: z.string(),
    WEBHOOK_API_KEY: z.string(),
    PLUG_AND_CHAGE_DOMAIN: z.string(),
    PLUG_AND_CHARGE_SEND_EVENT_SLUG: z.string(),
    PLUG_AND_CHARGE_AUTH_URL: z.string(),
    PLUG_AND_CHARGE_CLIENT_SECRET: z.string(),
    PLUG_AND_CHARGE_CLIENT_ID: z.string(),
    PLUG_AND_CHARGE_24H_DEV_BEARER: z.string(),
    LONGSHIP_AUTHORIZATIONS_API: z.string(),
  },

  /**
   * Specify your client-side environment variables schema here. This way you can ensure the app
   * isn't built with invalid env vars. To expose them to the client, prefix them with
   * `NEXT_PUBLIC_`.
   */
  client: {
    // NEXT_PUBLIC_CLIENTVAR: z.string().min(1),
    NEXT_PUBLIC_DEBUG: z.string().optional().default("false"),
    NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY: z.string(),
    NEXT_PUBLIC_SITE_URL: z.string().url(),
  },

  /**
   * You can't destruct `process.env` as a regular object in the Next.js edge runtimes (e.g.
   * middlewares) or client-side so we need to destruct manually.
   */
  experimental__runtimeEnv: {
    NEXT_PUBLIC_DEBUG: process.env.NEXT_PUBLIC_DEBUG,
    NEXT_PUBLIC_SITE_URL: process.env.NEXT_PUBLIC_SITE_URL,
    NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY: process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY,
  },
});
