
Object.defineProperty(exports, "__esModule", { value: true });

const {
  Decimal,
  objectEnumValues,
  makeStrictEnum,
  Public,
  getRuntime,
  skip
} = require('./runtime/index-browser.js')


const Prisma = {}

exports.Prisma = Prisma
exports.$Enums = {}

/**
 * Prisma Client JS version: 5.22.0
 * Query Engine version: 605197351a3c8bdd595af2d2a9bc3025bca48ea2
 */
Prisma.prismaVersion = {
  client: "5.22.0",
  engine: "605197351a3c8bdd595af2d2a9bc3025bca48ea2"
}

Prisma.PrismaClientKnownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientKnownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)};
Prisma.PrismaClientUnknownRequestError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientUnknownRequestError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientRustPanicError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientRustPanicError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientInitializationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientInitializationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.PrismaClientValidationError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`PrismaClientValidationError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.NotFoundError = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`NotFoundError is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.Decimal = Decimal

/**
 * Re-export of sql-template-tag
 */
Prisma.sql = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`sqltag is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.empty = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`empty is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.join = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`join is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.raw = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`raw is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.validator = Public.validator

/**
* Extensions
*/
Prisma.getExtensionContext = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.getExtensionContext is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}
Prisma.defineExtension = () => {
  const runtimeName = getRuntime().prettyName;
  throw new Error(`Extensions.defineExtension is unable to run in this browser environment, or has been bundled for the browser (running in ${runtimeName}).
In case this error is unexpected for you, please report it in https://pris.ly/prisma-prisma-bug-report`,
)}

/**
 * Shorthand utilities for JSON filtering
 */
Prisma.DbNull = objectEnumValues.instances.DbNull
Prisma.JsonNull = objectEnumValues.instances.JsonNull
Prisma.AnyNull = objectEnumValues.instances.AnyNull

Prisma.NullTypes = {
  DbNull: objectEnumValues.classes.DbNull,
  JsonNull: objectEnumValues.classes.JsonNull,
  AnyNull: objectEnumValues.classes.AnyNull
}



/**
 * Enums
 */

exports.Prisma.CdrScalarFieldEnum = {
  id: 'id',
  auth_method: 'auth_method',
  authorization_reference: 'authorization_reference',
  country_code: 'country_code',
  credit: 'credit',
  currency: 'currency',
  end_date_time: 'end_date_time',
  last_updated: 'last_updated',
  party_id: 'party_id',
  session_id: 'session_id',
  start_date_time: 'start_date_time',
  total_energy: 'total_energy',
  total_parking_time: 'total_parking_time',
  total_time: 'total_time'
};

exports.Prisma.CounterScalarFieldEnum = {
  id: 'id',
  sequence_value: 'sequence_value'
};

exports.Prisma.EmpScalarFieldEnum = {
  id: 'id',
  amount_to_block: 'amount_to_block',
  country_code: 'country_code',
  name: 'name',
  party_id: 'party_id',
  min_kwh_in_kwh: 'min_kwh_in_kwh',
  min_time_in_min: 'min_time_in_min'
};

exports.Prisma.EmpAddressScalarFieldEnum = {
  id: 'id',
  city: 'city',
  country: 'country',
  empId: 'empId',
  from: 'from',
  house_number: 'house_number',
  postal_code: 'postal_code',
  street: 'street',
  to: 'to',
  ust_id: 'ust_id',
  vat_id: 'vat_id'
};

exports.Prisma.EmpPriceScalarFieldEnum = {
  id: 'id',
  start: 'start',
  end: 'end',
  energy_price: 'energy_price',
  blocking_fee: 'blocking_fee',
  blocking_fee_start: 'blocking_fee_start',
  blocking_fee_max: 'blocking_fee_max',
  session_fee: 'session_fee',
  tax_rate: 'tax_rate',
  empId: 'empId',
  current_type: 'current_type'
};

exports.Prisma.InvoiceScalarFieldEnum = {
  id: 'id',
  authorization_reference: 'authorization_reference',
  create_date: 'create_date',
  currency: 'currency',
  emp_country_code: 'emp_country_code',
  emp_party_id: 'emp_party_id',
  invoice_date: 'invoice_date',
  invoice_number: 'invoice_number',
  kind_of_invoice: 'kind_of_invoice',
  last_update: 'last_update',
  local_end_datetime: 'local_end_datetime',
  local_start_datetime: 'local_start_datetime',
  mailToSend: 'mailToSend',
  paid_date: 'paid_date',
  sentAsEmail: 'sentAsEmail',
  service_period_from: 'service_period_from',
  service_period_to: 'service_period_to',
  status: 'status',
  subject: 'subject',
  sum_gross: 'sum_gross',
  sum_net: 'sum_net',
  sum_tax: 'sum_tax'
};

exports.Prisma.LocationScalarFieldEnum = {
  id: 'id',
  address: 'address',
  charging_when_closed: 'charging_when_closed',
  city: 'city',
  country: 'country',
  country_code: 'country_code',
  last_updated: 'last_updated',
  name: 'name',
  parking_type: 'parking_type',
  party_id: 'party_id',
  postal_code: 'postal_code',
  publish: 'publish',
  publish_allowed_to: 'publish_allowed_to',
  time_zone: 'time_zone'
};

exports.Prisma.LocationPriceScalarFieldEnum = {
  id: 'id',
  locationId: 'locationId',
  start: 'start',
  end: 'end',
  energy_price: 'energy_price',
  blocking_fee: 'blocking_fee',
  blocking_fee_start: 'blocking_fee_start',
  blocking_fee_max: 'blocking_fee_max',
  session_fee: 'session_fee',
  tax_rate: 'tax_rate',
  empId: 'empId',
  current_type: 'current_type'
};

exports.Prisma.OcpiConnectionScalarFieldEnum = {
  id: 'id',
  cpo_secret: 'cpo_secret',
  empId: 'empId',
  emp_secret: 'emp_secret',
  inital_token: 'inital_token',
  name: 'name',
  url: 'url',
  version: 'version'
};

exports.Prisma.PaymentIntentScalarFieldEnum = {
  id: 'id',
  amount: 'amount',
  amount_capturable: 'amount_capturable',
  amount_received: 'amount_received',
  authorization_reference: 'authorization_reference',
  cdrId: 'cdrId',
  createdAt: 'createdAt',
  energy_price: 'energy_price',
  evseId: 'evseId',
  invoiceMail: 'invoiceMail',
  locationId: 'locationId',
  min_kwh_in_kwh: 'min_kwh_in_kwh',
  min_time_in_min: 'min_time_in_min',
  parking_price: 'parking_price',
  sessionId: 'sessionId',
  session_fee: 'session_fee',
  session_start_token_uid: 'session_start_token_uid',
  status: 'status',
  tax_rate: 'tax_rate',
  updatedAt: 'updatedAt'
};

exports.Prisma.SessionScalarFieldEnum = {
  id: 'id',
  auth_method: 'auth_method',
  authorization_reference: 'authorization_reference',
  connector_id: 'connector_id',
  country_code: 'country_code',
  currency: 'currency',
  end_date_time: 'end_date_time',
  evse_uid: 'evse_uid',
  kwh: 'kwh',
  last_updated: 'last_updated',
  location_id: 'location_id',
  party_id: 'party_id',
  start_date_time: 'start_date_time',
  status: 'status'
};

exports.Prisma.SortOrder = {
  asc: 'asc',
  desc: 'desc'
};

exports.Prisma.QueryMode = {
  default: 'default',
  insensitive: 'insensitive'
};
exports.CurrentType = exports.$Enums.CurrentType = {
  AC: 'AC',
  DC: 'DC'
};

exports.Prisma.ModelName = {
  Cdr: 'Cdr',
  Counter: 'Counter',
  Emp: 'Emp',
  EmpAddress: 'EmpAddress',
  EmpPrice: 'EmpPrice',
  Invoice: 'Invoice',
  Location: 'Location',
  LocationPrice: 'LocationPrice',
  OcpiConnection: 'OcpiConnection',
  PaymentIntent: 'PaymentIntent',
  Session: 'Session'
};

/**
 * This is a stub Prisma Client that will error at runtime if called.
 */
class PrismaClient {
  constructor() {
    return new Proxy(this, {
      get(target, prop) {
        let message
        const runtime = getRuntime()
        if (runtime.isEdge) {
          message = `PrismaClient is not configured to run in ${runtime.prettyName}. In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters
`;
        } else {
          message = 'PrismaClient is unable to run in this browser environment, or has been bundled for the browser (running in `' + runtime.prettyName + '`).'
        }
        
        message += `
If this is unexpected, please open an issue: https://pris.ly/prisma-prisma-bug-report`

        throw new Error(message)
      }
    })
  }
}

exports.PrismaClient = PrismaClient

Object.assign(exports, Prisma)
