generator client {
  provider = "prisma-client-js"
  output   = "./client"
}

generator zod {
  provider                         = "zod-prisma-types"
  output                           = "./generated/zod"
  coerceDate                       = "false"
  prismaClientPath                 = "../../client"
  writeNullishInModelTypes         = "true"
  addSelectType                    = "true"
  createPartialTypes               = "true"
  useMultipleFiles                 = "false"
  addInputTypeValidation           = "true"
  validateWhereUniqueInput         = "true"
  addIncludeType                   = "true"
  createModelTypes                 = "true"
  createInputTypes                 = "false"
  createOptionalDefaultValuesTypes = "true"
  createRelationValuesTypes        = "false"
  useDefaultValidators             = "false"
}

datasource db {
  provider = "mongodb"
  url      = env("DATABASE_URL_MONGODB")
}

type CdrCdrLocation {
  address              String
  city                 String
  connector_format     String
  connector_id         String
  connector_power_type String
  connector_standard   String
  coordinates          CdrCdrLocationCoordinates
  country              String
  evse_id              String
  evse_uid             String
  id                   String
  name                 String
  postal_code          String
}

type CdrCdrLocationCoordinates {
  latitude  String
  longitude String
}

type CdrCdrToken {
  contract_id String
  type        String
  uid         String
}

type CdrChargingPeriods {
  dimensions      CdrChargingPeriodsDimensions[]
  start_date_time String
  tariff_id       String
}

type CdrChargingPeriodsDimensions {
  type   String
  /// Multiple data types found: Float: 39.6%, Int: 60.4% out of 48 sampled entries
  volume Json
}

type CdrTariffs {
  country_code    String
  currency        String
  elements        CdrTariffsElements[]
  id              String
  last_updated    String
  party_id        String
  tariff_alt_text CdrTariffsTariffAltText[]
  type            String
}

type CdrTariffsElements {
  price_components CdrTariffsElementsPriceComponents[]
}

type CdrTariffsElementsPriceComponents {
  /// Multiple data types found: Float: 50%, Int: 50% out of 34 sampled entries
  price     Json
  step_size Int
  type      String
}

type CdrTariffsTariffAltText {
  language String
  text     String
}

type CdrTotalCost {
  /// Multiple data types found: Float: 70.6%, BigInt: 29.4% out of 17 sampled entries
  excl_vat Json
}

type CdrTotalEnergyCost {
  /// Multiple data types found: Float: 70.6%, BigInt: 29.4% out of 17 sampled entries
  excl_vat Json
}

type CdrTotalFixedCost {
  /// Multiple data types found: Float: 70.6%, BigInt: 29.4% out of 17 sampled entries
  excl_vat Json
}

type CdrTotalParkingCost {
  /// Multiple data types found: Float: 70.6%, BigInt: 29.4% out of 17 sampled entries
  excl_vat Json
}

type CdrTotalTimeCost {
  /// Multiple data types found: Float: 70.6%, BigInt: 29.4% out of 17 sampled entries
  excl_vat Json
}

type InvoiceFiles {
  application_type String
  create_date      DateTime @db.Date
  kind_of_file     String
  name             String
  path             String
}

type InvoiceInvoicePositions {
  amount           Float
  description      String
  position         BigInt
  sum_gross        Float
  sum_net          Float
  sum_tax          Float
  tax_rate         Float
  title            String
  unit             String
  unit_price       Float
  unit_price_gross Float
}

type InvoiceMetadata {
  paymentIntent String
}

type LocationCoordinates {
  latitude  String
  longitude String
}

type LocationEvses {
  capabilities       String[]
  connectors         LocationEvsesConnectors[]
  coordinates        LocationEvsesCoordinates
  evse_id            String?
  last_updated       String
  physical_reference String
  status             String
  uid                String
}

type LocationEvsesConnectors {
  format             String
  id                 String
  last_updated       String
  max_amperage       Int
  max_electric_power Int
  max_voltage        Int
  power_type         String
  standard           String
  tariff_ids         String[]
}

type LocationEvsesCoordinates {
  latitude  String
  longitude String
}

type LocationOperator {
  name String
}

type LocationOwner {
  name String
}

type SessionCdrToken {
  contract_id String
  type        String
  uid         String
}

type SessionChargingPeriods {
  dimensions      SessionChargingPeriodsDimensions[]
  start_date_time String
  tariff_id       String
}

type SessionChargingPeriodsDimensions {
  type   String
  volume Float
}

type SessionTotalCost {
  excl_vat Float
}

model Cdr {
  id                      String               @id @map("_id")
  auth_method             String
  authorization_reference String
  cdr_location            CdrCdrLocation
  cdr_token               CdrCdrToken
  charging_periods        CdrChargingPeriods[]
  country_code            String
  credit                  Boolean
  currency                String
  end_date_time           String
  last_updated            String
  party_id                String
  session_id              String
  start_date_time         String
  tariffs                 CdrTariffs[]
  total_cost              CdrTotalCost
  /// Multiple data types found: Float: 47.1%, Int: 52.9% out of 17 sampled entries
  total_energy            Json
  total_energy_cost       CdrTotalEnergyCost
  total_fixed_cost        CdrTotalFixedCost
  total_parking_cost      CdrTotalParkingCost
  /// Multiple data types found: Float: 70.6%, BigInt: 29.4% out of 17 sampled entries
  total_parking_time      Json
  total_time              Float
  total_time_cost         CdrTotalTimeCost
}

model Counter {
  id             String @id @map("_id")
  sequence_value BigInt
}

model Emp {
  id              String @id @default(auto()) @map("_id") @db.ObjectId
  amount_to_block Int
  country_code    String
  name            String
  party_id        String
  min_kwh_in_kwh  Float            @default(0)
  min_time_in_min Int              @default(0)
  location_price  LocationPrice[]
  price           EmpPrice[]
}

model EmpAddress {
  id           String   @id @default(auto()) @map("_id") @db.ObjectId
  city         String
  country      String
  empId        String   @db.ObjectId
  from         DateTime @db.Date
  house_number String
  postal_code  String
  street       String
  to           DateTime @db.Date
  ust_id       String
  vat_id       String
}

model EmpPrice {
  id                 String   @id @default(auto()) @map("_id") @db.ObjectId
  start              DateTime
  end                DateTime
  energy_price       Float
  blocking_fee       Float
  blocking_fee_start Int
  blocking_fee_max   Float
  session_fee        Float
  tax_rate           Float
  Emp                Emp?     @relation(fields: [empId], references: [id])
  empId              String?  @db.ObjectId
  current_type       CurrentType @default(AC)
}
model Invoice {
  id                      String                    @id @default(auto()) @map("_id") @db.ObjectId
  authorization_reference String
  create_date             DateTime                  @db.Date
  currency                String
  emp_country_code        String
  emp_party_id            String
  files                   InvoiceFiles[]
  invoicePositions        InvoiceInvoicePositions[]
  invoice_date            DateTime                  @db.Date
  invoice_number          String?
  kind_of_invoice         String
  last_update             DateTime                  @db.Date
  local_end_datetime      String?
  local_start_datetime    String?
  mailToSend              String?
  metadata                InvoiceMetadata
  paid_date               DateTime?                 @db.Date
  sentAsEmail             Boolean
  service_period_from     DateTime                  @db.Date
  service_period_to       DateTime                  @db.Date
  status                  String
  subject                 String
  sum_gross               Float
  sum_net                 Float
  sum_tax                 Float
}

model Location {
  id                   String              @id @map("_id")
  address              String
  charging_when_closed Boolean
  city                 String
  coordinates          LocationCoordinates
  country              String
  country_code         String
  evses                LocationEvses[]
  last_updated         String
  name                 String
  operator             LocationOperator
  owner                LocationOwner
  parking_type         String
  party_id             String
  postal_code          String
  publish              Boolean
  /// Could not determine type: the field only had null or empty values in the sample set.
  publish_allowed_to   Json?
  time_zone            String
  LocationPrice        LocationPrice[]
}
enum CurrentType {
  AC
  DC
}

model LocationPrice {
  id                 String      @id @default(auto()) @map("_id") @db.ObjectId
  location           Location?   @relation(fields: [locationId], references: [id])
  locationId         String?
  start              DateTime
  end                DateTime
  energy_price       Float
  blocking_fee       Float
  blocking_fee_start Int
  blocking_fee_max   Float
  session_fee        Float
  tax_rate           Float
  Emp                Emp?        @relation(fields: [empId], references: [id])
  empId              String?     @db.ObjectId
  current_type       CurrentType @default(AC)
}



model OcpiConnection {
  id           String @id @default(auto()) @map("_id") @db.ObjectId
  cpo_secret   String
  empId        String @db.ObjectId
  emp_secret   String
  inital_token String
  name         String
  url          String
  version      String
}

model PaymentIntent {
  id                      String   @id @map("_id")
  /// Multiple data types found: Int: 65.6%, BigInt: 34.4% out of 131 sampled entries
  amount                  Json
  /// Multiple data types found: Int: 65.6%, BigInt: 34.4% out of 131 sampled entries
  amount_capturable       Json
  /// Multiple data types found: Int: 65.6%, BigInt: 34.4% out of 131 sampled entries
  amount_received         Json
  authorization_reference String
  cdrId                   String?
  createdAt               DateTime @db.Date
  energy_price            Float?
  evseId                  String?
  invoiceMail             String?
  locationId              String?
  min_kwh_in_kwh          Float?
  /// Multiple data types found: Int: 91.5%, BigInt: 8.5% out of 94 sampled entries
  min_time_in_min         Json?
  /// Multiple data types found: Float: 21.8%, Int: 78.2% out of 110 sampled entries
  parking_price           Json?
  sessionId               String?
  /// Multiple data types found: Float: 21.8%, Int: 78.2% out of 110 sampled entries
  session_fee             Json?
  session_start_token_uid String
  status                  String
  /// Multiple data types found: Float: 21.8%, Int: 78.2% out of 110 sampled entries
  tax_rate                Json?
  updatedAt               DateTime @db.Date
}

model Session {
  id                      String                   @id @map("_id")
  auth_method             String
  authorization_reference String
  cdr_token               SessionCdrToken
  charging_periods        SessionChargingPeriods[]
  connector_id            String
  country_code            String
  currency                String
  end_date_time           String?
  evse_uid                String
  kwh                     Float
  last_updated            String
  location_id             String
  party_id                String
  start_date_time         String
  status                  String
  total_cost              SessionTotalCost
}
