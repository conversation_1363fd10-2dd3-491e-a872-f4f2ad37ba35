version: "3"
services:
  redis:
    image: redis:7-alpine
    restart: always
    env_file:
      - .env
    ports:
      - 6379:6379
    command: redis-server --save 20 1 --loglevel warning --requirepass eYVX7EwVmmxKPCDmwMtyKVge8oLd2t81
    volumes:
      - "cache:/data"
    networks:
      - eulektro_app
  quirrel:
    restart: always
    image: ghcr.io/quirrel-dev/quirrel:main
    env_file:
      - .env
    ports:
      - '9181:9181'
    networks:
      - eulektro_app

volumes:
  cache:

networks:
  eulektro_app:
    driver: bridge
    ipam:
      config:
        - subnet: *********/24
