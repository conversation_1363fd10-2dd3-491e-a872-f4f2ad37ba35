#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const pdfParse = require('pdf-parse');

// RABOT E-Mail Matcher Dependencies
let Imap, simpleParser, axios, dotenv;
try {
  Imap = require('imap');
  simpleParser = require('mailparser').simpleParser;
  axios = require('axios');
  dotenv = require('dotenv');
} catch (error) {
  console.log('RABOT E-Mail Funktionen nicht verfügbar (fehlende Dependencies)');
}

// Konfiguration
const RABOT_FOLDER = './rabot_rechnungen';
const OUTPUT_BASE_FOLDER = './rabot_rechnungen_sortiert';

// Command line arguments
const args = process.argv.slice(2);
const mode = args[0] || 'organize';
const envFile = args[1] || '.env';

// Load environment variables if available
if (dotenv && fs.existsSync(envFile)) {
  console.log(`📄 Lade Umgebungsvariablen aus: ${envFile}`);
  dotenv.config({ path: envFile });
}

/**
 * Extrahiert Text aus einer PDF-Datei mit pdf-parse
 */
async function extractTextFromPDF(filePath) {
  try {
    console.log(`Extrahiere Text aus: ${filePath}`);
    
    const dataBuffer = fs.readFileSync(filePath);
    const data = await pdfParse(dataBuffer);
    
    return data.text;
  } catch (error) {
    console.error(`Fehler beim Extrahieren von Text aus ${filePath}:`, error.message);
    return '';
  }
}

/**
 * Extrahiert spezifische Informationen aus dem PDF-Text
 */
function extractInformation(text, filename) {
  const result = {
    filename,
    fullText: text,
    monatsauflistung: []
  };

  // Referenznummer/Vertragsnummer suchen
  const referenzPatterns = [
    /Vertragsnummer\s+(\d+)/i,
    /Referenznummer\s+(\d+)/i,
    /Vertrags-Nr\.?\s+(\d+)/i,
    /Ref\.?\s*Nr\.?\s+(\d+)/i,
    /Rechnung\s*Nr\.?\s*(\d+)/i
  ];
  
  for (const pattern of referenzPatterns) {
    const match = text.match(pattern);
    if (match) {
      result.referenznummer = match[1];
      break;
    }
  }

  // Fallback: Extrahiere aus Dateiname wenn nicht im Text gefunden
  if (!result.referenznummer) {
    const filenameMatch = filename.match(/Rechnung_Nr__(\d+)/i);
    if (filenameMatch) {
      result.referenznummer = filenameMatch[1];
    }
  }

  // Lieferanschrift suchen - nach "Lieferanschrift:" Pattern
  const lieferanschriftPatterns = [
    /Lieferanschrift:\s*\n?\s*([^]*?)(?=\n\n|\nRechnungsdatum|\nDatum|\nBeleg|\nRechnung|$)/i,
    /Lieferanschrift\s*\n?\s*([^]*?)(?=\n\n|\nRechnungsdatum|\nDatum|\nBeleg|\nRechnung|$)/i,
    /Lieferadresse:\s*\n?\s*([^]*?)(?=\n\n|\nRechnungsdatum|\nDatum|\nBeleg|\nRechnung|$)/i,
    /Lieferadresse\s*\n?\s*([^]*?)(?=\n\n|\nRechnungsdatum|\nDatum|\nBeleg|\nRechnung|$)/i
  ];
  
  for (const pattern of lieferanschriftPatterns) {
    const match = text.match(pattern);
    if (match && match[1]) {
      // Bereinige die Adresse
      let address = match[1]
        .replace(/\s+/g, ' ')
        .trim()
        .substring(0, 200); // Begrenze Länge
      
      // Entferne führende/nachfolgende Sonderzeichen
      address = address.replace(/^[:\s\-]+|[:\s\-]+$/g, '');
      
      if (address.length > 10) { // Nur wenn sinnvolle Länge
        result.lieferanschrift = address;
        console.log(`  📍 GEFUNDEN IM PDF: "${address}"`);
        break;
      }
    }
  }

  // Fallback: Markiere als unbekannt wenn nicht im Text gefunden
  if (!result.lieferanschrift) {
    console.log(`  ⚠️  KEINE LIEFERANSCHRIFT IM PDF GEFUNDEN`);
    result.lieferanschrift = 'Unbekannte Lieferanschrift';
  }

  // Rechnungsdatum suchen
  const datumPatterns = [
    /Rechnungsdatum[:\s]*(\d{1,2}\.?\d{1,2}\.?\d{2,4})/i,
    /Datum[:\s]*(\d{1,2}\.?\d{1,2}\.?\d{2,4})/i,
    /(\d{1,2}\.\d{1,2}\.\d{2,4})/g
  ];
  
  for (const pattern of datumPatterns) {
    const match = text.match(pattern);
    if (match) {
      result.rechnungsdatum = match[1];
      break;
    }
  }

  // Fallback: Extrahiere Datum aus Dateiname
  if (!result.rechnungsdatum) {
    const dateMatch = filename.match(/(\d{4}-\d{2}-\d{2})/);
    if (dateMatch) {
      const isoDate = dateMatch[1];
      const [year, month, day] = isoDate.split('-');
      result.rechnungsdatum = `${day}.${month}.${year.slice(2)}`;
    }
  }

  // Beleg-Nr. suchen
  const belegPatterns = [
    /Beleg-Nr\.?\s*(\d+)/i,
    /Belegnummer\s*(\d+)/i,
    /Rechnung\s*Nr\.?\s*(\d+)/i,
    /Invoice\s*No\.?\s*(\d+)/i,
    /Rechnungsnummer\s*(\d+)/i
  ];
  
  for (const pattern of belegPatterns) {
    const match = text.match(pattern);
    if (match) {
      result.belegNr = match[1];
      break;
    }
  }

  // Fallback: Verwende Referenznummer als Beleg-Nr.
  if (!result.belegNr && result.referenznummer) {
    result.belegNr = result.referenznummer;
  }

  // Monatsauflistung suchen
  const monatsauflistungPattern = /Monatsauflistung([^]*?)(?=\n\n|\nRechnungsdatum|\nDatum|\nBeleg|\nRechnung|\nSumme|$)/i;
  const monatsauflistungMatch = text.match(monatsauflistungPattern);

  if (monatsauflistungMatch && monatsauflistungMatch[1]) {
    const monatsauflistungText = monatsauflistungMatch[1];
    console.log(`  📊 MONATSAUFLISTUNG GEFUNDEN: "${monatsauflistungText.substring(0, 150)}..."`);

    // Suche nach Tabellenzeilen mit Monat, Verbrauch, Abrechnung, Abschlag, Verrechnung
    // Pattern für Tabellenzeilen: Monat (Text) + Zahlen/Beträge (mit expliziten Minus-Zeichen)
    const tabellenZeilenPattern = /([A-Za-zäöüÄÖÜ]+\s+\d{4})\s*([0-9.,]+)\s*kWh\s*([\-]?[0-9.,]+)\s*€\s*([\-]?[0-9.,]+)\s*€\s*([\-]?[0-9.,]+)\s*€/g;

    let match;
    while ((match = tabellenZeilenPattern.exec(monatsauflistungText)) !== null) {
      const monatsEintrag = {
        monat: match[1].trim(),
        verbrauch: match[2] + ' kWh',
        abrechnung: match[3] + ' €',
        abschlag: match[4] + ' €',
        verrechnung: match[5] + ' €'
      };

      result.monatsauflistung.push(monatsEintrag);
      console.log(`    📅 ${monatsEintrag.monat}: Verbrauch ${monatsEintrag.verbrauch}, Abrechnung ${monatsEintrag.abrechnung}, Abschlag ${monatsEintrag.abschlag}, Verrechnung ${monatsEintrag.verrechnung}`);
    }

    // Fallback: Flexibleres Pattern für verschiedene Formate
    if (result.monatsauflistung.length === 0) {
      const flexiblesPattern = /([A-Za-zäöüÄÖÜ]+\s+\d{4})[^]*?(\d+[.,]\d+)[^]*?kWh[^]*?([\-]?\d+[.,]\d+)[^]*?€[^]*?([\-]?\d+[.,]\d+)[^]*?€[^]*?([\-]?\d+[.,]\d+)[^]*?€/g;

      while ((match = flexiblesPattern.exec(monatsauflistungText)) !== null) {
        const monatsEintrag = {
          monat: match[1].trim(),
          verbrauch: match[2] + ' kWh',
          abrechnung: match[3] + ' €',
          abschlag: match[4] + ' €',
          verrechnung: match[5] + ' €'
        };

        result.monatsauflistung.push(monatsEintrag);
        console.log(`    📅 ${monatsEintrag.monat}: Verbrauch ${monatsEintrag.verbrauch}, Abrechnung ${monatsEintrag.abrechnung}, Abschlag ${monatsEintrag.abschlag}, Verrechnung ${monatsEintrag.verrechnung}`);
      }
    }

    if (result.monatsauflistung.length === 0) {
      console.log(`    ⚠️  Keine Tabellenzeilen in Monatsauflistung gefunden`);
    }
  }

  return result;
}

/**
 * Bereinigt einen String für die Verwendung als Ordnername
 */
function sanitizeDirectoryName(name) {
  return name
    .replace(/[<>:"/\\|?*]/g, '_')
    .replace(/\s+/g, '_')
    .replace(/_+/g, '_')
    .replace(/^_+|_+$/g, '')
    .substring(0, 150); // Längere Namen erlauben
}

/**
 * Erstellt einen Ordnernamen basierend auf der Lieferanschrift
 */
function createDirectoryName(lieferanschrift) {
  if (!lieferanschrift) {
    return 'Unbekannte_Lieferanschrift';
  }
  
  // Verwende die komplette Lieferanschrift als Ordnername
  return sanitizeDirectoryName(lieferanschrift);
}

/**
 * Kopiert eine Datei in den Zielordner
 */
async function copyFileToDirectory(sourceFile, targetDir, filename) {
  try {
    // Stelle sicher, dass der Zielordner existiert
    if (!fs.existsSync(targetDir)) {
      fs.mkdirSync(targetDir, { recursive: true });
      console.log(`Ordner erstellt: ${targetDir}`);
    }
    
    const targetFile = path.join(targetDir, filename);
    
    // Kopiere die Datei
    fs.copyFileSync(sourceFile, targetFile);
    console.log(`Datei kopiert: ${filename} -> ${targetDir}`);
  } catch (error) {
    console.error(`Fehler beim Kopieren von ${filename}:`, error);
    throw error;
  }
}

/**
 * Verarbeitet alle PDF-Dateien im Rabot-Ordner
 */
async function processPDFs() {
  const result = {
    processed: 0,
    successful: 0,
    failed: 0,
    results: []
  };

  try {
    // Prüfe ob der Rabot-Ordner existiert
    if (!fs.existsSync(RABOT_FOLDER)) {
      throw new Error(`Rabot-Ordner nicht gefunden: ${RABOT_FOLDER}`);
    }

    // Erstelle den Ausgabe-Ordner falls er nicht existiert
    if (!fs.existsSync(OUTPUT_BASE_FOLDER)) {
      fs.mkdirSync(OUTPUT_BASE_FOLDER, { recursive: true });
    }

    // Finde alle PDF-Dateien
    const files = fs.readdirSync(RABOT_FOLDER)
      .filter(file => file.toLowerCase().endsWith('.pdf'))
      .map(file => path.join(RABOT_FOLDER, file));

    console.log(`Gefunden: ${files.length} PDF-Dateien`);

    for (const filePath of files) {
      result.processed++;
      const filename = path.basename(filePath);
      
      try {
        console.log(`\n--- Verarbeite ${filename} ---`);
        
        // Extrahiere Text aus PDF
        const text = await extractTextFromPDF(filePath);
        
        // Extrahiere Informationen
        const info = extractInformation(text, filename);
        result.results.push(info);
        
        // Zeige extrahierte Informationen
        console.log('Extrahierte Informationen:');
        console.log(`  Referenznummer: ${info.referenznummer || 'Nicht gefunden'}`);
        console.log(`  🏢 LIEFERANSCHRIFT: ${info.lieferanschrift || 'Nicht gefunden'}`);
        console.log(`  Rechnungsdatum: ${info.rechnungsdatum || 'Nicht gefunden'}`);
        console.log(`  Beleg-Nr.: ${info.belegNr || 'Nicht gefunden'}`);

        // Monatsauflistung Informationen
        console.log(`\n  📊 MONATSAUFLISTUNG:`);
        if (info.monatsauflistung.length > 0) {
          console.log(`    Gefunden: ${info.monatsauflistung.length} Monate`);
          info.monatsauflistung.forEach(eintrag => {
            console.log(`    📅 ${eintrag.monat}: ${eintrag.verbrauch}, Abrechnung ${eintrag.abrechnung}, Abschlag ${eintrag.abschlag}, Verrechnung ${eintrag.verrechnung}`);
          });
        } else {
          console.log(`    Keine Monatsauflistung gefunden`);
        }

        // Zusätzliche Hervorhebung der Lieferanschrift
        if (info.lieferanschrift) {
          console.log(`\n  ✅ GEFUNDENE LIEFERANSCHRIFT: "${info.lieferanschrift}"`);
        } else {
          console.log(`\n  ❌ KEINE LIEFERANSCHRIFT GEFUNDEN`);
        }
        
        // Erstelle Ordnername basierend auf Lieferanschrift
        const dirName = createDirectoryName(info.lieferanschrift || 'Unbekannt');
        const targetDir = path.join(OUTPUT_BASE_FOLDER, dirName);
        
        // Kopiere Datei in den entsprechenden Ordner
        await copyFileToDirectory(filePath, targetDir, filename);
        
        result.successful++;
        
      } catch (error) {
        console.error(`Fehler bei der Verarbeitung von ${filename}:`, error);
        result.failed++;
      }
    }

  } catch (error) {
    console.error('Fehler beim Verarbeiten der PDFs:', error);
    throw error;
  }

  return result;
}

/**
 * Erstellt einen Bericht über die Verarbeitung
 */
function generateReport(result) {
  console.log('\n=== VERARBEITUNGSBERICHT ===');
  console.log(`Verarbeitet: ${result.processed} Dateien`);
  console.log(`Erfolgreich: ${result.successful} Dateien`);
  console.log(`Fehlgeschlagen: ${result.failed} Dateien`);
  
  // Gruppiere nach Lieferanschrift
  const grouped = result.results.reduce((acc, item) => {
    const key = item.lieferanschrift || 'Unbekannte Lieferanschrift';
    if (!acc[key]) {
      acc[key] = [];
    }
    acc[key].push(item);
    return acc;
  }, {});
  
  console.log('\n=== GRUPPIERUNG NACH LIEFERANSCHRIFT ===');
  Object.entries(grouped).forEach(([address, items]) => {
    console.log(`\n${address} (${items.length} Dateien):`);
    items.forEach(item => {
      console.log(`  - ${item.filename}`);
      if (item.referenznummer) console.log(`    Ref: ${item.referenznummer}`);
      if (item.rechnungsdatum) console.log(`    Datum: ${item.rechnungsdatum}`);
      if (item.belegNr) console.log(`    Beleg: ${item.belegNr}`);
    });
  });
  
  console.log('\n=== ZUSAMMENFASSUNG DER ORDNER ===');
  Object.entries(grouped).forEach(([address, items]) => {
    const dirName = sanitizeDirectoryName(address);
    console.log(`Ordner: ${dirName} -> ${items.length} Dateien`);
  });
  
  console.log('\n=== 🏢 ALLE GEFUNDENEN LIEFERANSCHRIFTEN ===');
  const uniqueAddresses = [...new Set(result.results.map(item => item.lieferanschrift).filter(Boolean))];
  uniqueAddresses.forEach((address, index) => {
    const count = result.results.filter(item => item.lieferanschrift === address).length;
    console.log(`${index + 1}. "${address}" (${count} Dateien)`);
  });
  
  if (uniqueAddresses.length === 0) {
    console.log('❌ Keine Lieferanschriften gefunden!');
  } else {
    console.log(`\n✅ Insgesamt ${uniqueAddresses.length} verschiedene Lieferanschriften gefunden.`);
  }

  // Monatsauflistung Statistiken
  console.log('\n=== 📊 MONATSAUFLISTUNG STATISTIKEN ===');
  const monatsStats = {
    mitMonatsauflistung: 0,
    ohneMonatsauflistung: 0,
    gesamtMonate: 0,
    durchschnittMonate: 0
  };

  result.results.forEach(item => {
    if (item.monatsauflistung.length > 0) {
      monatsStats.mitMonatsauflistung++;
      monatsStats.gesamtMonate += item.monatsauflistung.length;
    } else {
      monatsStats.ohneMonatsauflistung++;
    }
  });

  if (monatsStats.mitMonatsauflistung > 0) {
    monatsStats.durchschnittMonate = (monatsStats.gesamtMonate / monatsStats.mitMonatsauflistung).toFixed(1);
  }

  console.log(`📊 Dateien mit Monatsauflistung: ${monatsStats.mitMonatsauflistung}`);
  console.log(`📊 Dateien ohne Monatsauflistung: ${monatsStats.ohneMonatsauflistung}`);
  console.log(`📅 Gesamt gefundene Monate: ${monatsStats.gesamtMonate}`);
  console.log(`📈 Durchschnitt Monate pro Datei: ${monatsStats.durchschnittMonate}`);

  // Zeige alle gefundenen Monate
  if (monatsStats.gesamtMonate > 0) {
    console.log('\n=== 📅 ALLE GEFUNDENEN MONATE ===');
    const alleMonate = new Set();
    result.results.forEach(item => {
      item.monatsauflistung.forEach(eintrag => {
        alleMonate.add(eintrag.monat);
      });
    });

    const sortedMonate = Array.from(alleMonate).sort();
    sortedMonate.forEach((monat, index) => {
      console.log(`${index + 1}. ${monat}`);
    });
  }
}

/**
 * Erstellt eine CSV-Datei mit allen extrahierten Informationen
 */
function createCSVReport(result) {
  const csvPath = path.join(OUTPUT_BASE_FOLDER, 'rabot_rechnungen_monatsauflistung.csv');

  let csvContent = 'Dateiname,Referenznummer,Lieferanschrift,Rechnungsdatum,Beleg-Nr,Anzahl-Monate,Monate-Details,Ordner\n';

  result.results.forEach(item => {
    const dirName = createDirectoryName(item.lieferanschrift || 'Unbekannt');

    // Erstelle Monate-Details String
    const monateDetails = item.monatsauflistung.map(eintrag =>
      `${eintrag.monat}:${eintrag.verbrauch}:${eintrag.abrechnung}:${eintrag.abschlag}:${eintrag.verrechnung}`
    ).join(';');

    const row = [
      `"${item.filename}"`,
      `"${item.referenznummer || ''}"`,
      `"${item.lieferanschrift || ''}"`,
      `"${item.rechnungsdatum || ''}"`,
      `"${item.belegNr || ''}"`,
      `"${item.monatsauflistung.length}"`,
      `"${monateDetails}"`,
      `"${dirName}"`
    ].join(',');
    csvContent += row + '\n';
  });

  fs.writeFileSync(csvPath, csvContent, 'utf8');
  console.log(`\nCSV-Bericht erstellt: ${csvPath}`);

  // Erstelle zusätzliche detaillierte CSV für Monatsauflistung
  const detailCsvPath = path.join(OUTPUT_BASE_FOLDER, 'rabot_rechnungen_monate_detail.csv');
  let detailCsvContent = 'Dateiname,Referenznummer,Lieferanschrift,Monat,Verbrauch,Abrechnung,Abschlag,Verrechnung\n';

  result.results.forEach(item => {
    item.monatsauflistung.forEach(eintrag => {
      const row = [
        `"${item.filename}"`,
        `"${item.referenznummer || ''}"`,
        `"${item.lieferanschrift || ''}"`,
        `"${eintrag.monat}"`,
        `"${eintrag.verbrauch}"`,
        `"${eintrag.abrechnung}"`,
        `"${eintrag.abschlag}"`,
        `"${eintrag.verrechnung}"`
      ].join(',');
      detailCsvContent += row + '\n';
    });
  });

  fs.writeFileSync(detailCsvPath, detailCsvContent, 'utf8');
  console.log(`Detail-CSV erstellt: ${detailCsvPath}`);
}

// RABOT E-Mail Matcher Helper Functions
function validateEnvironmentVariables() {
  const requiredVars = [
    "RABOT_MATCHER_EMAIL_USER",
    "RABOT_MATCHER_EMAIL_PASSWORD",
    "RABOT_MATCHER_EMAIL_HOST",
    "RABOT_MATCHER_EMAIL_PORT",
    "RABOT_MATCHER_SENDER_EMAIL",
    "RABOT_MATCHER_DOWNLOAD_FOLDER",
    "RABOT_MATCHER_DAYS_BACK",
  ];

  const missing = requiredVars.filter((varName) => {
    const value = process.env[varName];
    return !value || value.trim() === "";
  });

  if (missing.length > 0) {
    throw new Error(`Missing required environment variables: ${missing.join(", ")}`);
  }

  console.log("Environment variables validated successfully");
}

function getImapConfig() {
  return {
    user: process.env.RABOT_MATCHER_EMAIL_USER,
    password: process.env.RABOT_MATCHER_EMAIL_PASSWORD,
    host: process.env.RABOT_MATCHER_EMAIL_HOST,
    port: parseInt(process.env.RABOT_MATCHER_EMAIL_PORT),
    tls: true,
    authTimeout: 30000,
    connTimeout: 30000,
    keepalive: false,
  };
}

function getDownloadDir() {
  return process.env.RABOT_MATCHER_DOWNLOAD_FOLDER || RABOT_FOLDER;
}

function ensureDirectoryExists(dir) {
  try {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
  } catch (error) {
    console.error(`Error creating directory ${dir}:`, error);
  }
}

function openInbox(imap) {
  return new Promise((resolve, reject) => {
    imap.openBox("INBOX", false, (err, box) => {
      if (err) {
        reject(err);
      } else {
        resolve(box);
      }
    });
  });
}

function trySearch(imap, criteria) {
  return new Promise((resolve, reject) => {
    imap.search(criteria, (err, results) => {
      if (err) {
        reject(err);
      } else {
        resolve(results);
      }
    });
  });
}

function extractLinksFromText(text) {
  // Specific regex for Billogram links only: https://billogram.com/l/xxxxx
  const billogramRegex = /https:\/\/billogram\.com\/l\/[a-zA-Z0-9]+/gi;
  const matches = text.match(billogramRegex);
  console.log(`Found ${matches?.length || 0} Billogram links in text`);
  return matches || [];
}

function extractLinksFromHtml(html) {
  // Only extract Billogram links from HTML
  const billogramLinks = [];

  // Extract Billogram links from href attributes
  const hrefRegex = /href=["']([^"']*?)["']/gi;
  let match;

  while ((match = hrefRegex.exec(html)) !== null) {
    const link = match[1];
    if (link && isBillogramLink(link)) {
      console.log(`Found Billogram link in href: ${link}`);
      billogramLinks.push(link);
    }
  }

  // Also extract Billogram links from plain text in HTML
  const textLinks = extractLinksFromText(html);

  // Combine and remove duplicates manually
  const uniqueLinks = [];
  const allLinksArray = billogramLinks.concat(textLinks);
  for (const link of allLinksArray) {
    if (uniqueLinks.indexOf(link) === -1) {
      uniqueLinks.push(link);
    }
  }

  console.log(`Found ${uniqueLinks.length} unique Billogram links in HTML`);
  return uniqueLinks;
}

function isBillogramLink(url) {
  return url.toLowerCase().includes("billogram.com/l/");
}

function isLikelyInvoiceLink(url) {
  return isBillogramLink(url);
}

async function searchRabotEmailsInImap(imap, sinceDateString) {
  console.log(`Starting RABOT email search since ${sinceDateString}`);

  // Strategy 1: Try exact FROM match first
  console.log(`Strategy 1: Exact FROM match with "${process.env.RABOT_MATCHER_SENDER_EMAIL}"`);
  try {
    const exactResults = await trySearch(imap, [
      ["FROM", process.env.RABOT_MATCHER_SENDER_EMAIL],
      ["SINCE", sinceDateString],
    ]);
    if (exactResults.length > 0) {
      console.log(`✅ Strategy 1 successful: Found ${exactResults.length} emails`);
      return exactResults;
    }
  } catch (err) {
    console.log(`❌ Strategy 1 failed:`, err);
  }

  // Strategy 2: Try just the email address part
  console.log(`Strategy 2: Email address only "<EMAIL>"`);
  try {
    const emailResults = await trySearch(imap, [
      ["FROM", "<EMAIL>"],
      ["SINCE", sinceDateString],
    ]);
    if (emailResults.length > 0) {
      console.log(`✅ Strategy 2 successful: Found ${emailResults.length} emails`);
      return emailResults;
    }
  } catch (err) {
    console.log(`❌ Strategy 2 failed:`, err);
  }

  // Strategy 3: Try domain search
  console.log(`Strategy 3: Domain search "billogram.com"`);
  try {
    const domainResults = await trySearch(imap, [
      ["FROM", "billogram.com"],
      ["SINCE", sinceDateString],
    ]);
    if (domainResults.length > 0) {
      console.log(`✅ Strategy 3 successful: Found ${domainResults.length} emails`);
      return domainResults;
    }
  } catch (err) {
    console.log(`❌ Strategy 3 failed:`, err);
  }

  // Strategy 4: Try company name search
  console.log(`Strategy 4: Company name search "RABOT"`);
  try {
    const companyResults = await trySearch(imap, [
      ["FROM", "RABOT"],
      ["SINCE", sinceDateString],
    ]);
    if (companyResults.length > 0) {
      console.log(`✅ Strategy 4 successful: Found ${companyResults.length} emails`);
      return companyResults;
    }
  } catch (err) {
    console.log(`❌ Strategy 4 failed:`, err);
  }

  // Strategy 5: Manual filtering - get all emails since date and filter manually
  console.log(`Strategy 5: Manual filtering - getting all emails since ${sinceDateString}`);
  try {
    const allResults = await trySearch(imap, [["SINCE", sinceDateString]]);
    console.log(`Got ${allResults.length} total emails, now filtering for RABOT/Billogram...`);

    const filteredResults = await filterEmailsManually(imap, allResults);
    console.log(
      `✅ Strategy 5 completed: Found ${filteredResults.length} RABOT emails after manual filtering`,
    );
    return filteredResults;
  } catch (err) {
    console.log(`❌ Strategy 5 failed:`, err);
    throw err;
  }
}

async function filterEmailsManually(imap, emailIds) {
  return new Promise((resolve) => {
    const filteredIds = [];
    let processedCount = 0;

    if (emailIds.length === 0) {
      resolve([]);
      return;
    }

    console.log(`Manually checking ${emailIds.length} emails for RABOT/Billogram FROM headers...`);

    const f = imap.fetch(emailIds, {
      bodies: "HEADER.FIELDS (FROM)",
      struct: false,
    });

    f.on("message", (msg, seqno) => {
      msg.on("body", (stream) => {
        let buffer = "";
        stream.on("data", (chunk) => {
          buffer += chunk.toString("ascii");
        });
        stream.on("end", () => {
          const fromMatch = buffer.match(/From: (.+)/i);
          if (fromMatch && fromMatch[1]) {
            const fromHeader = fromMatch[1].trim();
            console.log(`[${seqno}] FROM: ${fromHeader}`);

            // Check if it's from RABOT or Billogram
            const fromLower = fromHeader.toLowerCase();
            if (
              fromLower.includes("<EMAIL>") ||
              fromLower.includes("rabot") ||
              fromLower.includes("billogram.com")
            ) {
              console.log(`✅ [${seqno}] MATCH: ${fromHeader}`);
              filteredIds.push(seqno);
            } else {
              console.log(`❌ [${seqno}] NO MATCH: ${fromHeader}`);
            }
          }

          processedCount++;
          if (processedCount === emailIds.length) {
            console.log(
              `Manual filtering complete: ${filteredIds.length} RABOT/Billogram emails found from ${emailIds.length} total`,
            );
            resolve(filteredIds);
          }
        });
      });
    });

    f.once("error", (err) => {
      console.error("Error during manual filtering:", err);
      resolve(filteredIds); // Return what we have so far
    });
  });
}

async function downloadFileFromUrl(url, filename) {
  try {
    console.log(`Attempting to download from: ${url}`);

    const response = await axios({
      method: "GET",
      url: url,
      responseType: "stream",
      timeout: 30000, // 30 seconds timeout
      maxRedirects: 5, // Follow redirects (important for Billogram links)
      headers: {
        "User-Agent":
          "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
      },
    });

    // Check if response is actually a PDF or document
    const contentType = response.headers["content-type"];
    console.log(`Content-Type: ${contentType}`);

    // Adjust filename based on content type
    let finalFilename = filename;
    if (contentType && contentType.includes("application/pdf")) {
      finalFilename = filename.endsWith(".pdf") ? filename : filename.replace(/\.[^.]*$/, ".pdf");
    } else if (contentType && contentType.includes("text/html")) {
      // If we get HTML, it might be a redirect page or error
      console.warn(`Received HTML instead of document for ${url}`);
      return null;
    }

    // Download to the configured download directory
    const downloadDir = getDownloadDir();
    ensureDirectoryExists(downloadDir);

    const filePath = path.join(downloadDir, finalFilename);
    const writer = fs.createWriteStream(filePath);

    response.data.pipe(writer);

    return new Promise((resolve, reject) => {
      writer.on('finish', () => {
        console.log(`Successfully downloaded: ${filePath}`);
        resolve({
          filePath,
          deliveryAddresses: [] // We'll extract this from PDF later if needed
        });
      });
      writer.on('error', reject);
    });
  } catch (error) {
    console.error(`Error downloading file from ${url}:`, error);
    return null;
  }
}

async function handleMessage(imap, msg, seqno) {
  console.log("Processing message #%d", seqno);
  const chunks = [];

  return new Promise((resolve, reject) => {
    msg.on("body", (stream) => {
      stream.on("data", (chunk) => chunks.push(chunk));
      stream.on("end", async () => {
        try {
          const buffer = Buffer.concat(chunks);
          const mail = await simpleParser(buffer);

          const result = {
            seqno,
            subject: mail.subject || "No Subject",
            from: mail.from?.text || "Unknown Sender",
            date: mail.date || new Date(),
            links: [],
            downloadedFiles: [],
            deliveryAddresses: [],
          };

          // Extract Billogram links from email content
          let allLinks = [];

          if (mail.text) {
            allLinks = allLinks.concat(extractLinksFromText(mail.text));
          }

          if (mail.html) {
            allLinks = allLinks.concat(extractLinksFromHtml(mail.html));
          }

          // Remove duplicates manually
          const uniqueLinks = [];
          for (const link of allLinks) {
            if (uniqueLinks.indexOf(link) === -1) {
              uniqueLinks.push(link);
            }
          }
          result.links = uniqueLinks;

          console.log(`Email #${seqno}: Found ${result.links.length} links`);
          result.links.forEach((link) => {
            if (isBillogramLink(link)) {
              console.log(`  - Billogram link: ${link}`);
            } else {
              console.log(`  - Other link: ${link}`);
            }
          });

          console.log(`Found ${result.links.length} links in email #${seqno}`);

          // Download files from links that are likely to be invoices/documents
          // Prioritize Billogram links first
          const billogramLinks = result.links.filter((link) => isBillogramLink(link));
          const otherInvoiceLinks = result.links.filter(
            (link) => !isBillogramLink(link) && isLikelyInvoiceLink(link),
          );

          const linksToDownload = [...billogramLinks, ...otherInvoiceLinks];

          for (const link of linksToDownload) {
            try {
              // Generate filename based on email subject and timestamp
              const timestamp = new Date().toISOString().replace(/[:.]/g, "-");
              const sanitizedSubject = (mail.subject || "unknown")
                .replace(/[^a-zA-Z0-9]/g, "_")
                .substring(0, 50);
              const linkType = isBillogramLink(link) ? "billogram" : "other";
              const filename = `rabot_${seqno}_${linkType}_${sanitizedSubject}_${timestamp}.pdf`;

              console.log(`Attempting to download ${linkType} link: ${link}`);
              const downloadResult = await downloadFileFromUrl(link, filename);
              if (downloadResult) {
                result.downloadedFiles.push(downloadResult.filePath);
                result.deliveryAddresses.push(...downloadResult.deliveryAddresses);
                console.log(`Successfully downloaded: ${downloadResult.filePath}`);
              } else {
                console.log(`Failed to download from: ${link}`);
              }
            } catch (error) {
              console.error(`Failed to download from link ${link}:`, error);
            }
          }

          // Log skipped links
          const skippedLinks = result.links.filter((link) => !linksToDownload.includes(link));
          skippedLinks.forEach((link) => {
            console.log(`Skipping non-invoice link: ${link}`);
          });

          resolve(result);
        } catch (err) {
          const errorMessage = err instanceof Error ? err.message : "Unknown error";
          console.error(`Error parsing email: ${errorMessage}`);
          reject(err);
        }
      });
    });

    msg.once("attributes", (attrs) => {
      console.log("Email attributes:", attrs);
    });
  });
}

// Main RABOT E-Mail Matcher Functions
async function searchAndDownloadInvoices() {
  if (!Imap || !simpleParser || !axios) {
    throw new Error('RABOT E-Mail Funktionen nicht verfügbar. Installiere: npm install imap mailparser axios');
  }

  try {
    validateEnvironmentVariables();
  } catch (error) {
    throw new Error(
      `Configuration error: ${error instanceof Error ? error.message : "Unknown error"}`,
    );
  }

  const downloadDir = getDownloadDir();
  ensureDirectoryExists(downloadDir);

  const emails = [];

  return new Promise((resolve, reject) => {
    console.log("Initializing IMAP connection with config:", {
      user: process.env.RABOT_MATCHER_EMAIL_USER,
      host: process.env.RABOT_MATCHER_EMAIL_HOST,
      port: process.env.RABOT_MATCHER_EMAIL_PORT,
      // Don't log password for security
    });

    const imap = new Imap(getImapConfig());

    imap.once("ready", async () => {
      console.log("IMAP connection ready");
      try {
        await openInbox(imap);

        const daysBack = parseInt(process.env.RABOT_MATCHER_DAYS_BACK);
        const sinceDate = new Date();
        sinceDate.setDate(sinceDate.getDate() - daysBack);
        const sinceDateString = sinceDate.toISOString().split("T")[0] || "1970-01-01";

        console.log(
          `Searching for emails from ${process.env.RABOT_MATCHER_SENDER_EMAIL} since ${sinceDateString}`,
        );

        const results = await searchRabotEmailsInImap(imap, sinceDateString);

        if (!results.length) {
          const message = `No emails found from ${process.env.RABOT_MATCHER_SENDER_EMAIL} in the last ${daysBack} days.`;
          console.log(message);
          imap.end();
          resolve({
            status: "success",
            message,
            emails: [],
            totalEmails: 0,
            totalLinks: 0,
            totalDownloads: 0,
          });
          return;
        }

        console.log(`Found ${results.length} emails to process`);

        const f = imap.fetch(results, { bodies: "", struct: true });
        let processedCount = 0;

        f.on("message", async (msg, seqno) => {
          try {
            const emailResult = await handleMessage(imap, msg, seqno);
            emails.push(emailResult);
            processedCount++;

            if (processedCount === results.length) {
              // All emails processed
              const totalLinks = emails.reduce((sum, email) => sum + email.links.length, 0);
              const totalDownloads = emails.reduce(
                (sum, email) => sum + email.downloadedFiles.length,
                0,
              );

              imap.end();
              resolve({
                status: "success",
                message: `Successfully processed ${emails.length} emails.`,
                emails,
                totalEmails: emails.length,
                totalLinks,
                totalDownloads,
              });
            }
          } catch (error) {
            console.error(`Error processing message ${seqno}:`, error);
            processedCount++;

            if (processedCount === results.length) {
              imap.end();
              reject(error);
            }
          }
        });

        f.once("error", (err) => {
          const errorMessage = err instanceof Error ? err.message : "Unknown error";
          console.log("Fetch error: " + errorMessage);
          reject(new Error(errorMessage));
        });
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : "Unknown error";
        reject(new Error(errorMessage));
      }
    });

    imap.once("error", (err) => {
      const errorMessage = err instanceof Error ? err.message : "Unknown error";
      console.error("IMAP connection error:", err);
      console.error("Error details:", {
        message: errorMessage,
        config: {
          user: process.env.RABOT_MATCHER_EMAIL_USER,
          host: process.env.RABOT_MATCHER_EMAIL_HOST,
          port: process.env.RABOT_MATCHER_EMAIL_PORT,
        },
      });
      reject(new Error("IMAP connection error: " + errorMessage));
    });

    imap.once("end", () => {
      console.log("Connection to mail server ended.");
    });

    imap.connect();
  });
}

async function searchEmailsOnly() {
  if (!Imap || !simpleParser) {
    throw new Error('RABOT E-Mail Funktionen nicht verfügbar. Installiere: npm install imap mailparser');
  }

  try {
    validateEnvironmentVariables();
  } catch (error) {
    throw new Error(
      `Configuration error: ${error instanceof Error ? error.message : "Unknown error"}`,
    );
  }

  const emails = [];

  return new Promise((resolve, reject) => {
    console.log("Initializing IMAP connection for search-only mode");

    const imap = new Imap(getImapConfig());

    imap.once("ready", async () => {
      console.log("IMAP connection ready for search-only");
      try {
        await openInbox(imap);

        const daysBack = parseInt(process.env.RABOT_MATCHER_DAYS_BACK);
        const sinceDate = new Date();
        sinceDate.setDate(sinceDate.getDate() - daysBack);
        const sinceDateString = sinceDate.toISOString().split("T")[0] || "1970-01-01";

        console.log(
          `Searching for emails from ${process.env.RABOT_MATCHER_SENDER_EMAIL} since ${sinceDateString}`,
        );

        const results = await searchRabotEmailsInImap(imap, sinceDateString);

        if (!results.length) {
          const message = `No emails found from ${process.env.RABOT_MATCHER_SENDER_EMAIL} in the last ${daysBack} days.`;
          console.log(message);
          imap.end();
          resolve({
            status: "success",
            message,
            emails: [],
            totalEmails: 0,
            totalLinks: 0,
            totalDownloads: 0,
          });
          return;
        }

        console.log(`Found ${results.length} emails to analyze`);

        const f = imap.fetch(results, { bodies: "", struct: true });
        let processedCount = 0;

        f.on("message", async (msg, seqno) => {
          try {
            const chunks = [];

            msg.on("body", (stream) => {
              stream.on("data", (chunk) => chunks.push(chunk));
              stream.on("end", async () => {
                try {
                  const buffer = Buffer.concat(chunks);
                  const mail = await simpleParser(buffer);

                  const result = {
                    seqno,
                    subject: mail.subject || "No Subject",
                    from: mail.from?.text || "Unknown Sender",
                    date: mail.date || new Date(),
                    links: [],
                    downloadedFiles: [], // No downloads in search-only mode
                    deliveryAddresses: [], // No PDF analysis in search-only mode
                  };

                  // Extract links from email content
                  let allLinks = [];

                  if (mail.text) {
                    allLinks = allLinks.concat(extractLinksFromText(mail.text));
                  }

                  if (mail.html) {
                    allLinks = allLinks.concat(extractLinksFromHtml(mail.html));
                  }

                  // Remove duplicates manually
                  const uniqueLinks = [];
                  for (const link of allLinks) {
                    if (uniqueLinks.indexOf(link) === -1) {
                      uniqueLinks.push(link);
                    }
                  }
                  result.links = uniqueLinks;

                  emails.push(result);
                  processedCount++;

                  if (processedCount === results.length) {
                    // All emails processed
                    const totalLinks = emails.reduce((sum, email) => sum + email.links.length, 0);

                    imap.end();
                    resolve({
                      status: "success",
                      message: `Successfully analyzed ${emails.length} emails.`,
                      emails,
                      totalEmails: emails.length,
                      totalLinks,
                      totalDownloads: 0,
                    });
                  }
                } catch (err) {
                  console.error(`Error parsing email ${seqno}:`, err);
                  processedCount++;

                  if (processedCount === results.length) {
                    imap.end();
                    reject(err);
                  }
                }
              });
            });
          } catch (error) {
            console.error(`Error processing message ${seqno}:`, error);
            processedCount++;

            if (processedCount === results.length) {
              imap.end();
              reject(error);
            }
          }
        });

        f.once("error", (err) => {
          const errorMessage = err instanceof Error ? err.message : "Unknown error";
          console.log("Fetch error: " + errorMessage);
          reject(new Error(errorMessage));
        });
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : "Unknown error";
        reject(new Error(errorMessage));
      }
    });

    imap.once("error", (err) => {
      const errorMessage = err instanceof Error ? err.message : "Unknown error";
      console.log("Connection error: " + errorMessage);
      reject(new Error("Connection error: " + errorMessage));
    });

    imap.once("end", () => {
      console.log("Connection to mail server ended.");
    });

    imap.connect();
  });
}

/**
 * Hauptfunktion mit Modi-Unterstützung
 */
async function main() {
  try {
    console.log('=== PDF ORGANIZER & RABOT MATCHER ===');
    console.log(`Modus: ${mode}`);
    if (envFile !== '.env') {
      console.log(`Env-Datei: ${envFile}`);
    }
    console.log(`Quellordner: ${RABOT_FOLDER}`);
    console.log(`Zielordner: ${OUTPUT_BASE_FOLDER}`);
    console.log('');

    switch (mode) {
      case 'download':
        console.log('🔄 Modus: RABOT E-Mails herunterladen und organisieren');
        console.log('Schritt 1: RABOT Rechnungen von E-Mails herunterladen...');

        try {
          const matcherResult = await searchAndDownloadInvoices();

          console.log('\n=== RABOT E-MAIL MATCHER ERGEBNIS ===');
          console.log(`Status: ${matcherResult.status}`);
          console.log(`Nachricht: ${matcherResult.message}`);
          console.log(`Verarbeitete E-Mails: ${matcherResult.totalEmails}`);
          console.log(`Gefundene Links: ${matcherResult.totalLinks}`);
          console.log(`Heruntergeladene Dateien: ${matcherResult.totalDownloads}`);

          if (matcherResult.totalDownloads > 0) {
            console.log('\nSchritt 2: Heruntergeladene PDFs organisieren...');
            const result = await processPDFs();
            generateReport(result);
            createCSVReport(result);
          } else {
            console.log('\nKeine neuen PDFs zum Organisieren gefunden.');
          }
        } catch (error) {
          console.error('Fehler beim Herunterladen der RABOT E-Mails:', error);
          throw error;
        }
        break;

      case 'search':
        console.log('🔍 Modus: RABOT E-Mails durchsuchen (Vorschau)');
        console.log('RABOT E-Mails durchsuchen (ohne Download)...');

        try {
          const matcherResult = await searchEmailsOnly();

          console.log('\n=== RABOT E-MAIL SUCHE ERGEBNIS ===');
          console.log(`Status: ${matcherResult.status}`);
          console.log(`Nachricht: ${matcherResult.message}`);
          console.log(`Gefundene E-Mails: ${matcherResult.totalEmails}`);
          console.log(`Gefundene Links: ${matcherResult.totalLinks}`);

          if (matcherResult.emails.length > 0) {
            console.log('\n=== GEFUNDENE E-MAILS ===');
            matcherResult.emails.forEach((email, index) => {
              console.log(`\n${index + 1}. E-Mail #${email.seqno}:`);
              console.log(`   Von: ${email.from}`);
              console.log(`   Betreff: ${email.subject}`);
              console.log(`   Datum: ${email.date.toLocaleDateString('de-DE')}`);
              console.log(`   Links: ${email.links.length}`);
              email.links.forEach((link, linkIndex) => {
                console.log(`     ${linkIndex + 1}. ${link}`);
              });
            });
          }
        } catch (error) {
          console.error('Fehler beim Durchsuchen der RABOT E-Mails:', error);
          throw error;
        }
        break;

      case 'organize':
      default:
        console.log('📁 Modus: Vorhandene PDFs organisieren');
        console.log('Diese Version liest PDF-Inhalte und sucht nach "Lieferanschrift:" Pattern.');

        const result = await processPDFs();
        generateReport(result);
        createCSVReport(result);
        break;
    }

    console.log('\n=== VERARBEITUNG ABGESCHLOSSEN ===');

  } catch (error) {
    console.error('Fehler beim Ausführen des Scripts:', error);

    if (error.message.includes('RABOT E-Mail Funktionen nicht verfügbar')) {
      console.log('\n💡 Installiere die benötigten Dependencies:');
      console.log('   npm install imap mailparser axios');
      console.log('\n   Oder verwende das TypeScript Script:');
      console.log(`   npx ts-node scripts/pdf-organizer.ts ${mode}`);
    } else if (error.message.includes('Missing required environment variables')) {
      console.log('\n💡 Tipp: Überprüfe deine .env Datei:');
      console.log(`   Verwende: ${envFile}`);
      console.log('   Oder erstelle eine neue: cp .env.rabot.example .env');
    }

    process.exit(1);
  }
}

// Script ausführen wenn direkt aufgerufen
if (require.main === module) {
  main();
}

module.exports = { processPDFs, extractTextFromPDF, extractInformation };
