#!/usr/bin/env node

/**
 * PDF ORGANIZER - Komplette Version
 * 
 * Dieses Script durchsucht alle PDF-Dateien im rabot_rechnungen Ordner,
 * extrahiert wichtige Informationen und sortiert die Dateien nach Lieferanschrift.
 * 
 * Extrahierte Informationen:
 * - Referenznummer/Vertragsnummer
 * - Lieferanschrift (aus "Lieferanschrift:" Pattern)
 * - Rechnungsdatum
 * - Beleg-Nr.
 * - Monatsauflistung-Tabelle (Monat, Verbrauch, Abrechnung, Abschlag, Verrechnung)
 * 
 * Verwendung:
 * node pdf-organizer-complete.cjs
 * 
 * Voraussetzungen:
 * - Node.js
 * - pdf-parse Bibliothek (npm install pdf-parse --legacy-peer-deps)
 */

const fs = require('fs');
const path = require('path');
const pdfParse = require('pdf-parse');

// Konfiguration
const RABOT_FOLDER = './rabot_rechnungen';
const OUTPUT_BASE_FOLDER = './rabot_rechnungen_sortiert';

/**
 * Extrahiert Text aus einer PDF-Datei mit pdf-parse
 */
async function extractTextFromPDF(filePath) {
  try {
    console.log(`Extrahiere Text aus: ${filePath}`);
    
    const dataBuffer = fs.readFileSync(filePath);
    const data = await pdfParse(dataBuffer);
    
    return data.text;
  } catch (error) {
    console.error(`Fehler beim Extrahieren von Text aus ${filePath}:`, error.message);
    return '';
  }
}

/**
 * Extrahiert spezifische Informationen aus dem PDF-Text
 */
function extractInformation(text, filename) {
  const result = {
    filename,
    fullText: text,
    monatsauflistung: []
  };

  // Referenznummer/Vertragsnummer suchen
  const referenzPatterns = [
    /Vertragsnummer\s+(\d+)/i,
    /Referenznummer\s+(\d+)/i,
    /Vertrags-Nr\.?\s+(\d+)/i,
    /Ref\.?\s*Nr\.?\s+(\d+)/i,
    /Rechnung\s*Nr\.?\s*(\d+)/i
  ];
  
  for (const pattern of referenzPatterns) {
    const match = text.match(pattern);
    if (match) {
      result.referenznummer = match[1];
      break;
    }
  }

  // Fallback: Extrahiere aus Dateiname wenn nicht im Text gefunden
  if (!result.referenznummer) {
    const filenameMatch = filename.match(/Rechnung_Nr__(\d+)/i);
    if (filenameMatch) {
      result.referenznummer = filenameMatch[1];
    }
  }

  // Lieferanschrift suchen - nach "Lieferanschrift:" Pattern
  const lieferanschriftPatterns = [
    /Lieferanschrift:\s*\n?\s*([^]*?)(?=\n\n|\nRechnungsdatum|\nDatum|\nBeleg|\nRechnung|$)/i,
    /Lieferanschrift\s*\n?\s*([^]*?)(?=\n\n|\nRechnungsdatum|\nDatum|\nBeleg|\nRechnung|$)/i,
    /Lieferadresse:\s*\n?\s*([^]*?)(?=\n\n|\nRechnungsdatum|\nDatum|\nBeleg|\nRechnung|$)/i,
    /Lieferadresse\s*\n?\s*([^]*?)(?=\n\n|\nRechnungsdatum|\nDatum|\nBeleg|\nRechnung|$)/i
  ];
  
  for (const pattern of lieferanschriftPatterns) {
    const match = text.match(pattern);
    if (match && match[1]) {
      // Bereinige die Adresse
      let address = match[1]
        .replace(/\s+/g, ' ')
        .trim()
        .substring(0, 200); // Begrenze Länge
      
      // Entferne führende/nachfolgende Sonderzeichen
      address = address.replace(/^[:\s\-]+|[:\s\-]+$/g, '');
      
      if (address.length > 10) { // Nur wenn sinnvolle Länge
        result.lieferanschrift = address;
        console.log(`  📍 GEFUNDEN IM PDF: "${address}"`);
        break;
      }
    }
  }

  // Fallback: Markiere als unbekannt wenn nicht im Text gefunden
  if (!result.lieferanschrift) {
    console.log(`  ⚠️  KEINE LIEFERANSCHRIFT IM PDF GEFUNDEN`);
    result.lieferanschrift = 'Unbekannte Lieferanschrift';
  }

  // Rechnungsdatum suchen
  const datumPatterns = [
    /Rechnungsdatum[:\s]*(\d{1,2}\.?\d{1,2}\.?\d{2,4})/i,
    /Datum[:\s]*(\d{1,2}\.?\d{1,2}\.?\d{2,4})/i,
    /(\d{1,2}\.\d{1,2}\.\d{2,4})/g
  ];
  
  for (const pattern of datumPatterns) {
    const match = text.match(pattern);
    if (match) {
      result.rechnungsdatum = match[1];
      break;
    }
  }

  // Fallback: Extrahiere Datum aus Dateiname
  if (!result.rechnungsdatum) {
    const dateMatch = filename.match(/(\d{4}-\d{2}-\d{2})/);
    if (dateMatch) {
      const isoDate = dateMatch[1];
      const [year, month, day] = isoDate.split('-');
      result.rechnungsdatum = `${day}.${month}.${year.slice(2)}`;
    }
  }

  // Beleg-Nr. suchen
  const belegPatterns = [
    /Beleg-Nr\.?\s*(\d+)/i,
    /Belegnummer\s*(\d+)/i,
    /Rechnung\s*Nr\.?\s*(\d+)/i,
    /Invoice\s*No\.?\s*(\d+)/i,
    /Rechnungsnummer\s*(\d+)/i
  ];
  
  for (const pattern of belegPatterns) {
    const match = text.match(pattern);
    if (match) {
      result.belegNr = match[1];
      break;
    }
  }

  // Fallback: Verwende Referenznummer als Beleg-Nr.
  if (!result.belegNr && result.referenznummer) {
    result.belegNr = result.referenznummer;
  }

  // Monatsauflistung suchen
  const monatsauflistungPattern = /Monatsauflistung([^]*?)(?=\n\n|\nRechnungsdatum|\nDatum|\nBeleg|\nRechnung|\nSumme|$)/i;
  const monatsauflistungMatch = text.match(monatsauflistungPattern);
  
  if (monatsauflistungMatch && monatsauflistungMatch[1]) {
    const monatsauflistungText = monatsauflistungMatch[1];
    console.log(`  📊 MONATSAUFLISTUNG GEFUNDEN: "${monatsauflistungText.substring(0, 150)}..."`);
    
    // Suche nach Tabellenzeilen mit Monat, Verbrauch, Abrechnung, Abschlag, Verrechnung
    const tabellenZeilenPattern = /([A-Za-zäöüÄÖÜ]+\s+\d{4})\s+([0-9.,]+)\s*kWh\s+([0-9.,]+)\s*€\s+([0-9.,]+)\s*€\s+([0-9.,\-]+)\s*€/g;
    
    let match;
    while ((match = tabellenZeilenPattern.exec(monatsauflistungText)) !== null) {
      const monatsEintrag = {
        monat: match[1].trim(),
        verbrauch: match[2] + ' kWh',
        abrechnung: match[3] + ' €',
        abschlag: match[4] + ' €',
        verrechnung: match[5] + ' €'
      };
      
      result.monatsauflistung.push(monatsEintrag);
      console.log(`    📅 ${monatsEintrag.monat}: Verbrauch ${monatsEintrag.verbrauch}, Abrechnung ${monatsEintrag.abrechnung}, Abschlag ${monatsEintrag.abschlag}, Verrechnung ${monatsEintrag.verrechnung}`);
    }
    
    // Fallback: Flexibleres Pattern für verschiedene Formate
    if (result.monatsauflistung.length === 0) {
      const flexiblesPattern = /([A-Za-zäöüÄÖÜ]+\s+\d{4})[^]*?(\d+[.,]\d+)[^]*?kWh[^]*?(\d+[.,]\d+)[^]*?€[^]*?(\d+[.,]\d+)[^]*?€[^]*?([0-9.,\-]+)[^]*?€/g;
      
      while ((match = flexiblesPattern.exec(monatsauflistungText)) !== null) {
        const monatsEintrag = {
          monat: match[1].trim(),
          verbrauch: match[2] + ' kWh',
          abrechnung: match[3] + ' €',
          abschlag: match[4] + ' €',
          verrechnung: match[5] + ' €'
        };
        
        result.monatsauflistung.push(monatsEintrag);
        console.log(`    📅 ${monatsEintrag.monat}: Verbrauch ${monatsEintrag.verbrauch}, Abrechnung ${monatsEintrag.abrechnung}, Abschlag ${monatsEintrag.abschlag}, Verrechnung ${monatsEintrag.verrechnung}`);
      }
    }
    
    if (result.monatsauflistung.length === 0) {
      console.log(`    ⚠️  Keine Tabellenzeilen in Monatsauflistung gefunden`);
    }
  }

  return result;
}

/**
 * Bereinigt einen String für die Verwendung als Ordnername
 */
function sanitizeDirectoryName(name) {
  return name
    .replace(/[<>:"/\\|?*]/g, '_')
    .replace(/\s+/g, '_')
    .replace(/_+/g, '_')
    .replace(/^_+|_+$/g, '')
    .substring(0, 150); // Längere Namen erlauben
}

/**
 * Erstellt einen Ordnernamen basierend auf der Lieferanschrift
 */
function createDirectoryName(lieferanschrift) {
  if (!lieferanschrift) {
    return 'Unbekannte_Lieferanschrift';
  }
  
  // Verwende die komplette Lieferanschrift als Ordnername
  return sanitizeDirectoryName(lieferanschrift);
}

/**
 * Kopiert eine Datei in den Zielordner
 */
async function copyFileToDirectory(sourceFile, targetDir, filename) {
  try {
    // Stelle sicher, dass der Zielordner existiert
    if (!fs.existsSync(targetDir)) {
      fs.mkdirSync(targetDir, { recursive: true });
      console.log(`Ordner erstellt: ${targetDir}`);
    }
    
    const targetFile = path.join(targetDir, filename);
    
    // Kopiere die Datei
    fs.copyFileSync(sourceFile, targetFile);
    console.log(`Datei kopiert: ${filename} -> ${targetDir}`);
  } catch (error) {
    console.error(`Fehler beim Kopieren von ${filename}:`, error);
    throw error;
  }
}
