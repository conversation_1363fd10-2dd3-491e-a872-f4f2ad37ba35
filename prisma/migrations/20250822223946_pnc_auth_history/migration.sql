-- CreateTable
CREATE TABLE `LongshipAuthorization` (
    `id` VARCHAR(191) NOT NULL,
    `longshipAuthId` VARCHAR(191) NOT NULL,
    `emaid` VARCHAR(255) NOT NULL,
    `chargePointId` VARCHAR(255) NOT NULL,
    `evseId` VARCHAR(255) NOT NULL,
    `status` ENUM('Created', 'Pending', 'Approved', 'Rejected', 'Timeout', 'NoApprovals') NOT NULL DEFAULT 'Created',
    `idTag` VARCHAR(255) NOT NULL,
    `idTagType` ENUM('RFID', 'EMAID', 'Central', 'KeyCode', 'Local', 'MacAddress', 'NoAuthorization') NOT NULL DEFAULT 'EMAID',
    `trigger` ENUM('StartTransaction', 'TransactionEventStarted', 'TransactionEventUpdated', 'TransactionEventEnded', 'PlugAndCharge', 'Authorize', 'Manual') NOT NULL DEFAULT 'PlugAndCharge',
    `scenario` ENUM('Create', 'Authorization', 'Billing') NULL,
    `longshipCreated` DATETIME(3) NULL,
    `longshipModified` DATETIME(3) NULL,
    `longshipDeleted` DATETIME(3) NULL,
    `longshipValidTo` DATETIME(3) NULL,
    `transactionId` VARCHAR(255) NULL,
    `statusHistory` JSON NOT NULL,
    `pollingStarted` DATETIME(3) NULL,
    `pollingCompleted` DATETIME(3) NULL,
    `pollingAttempts` INTEGER NOT NULL DEFAULT 0,
    `plugAndChargeEventId` VARCHAR(191) NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `LongshipAuthorization_longshipAuthId_key`(`longshipAuthId`),
    INDEX `LongshipAuthorization_longshipAuthId_idx`(`longshipAuthId`),
    INDEX `LongshipAuthorization_emaid_idx`(`emaid`),
    INDEX `LongshipAuthorization_chargePointId_idx`(`chargePointId`),
    INDEX `LongshipAuthorization_evseId_idx`(`evseId`),
    INDEX `LongshipAuthorization_status_idx`(`status`),
    INDEX `LongshipAuthorization_createdAt_idx`(`createdAt`),
    INDEX `LongshipAuthorization_plugAndChargeEventId_idx`(`plugAndChargeEventId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `LongshipAuthorization` ADD CONSTRAINT `LongshipAuthorization_plugAndChargeEventId_fkey` FOREIGN KEY (`plugAndChargeEventId`) REFERENCES `PlugAndChargeEvent`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;
