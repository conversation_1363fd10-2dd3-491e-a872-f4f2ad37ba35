-- CreateTable
CREATE TABLE `ThgOffer` (
    `id` VARCHAR(191) NOT NULL,
    `buyerId` VARCHAR(191) NOT NULL,
    `sellerId` VARCHAR(191) NULL,
    `type` ENUM('PURCHASE', 'SALE') NOT NULL DEFAULT 'PURCHASE',
    `status` ENUM('DRAFT', 'ACTIVE', 'EXPIRED', 'ACCEPTED', 'REJECTED', 'CANCELLED') NOT NULL DEFAULT 'ACTIVE',
    `pricePerKwh` DECIMAL(10, 4) NOT NULL,
    `quantity` INTEGER NOT NULL,
    `totalAmount` DECIMAL(12, 2) NULL,
    `validFrom` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `validUntil` DATETIME(3) NOT NULL,
    `title` VARCHAR(191) NULL,
    `description` TEXT NULL,
    `notes` TEXT NULL,
    `paymentTerms` VARCHAR(191) NULL,
    `deliveryTerms` VARCHAR(191) NULL,
    `contractTerms` TEXT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,
    `acceptedAt` DATETIME(3) NULL,
    `rejectedAt` DATETIME(3) NULL,
    `cancelledAt` DATETIME(3) NULL,

    INDEX `ThgOffer_buyerId_idx`(`buyerId`),
    INDEX `ThgOffer_sellerId_idx`(`sellerId`),
    INDEX `ThgOffer_status_idx`(`status`),
    INDEX `ThgOffer_validUntil_idx`(`validUntil`),
    INDEX `ThgOffer_createdAt_idx`(`createdAt`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `ThgQuote` (
    `id` VARCHAR(191) NOT NULL,
    `offerId` VARCHAR(191) NOT NULL,
    `quoterId` VARCHAR(191) NOT NULL,
    `pricePerKwh` DECIMAL(10, 4) NOT NULL,
    `quantity` INTEGER NOT NULL,
    `totalAmount` DECIMAL(12, 2) NULL,
    `status` ENUM('DRAFT', 'ACTIVE', 'EXPIRED', 'ACCEPTED', 'REJECTED', 'CANCELLED') NOT NULL DEFAULT 'ACTIVE',
    `validUntil` DATETIME(3) NOT NULL,
    `message` TEXT NULL,
    `notes` TEXT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,
    `acceptedAt` DATETIME(3) NULL,
    `rejectedAt` DATETIME(3) NULL,

    INDEX `ThgQuote_offerId_idx`(`offerId`),
    INDEX `ThgQuote_quoterId_idx`(`quoterId`),
    INDEX `ThgQuote_status_idx`(`status`),
    INDEX `ThgQuote_createdAt_idx`(`createdAt`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `ThgContract` (
    `id` VARCHAR(191) NOT NULL,
    `offerId` VARCHAR(191) NOT NULL,
    `buyerId` VARCHAR(191) NOT NULL,
    `sellerId` VARCHAR(191) NOT NULL,
    `contractNumber` VARCHAR(191) NOT NULL,
    `pricePerKwh` DECIMAL(10, 4) NOT NULL,
    `quantity` INTEGER NOT NULL,
    `totalAmount` DECIMAL(12, 2) NOT NULL,
    `paymentTerms` VARCHAR(191) NULL,
    `deliveryTerms` VARCHAR(191) NULL,
    `contractTerms` TEXT NULL,
    `status` VARCHAR(191) NOT NULL DEFAULT 'DRAFT',
    `signedAt` DATETIME(3) NULL,
    `startDate` DATETIME(3) NULL,
    `endDate` DATETIME(3) NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `ThgContract_contractNumber_key`(`contractNumber`),
    INDEX `ThgContract_buyerId_idx`(`buyerId`),
    INDEX `ThgContract_sellerId_idx`(`sellerId`),
    INDEX `ThgContract_contractNumber_idx`(`contractNumber`),
    INDEX `ThgContract_status_idx`(`status`),
    INDEX `ThgContract_createdAt_idx`(`createdAt`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `ThgDelivery` (
    `id` VARCHAR(191) NOT NULL,
    `contractId` VARCHAR(191) NOT NULL,
    `deliveryNumber` VARCHAR(191) NOT NULL,
    `quantity` INTEGER NOT NULL,
    `pricePerKwh` DECIMAL(10, 4) NOT NULL,
    `totalAmount` DECIMAL(12, 2) NOT NULL,
    `periodStart` DATETIME(3) NOT NULL,
    `periodEnd` DATETIME(3) NOT NULL,
    `deliveredAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `status` VARCHAR(191) NOT NULL DEFAULT 'PENDING',
    `notes` TEXT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `ThgDelivery_deliveryNumber_key`(`deliveryNumber`),
    INDEX `ThgDelivery_contractId_idx`(`contractId`),
    INDEX `ThgDelivery_deliveryNumber_idx`(`deliveryNumber`),
    INDEX `ThgDelivery_status_idx`(`status`),
    INDEX `ThgDelivery_periodStart_idx`(`periodStart`),
    INDEX `ThgDelivery_createdAt_idx`(`createdAt`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `ThgMarketData` (
    `id` VARCHAR(191) NOT NULL,
    `date` DATE NOT NULL,
    `averagePrice` DECIMAL(10, 4) NULL,
    `minPrice` DECIMAL(10, 4) NULL,
    `maxPrice` DECIMAL(10, 4) NULL,
    `totalVolume` INTEGER NULL,
    `totalOffers` INTEGER NULL,
    `activeOffers` INTEGER NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    INDEX `ThgMarketData_date_idx`(`date`),
    UNIQUE INDEX `ThgMarketData_date_key`(`date`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `ThgOffer` ADD CONSTRAINT `ThgOffer_buyerId_fkey` FOREIGN KEY (`buyerId`) REFERENCES `User`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `ThgOffer` ADD CONSTRAINT `ThgOffer_sellerId_fkey` FOREIGN KEY (`sellerId`) REFERENCES `User`(`id`) ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `ThgQuote` ADD CONSTRAINT `ThgQuote_offerId_fkey` FOREIGN KEY (`offerId`) REFERENCES `ThgOffer`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `ThgQuote` ADD CONSTRAINT `ThgQuote_quoterId_fkey` FOREIGN KEY (`quoterId`) REFERENCES `User`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `ThgContract` ADD CONSTRAINT `ThgContract_offerId_fkey` FOREIGN KEY (`offerId`) REFERENCES `ThgOffer`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `ThgContract` ADD CONSTRAINT `ThgContract_buyerId_fkey` FOREIGN KEY (`buyerId`) REFERENCES `User`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `ThgContract` ADD CONSTRAINT `ThgContract_sellerId_fkey` FOREIGN KEY (`sellerId`) REFERENCES `User`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `ThgDelivery` ADD CONSTRAINT `ThgDelivery_contractId_fkey` FOREIGN KEY (`contractId`) REFERENCES `ThgContract`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
