/*
  Warnings:

  - You are about to drop the column `BadgeImageUrl` on the `AchievementEvent` table. All the data in the column will be lost.
  - You are about to drop the column `Stats` on the `AchievementEvent` table. All the data in the column will be lost.

*/
-- AlterTable
ALTER TABLE `AchievementEvent` DROP COLUMN `BadgeImageUrl`,
    DROP COLUMN `Stats`,
    ADD COLUMN `badgeImageUrl` TEXT NULL,
    ADD COLUMN `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    ADD COLUMN `rewardDescription` VARCHAR(191) NULL,
    ADD COLUMN `rewardType` ENUM('NONE', 'DISCOUNT_PERCENT', 'BADGE', 'TEXT') NOT NULL DEFAULT 'NONE',
    ADD COLUMN `rewardValue` INTEGER NULL,
    ADD COLUMN `updatedAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3);

-- CreateTable
CREATE TABLE `AchievementEventMetric` (
    `id` VARCHAR(191) NOT NULL,
    `eventId` VARCHAR(191) NOT NULL,
    `order` INTEGER NOT NULL DEFAULT 0,
    `label` VARCHAR(191) NOT NULL,
    `param` VARCHAR(191) NOT NULL,
    `target` DOUBLE NOT NULL,
    `start` DOUBLE NULL,
    `color` VARCHAR(191) NULL,
    `window` ENUM('EVENT', 'MONTH', 'WEEK', 'ROLLING30D') NOT NULL DEFAULT 'EVENT',

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `AchievementMetricProgress` (
    `id` VARCHAR(191) NOT NULL,
    `metricId` VARCHAR(191) NOT NULL,
    `profileId` VARCHAR(191) NOT NULL,
    `value` DOUBLE NOT NULL DEFAULT 0,
    `updatedAt` DATETIME(3) NOT NULL,

    INDEX `AchievementMetricProgress_metricId_idx`(`metricId`),
    INDEX `AchievementMetricProgress_profileId_idx`(`profileId`),
    UNIQUE INDEX `AchievementMetricProgress_metricId_profileId_key`(`metricId`, `profileId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `AchievementEventMetric` ADD CONSTRAINT `AchievementEventMetric_eventId_fkey` FOREIGN KEY (`eventId`) REFERENCES `AchievementEvent`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `AchievementMetricProgress` ADD CONSTRAINT `AchievementMetricProgress_metricId_fkey` FOREIGN KEY (`metricId`) REFERENCES `AchievementEventMetric`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `AchievementMetricProgress` ADD CONSTRAINT `AchievementMetricProgress_profileId_fkey` FOREIGN KEY (`profileId`) REFERENCES `AchievementProfile`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;
