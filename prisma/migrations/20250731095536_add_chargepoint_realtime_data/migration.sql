-- CreateTable
CREATE TABLE `OCPISession` (
    `id` VARCHAR(191) NOT NULL,
    `auth_method` VARCHAR(191) NOT NULL,
    `authorization_reference` VARCHAR(191) NOT NULL,
    `connector_id` VARCHAR(191) NOT NULL,
    `country_code` VARCHAR(191) NOT NULL,
    `currency` VARCHAR(191) NOT NULL,
    `end_date_time` VARCHAR(191) NULL,
    `evse_uid` VARCHAR(191) NOT NULL,
    `kwh` DOUBLE NOT NULL,
    `last_updated` VARCHAR(191) NOT NULL,
    `location_id` VARCHAR(191) NOT NULL,
    `party_id` VARCHAR(191) NOT NULL,
    `start_date_time` VARCHAR(191) NOT NULL,
    `status` VARCHAR(191) NOT NULL,

    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `ChargePointRealtimeData` (
    `chargePointId` VARCHAR(191) NOT NULL,
    `connectorNumber` INTEGER NOT NULL,
    `status` VARCHAR(191) NOT NULL,
    `kW` DOUBLE NOT NULL,
    `kWh` DOUBLE NOT NULL,
    `currentSessionStart` DATETIME(3) NOT NULL,
    `lastSessionEnd` DATETIME(3) NOT NULL,
    `lastUpdate` DATETIME(3) NOT NULL,
    `emp` VARCHAR(191) NOT NULL,
    `authenticationId` VARCHAR(191) NOT NULL,
    `locationId` VARCHAR(191) NULL,
    `evseId` VARCHAR(191) NULL,

    PRIMARY KEY (`chargePointId`, `connectorNumber`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
