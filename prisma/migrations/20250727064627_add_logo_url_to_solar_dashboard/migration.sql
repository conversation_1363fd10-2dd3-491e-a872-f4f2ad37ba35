-- CreateTable
CREATE TABLE `SolarDashboard` (
    `id` VARCHAR(191) NOT NULL,
    `name` VARCHAR(191) NOT NULL,
    `description` TEXT NULL,
    `ouId` VARCHAR(191) NOT NULL,
    `locationId` VARCHAR(191) NOT NULL,
    `isActive` BOOLEAN NOT NULL DEFAULT false,
    `isPublic` BOOLEAN NOT NULL DEFAULT false,
    `publicToken` VARCHAR(191) NULL,
    `solarEdgeApiKey` VARCHAR(191) NULL,
    `solarEdgeSiteId` VARCHAR(191) NULL,
    `logoUrl` VARCHAR(191) NULL,
    `settings` JSON NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `SolarDashboard_publicToken_key`(`publicToken`),
    UNIQUE INDEX `SolarDashboard_ouId_locationId_key`(`ouId`, `locationId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `SolarEdgeApiCall` (
    `id` VARCHAR(191) NOT NULL,
    `dashboardId` VARCHAR(191) NOT NULL,
    `endpoint` VARCHAR(191) NOT NULL,
    `success` BOOLEAN NOT NULL,
    `responseTime` INTEGER NULL,
    `errorMessage` TEXT NULL,
    `requestedAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),

    INDEX `SolarEdgeApiCall_dashboardId_requestedAt_idx`(`dashboardId`, `requestedAt`),
    INDEX `SolarEdgeApiCall_dashboardId_endpoint_idx`(`dashboardId`, `endpoint`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `SolarEdgeDataCache` (
    `id` VARCHAR(191) NOT NULL,
    `dashboardId` VARCHAR(191) NOT NULL,
    `dataType` VARCHAR(191) NOT NULL,
    `value` DOUBLE NOT NULL,
    `unit` VARCHAR(191) NOT NULL,
    `metadata` JSON NULL,
    `cachedAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `expiresAt` DATETIME(3) NOT NULL,

    INDEX `SolarEdgeDataCache_dashboardId_expiresAt_idx`(`dashboardId`, `expiresAt`),
    UNIQUE INDEX `SolarEdgeDataCache_dashboardId_dataType_key`(`dashboardId`, `dataType`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `SolarEdgeRateLimit` (
    `id` VARCHAR(191) NOT NULL,
    `dashboardId` VARCHAR(191) NOT NULL,
    `date` DATE NOT NULL,
    `requestCount` INTEGER NOT NULL DEFAULT 0,
    `lastRequestAt` DATETIME(3) NULL,

    INDEX `SolarEdgeRateLimit_dashboardId_date_idx`(`dashboardId`, `date`),
    UNIQUE INDEX `SolarEdgeRateLimit_dashboardId_date_key`(`dashboardId`, `date`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- CreateTable
CREATE TABLE `solar_edge_historical_data` (
    `id` VARCHAR(191) NOT NULL,
    `dashboardId` VARCHAR(191) NOT NULL,
    `date` DATE NOT NULL,
    `energyProduced` DOUBLE NULL,
    `energyConsumed` DOUBLE NULL,
    `gridFeedIn` DOUBLE NULL,
    `gridConsumption` DOUBLE NULL,
    `chargingConsumption` DOUBLE NULL,
    `dataSource` VARCHAR(191) NOT NULL DEFAULT 'api',
    `dataQuality` VARCHAR(191) NOT NULL DEFAULT 'good',
    `apiCallsUsed` INTEGER NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    INDEX `solar_edge_historical_data_dashboardId_date_idx`(`dashboardId`, `date`),
    INDEX `solar_edge_historical_data_date_idx`(`date`),
    UNIQUE INDEX `solar_edge_historical_data_dashboardId_date_key`(`dashboardId`, `date`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `SolarDashboard` ADD CONSTRAINT `SolarDashboard_ouId_fkey` FOREIGN KEY (`ouId`) REFERENCES `Ou`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `SolarDashboard` ADD CONSTRAINT `SolarDashboard_locationId_fkey` FOREIGN KEY (`locationId`) REFERENCES `Location`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `SolarEdgeApiCall` ADD CONSTRAINT `SolarEdgeApiCall_dashboardId_fkey` FOREIGN KEY (`dashboardId`) REFERENCES `SolarDashboard`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `SolarEdgeDataCache` ADD CONSTRAINT `SolarEdgeDataCache_dashboardId_fkey` FOREIGN KEY (`dashboardId`) REFERENCES `SolarDashboard`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `SolarEdgeRateLimit` ADD CONSTRAINT `SolarEdgeRateLimit_dashboardId_fkey` FOREIGN KEY (`dashboardId`) REFERENCES `SolarDashboard`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `solar_edge_historical_data` ADD CONSTRAINT `solar_edge_historical_data_dashboardId_fkey` FOREIGN KEY (`dashboardId`) REFERENCES `SolarDashboard`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;
