/*
  Warnings:

  - The primary key for the `OccpMonitorKey` table will be changed. If it partially fails, the table could be left without primary key constraint.
  - The required column `id` was added to the `OccpMonitorKey` table with a prisma-level default value. This is not possible if the table is not empty. Please add this column as optional, then populate it before making it required.

*/
-- AlterTable
ALTER TABLE `OccpMonitorKey` DROP PRIMARY KEY,
    ADD COLUMN `id` VARCHAR(191) NOT NULL,
    ADD PRIMARY KEY (`id`);

-- CreateTable
CREATE TABLE `OccpMonitorKeyOnChargePoint` (
    `occpMonitorKeyId` VARCHAR(191) NOT NULL,
    `chargePointId` VARCHAR(191) NOT NULL,

    INDEX `OccpMonitorKeyOnChargePoint_chargePointId_fkey`(`chargePointId`),
    PRIMARY KEY (`occpMonitorKeyId`, `chargePointId`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `OccpMonitorKeyOnChargePoint` ADD CONSTRAINT `OccpMonitorKeyOnChargePoint_occpMonitorKeyId_fkey` FOREIGN KEY (`occpMonitorKeyId`) REFERENCES `OccpMonitorKey`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `OccpMonitorKeyOnChargePoint` ADD CONSTRAINT `OccpMonitorKeyOnChargePoint_chargePointId_fkey` FOREIGN KEY (`chargePointId`) REFERENCES `ChargePoint`(`id`) ON DELETE RESTRICT ON UPDATE CASCADE;
