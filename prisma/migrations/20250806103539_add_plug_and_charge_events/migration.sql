-- CreateTable
CREATE TABLE `PlugAndChargeEvent` (
    `id` VARCHAR(191) NOT NULL,
    `raw` JSON NOT NULL,
    `evseid` VARCHAR(191) NOT NULL,
    `locationId` VARCHAR(191) NULL,
    `plugInEventTimestamp` DATETIME(3) NOT NULL,
    `latitude` DOUBLE NULL,
    `longitude` DOUBLE NULL,
    `type` ENUM('INCOMING', 'OUTGOING') NOT NULL,
    `emaid` VARCHAR(191) NOT NULL,
    `pcid` VARCHAR(191) NOT NULL,
    `pncSessionId` VARCHAR(191) NOT NULL,
    `matchingConfidence` DOUBLE NOT NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    INDEX `PlugAndChargeEvent_evseid_idx`(`evseid`),
    INDEX `PlugAndChargeEvent_type_idx`(`type`),
    INDEX `PlugAndChargeEvent_plugInEventTimestamp_idx`(`plugInEventTimestamp`),
    INDEX `PlugAndChargeEvent_createdAt_idx`(`createdAt`),
    INDEX `PlugAndChargeEvent_emaid_idx`(`emaid`),
    INDEX `PlugAndChargeEvent_pncSessionId_idx`(`pncSessionId`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
