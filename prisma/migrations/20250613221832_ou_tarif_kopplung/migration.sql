/*
  Warnings:

  - You are about to drop the column `operatorId` on the `Tarif` table. All the data in the column will be lost.

*/
-- AlterTable
ALTER TABLE `Tarif` DROP COLUMN `operatorId`;

-- CreateTable
CREATE TABLE `_TarifValidOus` (
    `A` VARCHAR(191) NOT NULL,
    `B` VARCHAR(191) NOT NULL,

    UNIQUE INDEX `_TarifValidOus_AB_unique`(`A`, `B`),
    INDEX `_TarifValidOus_B_index`(`B`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `_TarifValidOus` ADD CONSTRAINT `_TarifValidOus_A_fkey` FOREIGN KEY (`A`) REFERENCES `Ou`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE `_TarifValidOus` ADD CONSTRAINT `_TarifValidOus_B_fkey` FOREIGN KEY (`B`) REFERENCES `Tarif`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;
