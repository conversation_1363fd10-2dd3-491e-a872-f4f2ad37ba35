-- AlterTable
ALTER TABLE `CompanyTarif` MODIFY `oneTimeFeePayer` ENUM('USER', 'ADMI<PERSON>', 'CARD_HOLDER', 'CARD_MANAGER', 'CPO', 'THG_BUYER', 'PNC_REMOTE') NOT NULL DEFAULT 'CARD_HOLDER';

-- AlterTable
ALTER TABLE `PlugAndChargeEvent` ADD COLUMN `incomingRequestTimestamp` DATETIME(3) NULL,
    ADD COLUMN `outgoingRequestTimestamp` DATETIME(3) NULL,
    ADD COLUMN `outgoingResponseTimestamp` DATETIME(3) NULL,
    ADD COLUMN `responseUid` VARCHAR(191) NULL;

-- AlterTable
ALTER TABLE `User` MODIFY `role` ENUM('USER', 'ADMIN', 'CARD_HOLDER', 'CARD_MANAGER', 'CPO', 'THG_BUYER', 'PNC_REMOTE') NOT NULL DEFAULT 'USER';
