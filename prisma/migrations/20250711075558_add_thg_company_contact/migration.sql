-- CreateTable
CREATE TABLE `ThgCompanyContact` (
    `id` VARCHAR(191) NOT NULL,
    `userId` VARCHAR(191) NOT NULL,
    `companyName` VARCHAR(255) NOT NULL,
    `legalForm` VARCHAR(100) NULL,
    `registrationNumber` VARCHAR(100) NULL,
    `taxNumber` VARCHAR(100) NULL,
    `vatId` VARCHAR(100) NULL,
    `street` VARCHAR(255) NULL,
    `streetNumber` VARCHAR(20) NULL,
    `postalCode` VARCHAR(20) NULL,
    `city` VARCHAR(255) NULL,
    `country` VARCHAR(100) NULL DEFAULT 'Deutschland',
    `contact<PERSON>erson` VARCHAR(255) NULL,
    `phone` VARCHAR(50) NULL,
    `email` VARCHAR(255) NULL,
    `website` VARCHAR(255) NULL,
    `bankName` VARCHAR(255) NULL,
    `iban` VARCHAR(50) NULL,
    `bic` VARCHAR(20) NULL,
    `thgCertificateNumber` VARCHAR(100) NULL,
    `thgCertificateValid` DATE NULL,
    `createdAt` DATETIME(3) NOT NULL DEFAULT CURRENT_TIMESTAMP(3),
    `updatedAt` DATETIME(3) NOT NULL,

    UNIQUE INDEX `ThgCompanyContact_userId_key`(`userId`),
    INDEX `ThgCompanyContact_userId_idx`(`userId`),
    INDEX `ThgCompanyContact_companyName_idx`(`companyName`),
    PRIMARY KEY (`id`)
) DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- AddForeignKey
ALTER TABLE `ThgCompanyContact` ADD CONSTRAINT `ThgCompanyContact_userId_fkey` FOREIGN KEY (`userId`) REFERENCES `User`(`id`) ON DELETE CASCADE ON UPDATE CASCADE;
