# RABOT Matcher & PDF Organizer - Verwendungsanleitung

Das erweiterte PDF Organizer Script kann jetzt RABOT Rechnungen automatisch aus E-Mails herunterladen und organisieren.

## 🚀 Schnellstart

### 1. Umgebungsvariablen konfigurieren

Kopiere die Vorlage und fülle die Werte aus:
```bash
cp .env.rabot.example .env
```

Bearbeite die `.env` Datei:
```env
RABOT_MATCHER_EMAIL_USER=<EMAIL>
RABOT_MATCHER_EMAIL_PASSWORD=dein-email-passwort
RABOT_MATCHER_EMAIL_HOST=imap.eulektro.de
RABOT_MATCHER_EMAIL_PORT=993
RABOT_MATCHER_DAYS_BACK=30
RABOT_MATCHER_SENDER_EMAIL=RABOT Charge GmbH <<EMAIL>>
RABOT_MATCHER_SUBJECT_FILTER=
RABOT_MATCHER_DOWNLOAD_FOLDER=./rabot_rechnungen
```

### 2. Script ausführen

```bash
# RABOT E-Mails herunterladen und PDFs organisieren
./scripts/run-pdf-organizer.sh download

# Nur vorhandene PDFs organisieren
./scripts/run-pdf-organizer.sh organize

# Nur E-Mails durchsuchen (Vorschau)
./scripts/run-pdf-organizer.sh search

# Hilfe anzeigen
./scripts/run-pdf-organizer.sh help
```

## 📋 Verfügbare Modi

### `download` - Vollständiger Workflow
1. **E-Mail Verbindung**: Verbindet sich mit dem IMAP Server
2. **E-Mail Suche**: Sucht nach RABOT/Billogram E-Mails der letzten X Tage
3. **Link Extraktion**: Extrahiert Billogram Download-Links aus E-Mails
4. **PDF Download**: Lädt alle Rechnungs-PDFs herunter
5. **PDF Organisation**: Sortiert PDFs automatisch nach Lieferanschrift

```bash
./scripts/run-pdf-organizer.sh download
```

### `organize` - Nur PDF Organisation
Organisiert bereits vorhandene PDFs im `rabot_rechnungen` Ordner:
- Extrahiert Lieferanschrift, Referenznummer, Datum, etc.
- Erstellt Ordner basierend auf Lieferanschrift
- Kopiert PDFs in entsprechende Ordner

```bash
./scripts/run-pdf-organizer.sh organize
```

### `search` - E-Mail Vorschau
Durchsucht E-Mails ohne Download:
- Zeigt gefundene E-Mails an
- Listet verfügbare Download-Links auf
- Lädt keine Dateien herunter

```bash
./scripts/run-pdf-organizer.sh search
```

## 📁 Ordnerstruktur

```
./rabot_rechnungen/                 # Download-Ordner
├── rabot_3351_billogram_Rechnung_Nr_41885728.pdf
├── rabot_3352_billogram_Rechnung_Nr_41885729.pdf
└── ...

./rabot_rechnungen_sortiert/        # Organisierte PDFs
├── Eulektro_GmbH/
│   ├── rabot_3351_billogram_Rechnung_Nr_41885728.pdf
│   └── rabot_3355_billogram_Rechnung_Nr_41885732.pdf
├── Andere_Firma_GmbH/
│   └── rabot_3352_billogram_Rechnung_Nr_41885729.pdf
└── Unbekannte_Lieferanschrift/
    └── pdfs_ohne_erkennbare_adresse.pdf
```

## 🔧 Alternative Verwendung

### Mit npm scripts:
```bash
npm run pdf-organizer download
npm run pdf-organizer organize
npm run pdf-organizer search
```

### Direkt mit TypeScript:
```bash
npx ts-node scripts/pdf-organizer.ts download
npx ts-node scripts/pdf-organizer.ts organize
npx ts-node scripts/pdf-organizer.ts search
```

## ⚙️ Konfiguration

### E-Mail Einstellungen
- **IMAP Server**: Meist `imap.domain.de` auf Port 993 (SSL)
- **Authentifizierung**: Normale E-Mail Zugangsdaten
- **Gmail**: App-spezifische Passwörter verwenden

### Sucheinstellungen
- **Tage rückwirkend**: Wie weit in der Vergangenheit gesucht werden soll
- **Absender Filter**: Exakter FROM-Header aus den E-Mails
- **Download Ordner**: Wo PDFs gespeichert werden sollen

## 🐛 Troubleshooting

### E-Mail Verbindungsfehler
```bash
# Teste E-Mail Verbindung
./scripts/run-pdf-organizer.sh search
```

**Häufige Probleme:**
- Falsche IMAP-Einstellungen
- IMAP nicht aktiviert
- Falsche Zugangsdaten
- Firewall blockiert Port 993

### Keine E-Mails gefunden
- Überprüfe `RABOT_MATCHER_SENDER_EMAIL` Wert
- Erhöhe `RABOT_MATCHER_DAYS_BACK`
- Verwende `search` Modus zur Diagnose

### PDF-Verarbeitung Fehler
- Überprüfe Schreibrechte für Ordner
- Stelle sicher, dass PDFs nicht beschädigt sind

## 📊 Ausgabe-Beispiel

```
=== PDF ORGANIZER & RABOT MATCHER ===
Modus: download

Projektverzeichnis: /path/to/eulektro_app
✅ Umgebungsvariablen sind vollständig

Starte Verarbeitung...

🔄 Lade RABOT E-Mails herunter und organisiere PDFs...
Initializing IMAP connection...
IMAP connection ready
Found 5 emails to process
Successfully downloaded: ./rabot_rechnungen/rabot_3351_billogram_Rechnung.pdf
Successfully downloaded: ./rabot_rechnungen/rabot_3352_billogram_Rechnung.pdf

Schritt 2: Heruntergeladene PDFs organisieren...
Gefunden: 2 PDF-Dateien
--- Verarbeite rabot_3351_billogram_Rechnung.pdf ---
✅ GEFUNDENE LIEFERANSCHRIFT: "Eulektro GmbH Johann-Jacobs-Straße 9001"

=== ERFOLGREICH ABGESCHLOSSEN ===
RABOT E-Mails wurden heruntergeladen und PDFs organisiert.
Ergebnisse findest du im Ordner 'rabot_rechnungen_sortiert'.

Erstellte Ordner:
drwxr-xr-x  4 <USER>  <GROUP>  128 Jan 28 10:30 Eulektro_GmbH
drwxr-xr-x  3 <USER>  <GROUP>   96 Jan 28 10:30 Andere_Firma_GmbH

Heruntergeladene Dateien:
Gesamt PDFs im Download-Ordner: 2
```

## 🔄 Automatisierung

Du kannst das Script auch automatisiert ausführen:

```bash
# Täglich um 9:00 Uhr (crontab)
0 9 * * * cd /path/to/eulektro_app && ./scripts/run-pdf-organizer.sh download

# Oder als systemd Timer
# Siehe systemd-Dokumentation für Details
```
