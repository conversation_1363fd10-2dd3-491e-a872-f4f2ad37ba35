# Swagger API-Dokumentation

Diese Dokumentation beschreibt die implementierte Swagger API-Dokumentation für die Eulektro v1 API-Endpunkte.

## Übersicht

Die Swagger API-Dokumentation ist unter `/api/v1/docs` verfügbar und bietet eine interaktive Benutzeroberfläche zur Erkundung und zum Testen der v1 API-Endpunkte.

## Zugriff

### Swagger UI
- **URL**: `https://your-domain.com/api/v1/docs`
- **Beschreibung**: Interaktive Swagger UI mit allen v1 API-Endpunkten
- **Features**: 
  - API-Key Authentifizierung
  - Interaktive API-Tests
  - Vollständige Request/Response-Dokumentation
  - Beispiele für alle Endpunkte

### OpenAPI JSON Spezifikation
- **URL**: `https://your-domain.com/api/v1/docs/swagger.json`
- **Beschreibung**: OpenAPI 3.0 JSON-Spezifikation
- **Verwendung**: Für API-Clients, Code-Generierung, etc.

## Dokumentierte Endpunkte

### 1. Test-Endpunkte
#### `/api/v1/test/apikey`
- **GET**: API-Key Validierung testen
- **POST**: API-Key Validierung für POST-Requests testen
- **Beschreibung**: Test-Endpunkte zur Validierung von API-Keys

### 2. Charging Power
#### `/api/v1/ou/charging-power`
- **GET**: Aktuelle Ladeleistung aller Ladepunkte
- **Beschreibung**: Gibt die aktuelle Ladeleistung aller Ladepunkte einer OU zurück, inklusive heutiger kWh-Summe

### 3. Token Management
#### `/api/v1/contact/tokens`
- **POST**: Tokens erstellen/aktualisieren
- **Beschreibung**: Erstellt oder aktualisiert Tokens für einen Contact mit automatischer Longship-Synchronisation

### 4. iOS Widgets
#### `/api/v1/iOSWidgets/charging-power`
- **GET**: iOS Widget .scriptable Datei herunterladen
- **Beschreibung**: Lädt eine vorkonfigurierte .scriptable Datei für iOS Widgets herunter

## API-Key Authentifizierung

Alle API-Endpunkte erfordern einen gültigen API-Key im Authorization-Header:

### Unterstützte Formate:
```bash
# Bearer Token Format
Authorization: Bearer EUL_your_api_key_here

# ApiKey Format
Authorization: ApiKey EUL_your_api_key_here

# Direkter Key
Authorization: EUL_your_api_key_here
```

### In Swagger UI verwenden:
1. Klicken Sie auf den "Authorize" Button (🔒)
2. Geben Sie Ihren API-Key in einem der unterstützten Formate ein
3. Klicken Sie "Authorize"
4. Alle API-Calls verwenden nun automatisch Ihren API-Key

## Implementierte Dateien

### Konfiguration
- **`src/utils/swagger/swaggerConfig.ts`**: Basis-Swagger-Konfiguration mit OpenAPI 3.0 Schema
- **`src/utils/swagger/openApiSpec.ts`**: Erweiterte OpenAPI-Spezifikation mit manuell definierten Endpunkten

### API-Routen
- **`src/app/api/v1/docs/route.ts`**: Swagger UI HTML-Interface
- **`src/app/api/v1/docs/swagger.json/route.ts`**: OpenAPI JSON-Spezifikation Endpunkt

### JSDoc-Kommentare
Alle v1 API-Routen wurden mit JSDoc-Kommentaren erweitert:
- **`src/app/api/v1/test/apikey/route.ts`**: Test-Endpunkt Dokumentation
- **`src/app/api/v1/ou/charging-power/route.ts`**: Charging Power Dokumentation
- **`src/app/api/v1/contact/tokens/route.ts`**: Token Management Dokumentation
- **`src/app/api/v1/iOSWidgets/charging-power/route.ts`**: iOS Widget Dokumentation

## Features

### Interaktive API-Tests
- Direkte API-Calls aus der Swagger UI
- Automatische Request-Formatierung
- Response-Anzeige mit Syntax-Highlighting
- Fehlerbehandlung und Status-Codes

### Umfassende Dokumentation
- Detaillierte Beschreibungen für alle Endpunkte
- Request/Response-Schemas mit Beispielen
- Fehler-Codes und Fehlermeldungen
- API-Key Authentifizierung-Anweisungen

### Benutzerfreundliches Design
- Responsive Design für Desktop und Mobile
- Eulektro-Branding mit Custom-Styling
- Übersichtliche Kategorisierung nach Tags
- Suchfunktion für Endpunkte

## Verwendung für Entwickler

### Lokale Entwicklung
```bash
# Development Server starten
npm run nextDev

# Swagger UI öffnen
open http://localhost:3000/api/v1/docs
```

### API-Client Generierung
```bash
# OpenAPI Spec herunterladen
curl http://localhost:3000/api/v1/docs/swagger.json > api-spec.json

# Code-Generierung mit OpenAPI Generator
openapi-generator-cli generate -i api-spec.json -g typescript-axios -o ./generated-client
```

### Neue Endpunkte hinzufügen
1. Erstellen Sie die API-Route in `src/app/api/v1/`
2. Fügen Sie JSDoc-Kommentare mit `@swagger` Tags hinzu
3. Erweitern Sie `src/utils/swagger/openApiSpec.ts` bei Bedarf
4. Die Dokumentation wird automatisch aktualisiert

## Sicherheit

### API-Key Schutz
- Nur gültige API-Keys werden akzeptiert
- API-Keys sind an CPO-Contacts gebunden
- Automatische Validierung bei jedem Request

### CORS und Headers
- Angemessene CORS-Konfiguration
- Security-Headers für Produktionsumgebung
- Cache-Control für optimale Performance

## Wartung

### Updates
- Swagger UI wird über CDN geladen (automatische Updates)
- OpenAPI-Spezifikation wird bei Server-Start generiert
- Cache-Control für optimale Performance (1 Stunde)

### Monitoring
- Fehler-Logging für Swagger-Endpunkte
- Performance-Monitoring für API-Dokumentation
- Automatische Validierung der OpenAPI-Spezifikation

## Troubleshooting

### Häufige Probleme

#### Swagger UI lädt nicht
- Prüfen Sie die Netzwerkverbindung (CDN-Abhängigkeiten)
- Überprüfen Sie Browser-Konsole auf JavaScript-Fehler
- Stellen Sie sicher, dass der Server läuft

#### API-Key Authentifizierung funktioniert nicht
- Überprüfen Sie das API-Key Format
- Stellen Sie sicher, dass der API-Key gültig ist
- Prüfen Sie, ob der Contact CPO-Berechtigung hat

#### OpenAPI-Spezifikation ist unvollständig
- Überprüfen Sie JSDoc-Kommentare in den API-Routen
- Validieren Sie die OpenAPI-Syntax
- Prüfen Sie die Konsole auf Generierungsfehler

### Support
Bei Problemen mit der API-Dokumentation wenden Sie sich an das Entwicklungsteam oder erstellen Sie ein Issue im Repository.
